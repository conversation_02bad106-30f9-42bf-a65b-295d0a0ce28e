import { Injectable } from '@angular/core';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { ApiService } from '@shared/services/api.service';
import { Observable, of } from 'rxjs';
import { SubscriptionDetailObj, SubscriptionListObj, SubscriptionRequest } from '../models/subscription.model';
import { HttpClient } from '@angular/common/http';
import { GenericTableService } from '@shared/services/table/orll-table.interface';

@Injectable({
	providedIn: 'root',
})
export class SubscriptionRequestService extends ApiService implements GenericTableService<SubscriptionListObj> {
	constructor(http: HttpClient) {
		super(http);
	}

	loadAllData(param: any): Observable<SubscriptionListObj[]> {
		return this.postData('user-subscriptions-request/list', param);
	}

	getDataPerPage(request: SubscriptionRequest): Observable<PaginationResponse<SubscriptionListObj>> {
		if (request.fromTab && !request.groupId) {
			return of({ total: 0, pageNum: 1, rows: [] } as PaginationResponse<SubscriptionListObj>);
		}
		return this.postData('user-subscriptions-request/list', request);
	}

	getSubscriptionsDetails(id: string): Observable<SubscriptionDetailObj> {
		return super.postData<SubscriptionDetailObj>('user-subscriptions-request/detail', {
			id,
		});
	}

	updateSubscriptionsStatus(id: string, status: string): Observable<SubscriptionDetailObj> {
		return super.updateData<SubscriptionDetailObj>('user-subscriptions-request/change-status', { id, status });
	}
}
