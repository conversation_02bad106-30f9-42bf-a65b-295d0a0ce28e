.booking-create {
	margin-bottom: 40px;
	background-color: var(--iata-grey-50);
	box-shadow:
		0 1px 4px #0000000a,
		0 8px 18px #00000014;
	border-radius: 1px;
	&__option {
		padding: 0px 20px;
	}

	.option-toggle-btn {
		margin-left: auto;
		cursor: pointer;
	}
	.sub-total {
		margin-left: auto;
	}

	.sub-title {
		margin-bottom: 20px;
	}

	&__option-transport {
		background-color: var(--iata-grey-50);
		padding: 20px 20px 10px 20px;
		border-radius: 8px;
		margin: 10px 0px;
	}

	&__btn-panel {
		justify-content: flex-end;
		padding-bottom: 20px;
		margin: 0px 20px;
		gap: 20px;
	}
	.itinerary-btn-panel {
		justify-content: center;
		margin-top: 10px;
		gap: 10px;
	}
	&__option-other {
		margin-top: 10px;
	}
	.btn-pointer {
		cursor: pointer;
	}
	.add-btn,
	.remove-btn {
		border: none;
	}
	.remove-btn {
		color: var(--iata-red-500);
	}
	.flight-section {
		justify-content: center;
		margin-bottom: 10px;
	}
	.price-summary {
		margin-bottom: 10px;
		padding-left: 10px;
	}
}
