import { TestBed } from '@angular/core/testing';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { HomeDashboardService } from './home-dashboard.service';
import { ActivityStream, UserStatisticsChart } from '../models/home-dashboard.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { environment } from '@environments/environment';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('HomeDashboardService', () => {
	let service: HomeDashboardService;
	let httpMock: HttpTestingController;
	const baseUrl = environment.baseApi;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [HomeDashboardService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(HomeDashboardService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	describe('getUserStatistics', () => {
		it('should return user statistics data', () => {
			const mockUserStatistics: UserStatisticsChart[] = [
				{
					id: '1',
					type: 'total',
					userNumber: '1000',
					growingNumber: '50',
					growthRate: '5.2%',
					trendData: {
						weeks: ['2025-01-01', '2025-01-08'],
						data: [1000, 1050],
					},
				},
				{
					id: '2',
					type: 'active',
					userNumber: '800',
					growingNumber: '30',
					growthRate: '3.9%',
					trendData: {
						weeks: ['2025-01-01', '2025-01-08'],
						data: [800, 830],
					},
				},
			];

			service.getUserStatistics().subscribe((data) => {
				expect(data).toEqual(mockUserStatistics);
				expect(data.length).toBe(2);
				expect(data[0].type).toBe('total');
				expect(data[1].type).toBe('active');
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/user-statistics`);
			expect(req.request.method).toBe('GET');
			req.flush(mockUserStatistics);
		});

		it('should handle empty user statistics response', () => {
			const mockEmptyResponse: UserStatisticsChart[] = [];

			service.getUserStatistics().subscribe((data) => {
				expect(data).toEqual(mockEmptyResponse);
				expect(data.length).toBe(0);
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/user-statistics`);
			expect(req.request.method).toBe('GET');
			req.flush(mockEmptyResponse);
		});

		it('should handle HTTP error for getUserStatistics', () => {
			const errorMessage = 'Failed to fetch user statistics';

			service.getUserStatistics().subscribe({
				next: () => fail('Expected an error, not user statistics'),
				error: (error) => {
					expect(error.status).toBe(500);
					expect(error.statusText).toBe('Internal Server Error');
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/user-statistics`);
			expect(req.request.method).toBe('GET');
			req.flush(errorMessage, { status: 500, statusText: 'Internal Server Error' });
		});
	});

	describe('getActivityStream', () => {
		it('should return activity stream data with pagination', () => {
			const mockActivityStream: PaginationResponse<ActivityStream> = {
				total: 2,
				pageNum: 1,
				rows: [
					{
						id: '1',
						date: '2025-01-01T12:00:00.000Z',
						url: 'https://example.com/file1.pdf',
						fileName: 'report1.pdf',
						message: 'User initiated a POST request for Create Piece interface',
					},
					{
						id: '2',
						date: '2025-01-02T14:30:00.000Z',
						url: 'https://example.com/file2.pdf',
						fileName: 'report2.pdf',
						message: 'User completed a GET request for Retrieve Shipment interface',
					},
				],
			};

			service.getActivityStream().subscribe((data) => {
				expect(data).toEqual(mockActivityStream);
				expect(data.total).toBe(2);
				expect(data.rows.length).toBe(2);
				expect(data.rows[0].id).toBe('1');
				expect(data.rows[1].fileName).toBe('report2.pdf');
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/activity-stream`);
			expect(req.request.method).toBe('GET');
			req.flush(mockActivityStream);
		});

		it('should handle empty activity stream response', () => {
			const mockEmptyResponse: PaginationResponse<ActivityStream> = {
				total: 0,
				pageNum: 1,
				rows: [],
			};

			service.getActivityStream().subscribe((data) => {
				expect(data).toEqual(mockEmptyResponse);
				expect(data.total).toBe(0);
				expect(data.rows.length).toBe(0);
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/activity-stream`);
			expect(req.request.method).toBe('GET');
			req.flush(mockEmptyResponse);
		});

		it('should handle HTTP error for getActivityStream', () => {
			const errorMessage = 'Failed to fetch activity stream';

			service.getActivityStream().subscribe({
				next: () => fail('Expected an error, not activity stream'),
				error: (error) => {
					expect(error.status).toBe(404);
					expect(error.statusText).toBe('Not Found');
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/activity-stream`);
			expect(req.request.method).toBe('GET');
			req.flush(errorMessage, { status: 404, statusText: 'Not Found' });
		});
	});

	describe('downloadFile', () => {
		it('should download file as blob', () => {
			const fileName = 'test-report.pdf';
			const mockBlob = new Blob(['test content'], { type: 'application/pdf' });

			service.downloadFile(fileName).subscribe((blob) => {
				expect(blob).toEqual(mockBlob);
				expect(blob.type).toBe('application/pdf');
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/download/${fileName}`);
			expect(req.request.method).toBe('GET');
			expect(req.request.responseType).toBe('blob');
			req.flush(mockBlob);
		});

		it('should handle different file types', () => {
			const fileName = 'test-data.xlsx';
			const mockBlob = new Blob(['excel content'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

			service.downloadFile(fileName).subscribe((blob) => {
				expect(blob).toEqual(mockBlob);
				expect(blob.type).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/download/${fileName}`);
			expect(req.request.method).toBe('GET');
			expect(req.request.responseType).toBe('blob');
			req.flush(mockBlob);
		});

		it('should handle HTTP error for downloadFile', () => {
			const fileName = 'non-existent-file.pdf';

			service.downloadFile(fileName).subscribe({
				next: () => fail('Expected an error, not a blob'),
				error: (error) => {
					expect(error.status).toBe(404);
					expect(error.statusText).toBe('Not Found');
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/download/${fileName}`);
			expect(req.request.method).toBe('GET');
			req.error(new ProgressEvent('error'), { status: 404, statusText: 'Not Found' });
		});
	});

	describe('cleanActivityStream', () => {
		it('should clean activity stream successfully', () => {
			const mockResponse = true;

			service.cleanActivityStream().subscribe((result) => {
				expect(result).toBe(true);
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/housekeeping/activity`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toBeNull();
			req.flush(mockResponse);
		});

		it('should handle failed activity stream cleaning', () => {
			const mockResponse = false;

			service.cleanActivityStream().subscribe((result) => {
				expect(result).toBe(false);
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/housekeeping/activity`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toBeNull();
			req.flush(mockResponse);
		});

		it('should handle HTTP error for cleanActivityStream', () => {
			const errorMessage = 'Failed to clean activity stream';

			service.cleanActivityStream().subscribe({
				next: () => fail('Expected an error, not a boolean'),
				error: (error) => {
					expect(error.status).toBe(500);
					expect(error.statusText).toBe('Internal Server Error');
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/housekeeping/activity`);
			expect(req.request.method).toBe('POST');
			req.flush(errorMessage, { status: 500, statusText: 'Internal Server Error' });
		});
	});

	describe('cleanSystemData', () => {
		it('should clean system data successfully', () => {
			const mockResponse = true;

			service.cleanSystemData().subscribe((result) => {
				expect(result).toBe(true);
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/housekeeping/system`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toBeNull();
			req.flush(mockResponse);
		});

		it('should handle failed system data cleaning', () => {
			const mockResponse = false;

			service.cleanSystemData().subscribe((result) => {
				expect(result).toBe(false);
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/housekeeping/system`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toBeNull();
			req.flush(mockResponse);
		});

		it('should handle HTTP error for cleanSystemData', () => {
			const errorMessage = 'Failed to clean system data';

			service.cleanSystemData().subscribe({
				next: () => fail('Expected an error, not a boolean'),
				error: (error) => {
					expect(error.status).toBe(403);
					expect(error.statusText).toBe('Forbidden');
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/housekeeping/system`);
			expect(req.request.method).toBe('POST');
			req.flush(errorMessage, { status: 403, statusText: 'Forbidden' });
		});
	});

	describe('Service Integration Tests', () => {
		it('should handle multiple concurrent requests', () => {
			const mockUserStats: UserStatisticsChart[] = [
				{ id: '1', type: 'test', userNumber: '100', growingNumber: '10', growthRate: '10%', trendData: { weeks: [], data: [] } },
			];
			const mockActivityStream: PaginationResponse<ActivityStream> = { total: 1, pageNum: 1, rows: [] };

			// Make concurrent requests
			service.getUserStatistics().subscribe((data) => {
				expect(data).toEqual(mockUserStats);
			});

			service.getActivityStream().subscribe((data) => {
				expect(data).toEqual(mockActivityStream);
			});

			// Verify both requests were made
			const userStatsReq = httpMock.expectOne(`${baseUrl}/dashboard/user-statistics`);
			const activityStreamReq = httpMock.expectOne(`${baseUrl}/dashboard/activity-stream`);

			expect(userStatsReq.request.method).toBe('GET');
			expect(activityStreamReq.request.method).toBe('GET');

			userStatsReq.flush(mockUserStats);
			activityStreamReq.flush(mockActivityStream);
		});

		it('should properly extend ApiService', () => {
			expect(service).toBeInstanceOf(HomeDashboardService);
			expect(service.getUserStatistics).toBeDefined();
			expect(service.getActivityStream).toBeDefined();
			expect(service.downloadFile).toBeDefined();
			expect(service.cleanActivityStream).toBeDefined();
			expect(service.cleanSystemData).toBeDefined();
		});

		it('should handle special characters in file names', () => {
			const fileName = 'test file with spaces & special chars.pdf';
			const mockBlob = new Blob(['test content'], { type: 'application/pdf' });

			service.downloadFile(fileName).subscribe((blob) => {
				expect(blob).toEqual(mockBlob);
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/download/${fileName}`);
			expect(req.request.method).toBe('GET');
			req.flush(mockBlob);
		});

		it('should handle large activity stream responses', () => {
			const largeActivityStream: PaginationResponse<ActivityStream> = {
				total: 1000,
				pageNum: 1,
				// eslint-disable-next-line @typescript-eslint/naming-convention
				rows: Array.from({ length: 100 }, (_, i) => ({
					id: `${i + 1}`,
					date: `2025-01-${String(i + 1).padStart(2, '0')}T12:00:00.000Z`,
					url: `https://example.com/file${i + 1}.pdf`,
					fileName: `report${i + 1}.pdf`,
					message: `Activity message ${i + 1}`,
				})),
			};

			service.getActivityStream().subscribe((data) => {
				expect(data.total).toBe(1000);
				expect(data.rows.length).toBe(100);
				expect(data.rows[0].id).toBe('1');
				expect(data.rows[99].id).toBe('100');
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/activity-stream`);
			req.flush(largeActivityStream);
		});

		it('should handle network timeout errors', () => {
			service.getUserStatistics().subscribe({
				next: () => fail('Expected an error, not data'),
				error: (error) => {
					expect(error).toBeDefined();
					expect(error.error).toBeInstanceOf(ProgressEvent);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/dashboard/user-statistics`);
			req.error(new ProgressEvent('timeout'));
		});
	});
});
