import { ChangeDetector<PERSON><PERSON>, Component, Inject, <PERSON><PERSON><PERSON><PERSON>, Query<PERSON>ist, ViewChildren } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { AutocompleteComponent } from '@shared/components/autocomplete/autocomplete.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { Organization } from '@shared/models/organization.model';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatSortModule, Sort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { ShareType } from '@shared/models/share-type.model';
import { ShareDataService } from '@shared/services/share/share-data.service';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { SpinnerComponent } from '../spinner/spinner.component';

export interface ShareDialogData {
	title: string;
	param: string;
	shareType: ShareType;
}

@Component({
	selector: 'orll-share-dialog',
	imports: [
		AutocompleteComponent,
		MatIconModule,
		TranslateModule,
		ReactiveFormsModule,
		MatTableModule,
		MatSortModule,
		MatDialogModule,
		MatCheckboxModule,
		MatButtonModule,
		SpinnerComponent,
	],
	templateUrl: './share-dialog.component.html',
	styleUrl: './share-dialog.component.scss',
})
export class ShareDialogComponent extends RolesAwareComponent implements OnInit {
	@ViewChildren('orgTypeAutocomplete')
	autocompleteList!: QueryList<AutocompleteComponent<any>>;
	sharedOrgSearchForm: FormGroup = new FormGroup({
		orgType: new FormControl<string>(''),
	});
	dataLoading = false;
	// search function used
	selectedOrgType!: string;

	currentSort: Sort = { active: '', direction: '' };
	dataSource = new MatTableDataSource<Organization>([]);
	initialSelection = [];
	allowMultiSelect = true;
	selection = new SelectionModel<string>(this.allowMultiSelect, this.initialSelection, true);

	readonly displayedColumns: string[] = ['select', 'name', 'orgType'];
	constructor(
		public readonly orgMgmtRequestService: OrgMgmtRequestService,
		public readonly shareService: ShareDataService,
		private readonly cdr: ChangeDetectorRef,
		public dialogRef: MatDialogRef<ShareDialogComponent>,
		@Inject(MAT_DIALOG_DATA) public data: ShareDialogData
	) {
		super();
	}

	// select org type
	selectedItems($event: string) {
		this.selectedOrgType = $event;
	}

	onReset(event: any) {
		event.preventDefault();
		event.stopPropagation();
		this.sharedOrgSearchForm.reset();
		this.selectedOrgType = '';
		this.autocompleteList.forEach((autocomplete) => autocomplete.eraseValue(event));
	}

	onSearch() {
		this.dataLoading = false;
		this.orgMgmtRequestService
			.getOrgList(this.selectedOrgType)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.dataSource.data = res;
					this.cdr.markForCheck();
				},
				error: (err) => {
					console.error('org search error : ', err);
					this.dataLoading = false;
				},
			});
	}

	ngOnInit(): void {
		this.initData();
	}

	private initData() {
		this.dataLoading = true;
		this.orgMgmtRequestService
			.getOrgList()
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.dataSource.data = res;
					this.dataLoading = false;
					this.cdr.markForCheck();
				},
				error: (error) => {
					console.error('init org list error : ', error);
					this.dataLoading = false;
				},
			});
	}

	// eslint-disable-next-line @typescript-eslint/naming-convention
	trackByOrgId(_index: number, record: Organization): string {
		return record.id;
	}

	/** Whether the number of selected elements matches the total number of rows. */
	isAllSelected() {
		const numSelected = this.selection.selected.length;
		const numRows = this.dataSource.data.length;
		return numSelected === numRows;
	}

	/** Selects all rows if they are not all selected; otherwise clear selection. */
	toggleAllRows() {
		this.isAllSelected() ? this.selection.clear() : this.dataSource.data.forEach((row) => this.selection.select(row.id));
	}

	onSortChange(sort: Sort): void {
		this.currentSort = sort;
		const isAsc = sort.direction === 'asc';
		this.dataSource.data = [...this.dataSource.data].sort((a: Organization, b: Organization) => {
			if (sort.active === 'orgType') {
				return this.compare(a.orgType, b.orgType, isAsc);
			}
			return 0;
		});
	}

	onShare() {
		this.dataLoading = true;
		this.shareService.shareData(this.data.shareType, this.data.param, this.selection.selected).subscribe({
			next: () => {
				this.dataLoading = false;
				this.dialogRef.close();
			},
			error: () => {
				this.dataLoading = false;
			},
		});
		this.dialogRef.close();
	}
	private compare(a: any, b: any, isAsc: boolean) {
		return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
	}
}
