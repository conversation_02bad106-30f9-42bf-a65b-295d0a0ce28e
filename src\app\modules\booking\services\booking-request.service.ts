import { Injectable } from '@angular/core';
import { ApiService } from '@shared/services/api.service';
import { GenericTableService } from '@shared/services/table/orll-table.interface';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { BookingRequestListObj, BookingRequestObj, BookingRequestSearchParam } from '../models/booking.model';
import { BookingRequestDetailObj, Transport } from '@shared/models/booking-option.model';

@Injectable({
	providedIn: 'root',
})
export class BookingRequestService extends ApiService implements GenericTableService<BookingRequestListObj> {
	constructor(http: HttpClient) {
		super(http);
	}

	getDataPerPage(param: BookingRequestSearchParam): Observable<PaginationResponse<BookingRequestListObj>> {
		return this.getData('booking-request/list', param);
	}

	loadAllData(param: BookingRequestSearchParam): Observable<BookingRequestListObj[]> {
		return this.getData('booking-request/list', param);
	}

	createBookingRequest(data: BookingRequestObj): Observable<string> {
		return this.postData('booking-request/create', data);
	}

	getBookingRequestDetail(bookingRequestId: string): Observable<BookingRequestDetailObj> {
		return this.getData('booking-request/info', { bookingRequestId });
	}

	getBookingTransportInfo(bookingId: string): Observable<Transport[]> {
		return this.getData('booking-request/booking/info', { bookingId });
	}

	confirmBookingRequest(bookingRequestId: string): Observable<string> {
		return this.updateData('booking-request/confirm', { bookingRequestId }, true);
	}
}
