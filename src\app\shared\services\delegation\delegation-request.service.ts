import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { DelegationRequest } from '@shared/models/delegation-request';
import { ResponseObj } from '@shared/models/response.model';

@Injectable({
	providedIn: 'root',
})
export class DelegationRequestService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getDelegationList(pageParams: PaginationRequest, loId: string): Observable<PaginationResponse<DelegationRequest>> {
		return super.getData<PaginationResponse<DelegationRequest>>('access-delegations/list', {
			...pageParams,
			loId,
		});
	}

	getDelegationDetail(actionRequestId: string): Observable<DelegationRequest> {
		return super.getData<DelegationRequest>('access-delegations/detail', { actionRequestId });
	}

	requestDelegation(delegationRequest: DelegationRequest): Observable<ResponseObj<string>> {
		return super.postData<ResponseObj<string>>('access-delegations', delegationRequest);
	}

	approveDelegation(requestId: string): Observable<ResponseObj<string>> {
		return super.updateDataPatch<ResponseObj<string>>('access-delegations/accept', { requestId });
	}

	rejectDelegation(requestId: string): Observable<ResponseObj<string>> {
		return super.updateDataPatch<ResponseObj<string>>('access-delegations/reject', { requestId });
	}

	revokeDelegation(requestId: string): Observable<ResponseObj<string>> {
		return super.updateDataPatch<ResponseObj<string>>('access-delegations/revoke', { requestId });
	}
}
