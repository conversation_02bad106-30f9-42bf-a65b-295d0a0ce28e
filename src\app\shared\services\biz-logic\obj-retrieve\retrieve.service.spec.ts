import { TestBed } from '@angular/core/testing';
import { RetrieveService } from './retrieve.service';
import { environment } from '@environments/environment';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { LogisticObjType } from '@shared/models/share-type.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

const baseUrl = environment.baseApi;

describe('RetrieveService', () => {
	let service: RetrieveService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [RetrieveService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(RetrieveService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	describe('getObjDetail', () => {
		it('should call getData with efg endpoint and pagination params', () => {
			const expectedUrl = `${baseUrl}/mawb-management/retrieve-lo`;
			const mockResponse = true;

			service.getObjDetail({ loId: '1', type: LogisticObjType.HAWB.toLocaleLowerCase() }).subscribe((data) => {
				expect(data).toEqual(mockResponse);
			});

			const req = httpMock.expectOne(expectedUrl);
			expect(req.request.method).toBe('POST');
			req.flush(mockResponse);
		});
	});
});
