import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnChanges,
	Output,
	SimpleChanges,
} from '@angular/core';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { SliPieceTableComponent } from 'src/app/modules/sli-mgmt/components/sli-piece-table/sli-piece-table.component';
import { PieceList } from 'src/app/modules/sli-mgmt/models/piece/piece-list.model';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DelegationRequestComponent } from '@shared/components/delegation-request/delegation-request.component';

const PIECE_TAB_INDEX = 1;
@Component({
	selector: 'orll-hawb-piece-list',
	templateUrl: './hawb-piece-list.component.html',
	styleUrl: './hawb-piece-list.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [SliPieceTableComponent, SpinnerComponent],
})
export class HawbPieceListComponent extends DelegationRequestComponent implements OnChanges {
	@Input() hawbId = '';
	@Input() hawbNumber = '';
	@Input() sliNumber = '';
	@Input() selectedTabIndex = 0;

	@Output() refreshDelegationRequest = new EventEmitter<void>();

	pieceList: PieceList[] = [];
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};
	totalRecords = 0;
	totalQuantity = 0;
	totalSlac = 0;
	latestStatus = '';
	dataLoading = false;
	hasMoreData = true;

	constructor(
		private readonly hawbSearchRequestService: HawbSearchRequestService,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['selectedTabIndex']) {
			this.refreshData();
		}
	}

	refreshData() {
		if (this.hawbId && this.selectedTabIndex === PIECE_TAB_INDEX) {
			this.getPieceListPage(this.pageParams);
		}
	}

	onSortChange(sort: Sort): void {
		if (sort.direction === '') {
			this.pageParams.orderByColumn = '';
			this.pageParams.isAsc = '';
			return;
		}
		this.pageParams.pageNum = 1;
		this.pageParams.pageSize = 10;
		this.pageParams.orderByColumn = sort.active;
		this.pageParams.isAsc = sort.direction;
	}

	onPageChange(event: PageEvent & { sortField?: string; sortDirection?: string }): void {
		this.pageParams.pageNum = event.pageIndex + 1;
		this.pageParams.pageSize = event.pageSize;
		if (event.sortDirection) {
			this.pageParams.orderByColumn = event.sortField;
			this.pageParams.isAsc = event.sortDirection;
		}

		this.getPieceListPage(this.pageParams);
	}

	loadMoreData(): void {
		if (!this.hasMoreData || this.dataLoading) {
			return;
		}

		this.pageParams.pageNum += 1;
		this.getPieceListPage(this.pageParams, true);
	}

	private getPieceListPage(pageParams: PaginationRequest, appendData = false): void {
		this.dataLoading = true;

		this.hawbSearchRequestService.getPieceList(pageParams, this.hawbId).subscribe({
			next: (res) => {
				if (appendData) {
					this.pieceList = [...this.pieceList, ...res.rows];
					this.hasMoreData = this.pieceList.length < res.total;
				} else {
					this.pieceList = res.rows;
				}
				this.totalRecords = res.total;

				if (this.hawbId) {
					this.hawbSearchRequestService.getTotalPieceQuantity(this.hawbId).subscribe((res) => {
						this.totalQuantity = res.totalQuantity;
						this.totalSlac = res.totalSlac;
						this.latestStatus = res.eventStatus;

						this.dataLoading = false;
						this.cdr.markForCheck();
					});
				}
			},
			error: (err) => {
				this.dataLoading = false;

				// trigger delegation request when 403
				if (err.status === 403) {
					this.delegationRequest(err.error, this.hawbId)
						.pipe(takeUntilDestroyed(this.destroyRef))
						.subscribe((result) => {
							if (result) {
								this.refreshDelegationRequest.emit();
							}
						});
				}
			},
		});
	}
}
