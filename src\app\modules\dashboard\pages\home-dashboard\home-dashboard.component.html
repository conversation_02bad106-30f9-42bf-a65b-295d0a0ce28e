<div class="orll-home-dashboard">
	<h2 class="orll-home-dashboard__title">{{ 'home.dashboard.title' | translate }}</h2>

	<div class="orll-home-dashboard__document iata-box">
		<p class="orll-home-dashboard__document-title">{{ 'home.dashboard.links' | translate }}</p>

		<mat-divider></mat-divider>

		<div class="orll-home-dashboard__document-content">
			<div class="orll-home-dashboard__document-item">
				<div class="orll-home-dashboard__document-item-title">
					<mat-icon color="primary">article</mat-icon>
					<span>{{ 'home.dashboard.links.standard.title' | translate }}</span>
				</div>
				<div class="orll-home-dashboard__document-item-links">
					<span>{{ 'home.dashboard.links.standard' | translate }}</span>
					<a href="https://iata-cargo.github.io/ONE-Record/stable/" target="_blank">
						https://iata-cargo.github.io/ONE-Record/stable/
					</a>
				</div>
			</div>

			<mat-divider class="margin-b-20"></mat-divider>

			<div class="orll-home-dashboard__document-item">
				<div class="orll-home-dashboard__document-item-title">
					<mat-icon color="primary">article</mat-icon>
					<span>{{ 'home.dashboard.links.api.title' | translate }}</span>
				</div>
				<div class="orll-home-dashboard__document-item-links">
					<span>{{ 'home.dashboard.links.api' | translate }}</span>
					<a href="https://iata-cargo.github.io/ONE-Record/stable/API-Security/" target="_blank">
						https://iata-cargo.github.io/ONE-Record/stable/API-Security/
					</a>
				</div>
			</div>

			<mat-divider class="margin-b-20"></mat-divider>

			<div class="orll-home-dashboard__document-item">
				<div class="orll-home-dashboard__document-item-title">
					<mat-icon color="primary">article</mat-icon>
					<span>{{ 'home.dashboard.links.dataModel.title' | translate }}</span>
				</div>
				<div class="orll-home-dashboard__document-item-links">
					<span>{{ 'home.dashboard.links.dataModel' | translate }}</span>
					<a href="https://iata-cargo.github.io/ONE-Record/stable/Data-Model/" target="_blank">
						https://iata-cargo.github.io/ONE-Record/stable/Data-Model/
					</a>
				</div>
			</div>
		</div>
	</div>

	@if (isSuperUser() | async) {
		<div class="orll-home-dashboard__chart iata-box col-12">
			<div class="chart-wrapper total col-4">
				<div class="chart-container">
					<canvas
						baseChart
						[data]="getLineChartData(weeklyTotalData, fillGradientTotal, '#289632')"
						[options]="getLineChartOptions('home.dashboard.chart.weeklyTotal')"
						[type]="lineChartType"></canvas>
				</div>
				<div class="chart-container__summary">
					<div class="chart-container__summary-item">
						<div>{{ 'home.dashboard.chart.weeklyTotalUser' | translate }}:</div>
						<div class="right-number">{{ totalUsers }}</div>
					</div>
					<div class="chart-container__summary-item">
						<div>{{ 'home.dashboard.chart.growthNumber' | translate }}:</div>
						<div class="right-number">{{ totalGrowthNumber }}</div>
					</div>
					<div class="chart-container__summary-item">
						<div>{{ 'home.dashboard.chart.growthRate' | translate }}:</div>
						<div class="right-number">{{ totalGrowthRate }}</div>
					</div>
				</div>
			</div>

			<mat-divider [vertical]="true"></mat-divider>

			<div class="chart-wrapper external col-4">
				<div class="chart-container">
					<canvas
						baseChart
						[data]="getLineChartData(weeklyExternalData, fillGradientExternal, '#fac832')"
						[options]="getLineChartOptions('home.dashboard.chart.weeklyExternal')"
						[type]="lineChartType"></canvas>
				</div>
				<div class="chart-container__summary">
					<div class="chart-container__summary-item">
						<div>{{ 'home.dashboard.chart.weeklyTotalExternalUser' | translate }}:</div>
						<div class="right-number">{{ totalExternalUsers }}</div>
					</div>
					<div class="chart-container__summary-item">
						<div>{{ 'home.dashboard.chart.growthNumber' | translate }}:</div>
						<div class="right-number">{{ externalGrowthNumber }}</div>
					</div>
					<div class="chart-container__summary-item">
						<div>{{ 'home.dashboard.chart.growthRate' | translate }}:</div>
						<div class="right-number">{{ externalGrowthRate }}</div>
					</div>
				</div>
			</div>

			<mat-divider [vertical]="true"></mat-divider>

			<div class="chart-wrapper active col-4">
				<div class="chart-container">
					<canvas
						baseChart
						[data]="getLineChartData(weeklyActiveData, fillGradientActive, '#f04632')"
						[options]="getLineChartOptions('home.dashboard.chart.weeklyActive')"
						[type]="lineChartType"></canvas>
				</div>
				<div class="chart-container__summary">
					<div class="chart-container__summary-item">
						<div>{{ 'home.dashboard.chart.weeklyTotalActiveUser' | translate }}:</div>
						<div class="right-number">{{ totalActiveUsers }}</div>
					</div>
					<div class="chart-container__summary-item">
						<div>{{ 'home.dashboard.chart.growthNumber' | translate }}:</div>
						<div class="right-number">{{ activeGrowthNumber }}</div>
					</div>
					<div class="chart-container__summary-item">
						<div>{{ 'home.dashboard.chart.growthRate' | translate }}:</div>
						<div class="right-number">{{ activeGrowthRate }}</div>
					</div>
				</div>
			</div>
		</div>
	}

	<div class="orll-home-dashboard__activity iata-box">
		<div class="orll-home-dashboard__activity-header">
			<span class="orll-home-dashboard__activity-title">{{ 'home.dashboard.activity.stream' | translate }}</span>
			@if (isSuperUser() | async) {
				<div class="orll-home-dashboard__activity-header-right">
					<button
						mat-flat-button
						color="warn"
						(click)="openConfirmDialog('home.dashboard.activity.system.clean', 'home.dashboard.activity.system.clean.msg')"
						class="data-button">
						<mat-icon>cleaning_services</mat-icon>
						{{ 'home.dashboard.activity.system.clean' | translate }}
					</button>
					<button
						mat-flat-button
						color="primary"
						(click)="openConfirmDialog('home.dashboard.activity.stream.clean', 'home.dashboard.activity.stream.clean.msg')"
						class="stream-button">
						<mat-icon>cleaning_services</mat-icon>
						{{ 'home.dashboard.activity.stream.clean' | translate }}
					</button>
				</div>
			}
		</div>

		<mat-divider></mat-divider>

		<div class="orll-home-dashboard__activity-scroll-container" #scrollContainer>
			<div class="orll-home-dashboard__activity-scroll-content" #scrollContent>
				@for (item of activityStreamData; track item.id) {
					<div class="orll-home-dashboard__activity-content">
						<div class="orll-home-dashboard__activity-content-item">
							<p class="date">{{ item.date | iataDateFormat: 'dd MMM yyyy HH:mm' }}</p>
							<div class="link">
								<span>{{ item.message }}</span>
								<span class="download" (click)="downloadActivityFile(item)" (keydown)="$event.preventDefault()">
									{{ 'home.dashboard.activity.stream.download' | translate }}
								</span>
							</div>
						</div>
					</div>
					@if (!$last) {
						<mat-divider></mat-divider>
					}
				}
			</div>
		</div>
	</div>
</div>
