import { Directive, HostListener, Input } from '@angular/core';
import { CommonService } from '@shared/services/common.service';

@Directive({
	selector: '[orllCopy]',
})
export class OrllCopyDirective {
	@Input('orllCopy') textToCopy = '';

	constructor(private readonly commonService: CommonService) {}

	@HostListener('click')
	onClick() {
		this.copyText();
	}

	private async copyText() {
		const text = this.textToCopy;

		if (!text) {
			this.commonService.showWarning('common.copy.no.text');
			return;
		}

		try {
			await navigator.clipboard.writeText(text);
			this.commonService.showWarning('common.copy.success');
		} catch {
			this.commonService.showWarning('common.copy.failed');
		}
	}
}
