import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FormControl, FormGroup } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { CurrencyInputComponent } from './currency-input.component';

describe('CurrencyInputComponent', () => {
	let component: CurrencyInputComponent;
	let fixture: ComponentFixture<CurrencyInputComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [CurrencyInputComponent, NoopAnimationsModule],
		}).compileComponents();

		fixture = TestBed.createComponent(CurrencyInputComponent);
		component = fixture.componentInstance;
		component.currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD'];
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('Component Initialization', () => {
		it('should initialize with default values', () => {
			expect(component.formLabel).toBe('');
			expect(component.fieldName).toBe('');
			expect(component.hiddenUnit).toBe(false);
			expect(component.pattern).toBe('2');
			expect(component.defaultValue).toBe('');
			expect(component.numericalValue).toBe('');
			expect(component.currencyUnit).toBe('Unit');
		});

		it('should initialize currencyForm with correct structure', () => {
			expect(component.currencyForm.get('currencyUnit')).toBeTruthy();
			expect(component.currencyForm.get('numericalValue')).toBeTruthy();
			expect(component.currencyForm.get('currencyUnit')?.value).toBe('');
			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should initialize with custom form', () => {
			const customForm = new FormGroup({
				currencyUnit: new FormControl<string>('USD'),
				numericalValue: new FormControl<number | null>(100.5),
			});
			component.currencyForm = customForm;

			expect(component.currencyForm.get('currencyUnit')?.value).toBe('USD');
			expect(component.currencyForm.get('numericalValue')?.value).toBe(100.5);
		});

		it('should set filteredCurrency to all currencies after ngOnInit', () => {
			expect(component.filteredCurrency).toEqual(['USD', 'EUR', 'GBP', 'JPY', 'CAD']);
		});
	});

	describe('ngOnInit', () => {
		it('should subscribe to currencyUnit value changes', fakeAsync(() => {
			component.ngOnInit();

			component.currencyForm.get('currencyUnit')?.setValue('US');
			tick();

			expect(component.filteredCurrency).toEqual(['USD']);
		}));

		it('should filter currencies on initialization with empty string', fakeAsync(() => {
			component.ngOnInit();
			tick();

			expect(component.filteredCurrency).toEqual(['USD', 'EUR', 'GBP', 'JPY', 'CAD']);
		}));

		it('should handle null search value', fakeAsync(() => {
			component.ngOnInit();

			component.currencyForm.get('currencyUnit')?.setValue(null);
			tick();

			expect(component.filteredCurrency).toEqual(['USD', 'EUR', 'GBP', 'JPY', 'CAD']);
		}));
	});

	describe('ngOnChanges', () => {
		it('should set numericalValue to defaultValue when form value is null', () => {
			component.defaultValue = '0.00';
			component.currencyForm.patchValue({ numericalValue: null });

			component.ngOnChanges();

			expect(component.numericalValue).toBe('0.00');
		});

		it('should set numericalValue to defaultValue when form value is undefined', () => {
			component.defaultValue = '10.50';
			component.currencyForm.patchValue({ numericalValue: undefined as any });

			component.ngOnChanges();

			expect(component.numericalValue).toBe('10.50');
		});

		it('should set numericalValue to string representation of form value', () => {
			component.currencyForm.patchValue({ numericalValue: 123.45 });

			component.ngOnChanges();

			expect(component.numericalValue).toBe('123.45');
		});

		it('should handle zero value correctly', () => {
			component.currencyForm.patchValue({ numericalValue: 0 });

			component.ngOnChanges();

			expect(component.numericalValue).toBe('0');
		});

		it('should handle negative values', () => {
			component.currencyForm.patchValue({ numericalValue: -50.25 });

			component.ngOnChanges();

			expect(component.numericalValue).toBe('-50.25');
		});
	});

	describe('filterCurrency', () => {
		it('should return all currencies when search is empty', () => {
			const result = component.filterCurrency('');
			expect(result).toEqual(['USD', 'EUR', 'GBP', 'JPY', 'CAD']);
		});

		it('should filter currencies case-insensitively', () => {
			const result = component.filterCurrency('usd');
			expect(result).toEqual(['USD']);
		});

		it('should filter currencies with partial match', () => {
			const result = component.filterCurrency('U');
			expect(result).toEqual(['USD', 'EUR']);
		});

		it('should return empty array when no match found', () => {
			const result = component.filterCurrency('XYZ');
			expect(result).toEqual([]);
		});

		it('should handle null search parameter', () => {
			const result = component.filterCurrency(null as any);
			expect(result).toEqual(['USD', 'EUR', 'GBP', 'JPY', 'CAD']);
		});

		it('should handle undefined search parameter', () => {
			const result = component.filterCurrency(undefined as any);
			expect(result).toEqual(['USD', 'EUR', 'GBP', 'JPY', 'CAD']);
		});

		it('should trim whitespace from search', () => {
			const result = component.filterCurrency('  EUR  ');
			expect(result).toEqual(['EUR']);
		});

		it('should handle special characters in search', () => {
			component.currencies = ['USD', 'EUR', 'GBP-X', 'JPY@'];
			const result = component.filterCurrency('GBP-');
			expect(result).toEqual(['GBP-X']);
		});
	});

	describe('onNumericalValueChange', () => {
		it('should set valid numerical value when input matches regex', () => {
			component.onNumericalValueChange('123.45');

			expect(component.currencyForm.get('numericalValue')?.value).toBe(123.45);
		});

		it('should set null when input does not match regex', () => {
			component.onNumericalValueChange('invalid');

			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should handle valid integer values', () => {
			component.onNumericalValueChange('100');

			expect(component.currencyForm.get('numericalValue')?.value).toBe(100);
		});

		it('should handle valid decimal values with one decimal place', () => {
			component.onNumericalValueChange('99.5');

			expect(component.currencyForm.get('numericalValue')?.value).toBe(99.5);
		});

		it('should handle valid decimal values with two decimal places', () => {
			component.onNumericalValueChange('99.99');

			expect(component.currencyForm.get('numericalValue')?.value).toBe(99.99);
		});

		it('should reject values with more than two decimal places', () => {
			component.onNumericalValueChange('99.999');

			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should reject values starting with zero followed by digits', () => {
			component.onNumericalValueChange('01.50');

			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should accept zero with decimal places', () => {
			component.onNumericalValueChange('0.50');

			expect(component.currencyForm.get('numericalValue')?.value).toBe(0.5);
		});

		it('should reject zero followed by only zeros', () => {
			component.onNumericalValueChange('0.00');

			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should reject negative values', () => {
			component.onNumericalValueChange('-50.25');

			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should reject empty string', () => {
			component.onNumericalValueChange('');

			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should reject values with letters', () => {
			component.onNumericalValueChange('12a.50');

			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should reject values with special characters', () => {
			component.onNumericalValueChange('12@.50');

			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should handle large valid numbers', () => {
			component.onNumericalValueChange('999999.99');

			expect(component.currencyForm.get('numericalValue')?.value).toBe(999999.99);
		});
	});

	describe('onNumericalValueBlur', () => {
		it('should reset to defaultValue when numericalValue is invalid', () => {
			component.numericalValue = 'invalid';
			component.defaultValue = '0.00';

			component.onNumericalValueBlur();

			expect(component.numericalValue).toBe('0.00');
			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should not change numericalValue when it is valid', () => {
			component.numericalValue = '123.45';
			component.defaultValue = '0.00';

			component.onNumericalValueBlur();

			expect(component.numericalValue).toBe('123.45');
		});

		it('should not change numericalValue when it is empty', () => {
			component.numericalValue = '';
			component.defaultValue = '0.00';

			component.onNumericalValueBlur();

			expect(component.numericalValue).toBe('');
		});

		it('should reset when numericalValue has too many decimal places', () => {
			component.numericalValue = '123.456';
			component.defaultValue = '10.00';

			component.onNumericalValueBlur();

			expect(component.numericalValue).toBe('10.00');
			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should reset when numericalValue starts with zero and has digits', () => {
			component.numericalValue = '01.50';
			component.defaultValue = '5.00';

			component.onNumericalValueBlur();

			expect(component.numericalValue).toBe('5.00');
			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();
		});

		it('should not reset when numericalValue is valid zero decimal', () => {
			component.numericalValue = '0.50';
			component.defaultValue = '1.00';

			component.onNumericalValueBlur();

			expect(component.numericalValue).toBe('0.50');
		});

		it('should handle null numericalValue', () => {
			component.numericalValue = null as any;
			component.defaultValue = '0.00';

			component.onNumericalValueBlur();

			expect(component.numericalValue).toBeNull();
		});

		it('should handle undefined numericalValue', () => {
			component.numericalValue = undefined as any;
			component.defaultValue = '0.00';

			component.onNumericalValueBlur();

			expect(component.numericalValue).toBeUndefined();
		});
	});

	describe('Component Integration', () => {
		it('should handle complete workflow from initialization to value change', fakeAsync(() => {
			component.defaultValue = '0.00';
			component.currencies = ['USD', 'EUR'];

			// Initialize
			component.ngOnInit();
			tick();

			// Filter currencies
			component.currencyForm.get('currencyUnit')?.setValue('US');
			tick();
			expect(component.filteredCurrency).toEqual(['USD']);

			// Set valid numerical value
			component.onNumericalValueChange('100.50');
			expect(component.currencyForm.get('numericalValue')?.value).toBe(100.5);

			// Trigger ngOnChanges
			component.ngOnChanges();
			expect(component.numericalValue).toBe('100.5');
		}));

		it('should handle error recovery workflow', () => {
			component.defaultValue = '5.00';

			// Set invalid value
			component.onNumericalValueChange('invalid');
			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();

			// Trigger ngOnChanges with null value
			component.ngOnChanges();
			expect(component.numericalValue).toBe('5.00');

			// Set invalid value in numericalValue and blur
			component.numericalValue = 'still invalid';
			component.onNumericalValueBlur();
			expect(component.numericalValue).toBe('5.00');
		});

		it('should handle edge case with zero values', () => {
			component.defaultValue = '0.00';

			// Test valid zero decimal
			component.onNumericalValueChange('0.01');
			expect(component.currencyForm.get('numericalValue')?.value).toBe(0.01);

			// Test invalid zero
			component.onNumericalValueChange('0.00');
			expect(component.currencyForm.get('numericalValue')?.value).toBeNull();

			// Test blur with invalid zero format
			component.numericalValue = '0.00';
			component.onNumericalValueBlur();
			expect(component.numericalValue).toBe('0.00');
		});
	});
});
