import { Component } from '@angular/core';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { NotificationComponent } from '../../components/notification/notification.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { NotificationService } from '../../services/notification.service';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';

@Component({
	selector: 'orll-notification-list',
	imports: [TranslateModule, MatSlideToggleModule, NotificationComponent, MatIconModule, MatButtonModule, FormsModule, SpinnerComponent],
	templateUrl: './notification-list.component.html',
	styleUrl: './notification-list.component.scss',
})
export default class NotificationListComponent {
	showUnread = false;
	dataLoading = false;

	constructor(private readonly service: NotificationService) {}

	markAllViewed() {
		this.dataLoading = true;
		this.service.markAllRead().subscribe({
			next: () => {
				this.dataLoading = false;
			},
			error: () => {
				this.dataLoading = false;
			},
		});
	}

	onToggleChange(event: MatSlideToggleChange) {
		this.showUnread = event.checked;
	}
}
