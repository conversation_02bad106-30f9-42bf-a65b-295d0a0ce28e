import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BookingOptionRequestDetailObj, BookingOptionRequestListObj, OptionRequestDetail } from '../../models/booking.model';
// eslint-disable-next-line @typescript-eslint/naming-convention
import OrllDialogComponent from '@shared/components/dialog-template/dialog-template.component';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { buildOptionRequestDetail } from '../../util/booking.util';
import { BookingOptionRequestFormComponent } from '../booking-option-request-form/booking-option-request-form.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
	selector: 'orll-booking-option-request-create',
	imports: [TranslateModule, MatDialogModule, MatIconModule, MatButtonModule, OrllDialogComponent, BookingOptionRequestFormComponent],
	templateUrl: './booking-option-request-create.component.html',
	styleUrl: './booking-option-request-create.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BookingOptionRequestCreateComponent extends RolesAwareComponent implements OnInit, AfterViewInit {
	dataLoading = false;

	@ViewChild(BookingOptionRequestFormComponent)
	bookingOptionRequestFormComponent!: BookingOptionRequestFormComponent;

	constructor(
		private readonly bookingService: BookingOptionRequestService,
		private readonly dialog: MatDialog,
		private readonly dialogRef: MatDialogRef<BookingOptionRequestCreateComponent>,
		private readonly translateService: TranslateService,
		private readonly cdr: ChangeDetectorRef,
		@Inject(MAT_DIALOG_DATA) public data: BookingOptionRequestListObj | OptionRequestDetail
	) {
		super();
	}

	ngAfterViewInit(): void {
		if (!this.data?.bookingOptionRequestId) {
			this.patchValueFromMawb();
		}
	}

	ngOnInit() {
		if (this.data?.bookingOptionRequestId) {
			this.dataLoading = true;
			this.bookingService.getBookingOptionDetail(this.data.bookingOptionRequestId).subscribe((res) => {
				this.patchDetailValue(res);
				this.dataLoading = false;
				this.cdr.markForCheck();
			});
		}
	}

	private patchValueFromMawb() {
		this.bookingService
			.getAirports()
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((res) => {
				this.bookingOptionRequestFormComponent.locationList = res;

				if (this.data) {
					this.bookingOptionRequestFormComponent.bookingOptionReqForm.patchValue(this.data);
					this.bookingOptionRequestFormComponent.bookingOptionReqForm.controls['departureLocation'].setValue(
						this.bookingOptionRequestFormComponent.locationList.find((item) => item.code === this.data.departureLocation)
					);
					this.bookingOptionRequestFormComponent.bookingOptionReqForm.controls['arrivalLocation'].setValue(
						this.bookingOptionRequestFormComponent.locationList.find((item) => item.code === this.data.arrivalLocation)
					);
				}
			});
	}

	private patchDetailValue(res: BookingOptionRequestDetailObj) {
		const detail = buildOptionRequestDetail(res, this.bookingOptionRequestFormComponent.commodityCodeList);
		this.bookingOptionRequestFormComponent.bookingOptionReqForm.patchValue({
			...detail,
			specialHandlingCodes: detail.specialHandlingCodes ? detail.specialHandlingCodes.split(';') : [],
			expectedCommodity: res.bookingShipmentDetails.expectedCommodity ?? '',
		});
		this.bookingOptionRequestFormComponent.bookingOptionReqForm.controls['departureLocation'].setValue(
			this.bookingOptionRequestFormComponent.locationList.find(
				(item) => item.code === (res.transportLegs[0]?.departureLocation?.locationCodes[0]?.code ?? '')
			)
		);
		this.bookingOptionRequestFormComponent.bookingOptionReqForm.controls['arrivalLocation'].setValue(
			this.bookingOptionRequestFormComponent.locationList.find(
				(item) => item.code === (res.transportLegs[0]?.arrivalLocation?.locationCodes[0]?.code ?? '')
			)
		);
	}

	sendOptionRequest() {
		this.bookingOptionRequestFormComponent.bookingOptionReqForm.markAllAsTouched();
		if (this.bookingOptionRequestFormComponent.bookingOptionReqForm.invalid) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: { content: this.translateService.instant('common.dialog.form.validate') },
			});
			return;
		}
		this.dataLoading = true;
		this.bookingService
			.createBookingOptionRequest(this.bookingOptionRequestFormComponent.getFormData() as BookingOptionRequestDetailObj)
			.subscribe({
				next: () => {
					this.dataLoading = false;
					this.cdr.markForCheck();
					this.dialogRef.close(true);
				},
				error: () => {
					this.dataLoading = false;
					this.cdr.markForCheck();
					this.dialogRef.close();
				},
			});
	}
}
