<div class="orll-shared-dialog">
	<h2 mat-dialog-title>{{ data.title }}</h2>
	<mat-dialog-content>
		<div class="orll-shared-dialog__content">
			<form [formGroup]="sharedOrgSearchForm">
				<div class="orll-shared-dialog__search">
					<div class="orll-shared-dialog__search__input">
						<iata-autocomplete
							#orgTypeAutocomplete
							[id]="'orgType'"
							[api]="orgMgmtRequestService"
							[label]="'shared.table.column.orgType' | translate"
							[multiple]="false"
							(selected)="selectedItems($event)">
						</iata-autocomplete>
					</div>
					<div>
						<button mat-stroked-button color="primary" (click)="onReset($event)" class="orll-shared-dialog__reset-button">
							<mat-icon>refresh</mat-icon>
							{{'sli.mgmt.reset' | translate}}
						</button>
						<button mat-flat-button color="primary" [disabled]="sharedOrgSearchForm.invalid || dataLoading" (click)="onSearch()"
							fxLayout="row" fxLayoutAlign="center center">
							<mat-icon>search</mat-icon>
							{{'sli.mgmt.search' | translate}}
						</button>
					</div>
				</div>
			</form>
			<div class="orll-shared-org-dialog_table">
				<table mat-table [dataSource]="dataSource" [trackBy]="trackByOrgId"
					matSort
					(matSortChange)="onSortChange($event)"
					aria-label="Company table"
					class="orll-shared-org-table__mat">
					<ng-container matColumnDef="select">
					<th mat-header-cell *matHeaderCellDef>
						<mat-checkbox (change)="$event ? toggleAllRows() : null"
									[checked]="selection.hasValue() && isAllSelected()"
									[indeterminate]="selection.hasValue() && !isAllSelected()">
						</mat-checkbox>
					</th>
					<td mat-cell *matCellDef="let row">
						<mat-checkbox (click)="$event.stopPropagation()" 	(keydown.enter)="$event.stopPropagation()"
									(change)="$event.checked ? selection.select(row.id) : selection.deselect(row.id)"
									[checked]="selection.isSelected(row.id)">
						</mat-checkbox>
					</td>
					</ng-container>
					<ng-container matColumnDef="name">
						<th scope="col" mat-header-cell *matHeaderCellDef class="org-name-width">{{'shared.table.column.name' | translate}}</th>
						<td mat-cell *matCellDef="let row">
								{{row.name}}
						</td>
					</ng-container>

					<ng-container matColumnDef="orgType">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="org-type-width">{{'shared.table.column.orgType' | translate}}</th>
						<td mat-cell *matCellDef="let row">{{row.orgType}}</td>
					</ng-container>
					<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
					<tr mat-row *matRowDef="let row; columns: displayedColumns;" class="orll-mawb-table__row"></tr>
				</table>
			</div>
		</div>
	</mat-dialog-content>
	<mat-dialog-actions class="orll-shared-dialog__btn">
		<button mat-button (click)="dialogRef.close()">{{'common.dialog.cancel' | translate}}</button>
		<button mat-flat-button color="primary" (click)="onShare()" >{{'shared.button.name' | translate}}	<mat-icon>share</mat-icon></button>
	</mat-dialog-actions>
	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>



