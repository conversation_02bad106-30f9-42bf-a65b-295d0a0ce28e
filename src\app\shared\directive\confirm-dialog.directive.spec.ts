import { ConfirmDialogDirective } from './confirm-dialog.directive';
import { Component, ElementRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { By } from '@angular/platform-browser';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';

@Component({
	template: `<button confirmDialog (confirm)="onConfirm()">Click me</button>`,
	imports: [ConfirmDialogDirective],
})
class TestComponent {
	onConfirm() {
		return;
	}
}

describe('ConfirmDialogDirective', () => {
	let fixture: ComponentFixture<TestComponent>;
	let directiveEl: ElementRef;
	let mockDialog: jasmine.SpyObj<MatDialog>;
	let mockTranslateService: jasmine.SpyObj<TranslateService>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<any>>;

	beforeEach(() => {
		mockDialog = jasmine.createSpyObj('MatDialog', ['open', 'close']);
		mockTranslateService = jasmine.createSpyObj('TranslateService', ['instant']);
		mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
		mockDialogRef.afterClosed.and.returnValue(of(true));
		mockDialog.open.and.returnValue(mockDialogRef);

		TestBed.configureTestingModule({
			imports: [ConfirmDialogDirective, TestComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: TranslateService, useValue: mockTranslateService },
			],
		});

		fixture = TestBed.createComponent(TestComponent);
		directiveEl = fixture.debugElement.query(By.css('button'));
		fixture.detectChanges();
	});

	it('should create an instance', () => {
		expect(fixture.componentInstance).toBeTruthy();
	});

	it('should open dialog when click', () => {
		mockDialog.open.and.returnValue(mockDialogRef);
		directiveEl.nativeElement.click();
		fixture.detectChanges();

		expect(mockDialog.open).toHaveBeenCalled();
	});

	it('should emit confirm event when confirmed', () => {
		spyOn(fixture.componentInstance, 'onConfirm');
		directiveEl.nativeElement.click();
		fixture.detectChanges();
		expect(fixture.componentInstance.onConfirm).toHaveBeenCalled();
	});
});
