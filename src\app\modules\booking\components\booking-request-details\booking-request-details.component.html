<div class="orll-booking-request-details">
	<h2 mat-dialog-title>
		<span>{{ title | translate }}</span>
		<mat-icon class="orll-booking-request-details__clear_dialog" [matDialogClose]="'cancel'">clear_round</mat-icon>
	</h2>
	<mat-dialog-content>
		<div class="iata-box">
			<div class="row">
				<div class="col-12 sub-title">
					<span>{{ bookingRequestDetails?.forwarderName }}</span>
					<span>|</span>
					<span>{{ bookingRequestDetails?.forwarderIataCode }}</span>
				</div>
			</div>

			<div class="row">
				<div class="col-3">
					<mat-label>{{ 'booking.request.pieceGroupCount' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>{{ bookingRequestDetails?.pieceGroupCount }}</span>
				</div>
				<div class="col-3">
					<mat-label>{{ 'booking.request.totalGrossWeight' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>{{ bookingRequestDetails?.totalGrossWeight }}KG</span>
				</div>
				<div class="col-3">
					<mat-label>{{ 'booking.request.chargeableWeight' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>{{ bookingRequestDetails?.chargeableWeight }}KG</span>
				</div>
				<div class="col-3">
					<mat-label>{{ 'booking.request.totalDimensions' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>
						{{ bookingRequestDetails?.totalDimensions?.length }}CMx{{ bookingRequestDetails?.totalDimensions?.width }}CMx{{
							bookingRequestDetails?.totalDimensions?.height
						}}CM
					</span>
				</div>
			</div>

			<div class="row">
				<div class="col-3">
					<mat-label>{{ 'booking.request.expectedCommodity' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>{{ bookingRequestDetails?.expectedCommodity }}</span>
				</div>
				<div class="col-3">
					<mat-label>{{ 'booking.request.specialHandlingCodes' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>{{ bookingRequestDetails?.specialHandlingCodes?.join(';') }}</span>
				</div>
				<div class="col-3">
					<mat-label>{{ 'booking.request.textualHandlingInstructions' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>{{ bookingRequestDetails?.textualHandlingInstructions }}</span>
				</div>
				<div class="col-3">
					<mat-label>{{ 'mawb.formItem.mawbNumber' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>{{ bookingRequestDetails?.waybillNumber }}</span>
				</div>
			</div>
		</div>

		<orll-booking-transport [bookingRequestDetails]="bookingRequestDetails"></orll-booking-transport>

		<div class="mat-dialog-actions">
			<button mat-stroked-button color="primary" (click)="onCancel()" class="orll-booking-request-details__cancel-button">
				{{ 'common.dialog.cancel' | translate }}
			</button>
			@if (confirmedStatus && (hasSomeRole(forwarder) | async)) {
				<button mat-flat-button color="primary" (click)="onUpdate()" class="orll-booking-request-details__reverse-icon update">
					<mat-icon>update</mat-icon>
					{{ 'booking.dialog.button.update.master' | translate }}
				</button>
				<button mat-flat-button color="primary" (click)="onDone()" class="orll-booking-request-details__reverse-icon create">
					<mat-icon>add</mat-icon>
					{{ buttonName | translate }}
				</button>
			}
			@if (!confirmedStatus) {
				<button
					mat-flat-button
					color="primary"
					(click)="onDone()"
					[disabled]="!canConfirm"
					class="orll-booking-request-details__reverse-icon done">
					<mat-icon>done</mat-icon>
					{{ buttonName | translate }}
				</button>
			}
		</div>
	</mat-dialog-content>
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
