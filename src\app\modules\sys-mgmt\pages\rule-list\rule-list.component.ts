import { Component } from '@angular/core';
import { OrllTableComponent } from '@shared/components/orll-table/orll-table.component';
import { OrllColumnDef } from '@shared/models/orlll-common-table';
import { RuleListObj } from '../../models/rule.model';
import { RuleService } from '../../services/rule.service';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { RuleDetailComponent } from '../../components/rule-detail/rule-detail.component';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { Organization } from '@shared/models/organization.model';

@Component({
	selector: 'orll-rule-list',
	imports: [OrllTableComponent, MatDialogModule, TranslateModule, MatIconModule, MatDividerModule, MatButtonModule],
	templateUrl: './rule-list.component.html',
	styleUrl: './rule-list.component.scss',
})
export default class RuleListComponent {
	dataLoading = false;
	orgList: Organization[] = [];
	param: { dateTime: number } = { dateTime: new Date().getMilliseconds() };
	columns: OrllColumnDef<RuleListObj>[] = [
		{
			key: 'holderNames',
			header: 'system.rule.table.holder',
		},
		{
			key: 'requestTypeDescription',
			header: 'system.rule.table.request.type',
		},
		{
			key: 'requestNames',
			header: 'system.rule.table.requester',
		},
		{
			key: 'actionDescription',
			header: 'system.rule.table.action',
		},
		{
			key: 'actions',
			header: 'common.table.action',
			actions: [
				{
					iconKey: 'edit',
					iconClickAction: (row: RuleListObj) => this.createOrUpdateRule(row),
				},
				{
					iconKey: 'delete',
					iconClickAction: (row: RuleListObj) => this.deleteRule(row),
				},
			],
		},
	];

	constructor(
		public readonly ruleService: RuleService,
		private readonly dialog: MatDialog,
		private readonly translateService: TranslateService
	) {}

	createOrUpdateRule(row?: RuleListObj) {
		const dialogRef = this.dialog.open(RuleDetailComponent, {
			width: '60vw',
			autoFocus: false,
			data: row,
		});
		dialogRef.afterClosed().subscribe((res) => {
			if (res === true) {
				this.param = { ...this.param };
			}
		});
	}

	deleteRule(row: RuleListObj) {
		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translateService.instant('common.dialog.delete.content'),
			},
		});

		dialogRef.afterClosed().subscribe((res) => {
			if (res === true) {
				this.dataLoading = true;
				this.ruleService.deleteRule(row).subscribe({
					next: () => {
						this.dataLoading = false;
						this.param = { ...this.param };
					},
					error: () => (this.dataLoading = false),
				});
			}
		});
	}
}
