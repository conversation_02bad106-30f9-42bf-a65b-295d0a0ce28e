import { ComponentFix<PERSON>, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import BookingOptionCreateComponent from './booking-option-create.component';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { BookingOptionRequestDetailObj, OptionRequestDetail, PRICE_LIST, TRANS_LIST } from '../../models/booking.model';
import { of } from 'rxjs';
import { UserProfileService } from '@shared/services/user-profile.service';
import { Router } from '@angular/router';
import { CommonService } from '@shared/services/common.service';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { TranslateModule } from '@ngx-translate/core';
import { ShipmentParty } from 'src/app/modules/sli-mgmt/models/shipment-party.model';
import { By } from '@angular/platform-browser';
import { UserProfile } from '@shared/models/user-profile.model';

function createMockUserProfile(overrides: Partial<UserProfile> = {}): UserProfile {
	return {
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'ORG_TYPE',
		userType: '1',
		menuList: [],
		permissionList: [],
		orgList: [],
		...overrides,
	};
}

const mockDetailRes: BookingOptionRequestDetailObj = {
	bookingShipmentDetails: {
		id: '',
		pieceGroups: {
			id: '',
			pieceGroupCount: 0,
		},
		totalGrossWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		chargeableWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		dimensions: {
			length: 0,
			width: 0,
			height: 0,
		},
		expectedCommodity: '',
		specialHandlingCodes: [],
		textualHandlingInstructions: '',
	},
	transportLegs: [],
	bookingPreference: [],
	timePreferences: {
		earliestAcceptanceTime: '',
		latestAcceptanceTime: '',
		latestArrivalTime: '',
		timeOfAvailability: '',
	},
	unitsPreference: {
		currency: {
			currencyUnit: '',
		},
	},
	involvedParties: [],
	bookingOptionList: [
		{
			id: '111',
			bookingOptionRequestId: '111',
			carrier: { companyName: 'Air China' } as ShipmentParty,
			grandTotal: 0.1,
			priceList: [
				{
					chargeType: '11',
					rateClassCode: '11',
					subTotal: 0,
					chargePaymentType: '11',
					entitlement: '11',
				},
			],
			transportLegsList: [
				{
					departureLocation: '111',
					arrivalLocation: '222',
					airlineCode: '111',
					transportIdentifier: '111',
					legNumber: 0,
					departureDate: '2025-08-24 11:30:00',
					arrivalDate: '2025-08-24 12:30:00',
				},
			],
			productDescription: '',
			offerValidFrom: '',
			offerValidTo: '',
			isChoose: false,
		},
		{
			id: '222',
			bookingOptionRequestId: '222',
			carrier: { companyName: 'Air China' } as ShipmentParty,
			grandTotal: 0.1,
			priceList: [
				{
					chargeType: '23',
					rateClassCode: '23',
					subTotal: 0,
					chargePaymentType: '23',
					entitlement: '23',
				},
			],
			transportLegsList: [
				{
					departureLocation: '23',
					arrivalLocation: '23',
					airlineCode: '23',
					transportIdentifier: '23',
					legNumber: 0,
					departureDate: '2025-08-21 11:30:00',
					arrivalDate: '2025-08-21 11:30:00',
				},
				{
					departureLocation: '34',
					arrivalLocation: '34',
					airlineCode: '34',
					transportIdentifier: '34',
					legNumber: 0,
					departureDate: '32025-08-21 11:30:00',
					arrivalDate: '2025-08-21 11:30:00',
				},
			],
			productDescription: '23',
			offerValidFrom: '',
			offerValidTo: '',
			isChoose: false,
		},
	],
	shareList: [
		{
			orgId: '111',
			orgName: '111',
			requestTime: '2025-08-11 11:30:00',
			airlineCode: '',
		},
		{
			orgId: '111',
			orgName: '111',
			requestTime: '2025-08-11 11:30:00',
			airlineCode: '',
		},
	],
};

describe('BookingOptionCreateComponent', () => {
	let component: BookingOptionCreateComponent;
	let fixture: ComponentFixture<BookingOptionCreateComponent>;
	let mockService: jasmine.SpyObj<BookingOptionRequestService>;
	let routerSpy: jasmine.SpyObj<any>;
	let commonServiceSpy: jasmine.SpyObj<any>;
	let mockProfileService: jasmine.SpyObj<UserProfileService>;

	beforeEach(async () => {
		mockService = jasmine.createSpyObj('BookingOptionRequestService', [
			'getDataPerPage',
			'getAirports',
			'getCodeByType',
			'getBookingOptionDetail',
			'sendBookingOptonsToForwarder',
		]);
		mockService.getAirports.and.returnValue(of([]));
		mockService.getBookingOptionDetail.and.returnValue(of(mockDetailRes));
		mockService.getCodeByType.and.returnValue(of([]));

		mockProfileService = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		mockProfileService.hasPermission.and.returnValue(of(true));
		mockProfileService.hasSomeRole.and.returnValue(of(true));

		routerSpy = jasmine.createSpyObj('Router', ['navigate', 'getCurrentNavigation']);

		commonServiceSpy = jasmine.createSpyObj('CommonService', ['showFormInvalid', 'showWarning']);
		routerSpy.getCurrentNavigation.and.returnValue({ extras: { state: {} } });
		await TestBed.configureTestingModule({
			imports: [BookingOptionCreateComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: BookingOptionRequestService, useValue: mockService },
				{ provide: UserProfileService, useValue: mockProfileService },
				{ provide: Router, useValue: routerSpy },
				{ provide: CommonService, useValue: commonServiceSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(BookingOptionCreateComponent);
		component = fixture.componentInstance;
		const mockUser = createMockUserProfile({ primaryOrgId: 'org123' });

		spyOn(component, 'getCurrentUser').and.returnValue(of(mockUser));
		const childDebugElement = fixture.debugElement.query(By.css('orll-booking-option-request-detail'));
		const childComponent = childDebugElement.componentInstance;

		spyOn(childComponent, 'getCurrentUser').and.returnValue(of(mockUser));
		fixture.detectChanges();
	});

	describe('Helpers and sendOpitonRequestToForwarder', () => {
		it('getPriceList and getTransportLegsList should return FormArrays', () => {
			component.addOptionGroup();
			expect(component.getPriceList(0)).toBeInstanceOf(FormArray);
			expect(component.getTransportLegsList(0)).toBeInstanceOf(FormArray);
		});

		it('sendOpitonRequestToForwarder should show invalid form warning when form invalid', () => {
			// make form invalid
			component.optionForm.markAllAsTouched();
			(component.optionForm.get('options') as FormArray).clear();
			component.optionForm.setErrors({ invalid: true });

			component.sendOpitonRequestToForwarder();

			expect(commonServiceSpy.showFormInvalid).toHaveBeenCalled();
		});

		it('sendOpitonRequestToForwarder should return early when departure check fails', () => {
			component.addOptionGroup();
			const option = (component.optionForm.get('options') as FormArray).at(0) as FormGroup;
			const trans = option.get(TRANS_LIST) as FormArray;
			trans.clear();
			// Add two transport legs with mismatched arrival/departure locations
			trans.push(
				new FormGroup({
					departureLocation: new FormControl('A'),
					arrivalLocation: new FormControl('B'),
					transportIdentifier: new FormControl('T1'),
					departureDate: new FormControl('2025-01-01'),
				})
			);
			trans.push(
				new FormGroup({
					departureLocation: new FormControl('X'), // Different from previous arrival 'B'
					arrivalLocation: new FormControl('Y'),
					transportIdentifier: new FormControl('T2'),
					departureDate: new FormControl('2025-01-02'),
				})
			);

			// make priceList valid so the form doesn't trigger form-invalid path
			const priceList = option.get(PRICE_LIST) as FormArray;
			priceList.clear();
			priceList.push(
				new FormGroup({
					chargeType: new FormControl('CT'),
					rateClassCode: new FormControl('RC'),
					subTotal: new FormControl(1),
					chargePaymentType: new FormControl('PREPAID'),
				})
			);

			// Make form valid
			component.optionForm.setErrors(null);
			option.setErrors(null);

			component.sendOpitonRequestToForwarder();

			// The method returns early, so service should not be called
			expect(mockService.sendBookingOptonsToForwarder).not.toHaveBeenCalled();
			expect(commonServiceSpy.showWarning).toHaveBeenCalled();
		});

		it('sendOpitonRequestToForwarder should call service and navigate on success', () => {
			// prepare a valid option with multiple transport legs that connect properly
			component.addOptionGroup();
			const option = (component.optionForm.get('options') as FormArray).at(0) as FormGroup;
			const priceList = option.get(PRICE_LIST) as FormArray;
			priceList.clear();
			priceList.push(
				new FormGroup({
					subTotal: new FormControl(10),
					chargeType: new FormControl('CT'),
					rateClassCode: new FormControl('RC'),
					chargePaymentType: new FormControl('PREPAID'),
				})
			);
			const trans = option.get(TRANS_LIST) as FormArray;
			trans.clear();
			// Add two transport legs with matching arrival/departure locations
			trans.push(
				new FormGroup({
					departureLocation: new FormControl('A'),
					arrivalLocation: new FormControl('B'),
					transportIdentifier: new FormControl('T1'),
					departureDate: new FormControl('2025-01-01'),
				})
			);
			trans.push(
				new FormGroup({
					departureLocation: new FormControl('B'), // Matches previous arrival
					arrivalLocation: new FormControl('C'),
					transportIdentifier: new FormControl('T2'),
					departureDate: new FormControl('2025-01-02'),
				})
			);

			// Make form valid
			component.optionForm.setErrors(null);
			option.setErrors(null);

			mockService.sendBookingOptonsToForwarder.and.returnValue(of({}));
			(component as any).id = 'req-1';

			component.sendOpitonRequestToForwarder();

			expect(mockService.sendBookingOptonsToForwarder).toHaveBeenCalled();
			expect((component as any).dataLoading).toBeFalse();
		});
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('Form Creation', () => {
		it('should create a valid option group with default values', () => {
			const group = component.createOptionGroup(false);
			expect(group).toBeInstanceOf(FormGroup);
			expect(group.contains('priceList')).toBeTrue();
			expect(group.contains('transportLegsList')).toBeTrue();
			expect(group.contains('productDescription')).toBeTrue();
			expect(group.get('grandTotal')?.value).toBe(0);
		});

		it('should create piece group with required validators', () => {
			const group = component.createPieceGroup(false);
			expect(group.get('chargeType')?.hasValidator(Validators.required)).toBeTrue();
			expect(group.get('subTotal')?.hasValidator(Validators.required)).toBeTrue();
		});

		it('should create transport group with required fields', () => {
			const group = component.createTransGroup(false);
			expect(group.get('departureLocation')?.hasValidator(Validators.required)).toBeTrue();
			expect(group.get('arrivalLocation')?.hasValidator(Validators.required)).toBeTrue();
			expect(group.get('transportIdentifier')?.hasValidator(Validators.required)).toBeTrue();
		});
	});

	describe('Dynamic Form Operations', () => {
		let optionGroup: FormGroup;

		beforeEach(() => {
			optionGroup = component.createOptionGroup(false);
		});

		it('should add a new option group to options FormArray', () => {
			const optionsArray = component.optionForm.get('options') as FormArray;
			const initialLength = optionsArray.length;

			component.addOptionGroup();

			expect(optionsArray.length).toBe(initialLength + 1);
			expect(optionsArray.at(initialLength)).toBeInstanceOf(FormGroup);
		});

		it('should add a new piece to priceList', () => {
			const priceList = optionGroup.get(PRICE_LIST) as FormArray;
			const initialLength = priceList.length;

			component.addPrice(optionGroup);

			expect(priceList.length).toBe(initialLength + 1);
			expect(priceList.at(initialLength).get('chargeType')).toBeTruthy();
		});

		it('should mark form as untouched and pristine after adding piece', () => {
			spyOn(component as any, 'markFormGroupAsUntouched');
			component.addPrice(optionGroup);
			expect((component as any).markFormGroupAsUntouched).toHaveBeenCalledWith(component.optionForm);
		});

		it('should remove piece at given index', () => {
			const priceList = optionGroup.get(PRICE_LIST) as FormArray;
			const initialLength = priceList.length;

			component.delPrice(optionGroup, 0);

			expect(priceList.length).toBe(initialLength - 1);
		});

		it('should add a new transport leg', () => {
			const transList = optionGroup.get(TRANS_LIST) as FormArray;
			const initialLength = transList.length;

			component.addTrans(optionGroup);

			expect(transList.length).toBe(initialLength + 1);
			expect(transList.at(initialLength).get('departureLocation')).toBeTruthy();
		});

		it('should remove last transport leg', () => {
			const transList = optionGroup.get(TRANS_LIST) as FormArray;
			component.addTrans(optionGroup);
			const initialLength = transList.length;

			component.delTrans(optionGroup);

			expect(transList.length).toBe(initialLength - 1);
		});

		it('should remove item from any list by index', () => {
			const list = new FormArray([new FormControl('a'), new FormControl('b')]);
			component.removeItemFromList(list, 0);
			expect(list.length).toBe(1);
			expect(list.at(0).value).toBe('b');
		});
	});

	describe('Form State Management', () => {
		it('should recursively mark all controls as untouched and pristine', () => {
			const form = new FormGroup({
				name: new FormControl('test'),
				nested: new FormGroup({
					field: new FormControl('value'),
				}),
				list: new FormArray([new FormControl('item')]),
			});

			form.markAsTouched();
			form.get('nested.field')?.markAsDirty();

			(component as any).markFormGroupAsUntouched(form);

			expect(form.touched).toBeFalse();
			expect(form.get('name')?.touched).toBeFalse();
			expect(form.get('name')?.dirty).toBeFalse();
			expect(form.get('nested.field')?.touched).toBeFalse();
			expect(form.get('nested.field')?.dirty).toBeFalse();
		});
	});

	describe('updateGrandTotal', () => {
		let optionGroup: FormGroup;

		beforeEach(() => {
			component.detailInfo = { chargeableWeight: '1' } as OptionRequestDetail;
			optionGroup = component.createOptionGroup(false);
			const priceList = optionGroup.get(PRICE_LIST) as FormArray;
			priceList.clear();
			priceList.push(
				new FormGroup({
					subTotal: new FormControl(100.5),
				})
			);
			priceList.push(
				new FormGroup({
					subTotal: new FormControl(200.75),
				})
			);
		});

		it('should calculate and update grandTotal correctly', () => {
			component.updateGrandTotal(optionGroup);
			expect(optionGroup.get('grandTotal')?.value).toBe(301.25);
		});

		it('should handle null/undefined subTotal values', () => {
			const priceList = optionGroup.get(PRICE_LIST) as FormArray;
			priceList.push(
				new FormGroup({
					subTotal: new FormControl(null),
				})
			);

			component.updateGrandTotal(optionGroup);
			expect(optionGroup.get('grandTotal')?.value).toBe(301.25);
		});

		it('should format to 2 decimal places', () => {
			const priceList = optionGroup.get(PRICE_LIST) as FormArray;
			priceList.clear();
			priceList.push(new FormGroup({ subTotal: new FormControl(1.005) }));

			component.updateGrandTotal(optionGroup);
			// toFixed(2) -> "1.01", Number -> 1.01
			expect(optionGroup.get('grandTotal')?.value).toBeCloseTo(1, 2);
		});
	});
});
