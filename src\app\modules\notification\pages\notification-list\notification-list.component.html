<div class="iata-notification-list__container">
	<div class="iata-notification-list__container__header">
		<div class="row justify-content-between">
			<div class="col-8 iata-notification-list__container__title">
				{{ 'notifications.title' | translate }}
			</div>
			<div class="col-4 d-flex justify-content-end align-items-center">
				<mat-slide-toggle labelPosition="before" (change)="onToggleChange($event)">{{
					'notifications.toggle.read' | translate
				}}</mat-slide-toggle>
				<button mat-flat-button color="primary" (click)="markAllViewed()" class="iata-notification-list__container__btn">
					{{ 'notifications.btn.mark' | translate }} <mat-icon> visibility</mat-icon>
				</button>
			</div>
		</div>
	</div>
	<div class="iata-notification-tip__container__notifications iata-box">
		<orll-notification [showUnread]="showUnread" [hasPagination]="true"></orll-notification>
	</div>
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
