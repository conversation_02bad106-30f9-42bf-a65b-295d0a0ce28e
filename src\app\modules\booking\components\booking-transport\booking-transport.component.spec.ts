import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BookingTransportComponent } from './booking-transport.component';
import { TranslateModule } from '@ngx-translate/core';
import { BookingAirlineDetailObj, BookingRequestDetailObj, Transport } from '@shared/models/booking-option.model';
import { Component } from '@angular/core';

@Component({
	template: `<orll-booking-transport
		[bookingRequestDetails]="detail"
		[bookingAirlineDetail]="airDetail"
		(selectedOption)="onSelected($event)"></orll-booking-transport>`,
	imports: [BookingTransportComponent],
})
class TestHostComponent {
	detail?: BookingRequestDetailObj;
	airDetail?: BookingAirlineDetailObj;
	selectedId: string | null = null;

	onSelected(id: string) {
		this.selectedId = id;
	}
}

describe('BookingTransportComponent', () => {
	let component: BookingTransportComponent;
	let hostComponent: TestHostComponent;
	let fixture: ComponentFixture<TestHostComponent>;

	const mockTransport1: Transport = {
		departureLocation: '111',
		arrivalLocation: '222',
		departureDate: '2025-08-26 00:00:40',
		arrivalDate: '2025-08-26 02:00:40',
		carrier: '',
		sequenceNumber: 0,
	};
	const mockTransport2: Transport = {
		departureLocation: '222',
		arrivalLocation: '333',
		departureDate: '2025-08-26 02:00:40',
		arrivalDate: '2025-08-26 04:00:40',
		carrier: '',
		sequenceNumber: 0,
	};
	const mockTransport3: Transport = {
		departureLocation: '333',
		arrivalLocation: '444',
		departureDate: '2025-08-26 04:00:40',
		arrivalDate: '2025-08-26 06:00:40',
		carrier: '',
		sequenceNumber: 0,
	};

	const mockDetail: BookingRequestDetailObj = {
		transportVoList: [mockTransport1, mockTransport2],
		bookingId: '123',
		forwarderName: '',
		forwarderIataCode: '',
		pieceGroupCount: 0,
		totalGrossWeight: 0,
		chargeableWeight: 0,
		totalDimensions: {
			length: 0,
			width: 0,
			height: 0,
		},
		expectedCommodity: '',
		specialHandlingCodes: [],
		textualHandlingInstructions: '',
		totalPrice: {
			numericalValue: 0,
			currencyUnit: '',
		},
	};

	const mockAirDetail: BookingAirlineDetailObj = {
		id: 'AIR-123',
		transportVoList: [mockTransport2, mockTransport3],
		totalPrice: {
			numericalValue: 0,
			currencyUnit: '',
		},
	};

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [TestHostComponent, BookingTransportComponent, TranslateModule.forRoot()],
		}).compileComponents();

		fixture = TestBed.createComponent(TestHostComponent);
		hostComponent = fixture.componentInstance;
		component = fixture.debugElement.children[0].componentInstance;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnChanges', () => {
		it('should set first and last transport from detail.transportVoList when detail is provided', () => {
			hostComponent.detail = mockDetail;
			fixture.detectChanges();

			expect(component.firstTransport).toBe(mockTransport1);
			expect(component.lastTransport).toBe(mockTransport2);
		});

		it('should set first and last transport from airDetail.transportVoList when detail is not provided but airDetail is', () => {
			hostComponent.airDetail = mockAirDetail;
			fixture.detectChanges();

			expect(component.firstTransport).toBe(mockTransport2);
			expect(component.lastTransport).toBe(mockTransport3);
		});

		it('should set first and last transport to null when both detail and airDetail are not provided', () => {
			fixture.detectChanges();

			expect(component.firstTransport).toBeNull();
			expect(component.lastTransport).toBeNull();
		});

		it('should set first and last transport to null when transportVoList is empty', () => {
			hostComponent.detail = {
				transportVoList: [],
				bookingId: '123',
				forwarderName: '',
				forwarderIataCode: '',
				pieceGroupCount: 0,
				totalGrossWeight: 0,
				chargeableWeight: 0,
				totalDimensions: {
					length: 0,
					width: 0,
					height: 0,
				},
				expectedCommodity: '',
				specialHandlingCodes: [],
				textualHandlingInstructions: '',
				totalPrice: {
					numericalValue: 0,
					currencyUnit: '',
				},
			};
			fixture.detectChanges();

			expect(component.firstTransport).toBeNull();
			expect(component.lastTransport).toBeNull();
		});

		it('should prefer detail over airDetail when both are provided', () => {
			hostComponent.detail = mockDetail;
			hostComponent.airDetail = mockAirDetail;
			fixture.detectChanges();

			expect(component.firstTransport).toBe(mockTransport1); // from detail
			expect(component.lastTransport).toBe(mockTransport2); // from detail
		});

		it('should convert departure and arrival dates to ISO strings', () => {
			hostComponent.detail = mockDetail;
			fixture.detectChanges();

			const expectedDeparture = new Date(mockTransport1.departureDate).toISOString();
			const expectedArrival = new Date(mockTransport2.arrivalDate).toISOString();

			expect(component.firstTransport?.departureDate).toBe(expectedDeparture);
			expect(component.lastTransport?.arrivalDate).toBe(expectedArrival);
		});

		it('should populate priceList from bookingRequestDetails and prefer it over airDetail', () => {
			const priceA = [{ chargeType: 'A', subTotal: 10 } as any];
			const priceB = [{ chargeType: 'B', subTotal: 20 } as any];

			hostComponent.detail = { ...mockDetail, priceList: priceA } as any;
			hostComponent.airDetail = { ...mockAirDetail, priceList: priceB } as any;
			fixture.detectChanges();

			expect(component.priceList).toBe(priceA);

			// when detail not provided, should use airDetail.priceList
			hostComponent.detail = undefined;
			hostComponent.airDetail = { ...mockAirDetail, priceList: priceB } as any;
			fixture.detectChanges();

			expect(component.priceList).toBe(priceB);
		});
	});

	describe('selectOption', () => {
		it('should emit airDetail.id when airDetail is provided', () => {
			hostComponent.airDetail = mockAirDetail;
			fixture.detectChanges();

			spyOn(hostComponent as any, 'onSelected');

			component.selectOption();
			fixture.detectChanges();

			expect((hostComponent as any).onSelected).toHaveBeenCalledWith('AIR-123');
		});

		it('should emit undefined (emit null/undefined) when airDetail is not provided', () => {
			spyOn(hostComponent as any, 'onSelected');

			component.selectOption();
			fixture.detectChanges();

			expect((hostComponent as any).onSelected).toHaveBeenCalledWith(undefined);
		});
	});
});
