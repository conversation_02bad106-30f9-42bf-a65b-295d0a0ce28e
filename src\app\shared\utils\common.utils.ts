import { FormArray } from '@angular/forms';
import { CodeName } from '@shared/models/code-name.model';

export function deepCopy(obj: any): any {
	return JSON.parse(JSON.stringify(obj));
}

export function convertJsonStrToObj(objStr: string) {
	return JSON.parse(objStr);
}

export function downloadBinaryFile(blob: Blob | null, filename: string, isPreview = true): void {
	if (!blob?.size) return;

	const url = window.URL.createObjectURL(blob);
	const a = document.createElement('a');
	a.href = url;
	a.download = filename;
	a.click();
	if (isPreview) window.open(url, '_blank');
	window.URL.revokeObjectURL(url);
}

export function roundNumber(num: number, decimalDigits: number): number {
	// @ts-expect-error any
	return +(Math.round(num + `e+${decimalDigits}`) + `e-${decimalDigits}`);
}

export function clearFormArray(formArray: FormArray): void {
	while (formArray.length !== 0) {
		formArray.removeAt(0);
	}
}

export function displayPackagingTypeName(packagingTypes: CodeName[], code: string): string {
	const type = packagingTypes.find((item) => item.code === code);
	const name = type?.name ?? '';
	return name;
}

export function formatDateTime(val: string) {
	return val ? val.substring(0, 19).replace('T', ' ') : '';
}

export function roundGrossWeightVolume(volume: number): number {
	const result = (volume * Math.pow(10, 6)) / 6000;
	return Math.floor(result) + (result % 1 > 0.5 ? 1 : 0.5);
}
