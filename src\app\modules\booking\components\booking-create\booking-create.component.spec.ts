import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import BookingCreateComponent from './booking-create.component';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { BookingOptionRequestDetailObj, PRICE_LIST, TRANS_LIST } from '../../models/booking.model';
import { of, throwError } from 'rxjs';
import { UserProfileService } from '@shared/services/user-profile.service';
import { Router } from '@angular/router';
import { CommonService } from '@shared/services/common.service';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { TranslateModule } from '@ngx-translate/core';
import { ShipmentParty } from 'src/app/modules/sli-mgmt/models/shipment-party.model';

import { UserProfile } from '@shared/models/user-profile.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';

function createMockUserProfile(overrides: Partial<UserProfile> = {}): UserProfile {
	return {
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'ORG_TYPE',
		userType: '1',
		menuList: [],
		permissionList: [],
		orgList: [],
		...overrides,
	};
}

const mockDetailRes: BookingOptionRequestDetailObj = {
	bookingShipmentDetails: {
		id: '',
		pieceGroups: {
			id: '',
			pieceGroupCount: 0,
		},
		totalGrossWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		chargeableWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		dimensions: {
			length: 0,
			width: 0,
			height: 0,
		},
		expectedCommodity: '',
		specialHandlingCodes: [],
		textualHandlingInstructions: '',
	},
	transportLegs: [],
	bookingPreference: [],
	timePreferences: {
		earliestAcceptanceTime: '',
		latestAcceptanceTime: '',
		latestArrivalTime: '',
		timeOfAvailability: '',
	},
	unitsPreference: {
		currency: {
			currencyUnit: '',
		},
	},
	involvedParties: [],
	bookingOptionList: [
		{
			id: '111',
			bookingOptionRequestId: '111',
			carrier: { companyName: 'Air China' } as ShipmentParty,
			grandTotal: 0.1,
			priceList: [
				{
					chargeType: '11',
					rateClassCode: '11',
					subTotal: 0,
					chargePaymentType: '11',
					entitlement: '11',
				},
			],
			transportLegsList: [
				{
					departureLocation: '111',
					arrivalLocation: '222',
					airlineCode: '111',
					transportIdentifier: '111',
					legNumber: 0,
					departureDate: '2025-08-24 11:30:00',
					arrivalDate: '2025-08-24 12:30:00',
				},
			],
			productDescription: '',
			offerValidFrom: '',
			offerValidTo: '',
			isChoose: false,
		},
		{
			id: '222',
			bookingOptionRequestId: '222',
			carrier: { companyName: 'Air China' } as ShipmentParty,
			grandTotal: 0.1,
			priceList: [
				{
					chargeType: '23',
					rateClassCode: '23',
					subTotal: 0,
					chargePaymentType: '23',
					entitlement: '23',
				},
			],
			transportLegsList: [
				{
					departureLocation: '23',
					arrivalLocation: '23',
					airlineCode: '23',
					transportIdentifier: '23',
					legNumber: 0,
					departureDate: '2025-08-21 11:30:00',
					arrivalDate: '2025-08-21 11:30:00',
				},
				{
					departureLocation: '34',
					arrivalLocation: '34',
					airlineCode: '34',
					transportIdentifier: '34',
					legNumber: 0,
					departureDate: '32025-08-21 11:30:00',
					arrivalDate: '2025-08-21 11:30:00',
				},
			],
			productDescription: '23',
			offerValidFrom: '',
			offerValidTo: '',
			isChoose: false,
		},
	],
	shareList: [
		{
			orgId: '111',
			orgName: '111',
			requestTime: '2025-08-11 11:30:00',
			airlineCode: '',
		},
		{
			orgId: '111',
			orgName: '111',
			requestTime: '2025-08-11 11:30:00',
			airlineCode: '',
		},
	],
};

describe('BookingCreateComponent', () => {
	let component: BookingCreateComponent;
	let fixture: ComponentFixture<BookingCreateComponent>;
	let mockService: jasmine.SpyObj<BookingOptionRequestService>;
	let routerSpy: jasmine.SpyObj<any>;
	let commonServiceSpy: jasmine.SpyObj<any>;
	let mockProfileService: jasmine.SpyObj<UserProfileService>;

	beforeEach(async () => {
		mockService = jasmine.createSpyObj('BookingOptionRequestService', [
			'getDataPerPage',
			'getAirports',
			'getCodeByType',
			'getBookingOptionDetail',
			'sendBookingOptonsToForwarder',
			'createBookingRequest',
		]);
		mockService.getAirports.and.returnValue(of([]));
		mockService.getBookingOptionDetail.and.returnValue(of(mockDetailRes));
		mockService.getCodeByType.and.returnValue(of([]));

		mockProfileService = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		mockProfileService.hasPermission.and.returnValue(of(true));
		mockProfileService.hasSomeRole.and.returnValue(of(true));

		routerSpy = jasmine.createSpyObj('Router', ['navigate', 'getCurrentNavigation']);

		commonServiceSpy = jasmine.createSpyObj('CommonService', ['showFormInvalid', 'showWarning', 'showCancelConfirm']);
		routerSpy.getCurrentNavigation.and.returnValue({ extras: { state: {} } });
		await TestBed.configureTestingModule({
			imports: [BookingCreateComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: BookingOptionRequestService, useValue: mockService },
				{ provide: UserProfileService, useValue: mockProfileService },
				{ provide: Router, useValue: routerSpy },
				{ provide: CommonService, useValue: commonServiceSpy },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
			schemas: [NO_ERRORS_SCHEMA],
		})
			.overrideComponent(BookingCreateComponent, {
				set: {
					template: `
					<div class="booking-create">
						<div class="booking-create__detail iata-box">
							<!-- Mock child component removed -->
						</div>
						<div class="booking-create__option">
							<form [formGroup]="optionForm">
								<div formArrayName="options">
									<!-- Form content -->
								</div>
							</form>
						</div>
					</div>
				`,
				},
			})
			.compileComponents();

		fixture = TestBed.createComponent(BookingCreateComponent);
		component = fixture.componentInstance;
		const mockUser = createMockUserProfile({ primaryOrgId: 'org123' });

		spyOn(component, 'getCurrentUser').and.returnValue(of(mockUser));
		fixture.detectChanges();

		// Set up the mock child component after fixture initialization
		component.bookingOptionRequestFormComponent = {
			bookingOptionReqForm: new FormGroup({
				chargeableWeight: new FormControl('1'),
				departureLocation: new FormControl(''),
				arrivalLocation: new FormControl(''),
			}),
			getFormData: jasmine.createSpy('getFormData').and.returnValue({}),
		} as any;
	});

	describe('Helpers and saveBooking', () => {
		it('getPriceList and getTransportLegsList should return FormArrays', () => {
			component.addOptionGroup();
			expect(component.getPriceList(0)).toBeInstanceOf(FormArray);
			expect(component.getTransportLegsList(0)).toBeInstanceOf(FormArray);
		});

		it('saveBooking should show invalid form warning when form invalid', () => {
			// make form invalid
			component.optionForm.markAllAsTouched();
			(component.optionForm.get('options') as FormArray).clear();
			component.optionForm.setErrors({ invalid: true });

			component.saveBooking();

			expect(commonServiceSpy.showFormInvalid).toHaveBeenCalled();
		});

		it('saveBooking should call service and navigate on success', () => {
			// Clear existing options first
			const optionsArray = component.optionForm.get('options') as FormArray;
			optionsArray.clear();

			// prepare a valid option
			component.addOptionGroup();
			const option = (component.optionForm.get('options') as FormArray).at(0) as FormGroup;
			const priceList = option.get(PRICE_LIST) as FormArray;
			priceList.clear();
			priceList.push(
				new FormGroup({
					chargeType: new FormControl('TEST'),
					rateClassCode: new FormControl('TEST'),
					subTotal: new FormControl(10.5),
					chargePaymentType: new FormControl('TEST'),
					entitlement: new FormControl('A'),
				})
			);
			const trans = option.get(TRANS_LIST) as FormArray;
			trans.clear();
			trans.push(
				new FormGroup({
					departureLocation: new FormControl('PEK'),
					arrivalLocation: new FormControl('LAX'),
					transportIdentifier: new FormControl('CA123'),
					departureDate: new FormControl('2025-01-01T10:00:00'),
					arrivalDate: new FormControl('2025-01-01T15:00:00'),
					airlineCode: new FormControl('CA'),
				})
			);

			// Make child form valid by creating a completely new form with all required fields
			component.bookingOptionRequestFormComponent.bookingOptionReqForm = new FormGroup({
				chargeableWeight: new FormControl(1),
				departureLocation: new FormControl('PEK'),
				arrivalLocation: new FormControl('LAX'),
				// Add other required fields that might be needed
				mawbNumber: new FormControl('12345678'),
				shipper: new FormControl('Test Shipper'),
				consignee: new FormControl('Test Consignee'),
			});

			mockService.createBookingRequest.and.returnValue(of('success'));

			component.saveBooking();

			expect(mockService.createBookingRequest).toHaveBeenCalled();
			expect(routerSpy.navigate).toHaveBeenCalledWith(['booking']);
			expect(component.dataLoading).toBeFalse();
		});
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('Form Creation', () => {
		it('should create a valid option group with default values', () => {
			const group = component.createOptionGroup(false);
			expect(group).toBeInstanceOf(FormGroup);
			expect(group.contains('priceList')).toBeTrue();
			expect(group.contains('transportLegsList')).toBeTrue();
			expect(group.contains('grandTotal')).toBeTrue();
			expect(group.contains('disableOption')).toBeTrue();
			expect(group.contains('showOption')).toBeTrue();
			expect(group.get('grandTotal')?.value).toBe(0);
		});

		it('should create piece group with required validators', () => {
			const group = component.createPieceGroup(false);
			expect(group.get('chargeType')?.hasValidator(Validators.required)).toBeTrue();
			expect(group.get('subTotal')?.hasValidator(Validators.required)).toBeTrue();
		});

		it('should create transport group with required fields', () => {
			const group = component.createTransGroup(false);
			expect(group.get('departureLocation')?.hasValidator(Validators.required)).toBeTrue();
			expect(group.get('arrivalLocation')?.hasValidator(Validators.required)).toBeTrue();
			expect(group.get('transportIdentifier')?.hasValidator(Validators.required)).toBeTrue();
		});
	});

	describe('updateGrandTotal', () => {
		let optionGroup: FormGroup;

		beforeEach(() => {
			component.bookingOptionRequestFormComponent.bookingOptionReqForm.patchValue({ chargeableWeight: '1' });
			optionGroup = component.createOptionGroup(false);
			const priceList = optionGroup.get(PRICE_LIST) as FormArray;
			priceList.clear();
			priceList.push(
				new FormGroup({
					subTotal: new FormControl(100.5),
				})
			);
			priceList.push(
				new FormGroup({
					subTotal: new FormControl(200.75),
				})
			);
		});

		it('should calculate and update grandTotal correctly', () => {
			component.updateGrandTotal(optionGroup);
			expect(optionGroup.get('grandTotal')?.value).toBe(301.25);
		});

		it('should handle null/undefined subTotal values', () => {
			const priceList = optionGroup.get(PRICE_LIST) as FormArray;
			priceList.push(
				new FormGroup({
					subTotal: new FormControl(null),
				})
			);

			component.updateGrandTotal(optionGroup);
			expect(optionGroup.get('grandTotal')?.value).toBe(301.25);
		});

		it('should format to 2 decimal places', () => {
			const priceList = optionGroup.get(PRICE_LIST) as FormArray;
			priceList.clear();
			priceList.push(new FormGroup({ subTotal: new FormControl(1.005) }));

			component.updateGrandTotal(optionGroup);
			// toFixed(2) -> "1.01", Number -> 1.01
			expect(optionGroup.get('grandTotal')?.value).toBeCloseTo(1, 2);
		});
	});

	describe('ngOnInit', () => {
		it('should initialize form with one option group', () => {
			// Reset form state
			const optionsArray = component.optionForm.get('options') as FormArray;
			optionsArray.clear();
			component.ngOnInit();
			expect(optionsArray.length).toBe(1);
		});

		it('should load location list from service', () => {
			const mockLocations = [{ code: 'PEK', name: 'Beijing' }];
			mockService.getAirports.and.returnValue(of(mockLocations));
			// Reset spy call counts
			mockService.getAirports.calls.reset();

			component.ngOnInit();

			expect(mockService.getAirports).toHaveBeenCalled();
			expect(component.locationList).toEqual(mockLocations);
		});

		it('should load code types from service', () => {
			const mockCodes = [{ code: 'TEST', name: 'Test Code' }];
			mockService.getCodeByType.and.returnValue(of(mockCodes));
			// Reset spy call counts
			mockService.getCodeByType.calls.reset();

			component.ngOnInit();

			expect(mockService.getCodeByType).toHaveBeenCalledTimes(4);
			expect(component.chargeTypes).toEqual(mockCodes);
			expect(component.rateClassCodes).toEqual(mockCodes);
			expect(component.chargePaymentTypes).toEqual(mockCodes);
			expect(component.entitlements).toEqual(mockCodes);
		});
	});

	describe('removeItemFromList', () => {
		it('should remove item at specified index from FormArray', () => {
			const formArray = new FormArray([new FormControl('item1'), new FormControl('item2'), new FormControl('item3')]);

			component.removeItemFromList(formArray, 1);

			expect(formArray.length).toBe(2);
			expect(formArray.at(0).value).toBe('item1');
			expect(formArray.at(1).value).toBe('item3');
		});
	});

	describe('onCancel', () => {
		it('should call commonService.showCancelConfirm with booking route', () => {
			component.onCancel();

			expect(commonServiceSpy.showCancelConfirm).toHaveBeenCalledWith('booking');
		});
	});

	describe('addOptionGroup', () => {
		it('should add new option group to options FormArray', () => {
			const initialLength = (component.optionForm.get('options') as FormArray).length;

			component.addOptionGroup();

			const optionsArray = component.optionForm.get('options') as FormArray;
			expect(optionsArray.length).toBe(initialLength + 1);
		});

		it('should add disabled option group when disable parameter is true', () => {
			component.addOptionGroup(true);

			const optionsArray = component.optionForm.get('options') as FormArray;
			const lastOption = optionsArray.at(optionsArray.length - 1) as FormGroup;
			const priceList = lastOption.get(PRICE_LIST) as FormArray;
			const firstPrice = priceList.at(0) as FormGroup;

			expect(firstPrice.get('chargeType')?.disabled).toBe(true);
		});
	});

	describe('ngAfterViewInit', () => {
		it('should call patchValueFromMawb when bookingInfo exists', () => {
			component.bookingInfo = mockDetailRes;
			spyOn(component as any, 'patchValueFromMawb');

			component.ngAfterViewInit();

			expect((component as any).patchValueFromMawb).toHaveBeenCalled();
		});

		it('should not call patchValueFromMawb when bookingInfo is null', () => {
			component.bookingInfo = null;
			spyOn(component as any, 'patchValueFromMawb');

			component.ngAfterViewInit();

			expect((component as any).patchValueFromMawb).not.toHaveBeenCalled();
		});
	});

	describe('patchValueFromMawb', () => {
		beforeEach(() => {
			component.bookingInfo = {
				rateClassCode: 'RC1',
				rateCharge: 100.5,
				weightValuationIndicator: 'prepaid',
				departureLocation: 'PEK',
				arrivalLocation: 'SHA',
				requestedDate: '2025-01-01',
				requestedFlight: 'CA123',
				airlineCode: 'CA',
			};

			// Ensure we have at least one option group
			const optionsArray = component.optionForm.get('options') as FormArray;
			if (optionsArray.length === 0) {
				component.addOptionGroup();
			}
		});

		it('should patch form values from bookingInfo', () => {
			(component as any).patchValueFromMawb();

			const priceList = component.getPriceList(0);
			const transList = component.getTransportLegsList(0);

			expect(priceList.at(0).get('rateClassCode')?.value).toBe('RC1');
			expect(priceList.at(0).get('subTotal')?.value).toBe(100.5);
			expect(priceList.at(0).get('chargePaymentType')?.value).toBe('PREPAID');

			expect(transList.at(0).get('departureLocation')?.value).toBe('PEK');
			expect(transList.at(0).get('arrivalLocation')?.value).toBe('SHA');
			expect(transList.at(0).get('transportIdentifier')?.value).toBe('CA123');
			expect(transList.at(0).get('airlineCode')?.value).toBe('CA');
		});
	});

	describe('getPriceList', () => {
		it('should return price list FormArray for specified option index', () => {
			const result = component.getPriceList(0);

			expect(result).toBeInstanceOf(FormArray);
			expect(result.length).toBeGreaterThan(0);
		});
	});

	describe('getTransportLegsList', () => {
		it('should return transport legs list FormArray for specified option index', () => {
			const result = component.getTransportLegsList(0);

			expect(result).toBeInstanceOf(FormArray);
			expect(result.length).toBeGreaterThan(0);
		});
	});

	describe('createOptionGroup', () => {
		it('should create FormGroup with required structure', () => {
			const result = component.createOptionGroup(false);

			expect(result.get('priceList')).toBeInstanceOf(FormArray);
			expect(result.get('transportLegsList')).toBeInstanceOf(FormArray);
			expect(result.get('grandTotal')?.value).toBe(0);
			expect(result.get('disableOption')?.value).toBe(false);
			expect(result.get('showOption')?.value).toBe(true);
		});

		it('should create disabled FormGroup when disable is true', () => {
			const result = component.createOptionGroup(true);
			const priceList = result.get('priceList') as FormArray;
			const firstPrice = priceList.at(0) as FormGroup;

			expect(firstPrice.get('chargeType')?.disabled).toBe(true);
		});
	});

	describe('createPieceGroup', () => {
		it('should create FormGroup with price-related controls', () => {
			const result = component.createPieceGroup(false);

			expect(result.get('chargeType')).toBeTruthy();
			expect(result.get('rateClassCode')).toBeTruthy();
			expect(result.get('subTotal')).toBeTruthy();
			expect(result.get('chargePaymentType')?.value).toBe('PREPAID');
			expect(result.get('entitlement')?.value).toBe('A');
		});

		it('should create disabled FormGroup when disable is true', () => {
			const result = component.createPieceGroup(true);

			expect(result.get('chargeType')?.disabled).toBe(true);
			expect(result.get('rateClassCode')?.disabled).toBe(true);
			expect(result.get('subTotal')?.disabled).toBe(true);
		});
	});

	describe('createTransGroup', () => {
		it('should create FormGroup with transport-related controls', () => {
			const result = component.createTransGroup(false);

			expect(result.get('departureLocation')).toBeTruthy();
			expect(result.get('arrivalLocation')).toBeTruthy();
			expect(result.get('airlineCode')).toBeTruthy();
			expect(result.get('transportIdentifier')).toBeTruthy();
			expect(result.get('departureDate')).toBeTruthy();
			expect(result.get('arrivalDate')).toBeTruthy();
		});

		it('should create disabled FormGroup when disable is true', () => {
			const result = component.createTransGroup(true);

			expect(result.get('departureLocation')?.disabled).toBe(true);
			expect(result.get('arrivalLocation')?.disabled).toBe(true);
			expect(result.get('transportIdentifier')?.disabled).toBe(true);
		});
	});

	describe('saveBooking', () => {
		beforeEach(() => {
			// Set up valid form data
			component.bookingOptionRequestFormComponent.bookingOptionReqForm.patchValue({
				totalGrossWeight: '100',
				chargeableWeight: '95',
				expectedCommodity: 'TEST',
			});

			const optionsArray = component.optionForm.get('options') as FormArray;
			if (optionsArray.length === 0) {
				component.addOptionGroup();
			}

			const priceList = component.getPriceList(0);
			const transList = component.getTransportLegsList(0);

			priceList.at(0).patchValue({
				chargeType: 'FREIGHT',
				rateClassCode: 'N',
				subTotal: 100,
				chargePaymentType: 'PREPAID',
			});

			transList.at(0).patchValue({
				departureLocation: 'PEK',
				arrivalLocation: 'SHA',
				transportIdentifier: 'CA123',
				departureDate: '2025-01-01T10:00:00',
			});
		});

		it('should show form invalid message when forms are invalid', () => {
			// Make form invalid
			component.optionForm.get('options')?.setErrors({ invalid: true });

			component.saveBooking();

			expect(commonServiceSpy.showFormInvalid).toHaveBeenCalled();
			expect(component.dataLoading).toBe(false);
		});

		it('should call createBookingRequest and navigate on success when forms are valid', () => {
			mockService.createBookingRequest.and.returnValue(of('success'));

			component.saveBooking();

			expect(mockService.createBookingRequest).toHaveBeenCalled();
			expect(routerSpy.navigate).toHaveBeenCalledWith(['booking']);
			expect(component.dataLoading).toBe(false);
		});

		it('should handle error from createBookingRequest', () => {
			mockService.createBookingRequest.and.returnValue(throwError(() => new Error('Service error')));

			component.saveBooking();

			expect(mockService.createBookingRequest).toHaveBeenCalled();
			expect(component.dataLoading).toBe(false);
			expect(routerSpy.navigate).not.toHaveBeenCalled();
		});
	});
});
