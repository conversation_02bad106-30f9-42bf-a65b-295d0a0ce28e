import { Injectable } from '@angular/core';
import { GenericTableService } from '@shared/services/table/orll-table.interface';
import { EcsdObj, SubObj, SubObjSearchReq } from '../models/ecsd.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '@shared/services/api.service';
import { PaginationRequest } from '@shared/models/pagination-request.model';

@Injectable({
	providedIn: 'root',
})
export class EcsdService extends ApiService implements GenericTableService<EcsdObj> {
	constructor(http: HttpClient) {
		super(http);
	}

	getDataPerPage(param: PaginationRequest): Observable<PaginationResponse<EcsdObj>> {
		return this.postData('ecsd/pageList', param);
	}

	loadAllData(param: any): Observable<EcsdObj[]> {
		return this.postData('ecsd/pageList', param);
	}

	saveEcsd(param: EcsdObj): Observable<boolean> {
		if (param.id) {
			return this.updateData('ecsd', param);
		}
		return this.postData('ecsd', param);
	}

	getEcsd(param: EcsdObj): Observable<EcsdObj> {
		return this.postData('ecsd/detail', { id: param.id });
	}

	getPiecesAndHawb(param: SubObjSearchReq): Observable<SubObj> {
		return this.postData('ecsd/pieceOrHawbList', param);
	}

	deleteEcsd(id: string): Observable<boolean> {
		return this.deleteData('ecsd', { id });
	}
}
