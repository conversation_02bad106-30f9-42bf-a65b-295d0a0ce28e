import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from '../../api.service';
import { WeightInfoListObj } from '@shared/models/weight-info';
import { PaginationRequest } from '@shared/models/pagination-request.model';

@Injectable({
	providedIn: 'root',
})
export class WeightCalculateRequestService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getSliData(pageParams: PaginationRequest, sliId: string): Observable<WeightInfoListObj> {
		return this.getData('sli/piece/piecesCaculation', {
			...pageParams,
			sliId,
		});
	}

	getHawbData(pageParams: PaginationRequest, hawbIds: string[]): Observable<WeightInfoListObj> {
		return this.postData('hawb-management/piece/piecesCaculation', {
			...pageParams,
			hawbIds,
		});
	}
}
