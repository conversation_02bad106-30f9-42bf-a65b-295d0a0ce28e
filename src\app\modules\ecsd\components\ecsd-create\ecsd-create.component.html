<orll-dialog class="orll-ecsd-create">
	<ng-container dialog-title>
		@if (data.ecsdObj) {
			<span>{{ 'ecsd.edit.btn' | translate }}</span>
		} @else {
			<span>{{ 'ecsd.create.btn' | translate }}</span>
		}
	</ng-container>

	<div dialog-content class="orll-ecsd-create__content">
		<form [formGroup]="ecsdForm">
			<div class="row info">{{ 'ecsd.info.regulated' | translate }}</div>
			<div class="row">
				<mat-form-field class="col-6">
					<mat-label>{{ 'ecsd.form.category' | translate }}</mat-label>
					<mat-select formControlName="regulatedEntityCategory">
						@for (code of categoryList; track code.code) {
							<mat-option [value]="code.code">{{ code.name }}</mat-option>
						}
					</mat-select>
				</mat-form-field>
				<mat-form-field class="col-6">
					<mat-label>{{ 'ecsd.form.identifier' | translate }}</mat-label>
					<input matInput formControlName="regulatedEntityIdentifier" />
				</mat-form-field>
			</div>
			<div class="row">
				<mat-form-field class="col-6">
					<mat-label>{{ 'ecsd.form.status' | translate }}</mat-label>
					<mat-select formControlName="securityStatus">
						@for (code of securityStatusList; track code.code) {
							<mat-option [value]="code.code">{{ code.name }}</mat-option>
						}
					</mat-select>
				</mat-form-field>
			</div>
			<mat-divider class="divider"></mat-divider>
			<div class="row info">
				{{ 'ecsd.info.reason' | translate }}
			</div>
			<div class="row">
				<div class="col-4 d-flex">
					<div class="col-10" class="exempted-label">{{ 'ecsd.form.screen' | translate }}</div>
					<mat-radio-group formControlName="whetherExemptedForScreening" class="col-2">
						<mat-radio-button value="yes">{{ 'ecsd.yes' | translate }}</mat-radio-button>
						<mat-radio-button value="no">{{ 'ecsd.no' | translate }}</mat-radio-button>
					</mat-radio-group>
				</div>
				<mat-form-field class="col-4">
					<mat-label>{{ 'ecsd.form.method' | translate }}</mat-label>
					<mat-select formControlName="screeningMethod">
						@for (code of screenMethodList; track code.code) {
							<mat-option [value]="code.code">{{ code.name }}</mat-option>
						}
					</mat-select>
				</mat-form-field>
				<mat-form-field class="col-4">
					<mat-label>{{ 'ecsd.form.ground' | translate }}</mat-label>
					<mat-select formControlName="groundsForExemption">
						@for (code of screenExemptionList; track code.code) {
							<mat-option [value]="code.code">{{ code.name }}</mat-option>
						}
					</mat-select>
				</mat-form-field>
			</div>
			<div class="row">
				<mat-form-field class="col-4">
					<mat-label>{{ 'ecsd.form.from' | translate }}</mat-label>
					<input matInput formControlName="receivedFrom" />
				</mat-form-field>
				<mat-form-field class="col-4">
					<mat-label>{{ 'ecsd.form.issue.by' | translate }}</mat-label>
					<input matInput formControlName="issuedBy" />
				</mat-form-field>
				<mat-form-field class="col-4">
					<mat-label>{{ 'ecsd.form.issue.on' | translate }}</mat-label>
					<input matInput formControlName="issuedOn" [matDatepicker]="picker" />
					<mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
					<mat-datepicker #picker></mat-datepicker>
				</mat-form-field>
			</div>
			<div class="row info">
				{{ 'ecsd.info.accepted' | translate }}
			</div>
			<div class="row">
				<mat-form-field class="col-6">
					<mat-label>{{ 'ecsd.form.category' | translate }}</mat-label>
					<mat-select formControlName="regulatedEntityCategory1">
						@for (code of categoryList; track code.code) {
							<mat-option [value]="code.code">{{ code.name }}</mat-option>
						}
					</mat-select>
				</mat-form-field>
				<mat-form-field class="col-6">
					<mat-label>{{ 'ecsd.form.identifier' | translate }}</mat-label>
					<input matInput formControlName="regulatedEntityIdentifier1" />
				</mat-form-field>
			</div>
			<div class="row">
				<mat-form-field class="col-12">
					<mat-label>{{ 'ecsd.form.information' | translate }}</mat-label>
					<input matInput formControlName="additionalSecurityInfo" />
				</mat-form-field>
			</div>
		</form>
		<div class="orll-ecsd-create__table">
			@if (data.loType === moduleEnum.SLI || data.loType === moduleEnum.HAWB) {
				<table
					mat-table
					[dataSource]="dataSource"
					[trackBy]="trackByPieceId"
					aria-label="Piece table"
					class="orll-ecsd-create__table">
					<ng-container matColumnDef="select">
						<th mat-header-cell *matHeaderCellDef class="table-header">
							<mat-checkbox
								(change)="toggleAllRows()"
								[checked]="isAllSelected()"
								[disabled]="dataSource.data.length === 0"
								[indeterminate]="selection.hasValue() && !isAllSelected()">
							</mat-checkbox>
						</th>
						<td mat-cell *matCellDef="let row">
							<mat-checkbox
								(click)="$event.stopPropagation()"
								(keydown.enter)="$event.stopPropagation()"
								(change)="selection.toggle(row)"
								[checked]="selection.isSelected(row)">
							</mat-checkbox>
						</td>
					</ng-container>

					<ng-container matColumnDef="productDescription">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'sli.piece.table.column.productDescription' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">
							{{ record.productDescription }}
						</td>
					</ng-container>

					<ng-container matColumnDef="packageType">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'sli.piece.table.column.packagingType' | translate }}
						</th>
						<td mat-cell *matCellDef="let record" class="orll-sli-piece-table__cell">{{ record.packageType }}</td>
					</ng-container>

					<ng-container matColumnDef="grossWeight">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'sli.piece.table.column.grossWeight' | translate }}
						</th>
						<td mat-cell *matCellDef="let record" class="orll-sli-piece-table__cell">{{ record.grossWeight }}</td>
					</ng-container>

					<ng-container matColumnDef="dimensions">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'sli.piece.table.column.dimensions' | translate }}
						</th>
						<td mat-cell *matCellDef="let record" class="orll-sli-piece-table__cell">
							{{ record.dimensions?.length }}CMx{{ record.dimensions?.width }}CMx{{ record.dimensions?.height }}CM
						</td>
					</ng-container>

					<ng-container matColumnDef="pieceQuantity">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'sli.piece.table.column.pieceQuantity' | translate }}
						</th>
						<td mat-cell *matCellDef="let record" class="orll-sli-piece-table__cell">{{ record.pieceQuantity }}</td>
					</ng-container>

					<tr mat-header-row *matHeaderRowDef="pieceDisplayedColumns; sticky: true"></tr>
					<tr mat-row *matRowDef="let record; columns: pieceDisplayedColumns" class="orll-sli-piece-table__row"></tr>
				</table>
			}
			@if (data.loType === moduleEnum.MAWB) {
				<table mat-table [dataSource]="dataSource" [trackBy]="trackByHawbId" aria-label=" Hawb table" class="common-scroll-table">
					<ng-container matColumnDef="select">
						<th mat-header-cell *matHeaderCellDef class="table-header">
							<mat-checkbox
								(change)="toggleAllRows()"
								[checked]="isAllSelected()"
								[disabled]="dataSource.data.length === 0"
								[indeterminate]="selection.hasValue() && !isAllSelected()">
							</mat-checkbox>
						</th>
						<td mat-cell *matCellDef="let row">
							<mat-checkbox
								(click)="$event.stopPropagation()"
								(keydown.enter)="$event.stopPropagation()"
								(change)="selection.toggle(row)"
								[checked]="selection.isSelected(row)">
							</mat-checkbox>
						</td>
					</ng-container>

					<ng-container matColumnDef="waybillNumber">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.number' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">
							{{ record.waybillNumber }}
						</td>
					</ng-container>

					<ng-container matColumnDef="shipper">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.shipper' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.shipper }}</td>
					</ng-container>

					<ng-container matColumnDef="consignee">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.consignee' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.consignee }}</td>
					</ng-container>

					<ng-container matColumnDef="goodsDescription">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.description' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">
							{{ record.goodsDescription }}
						</td>
					</ng-container>
					<ng-container matColumnDef="origin">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.origin' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.origin }}</td>
					</ng-container>
					<ng-container matColumnDef="destination">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.destination' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.destination }}</td>
					</ng-container>

					<ng-container matColumnDef="weight">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.weight' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.weight }}</td>
					</ng-container>
					<ng-container matColumnDef="slac">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.slac' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.slac }}</td>
					</ng-container>

					<tr mat-header-row *matHeaderRowDef="hawbDisplayedColumns; sticky: true"></tr>
					<tr mat-row *matRowDef="let record; columns: hawbDisplayedColumns"></tr>
				</table>
			}
		</div>
		@if (dataLoading) {
			<iata-spinner></iata-spinner>
		}
	</div>
	<ng-container dialog-actions>
		<button mat-stroked-button [mat-dialog-close]="'cancel'" color="primary">
			{{ 'common.dialog.cancel' | translate }}
		</button>
		<button mat-flat-button color="primary" (click)="saveEcsd()">
			<mat-icon>check</mat-icon>
			{{ 'ecsd.save.btn' | translate }}
		</button>
	</ng-container>
</orll-dialog>
