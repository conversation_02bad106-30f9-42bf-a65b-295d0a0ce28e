.orll-weight-calculate {
	width: 100%;
	height: 100%;
	display: block;

	&__container {
		min-height: 400px;
	}

	.row {
		width: 100%;
		margin: 20px 0 0 0px;
		padding: 0;
	}

	mat-label {
		margin-right: 5px;
	}

	mat-input {
		width: 100%;
	}

	mat-form-field {
		width: 100%;
	}

	form.row.col-12 {
		display: flex;
		gap: 40px;
		width: 100%;
	}

	.col-4 {
		flex: 1 1 0;
		max-width: none;
		padding-left: 0 !important;
		padding-right: 0 !important;
	}

	&__reverse-icon {
		display: inline-flex;
		flex-direction: row-reverse;
		align-items: center;
		gap: 10px;
		margin-left: 20px;
	}

	&__mat-head {
		background-color: var(--iata-grey-50) !important;
		color: var(--iata-grey-300);
	}

	&__mat-cell {
		height: 100%;
		color: var(--iata-grey-600);
	}

	&__table {
		width: 100%;
		border: 1px solid var(--iata-grey-200);
	}
}

.button-group {
	display: flex;
	justify-content: flex-end;
	gap: 16px;
	margin-top: 20px;
}

.right-aligned-container {
	border: 1px solid #ddd;
	height: 70px;
	width: 100%;
	padding: 16px;
	box-sizing: border-box;
	display: flex;
	align-items: center;
}

.horizontal-group {
	display: flex;
	gap: 70px;
	justify-content: flex-end;
	margin-left: auto;
	width: fit-content;
}

.label-value-group {
	display: flex;
	gap: 8px;
}

.unit {
	margin-right: 5px;
	font-size: 14px;
	color: var(--iata-black);
}
