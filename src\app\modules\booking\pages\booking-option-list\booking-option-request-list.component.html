<div class="orll-booking-option-list iata-box">
	<div class="orll-booking-option-list__search_bar row">
		<div class="col-7">
			<form [formGroup]="bookingOptionSearchForm">
				<div class="orll-booking-option-list__search-form width-100">
					<mat-form-field appearance="outline" class="orll-booking-option-list__input width-100" floatLabel="always">
						<mat-icon matPrefix>search</mat-icon>
						<input matInput formControlName="productDescription" placeholder="{{ 'users.mgmt.keyword' | translate }}" />
					</mat-form-field>
					<button mat-stroked-button color="primary" (click)="onSearch()" fxLayout="row" fxLayoutAlign="center center">
						<mat-icon>search</mat-icon>
						{{ 'sli.mgmt.search' | translate }}
					</button>
				</div>
			</form>
		</div>
		<div class="col-5 orll-booking-option-list__btn">
			@if (hasSomeRole(forwarder) | async) {
				<button mat-flat-button color="primary" (click)="createBookingOptionRequest()" fxLayout="row" fxLayoutAlign="center center">
					<mat-icon>add</mat-icon>
					{{ 'booking.option.create.quote.btn' | translate }}
				</button>
			}
		</div>
	</div>
	<orll-table [columns]="columns" [service]="bookingService" [param]="param"></orll-table>
</div>
