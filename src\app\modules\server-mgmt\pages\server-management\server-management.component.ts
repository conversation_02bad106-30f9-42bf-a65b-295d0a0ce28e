import { Organization } from '@shared/models/organization.model';
import { ServerManagementService } from './../../services/server-management.service';
import { Component, OnInit } from '@angular/core';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ServerType } from '../../models/mgmt-server.model';
import { OrllServerDetailComponent } from '../../components/orll-server-detail/orll-server-detail.component';

@Component({
	selector: 'orll-server-management',
	standalone: true,
	imports: [
		MatButtonModule,
		MatIconModule,
		TranslateModule,
		MatTabsModule,
		CommonModule,
		MatExpansionModule,
		MatInputModule,
		MatAutocompleteModule,
		OrllServerDetailComponent,
	],
	templateUrl: './server-management.component.html',
	styleUrl: './server-management.component.scss',
})
export default class ServerManagementComponent implements OnInit {
	selectedItem: any = null;
	currentServerType = ServerType.RESIDENT;
	organizations: Organization[] = [];
	serverType = ServerType;
	currentTabLabel = '';

	constructor(
		private readonly serverManagementService: ServerManagementService,
		private readonly translateService: TranslateService
	) { }

	ngOnInit(): void {
		this.serverManagementService.getOrgList(ServerType.RESIDENT).subscribe((res) => {
			this.organizations = res;
			this.selectedItem = this.organizations[0] ?? null;
		});
	}

	onItemClick(item: any) {
		this.selectedItem = item;
	}

	onTabChanged(event: MatTabChangeEvent): void {
		this.currentTabLabel = event.tab.textLabel;
		const type =
			this.currentTabLabel === this.translateService.instant('system.server.tab.oRLLResident')
				? ServerType.RESIDENT
				: ServerType.EXTERNAL;
		this.serverManagementService.getOrgList(type).subscribe((res) => {
			this.organizations = res;
			this.selectedItem = this.organizations?.length > 0 ? this.organizations[0] : null;
			this.currentServerType = type;
		});
	}

	newServer() {
		this.selectedItem = { id: 'new-server' };
	}

	refreshList(event: ServerType) {
		this.serverManagementService.getOrgList(event).subscribe((res) => {
			this.organizations = res;
		});
	}
}
