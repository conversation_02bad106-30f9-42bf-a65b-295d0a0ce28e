import { Component, ViewChild } from '@angular/core';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { SubscriptionRequestListComponent } from '../../components/subscription-request-list/subscription-request-list.component';
import { SubscriptionRequest } from '../../models/subscription.model';
import { SubscriptionConfigurationListComponent } from '../../components/subscription-configuration-list/subscription-configuration-list.component';

@Component({
	selector: 'orll-subscription-list',
	imports: [MatTabsModule, TranslateModule, SubscriptionRequestListComponent, SubscriptionConfigurationListComponent],
	templateUrl: './subscription-list.component.html',
	styleUrl: './subscription-list.component.scss',
})
export default class SubscriptionListComponent {
	@ViewChild('configurationList') configurationList!: SubscriptionConfigurationListComponent;
	selectedTabIndex = 0;
	currentTabLabel = '';

	requestParam: SubscriptionRequest = { fromTab: false };

	constructor(private readonly translate: TranslateService) {}

	onTabChanged($event: MatTabChangeEvent) {
		this.currentTabLabel = $event.tab.textLabel;
		this.selectedTabIndex = $event.index;
		if (this.currentTabLabel === this.translate.instant('subscription.tab.request')) {
			this.requestParam = { ...this.requestParam };
		}

		if (this.currentTabLabel === this.translate.instant('subscription.tab.configuration')) {
			this.configurationList.refreshList();
		}
	}
}
