import { Injectable } from '@angular/core';
import { map, Observable, of } from 'rxjs';
import { UserProfileService } from './user-profile.service';
import { UserProfile } from '@shared/models/user-profile.model';
import { UserRole } from '@shared/models/user-role.model';

@Injectable({ providedIn: 'root' })
export class RouteGuardService {
	constructor(private readonly userProfileService: UserProfileService) {}

	isSuperUser(): Observable<boolean> {
		return this.userProfileService.getProfile().pipe(
			map((profile: UserProfile) => {
				return profile.userType.toString() === UserRole.SUPER_USER;
			})
		);
	}

	hasSomeRole(neededRoles: string[]): Observable<boolean> {
		if (!neededRoles?.length) {
			return of(false);
		}

		return this.userProfileService.hasSomeRole(neededRoles).pipe(
			map((hasRole: boolean) => {
				return hasRole;
			})
		);
	}
}
