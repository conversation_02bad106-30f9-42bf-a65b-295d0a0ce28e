import { Component, ChangeDetectionStrategy, OnInit, Input, SimpleChanges, OnChanges } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { SliCreateRequestService } from 'src/app/modules/sli-mgmt/services/sli-create-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { CommonModule } from '@angular/common';
import { Country } from 'src/app/modules/sli-mgmt/models/country.model';
import { Province } from 'src/app/modules/sli-mgmt/models/province.model';
import { ShipmentParty } from 'src/app/modules/sli-mgmt/models/shipment-party.model';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { startWith, tap } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { OrgInfo } from '@shared/models/org-info.model';
import { OrgType } from '@shared/models/org-type.model';
import { MatDividerModule } from '@angular/material/divider';
import { OverlayModule } from '@angular/cdk/overlay';
import { HawbListObject } from 'src/app/modules/hawb-mgmt/models/hawb-list-object.model';
import { MawbCreateRequestService } from '../../../services/mawb-create-request.service';
import { HawbCreateDto } from 'src/app/modules/hawb-mgmt/models/hawb-create.model';
import { MatButtonModule } from '@angular/material/button';
import { OrllCopyDirective } from '@shared/directive/orll-copy.directive';

@Component({
	selector: 'orll-shipper-or-consignee-info',
	templateUrl: './shipper-or-consignee-info.component.html',
	styleUrls: ['./shipper-or-consignee-info.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatInputModule,
		MatSelectModule,
		MatIconModule,
		MatDividerModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		CommonModule,
		MatAutocompleteModule,
		OverlayModule,
		MatButtonModule,
		OrllCopyDirective,
	],
})
export class ShipperOrConsigneeInfoComponent extends DestroyRefComponent implements OnInit, OnChanges {
	countries: Country[] = [];
	provinces: Province[] = [];
	cities: CodeName[] = [];
	filteredCountries: Country[] = [];
	filteredProvinces: Province[] = [];
	filteredCities: CodeName[] = [];
	isOpen = false;

	@Input() title = '';
	@Input() shipmentParty: OrgInfo | ShipmentParty | null = null;
	@Input() hawbList: HawbListObject[] = [];
	@Input() share = false;
	shipperConsigneeForm: FormGroup = new FormGroup({
		companyName: new FormControl<string>('', [Validators.required]),
		accountingNoteText: new FormControl<string>(''),
		contactName: new FormControl<string>(''),
		countryCode: new FormControl<string>('', [Validators.required]),
		regionCode: new FormControl<string>('', [Validators.required]),
		cityCode: new FormControl<string>('', [Validators.required]),
		textualPostCode: new FormControl<string>(''),
		locationName: new FormControl<string>('', [Validators.required]),
		phoneNumber: new FormControl<string>('', [Validators.required]),
		emailAddress: new FormControl<string>('', [Validators.email]),
	});

	constructor(
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly mawbCreateRequestService: MawbCreateRequestService
	) {
		super();
	}

	ngOnInit(): void {
		this.initRefData();
		this.setupAutocomplete();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['shipmentParty']) {
			this.fillShipmentInfo();
		}
		if (changes['share']) {
			Object.keys(this.shipperConsigneeForm.controls).forEach((key) => {
				if (key !== 'accountingNoteText') {
					const control = this.shipperConsigneeForm.get(key);
					changes['share'].currentValue ? control!.disable() : control!.enable();
				}
			});
		}
	}

	private initRefData(): void {
		this.sliCreateRequestService.getCountries().subscribe((countries: Country[]) => {
			this.countries = countries;
			this.filteredCountries = countries;
		});
	}

	getUniqueParties(list: HawbListObject[], key: 'shipper' | 'consignee') {
		return list.reduce((acc, current) => {
			const exists = acc.some((item) => item[key] === current[key]);
			return exists ? acc : [...acc, current];
		}, [] as HawbListObject[]);
	}

	onSelectParty(hawbId: string, title: string): void {
		this.mawbCreateRequestService
			.getHawbDetail(hawbId)
			.pipe(
				tap((result: HawbCreateDto) => {
					if (result) {
						const { sliPartyList } = result;
						if (sliPartyList && sliPartyList.length > 0) {
							const orgType = title === 'shipper' ? OrgType.SHIPPER : OrgType.CONSIGNEE;
							this.shipmentParty = sliPartyList.find((party) => party.companyType === orgType) ?? null;
							this.fillShipmentInfo();
							this.isOpen = false;
						}
					}
				}),
				takeUntilDestroyed(this.destroyRef)
			)
			.subscribe();
	}

	private isOrgInfo(info: OrgInfo | ShipmentParty): info is OrgInfo {
		return 'persons' in info;
	}

	private getShipmentInfo(info: OrgInfo | ShipmentParty): ShipmentParty {
		if (this.isOrgInfo(info)) {
			const contact = info.persons.find((person) => person.contactRole === OrgType.CUSTOMER_CONTACT);
			return {
				companyName: info.companyName,
				contactName: contact?.contactName ?? '',
				countryCode: info.countryCode,
				regionCode: info.regionCode,
				cityCode: info.cityCode,
				textualPostCode: info.textualPostCode,
				locationName: info.locationName,
				phoneNumber: contact?.phoneNumber ?? '',
				emailAddress: contact?.emailAddress ?? '',
				companyType: info.partyRole,
			};
		} else {
			return {
				companyName: info.companyName,
				contactName: info.contactName,
				countryCode: info.countryCode,
				regionCode: info.regionCode,
				cityCode: info.cityCode,
				textualPostCode: info.textualPostCode,
				locationName: info.locationName,
				phoneNumber: info.phoneNumber,
				emailAddress: info.emailAddress,
				companyType: info.companyType,
			};
		}
	}

	fillShipmentInfo(): void {
		if (!this.shipmentParty) return;

		this.setupCountryValueChange(this.shipmentParty.countryCode);
		this.setupRegionValueChange(this.shipmentParty.regionCode);

		const shipmentParty = this.getShipmentInfo(this.shipmentParty);
		this.shipperConsigneeForm.patchValue(shipmentParty);
	}

	private setupAutocomplete(): void {
		this.shipperConsigneeForm
			.get('countryCode')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredCountries = this.countries.filter((country) =>
					country.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
				);
			});
	}

	displayCountryName = (code: string): string => {
		const country = this.countries.find((item) => item.code === code);
		return country?.name ?? '';
	};

	displayProvinceName = (code: string): string => {
		const province = this.provinces.find((item) => item.code === code);
		return province?.name ?? '';
	};

	displayCityName = (code: string): string => {
		const city = this.cities.find((item) => item.code === code);
		return city?.name ?? '';
	};

	private setupCountryValueChange(value: string): void {
		const selectedCountry: Country[] = this.countries.filter((country: Country) => country.code === value);
		this.sliCreateRequestService.getProvinces(selectedCountry[0]).subscribe((provinces: Province[]) => {
			this.provinces = provinces;
			this.filteredProvinces = provinces;

			this.shipperConsigneeForm
				.get('regionCode')
				?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
				.subscribe((search) => {
					this.filteredProvinces = this.provinces.filter((province) =>
						province.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
					);
				});
		});
	}

	private setupRegionValueChange(value: string): void {
		const selectedProvince: Province[] = this.provinces.filter((province: Province) => province.code === value);
		this.sliCreateRequestService.getCities(selectedProvince[0]).subscribe((cities: CodeName[]) => {
			this.cities = cities;
			this.filteredCities = cities;

			this.shipperConsigneeForm
				.get('cityCode')
				?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
				.subscribe((search) => {
					this.filteredCities = this.cities.filter((city) =>
						city.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
					);
				});
		});
	}

	countryValueChange(event?: MatAutocompleteSelectedEvent): void {
		this.shipperConsigneeForm.get('regionCode')?.setValue('');
		this.shipperConsigneeForm.get('cityCode')?.setValue('');

		const value = event?.option.value ?? '';
		this.setupCountryValueChange(value);
	}

	regionValueChange(event?: MatAutocompleteSelectedEvent): void {
		const value = event?.option.value ?? '';
		this.setupRegionValueChange(value);
	}

	getFormData(ignore?: boolean): ShipmentParty | null {
		if (!ignore && this.shipperConsigneeForm.invalid) {
			return null;
		}
		return {
			id: this.shipmentParty?.id ?? null,
			companyName: this.shipperConsigneeForm.value.companyName!,
			contactName: this.shipperConsigneeForm.value.contactName,
			countryCode: this.shipperConsigneeForm.value.countryCode!,
			regionCode: this.shipperConsigneeForm.value.regionCode!,
			cityCode: this.shipperConsigneeForm.value.cityCode!,
			textualPostCode: this.shipperConsigneeForm.value.textualPostCode,
			locationName: this.shipperConsigneeForm.value.locationName!,
			phoneNumber: this.shipperConsigneeForm.value.phoneNumber!,
			emailAddress: this.shipperConsigneeForm.value.emailAddress,
			companyType: this.title === 'shipper' ? OrgType.SHIPPER : OrgType.CONSIGNEE,
		};
	}
}
