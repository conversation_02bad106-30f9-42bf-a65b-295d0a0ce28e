import { TestBed } from '@angular/core/testing';
import { SubscriptionRequestService } from './subscription-request.service';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { environment } from '@environments/environment';
import { SubscriptionDetailObj, SubscriptionListObj, SubscriptionRequest } from '../models/subscription.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { LogisticObjType } from '@shared/models/share-type.model';
import { TranslateService } from '@ngx-translate/core';

const baseUrl = environment.baseApi;

describe('SubscriptionService', () => {
	let service: SubscriptionRequestService;
	let httpMock: HttpTestingController;
	let mockTranslateService: jasmine.SpyObj<TranslateService>;

	const mockAllData: SubscriptionListObj[] = [
		{
			topic: '111',
			topicType: 'Log',
			createDate: '2025-08-01',
			approvedDate: '2025-08-01',
			subscriptionType: '1222',
			status: 'Approved',
			isRequestedBy: 'company a',
			isRequestedAt: '2025-08-01',
			subscriberOrgName: '111',
			orgId: '',
			id: '',
			subscriberOrgId: '',
			publisherOrgId: '',
		},
	];

	const mockPagedRequest: SubscriptionRequest = {
		subscriberId: '111',
		fromTab: false,
	};
	const mockPagedResponse: PaginationResponse<SubscriptionListObj> = {
		total: 25,
		pageNum: 1,
		rows: [
			{
				topic: '111',
				topicType: 'Log',
				createDate: '2025-08-01',
				approvedDate: '2025-08-01',
				subscriptionType: '1222',
				status: 'Approved',
				isRequestedBy: 'company a',
				isRequestedAt: '2025-08-01',
				subscriberOrgName: '111',
				orgId: '',
				id: '',
				subscriberOrgId: '',
				publisherOrgId: '',
			},
			{
				topic: '222',
				topicType: 'Log',
				createDate: '2025-08-01',
				approvedDate: '2025-08-01',
				subscriptionType: '1222',
				status: 'Approved',
				isRequestedBy: 'company a',
				isRequestedAt: '2025-08-01',
				subscriberOrgName: '111',
				orgId: '',
				id: '',
				subscriberOrgId: '',
				publisherOrgId: '',
			},
		],
	};

	beforeEach(() => {
		mockTranslateService = jasmine.createSpyObj('TranslateService', ['instant']);
		mockTranslateService.instant.and.returnValue('Are you sure to delete?');

		TestBed.configureTestingModule({
			imports: [],
			providers: [SubscriptionRequestService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(SubscriptionRequestService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should send POST request and return SubscriptionListObj[]', () => {
		const param = { filter: 'all' };

		service.loadAllData(param).subscribe((data) => {
			expect(data).toEqual(mockAllData);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
		expect(req.request.method).toBe('POST');
		expect(req.request.body).toEqual(param);
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(mockAllData);
	});

	it('should send POST request and return PaginationResponse<SubListObj>', () => {
		service.getDataPerPage(mockPagedRequest).subscribe((response) => {
			expect(response).toEqual(mockPagedResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
		expect(req.request.method).toBe('POST');
		expect(req.request.body).toEqual(mockPagedRequest);

		req.flush(mockPagedResponse);
	});

	it('should handle empty response for loadAllData', () => {
		service.loadAllData({}).subscribe((data) => {
			expect(data.length).toBe(0);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
		req.flush([]);
	});

	it('should handle pagination with total count', () => {
		service
			.getDataPerPage({
				subscriberId: '111',
				fromTab: false,
			})
			.subscribe((res) => {
				expect(res.total).toBe(25);
				expect(res.pageNum).toBe(1);
			});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
		req.flush(mockPagedResponse);
	});

	it('should return [] if fromTab is true - topic is empty', () => {
		service
			.getDataPerPage({
				subscriptionType: LogisticObjType.HAWB,
				fromTab: true,
				topic: '',
			})
			.subscribe((res) => {
				expect(res.total).toBe(0);
				expect(res.pageNum).toBe(1);
			});

		httpMock.expectNone(`${baseUrl}/user-subscriptions-request/list`);
	});

	it('should fail gracefully on 500 error', () => {
		const errorMsg = 'Internal Server Error';

		service.loadAllData({}).subscribe({
			next: () => fail('should have failed'),
			error: (error) => {
				expect(error.status).toBe(500);
				expect(error.error.message).toContain(errorMsg);
			},
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
		req.flush({ message: errorMsg }, { status: 500, statusText: 'Internal Server Error' });
	});

	describe('getSubscriptionsDetails', () => {
		const mockSubscriptionDetail: SubscriptionDetailObj = {
			id: 'detail-123',
			topic: 'test-topic',
			topicType: 'Log',
			createDate: '2025-08-01',
			approvedDate: '2025-08-02',
			subscriptionType: 'premium',
			status: 'Approved',
			isRequestedBy: 'Test Company',
			isRequestedAt: '2025-08-01',
			subscriberOrgName: 'Subscriber Org',
			orgId: 'org-123',
			subscriberOrgId: 'sub-org-123',
			publisherOrgId: 'pub-org-123',
			subscriptionRequestUri: 'http://example.com/request',
			includeSubscriptionEventType: 'create,update,delete',
			permissions: 'read,write',
		};

		it('should get subscription details by id', () => {
			const subscriptionId = 'detail-123';

			service.getSubscriptionsDetails(subscriptionId).subscribe((detail) => {
				expect(detail).toEqual(mockSubscriptionDetail);
				expect(detail.id).toBe(subscriptionId);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/detail`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toEqual({ id: subscriptionId });
			expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

			req.flush(mockSubscriptionDetail);
		});

		it('should handle empty subscription id', () => {
			const subscriptionId = '';

			service.getSubscriptionsDetails(subscriptionId).subscribe((detail) => {
				expect(detail).toEqual(mockSubscriptionDetail);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/detail`);
			expect(req.request.body).toEqual({ id: '' });

			req.flush(mockSubscriptionDetail);
		});

		it('should handle subscription not found error', () => {
			const subscriptionId = 'non-existent-123';
			const errorMessage = 'Subscription not found';

			service.getSubscriptionsDetails(subscriptionId).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toBe(404);
					expect(error.error.message).toContain(errorMessage);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/detail`);
			req.flush({ message: errorMessage }, { status: 404, statusText: 'Not Found' });
		});

		it('should handle subscription details with null permissions', () => {
			const detailWithNullPermissions: SubscriptionDetailObj = {
				...mockSubscriptionDetail,
				includeSubscriptionEventType: null,
				permissions: null,
			};

			service.getSubscriptionsDetails('test-id').subscribe((detail) => {
				expect(detail.includeSubscriptionEventType).toBeNull();
				expect(detail.permissions).toBeNull();
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/detail`);
			req.flush(detailWithNullPermissions);
		});
	});

	describe('updateSubscriptionsStatus', () => {
		const mockUpdatedSubscription: SubscriptionDetailObj = {
			id: 'update-123',
			topic: 'updated-topic',
			topicType: 'Event',
			createDate: '2025-08-01',
			approvedDate: '2025-08-03',
			subscriptionType: 'standard',
			status: 'Approved',
			isRequestedBy: 'Updated Company',
			isRequestedAt: '2025-08-01',
			subscriberOrgName: 'Updated Subscriber',
			orgId: 'org-456',
			subscriberOrgId: 'sub-org-456',
			publisherOrgId: 'pub-org-456',
			subscriptionRequestUri: 'http://example.com/updated',
			includeSubscriptionEventType: 'create,update',
			permissions: 'read',
		};

		it('should update subscription status to Approved', () => {
			const subscriptionId = 'update-123';
			const newStatus = 'Approved';

			service.updateSubscriptionsStatus(subscriptionId, newStatus).subscribe((result) => {
				expect(result).toEqual(mockUpdatedSubscription);
				expect(result.status).toBe(newStatus);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/change-status`);
			expect(req.request.method).toBe('PUT');
			expect(req.request.body).toEqual({ id: subscriptionId, status: newStatus });
			expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

			req.flush(mockUpdatedSubscription);
		});

		it('should update subscription status to Rejected', () => {
			const subscriptionId = 'update-456';
			const newStatus = 'Rejected';

			service.updateSubscriptionsStatus(subscriptionId, newStatus).subscribe((result) => {
				expect(result).toEqual(mockUpdatedSubscription);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/change-status`);
			expect(req.request.body).toEqual({ id: subscriptionId, status: newStatus });

			req.flush(mockUpdatedSubscription);
		});

		it('should update subscription status to Revoked', () => {
			const subscriptionId = 'update-789';
			const newStatus = 'Revoked';

			service.updateSubscriptionsStatus(subscriptionId, newStatus).subscribe((result) => {
				expect(result).toEqual(mockUpdatedSubscription);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/change-status`);
			expect(req.request.body).toEqual({ id: subscriptionId, status: newStatus });

			req.flush(mockUpdatedSubscription);
		});

		it('should handle empty parameters in status update', () => {
			const subscriptionId = '';
			const newStatus = '';

			service.updateSubscriptionsStatus(subscriptionId, newStatus).subscribe((result) => {
				expect(result).toEqual(mockUpdatedSubscription);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/change-status`);
			expect(req.request.body).toEqual({ id: '', status: '' });

			req.flush(mockUpdatedSubscription);
		});

		it('should handle status update error', () => {
			const subscriptionId = 'error-123';
			const newStatus = 'InvalidStatus';
			const errorMessage = 'Invalid status transition';

			service.updateSubscriptionsStatus(subscriptionId, newStatus).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toBe(400);
					expect(error.error.message).toContain(errorMessage);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/change-status`);
			req.flush({ message: errorMessage }, { status: 400, statusText: 'Bad Request' });
		});

		it('should handle unauthorized status update', () => {
			const subscriptionId = 'unauthorized-123';
			const newStatus = 'Approved';

			service.updateSubscriptionsStatus(subscriptionId, newStatus).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toBe(403);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/change-status`);
			req.flush({ message: 'Forbidden' }, { status: 403, statusText: 'Forbidden' });
		});
	});

	describe('getDataPerPage Edge Cases', () => {
		it('should return empty response when fromTab is true and groupId is undefined', () => {
			const request: SubscriptionRequest = {
				fromTab: true,
				topic: 'test-topic',
				// groupId is undefined
			};

			service.getDataPerPage(request).subscribe((response) => {
				expect(response.total).toBe(0);
				expect(response.pageNum).toBe(1);
				expect(response.rows).toEqual([]);
			});

			// Should not make HTTP request
			httpMock.expectNone(`${baseUrl}/user-subscriptions-request/list`);
		});

		it('should return empty response when fromTab is true and groupId is null', () => {
			const request: SubscriptionRequest = {
				fromTab: true,
				topic: 'test-topic',
				groupId: null as any,
			};

			service.getDataPerPage(request).subscribe((response) => {
				expect(response.total).toBe(0);
				expect(response.pageNum).toBe(1);
				expect(response.rows).toEqual([]);
			});

			httpMock.expectNone(`${baseUrl}/user-subscriptions-request/list`);
		});

		it('should return empty response when fromTab is true and groupId is empty string', () => {
			const request: SubscriptionRequest = {
				fromTab: true,
				topic: 'test-topic',
				groupId: '',
			};

			service.getDataPerPage(request).subscribe((response) => {
				expect(response.total).toBe(0);
				expect(response.pageNum).toBe(1);
				expect(response.rows).toEqual([]);
			});

			httpMock.expectNone(`${baseUrl}/user-subscriptions-request/list`);
		});

		it('should make HTTP request when fromTab is true but groupId has value', () => {
			const request: SubscriptionRequest = {
				fromTab: true,
				topic: 'test-topic',
				groupId: 'valid-group-id',
			};

			service.getDataPerPage(request).subscribe((response) => {
				expect(response).toEqual(mockPagedResponse);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toEqual(request);

			req.flush(mockPagedResponse);
		});

		it('should make HTTP request when fromTab is false regardless of groupId', () => {
			const request: SubscriptionRequest = {
				fromTab: false,
				topic: 'test-topic',
				// groupId is undefined but should still make request
			};

			service.getDataPerPage(request).subscribe((response) => {
				expect(response).toEqual(mockPagedResponse);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
			expect(req.request.body).toEqual(request);

			req.flush(mockPagedResponse);
		});

		it('should handle complex subscription request parameters', () => {
			const complexRequest: SubscriptionRequest = {
				topic: 'complex-topic-with-special-chars@#$',
				subscriptionType: LogisticObjType.MAWB,
				ubscriptionEventType: 'create,update,delete',
				description: 'Complex subscription with detailed description',
				userId: 'user-complex-123',
				subscriberId: 'subscriber-complex-456',
				fromTab: false,
				groupId: 'group-complex-789',
			};

			service.getDataPerPage(complexRequest).subscribe((response) => {
				expect(response).toEqual(mockPagedResponse);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
			expect(req.request.body).toEqual(complexRequest);
			expect(req.request.body.topic).toContain('@#$');

			req.flush(mockPagedResponse);
		});
	});

	describe('loadAllData Edge Cases', () => {
		it('should handle loadAllData with complex parameters', () => {
			const complexParam = {
				filter: 'advanced',
				dateRange: {
					start: '2024-01-01',
					end: '2024-12-31',
				},
				statusList: ['Approved', 'Pending', 'Rejected'],
				orgIds: ['org-1', 'org-2', 'org-3'],
			};

			service.loadAllData(complexParam).subscribe((data) => {
				expect(data).toEqual(mockAllData);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
			expect(req.request.body).toEqual(complexParam);

			req.flush(mockAllData);
		});

		it('should handle loadAllData with null parameter', () => {
			service.loadAllData(null).subscribe((data) => {
				expect(data).toEqual([]);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
			expect(req.request.body).toBeNull();

			req.flush([]);
		});

		it('should handle loadAllData with large dataset', () => {
			// eslint-disable-next-line @typescript-eslint/naming-convention
			const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
				...mockAllData[0],
				id: `large-dataset-${i}`,
				topic: `topic-${i}`,
			}));

			service.loadAllData({ filter: 'large' }).subscribe((data) => {
				expect(data.length).toBe(1000);
				expect(data[0].id).toBe('large-dataset-0');
				expect(data[999].id).toBe('large-dataset-999');
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
			req.flush(largeDataset);
		});
	});

	describe('Error Handling and Network Issues', () => {
		it('should handle network timeout in getDataPerPage', () => {
			service.getDataPerPage(mockPagedRequest).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toBe(0);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
			req.error(new ProgressEvent('timeout'));
		});

		it('should handle JSON parsing error in loadAllData', () => {
			service.loadAllData({}).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toBe(0);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
			// Simulate a network error
			req.error(new ProgressEvent('JSON Parse Error'));
		});

		it('should handle service unavailable error', () => {
			const errorMessage = 'Service temporarily unavailable';

			service.getDataPerPage(mockPagedRequest).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toBe(503);
					expect(error.error.message).toContain(errorMessage);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
			req.flush({ message: errorMessage }, { status: 503, statusText: 'Service Unavailable' });
		});
	});

	describe('Integration and Workflow Tests', () => {
		it('should handle complete subscription workflow', () => {
			// Step 1: Load all data
			service.loadAllData({}).subscribe((allData) => {
				expect(allData).toEqual(mockAllData);
			});

			const loadReq = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
			loadReq.flush(mockAllData);

			// Step 2: Get paginated data
			service.getDataPerPage(mockPagedRequest).subscribe((pagedData) => {
				expect(pagedData).toEqual(mockPagedResponse);
			});

			const pageReq = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
			pageReq.flush(mockPagedResponse);

			// Step 3: Get subscription details
			service.getSubscriptionsDetails('detail-123').subscribe((details) => {
				expect(details.id).toBe('detail-123');
			});

			const detailReq = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/detail`);
			detailReq.flush({
				...mockAllData[0],
				id: 'detail-123',
				publisherOrgId: 'pub-123',
				subscriptionRequestUri: 'http://example.com',
				includeSubscriptionEventType: 'create',
				permissions: 'read',
			});

			// Step 4: Update subscription status
			service.updateSubscriptionsStatus('detail-123', 'Approved').subscribe((updated) => {
				expect(updated.status).toBe('Approved');
			});

			const updateReq = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/change-status`);
			updateReq.flush({
				...mockAllData[0],
				id: 'detail-123',
				status: 'Approved',
				publisherOrgId: 'pub-123',
				subscriptionRequestUri: 'http://example.com',
				includeSubscriptionEventType: 'create',
				permissions: 'read',
			});
		});

		it('should handle concurrent requests properly', () => {
			// Make multiple concurrent requests
			service.loadAllData({ filter: 'concurrent1' }).subscribe();
			service.loadAllData({ filter: 'concurrent2' }).subscribe();
			service.getDataPerPage({ ...mockPagedRequest, subscriberId: 'concurrent' }).subscribe();

			const requests = httpMock.match(`${baseUrl}/user-subscriptions-request/list`);
			expect(requests.length).toBe(3);

			requests[0].flush(mockAllData);
			requests[1].flush(mockAllData);
			requests[2].flush(mockPagedResponse);
		});
	});
});
