@if (detailInfo) {
	<div class="booking-option-request-detail">
		@if (quote) {
			<div class="row detail-title">
				@if (hasSomeRole(forwarder) | async) {
					<span>{{ currentOrgName }}{{ ' | ' }}</span
					><span>{{ 'booking.option.iata.code' | translate }}{{ detailInfo?.iataCargoAgentCode ?? '' }}</span>
				} @else {
					<span>{{ 'booking.option.request.title' | translate }}</span
					><span class="title-from"
						>{{ detailInfo?.companyName ?? '' }}{{ 'booking.option.from' | translate }}{{ detailInfo.iataCargoAgentCode }}</span
					>
				}
			</div>
			<div class="booking-option-request-detail_quote">
				<div class="row">
					<div class="col-3 value">
						{{ 'sli.piece.table.column.pieceQuantity' | translate }}: {{ detailInfo.pieceGroupCount }}
					</div>
					<div class="col-3 value">{{ 'sli.piece.grossWeight' | translate }}: {{ detailInfo.totalGrossWeight }}</div>
					<div class="col-2 value">{{ 'booking.option.chargeable.weight' | translate }}: {{ detailInfo.chargeableWeight }}</div>
					<div class="col-3 value">
						{{ 'sli.piece.dimensions' | translate }}:
						{{ `${detailInfo.dimLength}CM*${detailInfo.dimWidth}CM*${detailInfo.dimHeight}CM` }}
					</div>
				</div>
				<div class="row">
					<div class="col-3 value">{{ 'booking.option.commodity.code' | translate }}: {{ detailInfo.expectedCommodity }}</div>

					<div class="col-3 value">
						{{ 'booking.option.handling.code' | translate }}:
						{{ detailInfo.specialHandlingCodes }}
					</div>
					<div class="col-5 value">
						{{ 'booking.option.handling.instruction' | translate }}:
						{{ detailInfo.textualHandlingInstructions }}
					</div>
				</div>
				<mat-divider class="divider"></mat-divider>
				<div class="row">
					<div class="col-3 value">{{ 'booking.option.departure.location' | translate }}: {{ detailInfo.departureLocation }}</div>

					<div class="col-3 value">
						{{ 'booking.option.arrival.location' | translate }}:
						{{ detailInfo.arrivalLocation }}
					</div>
					<div class="col-2 value">
						{{ 'booking.option.max.segment' | translate }}:
						{{ detailInfo.maxSegments }}
					</div>
					<div class="col-3 value">
						{{ 'booking.option.transport.id' | translate }}:
						{{ detailInfo.preferredTransportId }}
					</div>
				</div>
				<div class="row">
					<div class="col-3 value">
						{{ 'booking.option.earliest.acceptance.time' | translate }}:
						{{ detailInfo.earliestAcceptanceTime | iataDateFormat: 'dd MM yyyy HH:mm' }}
					</div>

					<div class="col-3 value">
						{{ 'booking.option.latest.acceptance.time' | translate }}:
						{{ detailInfo.latestAcceptanceTime | iataDateFormat: 'dd MM yyyy HH:mm' }}
					</div>

					<div class="col-2 value">
						{{ 'booking.option.latest.arrival.time' | translate }}:
						{{ detailInfo.latestArrivalTime | iataDateFormat: 'dd MM yyyy HH:mm' }}
					</div>

					<div class="col-3 value">
						{{ 'booking.option.shipment.available.date' | translate }}:
						{{ detailInfo.timeOfAvailability | iataDateFormat: 'dd MM yyyy HH:mm' }}
					</div>
				</div>
				<mat-divider class="divider"></mat-divider>
				<div class="row">
					<div class="col-3 value">{{ 'booking.option.currency' | translate }}: {{ detailInfo.currency }}</div>
				</div>
			</div>
		} @else {
			<div class="row booking-option-request-detail__sub-title">
				{{ 'booking.option.subTitle.booking.shipment' | translate }}
			</div>
			<div class="row">
				<div class="width-20">
					<div class="label">{{ 'sli.piece.table.column.pieceQuantity' | translate }}</div>
					<div class="value">{{ detailInfo.pieceGroupCount }}</div>
				</div>
				<div class="width-20">
					<div class="label">{{ 'sli.piece.grossWeight' | translate }}</div>
					<div class="value">
						<div>{{ detailInfo.totalGrossWeight }}</div>
						<div>KG</div>
					</div>
				</div>
				<div class="width-20">
					<div class="label">{{ 'booking.option.chargeable.weight' | translate }}</div>
					<div class="value">
						<div>{{ detailInfo.chargeableWeight }}</div>
						<div>KG</div>
					</div>
				</div>
				<div class="width-35">
					<div class="label">{{ 'sli.piece.dimensions' | translate }}</div>
					<div class="dimension-value">
						<div>{{ detailInfo.dimLength }}</div>
						<div>CM</div>
						<div>{{ detailInfo.dimWidth }}</div>
						<div>CM</div>
						<div>{{ detailInfo.dimHeight }}</div>
						<div>CM</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="width-20">
					<div class="label">{{ 'booking.option.commodity.code' | translate }}</div>
					<div class="value">{{ detailInfo.expectedCommodity }}</div>
				</div>
				<div class="width-20">
					<div class="label">{{ 'booking.option.handling.code' | translate }}</div>
					<div class="value">
						{{ detailInfo.specialHandlingCodes }}
					</div>
				</div>
				<div class="width-55">
					<div class="label">{{ 'booking.option.handling.instruction' | translate }}</div>
					<div class="value">
						{{ detailInfo.textualHandlingInstructions }}
					</div>
				</div>
			</div>
			<div class="row booking-option-request-detail__sub-title">
				{{ 'booking.option.subTitle.itinerary' | translate }}
			</div>
			<div class="row">
				<div class="width-20">
					<div class="label">{{ 'booking.option.departure.location' | translate }}</div>
					<div class="value">{{ detailInfo.departureLocation }}</div>
				</div>
				<div class="width-20">
					<div class="label">{{ 'booking.option.arrival.location' | translate }}</div>
					<div class="value">
						{{ detailInfo.arrivalLocation }}
					</div>
				</div>
				<div class="width-20">
					<div class="label">{{ 'booking.option.max.segment' | translate }}</div>
					<div class="value">
						{{ detailInfo.maxSegments }}
					</div>
				</div>
				<div class="width-20">
					<div class="label">{{ 'booking.option.transport.id' | translate }}</div>
					<div class="value">
						{{ detailInfo.preferredTransportId }}
					</div>
				</div>
			</div>
			<div class="row booking-option-request-detail__sub-title">
				{{ 'booking.option.subTitle.preference' | translate }}
			</div>
			<div class="row">
				<div class="width-20">
					<div class="label">{{ 'booking.option.earliest.acceptance.time' | translate }}</div>
					<div class="value">{{ detailInfo.earliestAcceptanceTime | iataDateFormat: 'dd MM yyyy HH:mm' }}</div>
				</div>
				<div class="width-20">
					<div class="label">{{ 'booking.option.latest.acceptance.time' | translate }}</div>
					<div class="value">{{ detailInfo.latestAcceptanceTime | iataDateFormat: 'dd MM yyyy HH:mm' }}</div>
				</div>
				<div class="width-20">
					<div class="label">{{ 'booking.option.latest.arrival.time' | translate }}</div>
					<div class="value">{{ detailInfo.latestArrivalTime | iataDateFormat: 'dd MM yyyy HH:mm' }}</div>
				</div>
				<div class="width-20">
					<div class="label">{{ 'booking.option.shipment.available.date' | translate }}</div>
					<div class="value">{{ detailInfo.timeOfAvailability | iataDateFormat: 'dd MM yyyy HH:mm' }}</div>
				</div>
			</div>
			<div class="row">
				<div class="width-20">
					<div class="label">{{ 'booking.option.currency' | translate }}</div>
					<div class="value">{{ detailInfo.currency }}</div>
				</div>
			</div>
		}
	</div>
}
