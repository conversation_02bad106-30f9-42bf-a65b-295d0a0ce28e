export interface PieceDgDto {
	id?: string;
	type: string;
	packagingType: PackagingType;
	nvdForCustoms: boolean;
	nvdForCarriage: boolean;
	grossWeight: number;
	upid: any;
	dimensions: Dimensions;
	packagedIdentifier: string;
	textualHandlingInstructions: string;
	shippingMarks: string;
	pieceQuantity: number;
	sliNumber: string;
	slac: number;
	product: Product;
	allPackedInOneIndicator: boolean;
	dgDeclaration: DgDeclaration;
	containedItems: ContainedItem[];
	qvalueNumeric: number;
}

export interface PackagingType {
	typeCode: string;
	description: string;
}

export interface Dimensions {
	length: number;
	width: number;
	height: number;
}

export interface DgDeclaration {
	shipperDeclarationText: string;
	handlingInformation: string;
	complianceDeclarationText: string;
	exclusiveUseIndicator: string;
	aircraftLimitationInformation: string;
}

export interface ContainedItem {
	product: Product;
	weight: number;
	itemQuantity: number;
}

export interface Product {
	description: string;
	hsCommodityDescription: string;
}
