import { OrllTableComponent } from '@shared/components/orll-table/orll-table.component';
import { SubscriptionRequestService } from '../../services/subscription-request.service';
import { SubcriptionRequestStatus, SubscriptionListObj, SubscriptionRequest } from '../../models/subscription.model';
import { OrllColumnDef } from '@shared/models/orlll-common-table';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { ChangeDetectorRef, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { SubscriptionRequestDetailsComponent } from '../subscription-request-details/subscription-request-details.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';

@Component({
	selector: 'orll-subscription-request-list',
	imports: [OrllTableComponent, MatFormFieldModule, ReactiveFormsModule, TranslateModule],
	templateUrl: './subscription-request-list.component.html',
	styleUrl: './subscription-request-list.component.scss',
	providers: [IataDateFormatPipe],
})
export class SubscriptionRequestListComponent extends DestroyRefComponent implements OnInit, OnChanges {
	@Input() param: SubscriptionRequest = { fromTab: true };
	orgMap: Map<string, string> = new Map<string, string>();

	columns: OrllColumnDef<SubscriptionListObj>[] = [
		{
			key: 'publisherOrgId',
			header: 'subscription.request.table.publisher',
			transform: (val) => {
				return this.orgMap.get(val) ?? val;
			},
		},
		{
			key: 'topicType',
			header: 'subscription.request.table.topicType',
		},
		{
			key: 'topic',
			header: 'subscription.request.table.topic',
		},
		{
			key: 'subscriberOrgName',
			header: 'subscription.request.table.requestBy',
		},
		{
			key: 'isRequestedAt',
			header: 'subscription.request.table.requestAt',
			transform: (val) => this.iataDateFormatPipe.transform(val),
		},
		{
			key: 'status',
			header: 'subscription.request.table.status',
			transform: (val) => this.showStatus(val),
			clickCell: (row: SubscriptionListObj) => this.openRequestDetailDialog(row),
		},
	];

	constructor(
		public readonly subscriptionService: SubscriptionRequestService,
		private readonly orgMgmtRequestService: OrgMgmtRequestService,
		private readonly dialog: MatDialog,
		private readonly cdr: ChangeDetectorRef,
		private readonly iataDateFormatPipe: IataDateFormatPipe
	) {
		super();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['param']) {
			this.cdr.detectChanges();
		}
	}

	ngOnInit(): void {
		this.orgMgmtRequestService.getOrgList().subscribe({
			next: (res) => {
				if (res) {
					res.forEach((org) => {
						this.orgMap.set(org.id, org.name);
					});
				}
			},
		});
	}

	openRequestDetailDialog(row: SubscriptionListObj) {
		const dialogRef = this.dialog.open(SubscriptionRequestDetailsComponent, {
			width: '65vw',
			data: {
				id: row.id,
				status: row.status,
				publisherOrgId: row.publisherOrgId,
				subscriberOrgId: row.subscriberOrgId,
				orgMap: this.orgMap,
			},
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (result === true) {
				this.param = { ...this.param };
			}
		});
	}

	showStatus(val: string) {
		return SubcriptionRequestStatus[val as keyof typeof SubcriptionRequestStatus] ?? val;
	}
}
