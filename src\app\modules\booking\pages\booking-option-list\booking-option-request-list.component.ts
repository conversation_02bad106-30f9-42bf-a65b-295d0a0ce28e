import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { BOOKING_FINAL_STATUS, BookingOptionRequestListObj } from '../../models/booking.model';
import { ActionType, OrllColumnDef } from '@shared/models/orlll-common-table';
import { OrllTableComponent } from '@shared/components/orll-table/orll-table.component';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { FormGroup, FormControl, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { BookingOptionAirlineComponent } from '../../components/booking-option-airline/booking-option-airline.component';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { AsyncPipe } from '@angular/common';
import { UserRole } from '@shared/models/user-role.model';
import { BookingOptionRequestCreateComponent } from '../../components/booking-option-request-create/booking-option-request-create.component';
import { BookingOptionDetailComponent } from '../../components/booking-option-detail/booking-option-detail.component';
import { Router } from '@angular/router';
import { forkJoin } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { StatusHistoryComponent } from 'src/app/modules/mawb-mgmt/components/status-history/status-history.component';

@Component({
	selector: 'orll-booking-quote-list',
	imports: [
		OrllTableComponent,
		TranslateModule,
		MatFormFieldModule,
		MatIconModule,
		ReactiveFormsModule,
		MatButtonModule,
		TranslateModule,
		MatInputModule,
		MatDialogModule,
		AsyncPipe,
	],
	templateUrl: './booking-option-request-list.component.html',
	styleUrl: './booking-option-request-list.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export default class BookingOptionRequestListComponent extends RolesAwareComponent implements OnInit {
	bookingOptionSearchForm: FormGroup = new FormGroup({
		productDescription: new FormControl<string>(''),
	});

	readonly forwarder: string[] = [UserRole.FORWARDER];
	readonly carrier: string[] = [UserRole.CARRIER];

	columns: OrllColumnDef<BookingOptionRequestListObj>[] = [
		{
			key: 'expectedCommodity',
			header: 'booking.option.table.product',
			clickCell: (row) => this.viewOptionDetail(row),
		},
		{
			key: 'departureLocation',
			header: 'booking.option.table.departure',
		},
		{
			key: 'arrivalLocation',
			header: 'booking.option.table.arrival',
		},
		{
			key: 'latestStatus',
			header: 'booking.option.table.status',
			clickCell: (row) => this.viewEventHsitory(row),
		},
	];

	param = {};

	constructor(
		public readonly bookingService: BookingOptionRequestService,
		private readonly dialog: MatDialog,
		private readonly router: Router,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnInit(): void {
		const roleChecks$ = forkJoin({
			hasCarrierRole: this.hasSomeRole(this.carrier),
			hasForwarderRole: this.hasSomeRole(this.forwarder),
		});

		roleChecks$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(({ hasCarrierRole, hasForwarderRole }) => {
			if (!hasCarrierRole && !hasForwarderRole) {
				return;
			}
			const actions: ActionType<BookingOptionRequestListObj>[] = [
				{
					iconKey: 'share',
					iconClickAction: (row: BookingOptionRequestListObj) => this.shareBookingOption(row),
					showCondition: (row: BookingOptionRequestListObj) => row && hasForwarderRole,
				},
				{
					iconKey: 'add',
					iconClickAction: (row: BookingOptionRequestListObj) => this.createBookingOptions(row),
					showCondition: (row: BookingOptionRequestListObj) => !BOOKING_FINAL_STATUS.includes(row.latestStatus) && hasCarrierRole,
				},
			];

			this.columns = [
				...this.columns,
				{
					key: 'actions',
					header: 'common.table.action',
					actions: actions,
				},
			];
		});
	}

	shareBookingOption(row: BookingOptionRequestListObj) {
		const dialogRef = this.dialog.open(BookingOptionAirlineComponent, {
			width: '30vw',
			autoFocus: false,
			data: { id: row.bookingOptionRequestId, fromBooking: false },
		});
		dialogRef.afterClosed().subscribe((shareSucc) => {
			if (shareSucc) {
				this.refreshList();
			}
		});
	}

	onSearch() {
		if (this.bookingOptionSearchForm.value.productDescription) {
			this.param = { ...this.param, keyword: this.bookingOptionSearchForm.value.productDescription };
		} else {
			this.param = {};
		}
	}

	createBookingOptionRequest(row?: BookingOptionRequestListObj) {
		const dialogRef = this.dialog.open(BookingOptionRequestCreateComponent, {
			width: '70vw',
			autoFocus: false,
			data: row,
		});
		dialogRef.afterClosed().subscribe((res) => {
			if (res === true) {
				this.refreshList();
			}
		});
	}

	refreshList() {
		this.param = { ...this.param };
		this.cdr.markForCheck();
	}

	createBookingOptions(row: BookingOptionRequestListObj) {
		this.router.navigate(['/quote/option'], {
			state: { id: row.bookingOptionRequestId },
		});
	}

	viewOptionDetail(row: BookingOptionRequestListObj) {
		const dialogRef = this.dialog.open(BookingOptionDetailComponent, {
			width: '95vw',
			autoFocus: false,
			data: row,
		});
		dialogRef.afterClosed().subscribe((res) => {
			if (res === true) {
				this.param = { ...this.param };
			}
		});
	}

	viewEventHsitory(row: BookingOptionRequestListObj) {
		this.dialog.open(StatusHistoryComponent, {
			width: '60vw',
			autoFocus: false,
			data: { loId: row.bookingOptionRequestId, type: 'piece' },
		});
	}
}
