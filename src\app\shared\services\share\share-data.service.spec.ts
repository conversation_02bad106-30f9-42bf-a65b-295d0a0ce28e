import { TestBed } from '@angular/core/testing';
import { ShareDataService } from './share-data.service';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { ResponseObj } from '@shared/models/response.model';
import { ShareType } from '@shared/models/share-type.model';
import { environment } from '@environments/environment';
const baseUrl = environment.baseApi;
describe('ShareServiceService', () => {
	let httpMock: HttpTestingController;

	let service: ShareDataService;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [
				ShareDataService,
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		});
		service = TestBed.inject(ShareDataService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should share sli', () => {
		const mockResponse: ResponseObj<string> = {
			data: 'success',
			code: 200,
			msg: 'Success',
		};

		service.shareData(ShareType.SLI, 'SLIID', ['12', '3']).subscribe((res) => {
			expect(res).toEqual('success');
		});

		const req = httpMock.expectOne(`${baseUrl}/sli/share`);
		expect(req.request.method).toBe('POST');
		req.flush(mockResponse);
	});

	it('should share mawb', () => {
		const mockResponse: ResponseObj<string> = {
			data: 'success',
			code: 200,
			msg: 'Success',
		};

		service.shareData(ShareType.MAWB, 'mawbID', ['12', '3']).subscribe((res) => {
			expect(res).toEqual('success');
		});

		const req = httpMock.expectOne(`${baseUrl}/mawb-management/share`);
		expect(req.request.method).toBe('POST');
		req.flush(mockResponse);
	});
});
