import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { EcsdService } from '../../service/ecsd.service';
import { OrllTableComponent } from '@shared/components/orll-table/orll-table.component';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { EcsdObj } from '../../models/ecsd.model';
import { OrllColumnDef } from '@shared/models/orlll-common-table';
import { EcsdCreateComponent } from '../../components/ecsd-create/ecsd-create.component';
import { Modules, UserRole } from '@shared/models/user-role.model';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { EcsdDetailComponent } from '../../components/ecsd-detail/ecsd-detail.component';
import { CommonService } from '@shared/services/common.service';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { UserProfile } from '@shared/models/user-profile.model';
import { AsyncPipe } from '@angular/common';

@Component({
	selector: 'orll-ecsd-list',
	imports: [OrllTableComponent, TranslateModule, MatIconModule, MatButtonModule, MatDialogModule, SpinnerComponent, AsyncPipe],
	templateUrl: './ecsd-list.component.html',
	styleUrl: './ecsd-list.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EcsdListComponent extends RolesAwareComponent implements OnInit {
	@Input() loId!: string;
	@Input() loType!: Modules;

	escdParam = {};
	dataLoading = false;

	currentUser: UserProfile | null = null;
	moduleName = Modules;
	readonly shipper = [UserRole.SHIPPER];
	readonly forwarder = [UserRole.FORWARDER];

	columns: OrllColumnDef<EcsdObj>[] = [
		{
			key: 'securityStatus',
			header: 'ecsd.table.status',
			clickCell: (row: EcsdObj) => this.viewEcsdDetail(row),
		},
		{
			key: 'screeningMethod',
			header: 'ecsd.table.method',
		},
		{
			key: 'groundsForExemption',
			header: 'ecsd.table.ground',
		},
		{
			key: 'receivedFrom',
			header: 'ecsd.table.from',
		},
		{
			key: 'issuedBy',
			header: 'ecsd.table.issue.by',
		},
		{
			key: 'issuedOn',
			header: 'ecsd.table.issue.on',
		},
		{
			key: 'actions',
			header: 'common.table.action',
			actions: [
				{
					iconKey: 'edit',
					iconClickAction: (row: EcsdObj) => this.createOrEditEcsd(row),
					showCondition: (row: EcsdObj) => this.canOperate(row),
				},
				{
					iconKey: 'delete',
					iconClickAction: (row: EcsdObj) => this.deleteEcsd(row),
					showCondition: (row: EcsdObj) => this.canOperate(row),
				},
			],
		},
	];

	constructor(
		readonly escdService: EcsdService,
		private readonly dialog: MatDialog,
		private readonly cdr: ChangeDetectorRef,
		private readonly commonService: CommonService
	) {
		super();
	}

	ngOnInit(): void {
		this.getCurrentUser().subscribe((res) => {
			this.currentUser = res;
			this.escdParam = { loId: this.loId ?? '', loType: this.loType ?? '' };
		});
	}

	createOrEditEcsd(row?: EcsdObj) {
		const dialogRef = this.dialog.open(EcsdCreateComponent, {
			width: '80vw',
			autoFocus: false,
			data: { loId: this.loId, loType: this.loType, ecsdObj: row },
		});
		dialogRef.afterClosed().subscribe((res) => {
			if (res === true) {
				this.refreshList();
			}
		});
	}

	refreshList() {
		this.escdParam = { ...this.escdParam };
		this.cdr.markForCheck();
	}

	viewEcsdDetail(row: EcsdObj) {
		this.dialog.open(EcsdDetailComponent, {
			width: '80vw',
			autoFocus: false,
			data: { loId: this.loId, loType: this.loType, ecsdObj: row },
		});
	}

	deleteEcsd(row: EcsdObj) {
		const dialogRef = this.commonService.showDeleteConfirm();

		dialogRef.afterClosed().subscribe((res) => {
			if (res === true && row.id) {
				this.dataLoading = true;
				this.escdService.deleteEcsd(row.id).subscribe({
					next: () => {
						this.dataLoading = false;
						this.refreshList();
					},
					error: () => {
						this.dataLoading = false;
						this.cdr.markForCheck();
					},
				});
			}
		});
	}

	canOperate(row: EcsdObj) {
		const rowIdDomain = row.id?.split('/logistics-objects/')[0];
		const orgDomain = this.currentUser?.selectOrg?.orgIri?.split('/logistics-objects/')[0];

		return rowIdDomain === orgDomain;
	}
}
