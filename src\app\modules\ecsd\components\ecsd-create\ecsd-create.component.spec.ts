import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EcsdCreateComponent } from './ecsd-create.component';
import { TranslateModule } from '@ngx-translate/core';
import { EcsdService } from '../../service/ecsd.service';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { EcsdDialogObj, EcsdObj, PieceListObj, HawbListObj } from '../../models/ecsd.model';
import { Modules } from '@shared/models/user-role.model';
import { UserProfileService } from '@shared/services/user-profile.service';
import { CommonService } from '@shared/services/common.service';
import { of, throwError } from 'rxjs';
import { DateTime } from 'luxon';
import { CodeName } from '@shared/models/code-name.model';

const mockListObj: EcsdDialogObj = {
	loId: '111',
	loType: Modules.SLI,
};

const mockListObjWithEcsd: EcsdDialogObj = {
	loId: '111',
	loType: Modules.SLI,
	ecsdObj: {
		id: '123',
		securityStatus: 'SPX',
		receivedFrom: 'Test Shipper',
		screeningMethod: 'X-RAY',
		groundsForExemption: '',
		issuedBy: 'Test User',
		employeeId: 'EMP001',
		issuedOn: '01/15/2024 10:30:00',
		additionalSecurityInfo: 'Additional info',
		loId: '111',
		loType: 'SLI',
		regulatedEntityCategory1: 'CAT1',
		regulatedEntityIdentifier1: 'ID1',
		regulatedEntityCategory: 'CAT2',
		regulatedEntityIdentifier: 'ID2',
		createdBy: 'testuser',
	},
};

const mockEcsdDetail: EcsdObj = {
	id: '123',
	securityStatus: 'SPX',
	receivedFrom: 'Test Shipper',
	screeningMethod: 'X-RAY',
	groundsForExemption: '',
	issuedBy: 'Test User',
	employeeId: 'EMP001',
	issuedOn: '01/15/2024 10:30:00',
	additionalSecurityInfo: 'Additional info',
	loId: '111',
	loType: 'SLI',
	regulatedEntityCategory1: 'CAT1',
	regulatedEntityIdentifier1: 'ID1',
	regulatedEntityCategory: 'CAT2',
	regulatedEntityIdentifier: 'ID2',
	createdBy: 'testuser',
	pieceList: [
		{
			id: '1',
			pieceId: 'P001',
			productDescription: 'Electronics',
			packageType: 'BOX',
			grossWeight: '10.5',
			pieceQuantity: 2,
			dimensions: { length: '10', height: '5', width: '8' },
		},
	],
};

const mockPieceList: PieceListObj[] = [
	{
		id: '1',
		pieceId: 'P001',
		productDescription: 'Electronics',
		packageType: '',
		grossWeight: '10.5',
		pieceQuantity: 2,
		dimensions: { length: '10', height: '5', width: '8' },
	},
	{
		id: '2',
		pieceId: 'P002',
		productDescription: 'Clothing',
		packageType: '',
		grossWeight: '5.2',
		pieceQuantity: 1,
		dimensions: { length: '15', height: '3', width: '12' },
	},
];

const mockHawbList: HawbListObj[] = [
	{
		id: '1',
		hawbId: 'H001',
		waybillNumber: 'WB001',
		shipper: 'Test Shipper',
		consignee: 'Test Consignee',
		goodsDescription: 'General Cargo',
		origin: 'NYC',
		destination: 'LAX',
		weight: '25.5',
		slac: 1,
	},
];

const mockCodeNames: CodeName[] = [
	{ code: 'SPX', name: 'Secure' },
	{ code: 'X-RAY', name: 'X-Ray Screening' },
	{ code: 'CAT1', name: 'Category 1' },
];

describe('EcsdCreateComponent', () => {
	let component: EcsdCreateComponent;
	let fixture: ComponentFixture<EcsdCreateComponent>;
	let mockDialog: jasmine.SpyObj<MatDialog>;
	let mockEcsdService: jasmine.SpyObj<EcsdService>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<EcsdCreateComponent>>;
	let mockProfileService: jasmine.SpyObj<UserProfileService>;
	let mockCommonService: jasmine.SpyObj<CommonService>;

	beforeEach(async () => {
		mockEcsdService = jasmine.createSpyObj('EcsdService', ['getEcsd', 'getCodeByType', 'getPiecesAndHawb', 'saveEcsd']);
		mockEcsdService.getCodeByType.and.returnValue(of(mockCodeNames));
		mockEcsdService.getEcsd.and.returnValue(of(mockEcsdDetail));
		mockEcsdService.getPiecesAndHawb.and.returnValue(of({ pieceList: mockPieceList, hawbList: mockHawbList }));
		mockEcsdService.saveEcsd.and.returnValue(of(true));

		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		mockDialogRef = jasmine.createSpyObj<MatDialogRef<EcsdCreateComponent>>('MatDialogRef', ['close']);
		mockProfileService = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		mockProfileService.hasPermission.and.returnValue(of(true));
		mockProfileService.hasSomeRole.and.returnValue(of(true));

		mockCommonService = jasmine.createSpyObj('CommonService', ['showFormInvalid']);

		await TestBed.configureTestingModule({
			imports: [EcsdCreateComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: EcsdService, useValue: mockEcsdService },
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: MAT_DIALOG_DATA, useValue: mockListObj },
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: UserProfileService, useValue: mockProfileService },
				{ provide: CommonService, useValue: mockCommonService },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(EcsdCreateComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should initialize code lists on component init', () => {
			expect(mockEcsdService.getCodeByType).toHaveBeenCalledTimes(5);
			expect(component.screenExemptionList).toEqual(mockCodeNames);
			expect(component.screenMethodList).toEqual(mockCodeNames);
			expect(component.securityStatusList).toEqual(mockCodeNames);
			expect(component.categoryList).toEqual(mockCodeNames);
			expect(component.packageTypes).toEqual(mockCodeNames);
		});

		it('should load pieces and hawb data when no ecsdObj exists', () => {
			expect(mockEcsdService.getPiecesAndHawb).toHaveBeenCalledWith({
				loId: '111',
				loType: 'sli',
			});
			// Should load piece data when available
			expect(component.dataSource.data).toEqual(mockPieceList);
		});

		it('should load existing ECSD data when ecsdObj exists', async () => {
			// Create a new test bed configuration for this specific test
			await TestBed.resetTestingModule();
			await TestBed.configureTestingModule({
				imports: [EcsdCreateComponent, TranslateModule.forRoot()],
				providers: [
					{ provide: EcsdService, useValue: mockEcsdService },
					{ provide: MatDialog, useValue: mockDialog },
					{ provide: MAT_DIALOG_DATA, useValue: mockListObjWithEcsd },
					{ provide: MatDialogRef, useValue: mockDialogRef },
					{ provide: UserProfileService, useValue: mockProfileService },
					{ provide: CommonService, useValue: mockCommonService },
				],
			}).compileComponents();

			const newFixture = TestBed.createComponent(EcsdCreateComponent);
			const newComponent = newFixture.componentInstance;
			newFixture.detectChanges();

			expect(mockEcsdService.getEcsd).toHaveBeenCalledWith(mockListObjWithEcsd.ecsdObj!);
			expect(newComponent.ecsdForm.get('securityStatus')?.value).toBe('SPX');
			expect(newComponent.ecsdForm.get('issuedBy')?.value).toBe('Test User');
		});

		it('should handle error when loading data', () => {
			mockEcsdService.getPiecesAndHawb.and.returnValue(throwError(() => new Error('API Error')));

			component.ngOnInit();

			expect(component.dataLoading).toBeFalse();
		});
	});

	describe('isAllSelected', () => {
		it('should return false when no rows in dataSource', () => {
			component.dataSource.data = [];
			expect(component.isAllSelected()).toBeFalse();
		});

		it('should return true when all rows are selected', () => {
			component.dataSource.data = mockPieceList;
			component.selection.clear();
			component.selection.select(...mockPieceList);
			expect(component.isAllSelected()).toBeTrue();
		});

		it('should return false when some rows are selected', () => {
			component.dataSource.data = mockPieceList;
			component.selection.clear();
			component.selection.select(mockPieceList[0]);
			expect(component.isAllSelected()).toBeFalse();
		});
	});

	describe('toggleAllRows', () => {
		beforeEach(() => {
			component.dataSource.data = mockPieceList;
		});

		it('should select all rows when none are selected', () => {
			component.selection.clear();
			component.toggleAllRows();
			expect(component.selection.selected.length).toBe(mockPieceList.length);
		});

		it('should clear selection when all rows are selected', () => {
			component.dataSource.data = mockPieceList;
			component.selection.clear();
			component.selection.select(...mockPieceList);
			component.toggleAllRows();
			expect(component.selection.selected.length).toBe(0);
		});
	});

	describe('trackByPieceId', () => {
		it('should return pieceId for tracking', () => {
			const piece = mockPieceList[0];
			expect(component.trackByPieceId(piece)).toBe(piece.pieceId);
		});
	});

	describe('trackByHawbId', () => {
		it('should return hawbId for tracking', () => {
			const hawb = mockHawbList[0];
			expect(component.trackByHawbId(hawb)).toBe(hawb.hawbId);
		});
	});

	describe('getFormData', () => {
		beforeEach(() => {
			component.ecsdForm.patchValue({
				securityStatus: 'SPX',
				receivedFrom: 'Test Shipper',
				screeningMethod: 'X-RAY',
				issuedBy: 'Test User',
				employeeId: 'EMP001',
				issuedOn: DateTime.fromFormat('01/15/2024 10:30:00', 'MM/dd/yyyy HH:mm:ss'),
				additionalSecurityInfo: 'Additional info',
				regulatedEntityCategory: 'CAT1',
				regulatedEntityIdentifier: 'ID1',
				regulatedEntityCategory1: 'CAT2',
				regulatedEntityIdentifier1: 'ID2',
			});
		});

		it('should return form data as EcsdObj', () => {
			const result = component.getFormData();

			expect(result.securityStatus).toBe('SPX');
			expect(result.receivedFrom).toBe('Test Shipper');
			expect(result.screeningMethod).toBe('X-RAY');
			expect(result.issuedBy).toBe('Test User');
			expect(result.employeeId).toBe('EMP001');
			expect(result.loId).toBe('111');
			expect(result.loType).toBe(Modules.SLI);
		});

		it('should include existing ECSD id and createdBy when editing', () => {
			component.data = mockListObjWithEcsd;
			const result = component.getFormData();

			expect(result.id).toBe('123');
			expect(result.createdBy).toBe('testuser');
		});

		it('should handle null date value', () => {
			component.ecsdForm.patchValue({ issuedOn: null });
			const result = component.getFormData();

			expect(result.issuedOn).toBe('');
		});
	});

	describe('saveEcsd', () => {
		beforeEach(() => {
			// Set up valid form data
			component.ecsdForm.patchValue({
				securityStatus: 'SPX',
				issuedBy: 'Test User',
				issuedOn: DateTime.fromFormat('01/15/2024 10:30:00', 'MM/dd/yyyy HH:mm:ss'),
				regulatedEntityCategory: 'CAT1',
				regulatedEntityIdentifier: 'ID1',
				regulatedEntityCategory1: 'CAT2',
				regulatedEntityIdentifier1: 'ID2',
				whetherExemptedForScreening: 'no',
				screeningMethod: 'X-RAY',
			});
		});

		it('should save ECSD with piece list for SLI module', () => {
			// Component is already set up with SLI loType
			component.dataSource.data = mockPieceList;
			component.selection.clear();
			component.selection.select(...mockPieceList);

			component.saveEcsd();

			expect(mockEcsdService.saveEcsd).toHaveBeenCalledWith(
				jasmine.objectContaining({
					securityStatus: 'SPX',
					pieceList: jasmine.any(Array),
				})
			);
			expect(mockDialogRef.close).toHaveBeenCalledWith(true);
		});

		it('should save ECSD with piece list for SLI module', () => {
			component.data.loType = Modules.SLI;
			component.dataSource.data = mockPieceList;
			component.selection.clear();
			component.selection.select(...mockPieceList);

			component.saveEcsd();

			expect(mockEcsdService.saveEcsd).toHaveBeenCalledWith(
				jasmine.objectContaining({
					securityStatus: 'SPX',
					pieceList: jasmine.any(Array),
				})
			);
		});

		it('should show form invalid message when form is invalid', () => {
			component.ecsdForm.patchValue({ securityStatus: '' }); // Make form invalid

			component.saveEcsd();

			expect(mockCommonService.showFormInvalid).toHaveBeenCalled();
			expect(mockEcsdService.saveEcsd).not.toHaveBeenCalled();
		});

		it('should handle save error', () => {
			mockEcsdService.saveEcsd.and.returnValue(throwError(() => new Error('Save failed')));

			component.saveEcsd();

			expect(component.dataLoading).toBeFalse();
			expect(mockDialogRef.close).toHaveBeenCalledWith(false);
		});
	});

	describe('Form Validation', () => {
		it('should have required validators on required fields', () => {
			const securityStatusControl = component.ecsdForm.get('securityStatus');
			const issuedByControl = component.ecsdForm.get('issuedBy');
			const issuedOnControl = component.ecsdForm.get('issuedOn');
			const regulatedEntityCategoryControl = component.ecsdForm.get('regulatedEntityCategory');
			const regulatedEntityIdentifierControl = component.ecsdForm.get('regulatedEntityIdentifier');
			const regulatedEntityCategory1Control = component.ecsdForm.get('regulatedEntityCategory1');
			const regulatedEntityIdentifier1Control = component.ecsdForm.get('regulatedEntityIdentifier1');

			expect(securityStatusControl?.hasError('required')).toBeTrue();
			expect(issuedByControl?.hasError('required')).toBeTrue();
			expect(issuedOnControl?.hasError('required')).toBeTrue();
			expect(regulatedEntityCategoryControl?.hasError('required')).toBeTrue();
			expect(regulatedEntityIdentifierControl?.hasError('required')).toBeTrue();
			expect(regulatedEntityCategory1Control?.hasError('required')).toBeTrue();
			expect(regulatedEntityIdentifier1Control?.hasError('required')).toBeTrue();
		});

		it('should validate form correctly when all required fields are filled', () => {
			component.ecsdForm.patchValue({
				securityStatus: 'SPX',
				issuedBy: 'Test User',
				issuedOn: DateTime.now(),
				regulatedEntityCategory: 'CAT1',
				regulatedEntityIdentifier: 'ID1',
				regulatedEntityCategory1: 'CAT2',
				regulatedEntityIdentifier1: 'ID2',
				screeningMethod: 'X-RAY', // Required when not exempted
			});

			expect(component.ecsdForm.valid).toBeTrue();
		});
	});

	describe('Screening Exemption Logic', () => {
		it('should enable screening method and disable grounds for exemption when not exempted', () => {
			component.ecsdForm.get('whetherExemptedForScreening')?.setValue('no');

			const screeningMethodControl = component.ecsdForm.get('screeningMethod');
			const groundsForExemptionControl = component.ecsdForm.get('groundsForExemption');

			expect(screeningMethodControl?.enabled).toBeTrue();
			expect(screeningMethodControl?.hasError('required')).toBeTrue();
			expect(groundsForExemptionControl?.disabled).toBeTrue();
			expect(groundsForExemptionControl?.value).toBe('');
		});

		it('should enable grounds for exemption and disable screening method when exempted', () => {
			component.ecsdForm.get('whetherExemptedForScreening')?.setValue('yes');

			const screeningMethodControl = component.ecsdForm.get('screeningMethod');
			const groundsForExemptionControl = component.ecsdForm.get('groundsForExemption');

			expect(groundsForExemptionControl?.enabled).toBeTrue();
			expect(groundsForExemptionControl?.hasError('required')).toBeTrue();
			expect(screeningMethodControl?.disabled).toBeTrue();
			expect(screeningMethodControl?.value).toBe('');
		});
	});

	describe('Data Loading States', () => {
		it('should set dataLoading to false after successful data load', () => {
			expect(component.dataLoading).toBeFalse();
		});

		it('should handle loading state during save operation', () => {
			// Set up valid form
			component.ecsdForm.patchValue({
				securityStatus: 'SPX',
				issuedBy: 'Test User',
				issuedOn: DateTime.now(),
				regulatedEntityCategory: 'CAT1',
				regulatedEntityIdentifier: 'ID1',
				regulatedEntityCategory1: 'CAT2',
				regulatedEntityIdentifier1: 'ID2',
				screeningMethod: 'X-RAY',
			});

			component.saveEcsd();

			expect(mockEcsdService.saveEcsd).toHaveBeenCalled();
		});
	});

	describe('Selection Model', () => {
		it('should initialize selection model with correct configuration', () => {
			expect(component.selection.isMultipleSelection()).toBeTrue();
			expect(component.selection.selected.length).toBeGreaterThan(0); // Should have initial selection
		});

		it('should handle selection correctly', () => {
			const item1 = mockPieceList[0];
			const item2 = mockPieceList[1];

			component.selection.clear();
			component.selection.select(item1);

			expect(component.selection.isSelected(item1)).toBeTrue();
			expect(component.selection.isSelected(item2)).toBeFalse();
		});
	});

	describe('Display Columns', () => {
		it('should have correct piece display columns', () => {
			const expectedColumns = ['select', 'productDescription', 'packageType', 'grossWeight', 'pieceQuantity', 'dimensions'];
			expect(component.pieceDisplayedColumns).toEqual(expectedColumns);
		});

		it('should have correct hawb display columns', () => {
			const expectedColumns = [
				'select',
				'waybillNumber',
				'shipper',
				'consignee',
				'goodsDescription',
				'origin',
				'destination',
				'weight',
				'slac',
			];
			expect(component.hawbDisplayedColumns).toEqual(expectedColumns);
		});
	});
});
