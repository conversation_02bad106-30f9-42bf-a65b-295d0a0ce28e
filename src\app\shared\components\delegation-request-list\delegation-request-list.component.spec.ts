import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { DestroyRef, ChangeDetectorRef } from '@angular/core';
import { of, throwError } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';
import { DelegationRequestListComponent } from './delegation-request-list.component';
import { DelegationRequestService } from '../../services/delegation/delegation-request.service';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { DelegationRequest } from '@shared/models/delegation-request';

describe('DelegationRequestListComponent', () => {
	let component: DelegationRequestListComponent;
	let fixture: ComponentFixture<DelegationRequestListComponent>;
	let mockDelegationRequestService: jasmine.SpyObj<DelegationRequestService>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;
	let mockDestroyRef: jasmine.SpyObj<DestroyRef>;

	// Mock test data
	const mockDelegationRequestListResponse: PaginationResponse<DelegationRequest> = {
		rows: [
			{
				isRequestedFor: ['123'],
				isRequestedBy: 'abc',
				isRequestedAt: '2023-01-01',
				requestStatus: 'pending',
				hasDescription: '123456',
				hasPermission: ['234'],
				hasLogisticsObject: ['345'],
			},
			{
				isRequestedFor: ['123456'],
				isRequestedBy: 'abcd',
				isRequestedAt: '2024-01-01',
				requestStatus: 'approved',
				hasDescription: '1234567',
				hasPermission: ['2345'],
				hasLogisticsObject: ['3456'],
			},
		],
		total: 2,
	};

	const mockEmptyDelegationRequestListResponse: PaginationResponse<DelegationRequest> = {
		rows: [],
		total: 0,
	};

	beforeEach(async () => {
		// Create spies for dependencies
		mockDelegationRequestService = jasmine.createSpyObj<DelegationRequestService>('DelegationRequestService', ['getDelegationList']);
		mockChangeDetectorRef = jasmine.createSpyObj<ChangeDetectorRef>('ChangeDetectorRef', ['markForCheck']);
		mockDestroyRef = jasmine.createSpyObj<DestroyRef>('DestroyRef', ['onDestroy']);

		// Configure default mock return values
		mockDelegationRequestService.getDelegationList.and.returnValue(of(mockDelegationRequestListResponse));

		await TestBed.configureTestingModule({
			imports: [DelegationRequestListComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			providers: [
				provideHttpClient(),
				provideHttpClientTesting(),
				{ provide: DelegationRequestService, useValue: mockDelegationRequestService },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
				{ provide: DestroyRef, useValue: mockDestroyRef },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(DelegationRequestListComponent);
		component = fixture.componentInstance;
	});

	describe('Component Initialization', () => {
		it('should create the component', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with default values', () => {
			expect(component.delegationList).toEqual([]);
			expect(component.pageParams).toEqual({
				pageNum: 1,
				pageSize: 10,
			});
			expect(component.totalRecords).toBe(0);
			expect(component.dataLoading).toBe(false);
			expect(component.hasMoreData).toBe(true);
			expect(component.loId).toBe('');
		});

		it('should call refreshData on ngOnInit', () => {
			spyOn(component, 'refreshData');

			component.ngOnInit();

			expect(component.refreshData).toHaveBeenCalled();
		});
	});

	describe('refreshData Method', () => {
		it('should fetch piece list when loId is provided', () => {
			component.loId = 'LO123';
			spyOn(component as any, 'getDelegationListPage');

			component.refreshData();

			expect(component['getDelegationListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should not fetch piece list when loId is empty', () => {
			component.loId = '';
			spyOn(component as any, 'getDelegationListPage');

			component.refreshData();

			expect(component['getDelegationListPage']).not.toHaveBeenCalled();
		});

		it('should not fetch piece list when loId is null', () => {
			component.loId = null as any;
			spyOn(component as any, 'getDelegationListPage');

			component.refreshData();

			expect(component['getDelegationListPage']).not.toHaveBeenCalled();
		});

		it('should not fetch piece list when loId is undefined', () => {
			component.loId = undefined as any;
			spyOn(component as any, 'getDelegationListPage');

			component.refreshData();

			expect(component['getDelegationListPage']).not.toHaveBeenCalled();
		});
	});

	describe('onSortChange Method', () => {
		it('should clear sort parameters when direction is empty', () => {
			const sort: Sort = { active: 'productDescription', direction: '' };
			component.pageParams.orderByColumn = 'previousColumn';
			component.pageParams.isAsc = 'asc';

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});

		it('should set sort parameters when direction is asc', () => {
			const sort: Sort = { active: 'productDescription', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should set sort parameters when direction is desc', () => {
			const sort: Sort = { active: 'grossWeight', direction: 'desc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('grossWeight');
			expect(component.pageParams.isAsc).toBe('desc');
		});

		it('should handle different column names', () => {
			const sort: Sort = { active: 'pieceQuantity', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('pieceQuantity');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should reset pageNum and pageSize when sorting', () => {
			const sort: Sort = { active: 'productDescription', direction: 'asc' };
			component.pageParams.pageNum = 5;
			component.pageParams.pageSize = 25;

			component.onSortChange(sort);

			expect(component.pageParams.pageNum).toBe(1);
			expect(component.pageParams.pageSize).toBe(10);
			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should not reset pageNum and pageSize when direction is empty', () => {
			const sort: Sort = { active: 'productDescription', direction: '' };
			component.pageParams.pageNum = 5;
			component.pageParams.pageSize = 25;

			component.onSortChange(sort);

			expect(component.pageParams.pageNum).toBe(5);
			expect(component.pageParams.pageSize).toBe(25);
			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});
	});

	describe('loadMoreData Method', () => {
		it('should increment pageNum and call getDelegationListPage with appendData true when hasMoreData is true and not loading', () => {
			component.hasMoreData = true;
			component.dataLoading = false;
			component.pageParams.pageNum = 2;
			spyOn(component as any, 'getDelegationListPage');

			component.loadMoreData();

			expect(component.pageParams.pageNum).toBe(3);
			expect(component['getDelegationListPage']).toHaveBeenCalledWith(component.pageParams, true);
		});

		it('should not load more data when hasMoreData is false', () => {
			component.hasMoreData = false;
			component.dataLoading = false;
			component.pageParams.pageNum = 2;
			spyOn(component as any, 'getDelegationListPage');

			component.loadMoreData();

			expect(component.pageParams.pageNum).toBe(2);
			expect(component['getDelegationListPage']).not.toHaveBeenCalled();
		});

		it('should not load more data when dataLoading is true', () => {
			component.hasMoreData = true;
			component.dataLoading = true;
			component.pageParams.pageNum = 2;
			spyOn(component as any, 'getDelegationListPage');

			component.loadMoreData();

			expect(component.pageParams.pageNum).toBe(2);
			expect(component['getDelegationListPage']).not.toHaveBeenCalled();
		});

		it('should not load more data when both hasMoreData is false and dataLoading is true', () => {
			component.hasMoreData = false;
			component.dataLoading = true;
			component.pageParams.pageNum = 2;
			spyOn(component as any, 'getDelegationListPage');

			component.loadMoreData();

			expect(component.pageParams.pageNum).toBe(2);
			expect(component['getDelegationListPage']).not.toHaveBeenCalled();
		});

		it('should handle multiple consecutive calls correctly', () => {
			component.hasMoreData = true;
			component.dataLoading = false;
			component.pageParams.pageNum = 1;
			spyOn(component as any, 'getDelegationListPage');

			component.loadMoreData();
			component.loadMoreData();

			expect(component.pageParams.pageNum).toBe(3);
			expect(component['getDelegationListPage']).toHaveBeenCalledTimes(2);
		});
	});

	describe('onPageChange Method', () => {
		it('should update pageParams and call getDelegationListPage', () => {
			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 20,
				length: 100,
			};
			spyOn(component as any, 'getDelegationListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(3); // pageIndex + 1
			expect(component.pageParams.pageSize).toBe(20);
			expect(component['getDelegationListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should update sort parameters when provided in pageEvent', () => {
			const pageEvent: PageEvent & { sortField?: string; sortDirection?: string } = {
				pageIndex: 1,
				pageSize: 15,
				length: 50,
				sortField: 'grossWeight',
				sortDirection: 'desc',
			};
			spyOn(component as any, 'getDelegationListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(2);
			expect(component.pageParams.pageSize).toBe(15);
			expect(component.pageParams.orderByColumn).toBe('grossWeight');
			expect(component.pageParams.isAsc).toBe('desc');
			expect(component['getDelegationListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should not update sort parameters when not provided in pageEvent', () => {
			const pageEvent: PageEvent = {
				pageIndex: 0,
				pageSize: 10,
				length: 20,
			};
			component.pageParams.orderByColumn = 'existingColumn';
			component.pageParams.isAsc = 'asc';
			spyOn(component as any, 'getDelegationListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(1);
			expect(component.pageParams.pageSize).toBe(10);
			expect(component.pageParams.orderByColumn).toBe('existingColumn');
			expect(component.pageParams.isAsc).toBe('asc');
		});
	});

	describe('getDelegationListPage Method', () => {
		it('should fetch piece list data successfully with appendData false (default)', fakeAsync(() => {
			component.loId = 'LO123';
			const pageParams: PaginationRequest = { pageNum: 1, pageSize: 10 };

			component['getDelegationListPage'](pageParams);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(component.delegationList).toEqual(mockDelegationRequestListResponse.rows);
			expect(component.totalRecords).toBe(mockDelegationRequestListResponse.total);
			expect(mockDelegationRequestService.getDelegationList).toHaveBeenCalledWith(pageParams, 'LO123');
		}));

		it('should append data when appendData is true', fakeAsync(() => {
			component.loId = 'LO123';
			component.delegationList = [mockDelegationRequestListResponse.rows[0]]; // Existing data
			component.hasMoreData = true;
			const pageParams: PaginationRequest = { pageNum: 2, pageSize: 10 };

			component['getDelegationListPage'](pageParams, true);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(component.delegationList).toEqual([
				mockDelegationRequestListResponse.rows[0], // Existing data
				...mockDelegationRequestListResponse.rows, // New data appended
			]);
			expect(component.totalRecords).toBe(mockDelegationRequestListResponse.total);
			expect(component.hasMoreData).toBe(false); // Should be false since delegationList.length (3) >= total (2)
		}));

		it('should replace data when appendData is false', fakeAsync(() => {
			component.loId = 'LO123';
			component.delegationList = [mockDelegationRequestListResponse.rows[0]]; // Existing data
			const pageParams: PaginationRequest = { pageNum: 1, pageSize: 10 };

			component['getDelegationListPage'](pageParams, false);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(component.delegationList).toEqual(mockDelegationRequestListResponse.rows); // Data replaced, not appended
			expect(component.totalRecords).toBe(mockDelegationRequestListResponse.total);
		}));

		it('should set hasMoreData correctly when appending data', fakeAsync(() => {
			component.loId = 'LO123';
			component.delegationList = []; // Start with empty list
			const largeResponse = {
				rows: [mockDelegationRequestListResponse.rows[0]],
				total: 10, // More total than current data
			};
			mockDelegationRequestService.getDelegationList.and.returnValue(of(largeResponse));

			component['getDelegationListPage'](component.pageParams, true);
			tick();

			expect(component.hasMoreData).toBe(true); // Should be true since delegationList.length (1) < total (10)
			expect(component.delegationList.length).toBe(1);
			expect(component.totalRecords).toBe(10);
		}));

		it('should set dataLoading to true and clear delegationList at start, then complete successfully', fakeAsync(() => {
			component.loId = 'LO123';
			component.dataLoading = false;
			component.delegationList = [mockDelegationRequestListResponse.rows[0]];

			// Call the method and complete immediately
			component['getDelegationListPage'](component.pageParams);
			tick();

			// Since the observable completes synchronously, check final state
			expect(component.dataLoading).toBe(false);
			expect(component.delegationList).toEqual(mockDelegationRequestListResponse.rows);
			expect(component.totalRecords).toBe(mockDelegationRequestListResponse.total);
		}));

		it('should handle empty response', fakeAsync(() => {
			component.loId = 'LO123';
			mockDelegationRequestService.getDelegationList.and.returnValue(of(mockEmptyDelegationRequestListResponse));

			component['getDelegationListPage'](component.pageParams);
			tick();

			expect(component.delegationList).toEqual([]);
			expect(component.totalRecords).toBe(0);
			expect(component.dataLoading).toBe(false);
		}));

		it('should handle empty response when appending data', fakeAsync(() => {
			component.loId = 'LO123';
			component.delegationList = [mockDelegationRequestListResponse.rows[0]]; // Existing data
			mockDelegationRequestService.getDelegationList.and.returnValue(of(mockEmptyDelegationRequestListResponse));

			component['getDelegationListPage'](component.pageParams, true);
			tick();

			expect(component.delegationList).toEqual([mockDelegationRequestListResponse.rows[0]]); // Existing data preserved
			expect(component.totalRecords).toBe(0);
			expect(component.dataLoading).toBe(false);
			expect(component.hasMoreData).toBe(false);
		}));

		it('should handle service error', fakeAsync(() => {
			component.loId = 'LO123';
			component.dataLoading = false;
			mockDelegationRequestService.getDelegationList.and.returnValue(throwError(() => new Error('Service error')));

			component['getDelegationListPage'](component.pageParams);
			tick();

			expect(component.dataLoading).toBe(false);
		}));

		it('should call service with correct parameters', () => {
			component.loId = 'LO456';
			const customPageParams: PaginationRequest = {
				pageNum: 3,
				pageSize: 25,
				orderByColumn: 'productDescription',
				isAsc: 'desc',
			};

			component['getDelegationListPage'](customPageParams);

			expect(mockDelegationRequestService.getDelegationList).toHaveBeenCalledWith(customPageParams, 'LO456');
		});
	});

	describe('Input Properties', () => {
		it('should accept loId input', () => {
			const testLoId = 'TEST-LO-001';
			component.loId = testLoId;

			expect(component.loId).toBe(testLoId);
		});
	});

	describe('Component Integration', () => {
		it('should initialize and fetch data when loId is set', () => {
			component.loId = 'LO789';
			spyOn(component as any, 'getDelegationListPage');

			component.ngOnInit();

			expect(component['getDelegationListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should handle complete workflow: sort, paginate, and fetch', fakeAsync(() => {
			component.loId = 'LO999';

			// Test sorting
			const sort: Sort = { active: 'productDescription', direction: 'asc' };
			component.onSortChange(sort);

			// Test pagination
			const pageEvent: PageEvent = { pageIndex: 1, pageSize: 20, length: 100 };
			component.onPageChange(pageEvent);
			tick();

			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBe('asc');
			expect(component.pageParams.pageNum).toBe(2);
			expect(component.pageParams.pageSize).toBe(20);
			expect(mockDelegationRequestService.getDelegationList).toHaveBeenCalledWith(component.pageParams, 'LO999');
		}));

		it('should handle complete workflow: load more data', fakeAsync(() => {
			component.loId = 'LO888';
			component.hasMoreData = true;
			component.dataLoading = false;
			component.pageParams.pageNum = 1;

			component.loadMoreData();
			tick();

			expect(component.pageParams.pageNum).toBe(2);
			expect(mockDelegationRequestService.getDelegationList).toHaveBeenCalledWith(component.pageParams, 'LO888');
		}));

		it('should handle workflow: refresh data after load more', () => {
			component.loId = 'LO777';
			component.pageParams.pageNum = 3; // Simulate after loading more data
			spyOn(component as any, 'getDelegationListPage');

			component.refreshData();

			// refreshData should reset to initial page params and call with appendData false
			expect(component['getDelegationListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should maintain state consistency during multiple operations', () => {
			// Initial state
			expect(component.dataLoading).toBe(false);
			expect(component.delegationList).toEqual([]);
			expect(component.totalRecords).toBe(0);
			expect(component.hasMoreData).toBe(true);

			// After setting inputs
			component.loId = 'LO-MULTI';

			expect(component.loId).toBe('LO-MULTI');
		});

		it('should handle complex scenario: sort, load more, then paginate', fakeAsync(() => {
			component.loId = 'LO-COMPLEX';
			component.hasMoreData = true;
			component.dataLoading = false;

			// First sort
			const sort: Sort = { active: 'grossWeight', direction: 'desc' };
			component.onSortChange(sort);

			expect(component.pageParams.pageNum).toBe(1);
			expect(component.pageParams.pageSize).toBe(10);

			// Then load more data
			component.loadMoreData();
			expect(component.pageParams.pageNum).toBe(2);

			// Then paginate
			const pageEvent: PageEvent = { pageIndex: 2, pageSize: 15, length: 100 };
			component.onPageChange(pageEvent);
			tick();

			expect(component.pageParams.pageNum).toBe(3);
			expect(component.pageParams.pageSize).toBe(15);
			expect(component.pageParams.orderByColumn).toBe('grossWeight');
			expect(component.pageParams.isAsc).toBe('desc');
		}));
	});

	describe('Edge Cases', () => {
		it('should handle undefined sort direction', () => {
			const sort: Sort = { active: 'productDescription', direction: undefined as any };
			// Set initial values
			component.pageParams.orderByColumn = 'initialColumn';
			component.pageParams.isAsc = 'asc';

			component.onSortChange(sort);

			// Undefined direction is not the same as empty string, so values should be set
			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBeUndefined();
		});

		it('should handle zero pageIndex in pagination', () => {
			const pageEvent: PageEvent = { pageIndex: 0, pageSize: 10, length: 50 };
			spyOn(component as any, 'getDelegationListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(1);
		});

		it('should handle large page sizes', () => {
			const pageEvent: PageEvent = { pageIndex: 0, pageSize: 1000, length: 5000 };
			spyOn(component as any, 'getDelegationListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageSize).toBe(1000);
		});

		it('should handle service returning null response', fakeAsync(() => {
			component.loId = 'LO123';
			mockDelegationRequestService.getDelegationList.and.returnValue(of(null as any));

			expect(component.dataLoading).toBe(false);
		}));

		it('should handle loadMoreData when pageNum is at maximum value', () => {
			component.hasMoreData = true;
			component.dataLoading = false;
			component.pageParams.pageNum = Number.MAX_SAFE_INTEGER;
			spyOn(component as any, 'getDelegationListPage');

			component.loadMoreData();

			// Should still increment even if it's a very large number
			expect(component.pageParams.pageNum).toBe(Number.MAX_SAFE_INTEGER + 1);
			expect(component['getDelegationListPage']).toHaveBeenCalledWith(component.pageParams, true);
		});

		it('should handle hasMoreData calculation when delegationList length equals total', fakeAsync(() => {
			component.loId = 'LO123';
			component.delegationList = [mockDelegationRequestListResponse.rows[0]]; // 1 item
			const exactResponse = {
				rows: [mockDelegationRequestListResponse.rows[1]], // 1 more item
				total: 2, // Total is exactly 2
			};
			mockDelegationRequestService.getDelegationList.and.returnValue(of(exactResponse));

			component['getDelegationListPage'](component.pageParams, true);
			tick();

			expect(component.delegationList.length).toBe(2); // 1 existing + 1 new
			expect(component.totalRecords).toBe(2);
			expect(component.hasMoreData).toBe(false); // Should be false since length equals total
		}));

		it('should handle empty loId during getDelegationListPage', () => {
			component.loId = '';
			spyOn(component as any, 'getDelegationListPage').and.callThrough();

			// This should still call the service even with empty loId
			component['getDelegationListPage'](component.pageParams);

			expect(mockDelegationRequestService.getDelegationList).toHaveBeenCalledWith(component.pageParams, '');
		});

		it('should handle service error when appending data', fakeAsync(() => {
			component.loId = 'LO123';
			component.delegationList = [mockDelegationRequestListResponse.rows[0]]; // Existing data
			component.dataLoading = false;
			mockDelegationRequestService.getDelegationList.and.returnValue(throwError(() => new Error('Service error')));

			component['getDelegationListPage'](component.pageParams, true);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(component.delegationList).toEqual([mockDelegationRequestListResponse.rows[0]]); // Existing data should remain
		}));
	});
});
