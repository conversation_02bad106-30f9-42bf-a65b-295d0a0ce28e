export interface OrllColumnDef<T> {
	key: keyof T | string;
	header: string;
	accessor?: (row: T) => any;
	sortable?: boolean;

	transform?: (value: any) => any;
	clickCell?: (row: T) => void;
	noshowLink?: (row: T) => void;
	actions?: ActionType<T>[];
}

export interface ActionType<T> {
	iconKey: string;
	iconClickAction?: (row: T) => void;
	showCondition?: (row: T) => void;
	showCopy?: (row: T) => void;
}
