import { ChangeDetectorRef, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { OrllTableComponent } from '@shared/components/orll-table/orll-table.component';
import { OrllColumnDef } from '@shared/models/orlll-common-table';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { VersionHistoryDetailComponent } from '../version-history-detail/version-history-detail.component';
import { ShareType } from '@shared/models/share-type.model';
import { RequestStatus, VersionHistoryObj } from '@shared/models/biz-logic/version-history.model';
import { VersionHistoryService } from '@shared/services/biz-logic/verion-history/version-history.service';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';

@Component({
	selector: 'orll-change-request-list',
	imports: [OrllTableComponent],
	templateUrl: './change-request-list.component.html',
	styleUrl: './change-request-list.component.scss',
	providers: [IataDateFormatPipe],
})
export default class ChangeRequestListComponent implements OnChanges {
	@Input() loId!: string;
	@Input() type!: string;
	shareType!: ShareType | null;
	@Input() param: { loId: string; type: string; shareType: ShareType.CHANGE_REQUEST } | null = null;

	columns: OrllColumnDef<VersionHistoryObj>[] = [
		{
			key: 'loType',
			header: 'common.change.request.object.type',
		},
		{
			key: 'actionRequestUri',
			header: 'common.change.request.object.url',
		},
		{
			key: 'version',
			header: 'common.change.request.version',
		},
		{
			key: 'date',
			header: 'common.change.request.date',
			transform: (val) => this.iataDateFormatPipe.transform(val),
		},
		{
			key: 'changeBy',
			header: 'common.change.request.changed.by',
		},
		{
			key: 'status',
			header: 'common.change.request.status',

			transform: (val: any) => this.transformStatus(val.split('#')[1]),
			clickCell: (row: VersionHistoryObj) => this.openVersionHistory(row),
		},
	];

	constructor(
		public readonly requestService: VersionHistoryService,
		private readonly verdionDialog: MatDialog,
		private readonly translateService: TranslateService,
		private readonly cdr: ChangeDetectorRef,
		private readonly iataDateFormatPipe: IataDateFormatPipe
	) {}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['loId'] || changes['type']) {
			this.param = { loId: this.loId, type: this.type, shareType: ShareType.CHANGE_REQUEST };
		}
	}

	openVersionHistory(row: VersionHistoryObj) {
		const dialogRef = this.verdionDialog.open(VersionHistoryDetailComponent, {
			width: '80vw',
			autoFocus: false,
			data: {
				title: this.translateService.instant('Change Request'),
				actionRequestUri: row.actionRequestUri,
				id: row.id,
				type: ShareType.CHANGE_REQUEST,
			},
		});
		dialogRef.afterClosed().subscribe((res) => {
			//refresh the dat
			if (res) {
				this.param = { loId: this.param?.loId ?? '', type: this.param?.type ?? '', shareType: ShareType.CHANGE_REQUEST };
				this.cdr.detectChanges();
			}
		});
	}

	transformStatus(val: any): string {
		return RequestStatus[val as keyof typeof RequestStatus];
	}

	refreshList() {
		this.param = { loId: this.loId, type: this.type, shareType: ShareType.CHANGE_REQUEST };
	}
}
