import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RuleDetailComponent } from './rule-detail.component';
import { RuleService } from '../../services/rule.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { FormBuilder } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { RuleDetailObj, RuleListObj } from '../../models/rule.model';
import { of, throwError } from 'rxjs';
import { Organization } from '@shared/models/organization.model';
import { TranslateModule } from '@ngx-translate/core';

const mockRule: RuleDetailObj = {
	id: '123',
	holder: ['org1'],
	requestType: 'type1',
	action: 'allow',
	request: ['org2'],
};

const mockRuleListObj: RuleListObj = {
	id: '111',
	holder: '222',
	requestType: '22',
	request: '',
	action: '',
	holderNames: '',
	requestTypeDescription: '',
	requestNames: '',
	actionDescription: '',
};

const orgs: Organization[] = [
	{
		id: '111',
		name: '111',
		orgType: '11',
	},
	{
		id: '222',
		name: '222',
		orgType: '222',
	},
];

describe('RuleDetailComponent', () => {
	let component: RuleDetailComponent;
	let fixture: ComponentFixture<RuleDetailComponent>;
	let ruleService: jasmine.SpyObj<RuleService>;
	let orgService: jasmine.SpyObj<OrgMgmtRequestService>;
	let dialogRef: jasmine.SpyObj<MatDialogRef<RuleDetailComponent>>;
	let dialog: jasmine.SpyObj<MatDialog>;

	beforeEach(async () => {
		ruleService = jasmine.createSpyObj('RuleService', ['deleteRule', 'saveRule', 'getRule']);
		orgService = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList']);
		dialogRef = jasmine.createSpyObj<MatDialogRef<RuleDetailComponent>>('MatDialogRef', ['close']);

		dialog = jasmine.createSpyObj('MatDialog', ['open']);

		orgService.getOrgList.and.returnValue(of(orgs));

		await TestBed.configureTestingModule({
			imports: [RuleDetailComponent, TranslateModule.forRoot()],
			providers: [
				FormBuilder,
				{ provide: RuleService, useValue: ruleService },
				{ provide: OrgMgmtRequestService, useValue: orgService },
				{ provide: MatDialogRef, useValue: dialogRef },
				{ provide: MatDialog, useValue: dialog },
				{ provide: MAT_DIALOG_DATA, useValue: {} },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(RuleDetailComponent);
		component = fixture.componentInstance;
		component.orgList = [];
		component.dataLoading = false;
		component.isSave = false;
		component.isEdit = false;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should load orgList on init', () => {
			const orgList: Organization[] = [
				{
					id: 'org1',
					name: 'Organization 1',
					orgType: '',
				},
			];
			orgService.getOrgList.and.returnValue(of(orgList));

			component.ngOnInit();
			fixture.detectChanges();

			expect(orgService.getOrgList).toHaveBeenCalled();
			expect(component.orgList).toEqual(orgList);
		});

		it('should load rule data and patch form when data.id exists', () => {
			const ruleData = { ...mockRule, requestTypeDescription: 'Description' };
			ruleService.getRule.and.returnValue(of(ruleData));
			component.data = mockRuleListObj;

			component.ngOnInit();
			fixture.detectChanges();

			expect(ruleService.getRule).toHaveBeenCalledWith(mockRuleListObj);

			ruleService.getRule.calls.mostRecent().returnValue.subscribe();
			expect(component.ruleForm.value.requestType).toBe('type1');
			expect(component.isEdit).toBeTrue();
			expect(component.dataLoading).toBeFalse();
		});

		it('should not call getRule if no data.id', () => {
			component.data = {
				holder: '',
				requestType: '',
				request: '',
				action: '',
				holderNames: '',
				requestTypeDescription: '',
				requestNames: '',
				actionDescription: '',
			};
			component.ngOnInit();

			expect(ruleService.getRule).not.toHaveBeenCalled();
		});
	});

	describe('getFormData', () => {
		it('should return form data with correct structure', () => {
			component.ruleForm.patchValue({
				holder: ['org1'],
				requestType: 'type1',
				request: ['req1'],
				action: 'allow',
			});

			const result = component.getFormData();

			expect(result.holder).toEqual(['org1']);
			expect(result.requestType).toBe('type1');
			expect(result.action).toBe('allow');
			expect(result.request).toEqual(['req1']);
		});

		it('should include id when editing', () => {
			component.data = mockRuleListObj;
			component.ruleForm.patchValue(mockRule);

			const result = component.getFormData();

			expect(result.id).toBe('111');
		});
	});

	describe('saveRule', () => {
		beforeEach(() => {
			component.ruleForm.patchValue({
				holder: ['org1'],
				requestType: 'type1',
				request: ['req1'],
				action: 'allow',
			});
		});

		it('should call saveRule with correct data and close dialog on success', () => {
			ruleService.saveRule.and.returnValue(of(''));
			component.saveRule();

			expect(component.isSave).toBeTrue();
			expect(component.dataLoading).toBe(false); // dataLoading is set to false after the operation completes
			expect(ruleService.saveRule).toHaveBeenCalledWith(
				jasmine.objectContaining({
					holder: ['org1'],
					requestType: 'type1',
					action: 'allow',
					request: ['req1'],
				})
			);

			ruleService.saveRule.calls.mostRecent().returnValue.subscribe();
			expect(component.dataLoading).toBe(false);
			expect(dialogRef.close).toHaveBeenCalledWith(true);
		});

		it('should show validation dialog when form is invalid', () => {
			component.ruleForm.patchValue({
				holder: ['org1'],
				requestType: '', // Invalid - required field
				request: ['req1'],
				action: '', // Invalid - required field
			});
			spyOn(component['dialog'], 'open').and.returnValue({} as any);

			component.saveRule();

			expect(component.isSave).toBeTrue();
			expect(component.ruleForm.touched).toBeTruthy();
			expect(component['dialog'].open).toHaveBeenCalled();
			expect(ruleService.saveRule).not.toHaveBeenCalled();
		});

		it('should handle save error', () => {
			ruleService.saveRule.and.returnValue(throwError(() => new Error('Save failed')));
			component.saveRule();

			expect(component.dataLoading).toBe(false);
			expect(dialogRef.close).not.toHaveBeenCalled();
		});
	});

	describe('Form Validation', () => {
		it('should initialize form with correct structure', () => {
			expect(component.ruleForm.get('holder')).toBeDefined();
			expect(component.ruleForm.get('requestType')).toBeDefined();
			expect(component.ruleForm.get('request')).toBeDefined();
			expect(component.ruleForm.get('action')).toBeDefined();
		});

		it('should require requestType field', () => {
			const requestTypeControl = component.ruleForm.get('requestType');
			requestTypeControl?.setValue('');
			expect(requestTypeControl?.hasError('required')).toBeTruthy();
		});

		it('should require action field', () => {
			const actionControl = component.ruleForm.get('action');
			actionControl?.setValue('');
			expect(actionControl?.hasError('required')).toBeTruthy();
		});

		it('should validate multiSelect when holder has 2+ items and request is not empty', () => {
			component.ruleForm.patchValue({
				holder: ['org1', 'org2'],
				request: ['req1'],
			});
			expect(component.ruleForm.hasError('requestEmpty')).toBeTruthy();
		});

		it('should validate multiSelect when holder includes same value as request', () => {
			component.ruleForm.patchValue({
				holder: ['org1'],
				request: ['org1'],
			});
			expect(component.ruleForm.hasError('requestIncludeHolder')).toBeTruthy();
			expect(component.ruleForm.getError('conflictValue')).toBe('org1');
		});

		it('should pass validation when holder has 2+ items and request is empty', () => {
			component.ruleForm.patchValue({
				holder: ['org1', 'org2'],
				request: [],
			});
			expect(component.ruleForm.hasError('requestEmpty')).toBeFalsy();
		});

		it('should pass validation when holder and request have different values', () => {
			component.ruleForm.patchValue({
				holder: ['org1'],
				request: ['org2'],
			});
			expect(component.ruleForm.hasError('requestIncludeHolder')).toBeFalsy();
		});
	});

	describe('Holder Value Changes', () => {
		it('should filter requestList when holder changes', () => {
			component.orgList = orgs;
			component.requestList = orgs;

			component.ruleForm.patchValue({
				holder: ['111'],
			});

			// Trigger the valueChanges subscription
			component.ngOnInit();
			component.ruleForm.get('holder')?.setValue(['111']);

			expect(component.requestList.length).toBe(1);
			expect(component.requestList[0].id).toBe('222');
		});

		it('should remove conflicting values from request when holder changes', () => {
			component.orgList = orgs;
			component.ruleForm.patchValue({
				holder: ['111'],
				request: ['111', '222'],
			});

			component.ngOnInit();
			component.ruleForm.get('holder')?.setValue(['111']);

			expect(component.ruleForm.get('request')?.value).toEqual(['222']);
		});
	});

	describe('Error Handling', () => {
		it('should handle getRule error', () => {
			ruleService.getRule.and.returnValue(throwError(() => new Error('Get rule failed')));
			component.data = mockRuleListObj;

			component.ngOnInit();

			expect(component.dataLoading).toBe(false);
			expect(component.isEdit).toBe(false);
		});
	});

	describe('Component Properties', () => {
		it('should initialize with correct default values', () => {
			expect(component.isEdit).toBe(false);
			expect(component.isSave).toBe(false);
			expect(component.dataLoading).toBe(false);
			// orgList is loaded from service on init, so it should contain the mock data
			expect(component.orgList).toEqual(orgs);
			expect(component.requestList).toEqual(orgs);
		});

		it('should have correct requestTypes', () => {
			expect(component.requestTypes).toEqual([
				{ code: '1', name: 'Change Request' },
				{ code: '2', name: 'Subscription Request' },
				{ code: '3', name: 'Access Delegation Request' },
			]);
		});
	});
});
