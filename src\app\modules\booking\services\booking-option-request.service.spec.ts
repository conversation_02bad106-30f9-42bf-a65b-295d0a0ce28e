import { TestBed } from '@angular/core/testing';
import { BookingOptionRequestService } from './booking-option-request.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { CURRENCIES } from '../../sli-mgmt/ref-data/currencies.data';
import { BookingOptionRequestDetailObj, BookingOptionRequestListObj } from '../models/booking.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { environment } from '@environments/environment';
import { CodeName } from '@shared/models/code-name.model';
import { AIRPORTS } from '../../sli-mgmt/ref-data/airports.data';
import { CodeType } from '@shared/models/search-type.model';

const baseUrl = environment.baseApi;

describe('BookingOptionRequestService', () => {
	let service: BookingOptionRequestService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [BookingOptionRequestService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(BookingOptionRequestService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify(); // Verify that no outstanding requests remain
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('#getCurrencies should retrieve all currencies', () => {
		service.getCurrencies().subscribe((response: string[]) => {
			expect(response).toHaveSize(CURRENCIES.length);
			expect(response).toContain(CURRENCIES[0]);
		});
	});

	it('#getAirPorts should retrieve all airPorts', () => {
		service.getAirports().subscribe((response: CodeName[]) => {
			expect(response).toHaveSize(AIRPORTS.length);
			expect(response[0].code).toBe(AIRPORTS[0].code);
			expect(response[0].name).toContain(AIRPORTS[0].name);
		});
	});

	describe('sendBookingOptonsToForwarder', () => {
		it('should POST booking options payload to add-booking-option endpoint', () => {
			const params = [
				{
					bookingOptionRequestId: '111',
					priceList: [],
					transportLegsList: [],
					productDescription: '',
				},
			];

			service.sendBookingOptonsToForwarder(params as any).subscribe((res) => {
				expect(res).toBe('ok');
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-option-management/add-booking-option`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body[0].bookingOptionRequestId).toBe('111');
			req.flush('ok');
		});
	});

	describe('getDataPerPage', () => {
		it('should return paginated booking request data', () => {
			const mockSearchParam = {
				productDescription: 'GPU',
			};

			const mockResponse: PaginationResponse<BookingOptionRequestListObj> = {
				total: 2,
				rows: [
					{
						id: '111',
						bookingOptionRequestId: '111',
						expectedCommodity: '111',
						departureLocation: '111',
						arrivalLocation: '111',
						latestStatus: 'Created',
					},
					{
						id: '222',
						bookingOptionRequestId: '222',
						expectedCommodity: '222',
						departureLocation: '22',
						arrivalLocation: '222',
						latestStatus: 'Shared',
					},
				],
			};

			service.getDataPerPage(mockSearchParam).subscribe((response) => {
				expect(response).toEqual(mockResponse);
				expect(response.total).toBe(2);
				expect(response.rows.length).toBe(2);
				expect(response.rows[0].arrivalLocation).toBe('111');
				expect(response.rows[1].latestStatus).toBe('Shared');
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-option-management`);
			expect(req.request.method).toBe('GET');
			expect(req.request.params.get('productDescription')).toBe('GPU');

			req.flush(mockResponse);
		});
	});

	describe('loadAllData', () => {
		it('should return  booking request data list', () => {
			const mockSearchParam = {
				productDescription: 'GPU',
			};

			const mockResponse: BookingOptionRequestListObj[] = [
				{
					id: '111',
					bookingOptionRequestId: '111',
					expectedCommodity: '111',
					departureLocation: '111',
					arrivalLocation: '111',
					latestStatus: 'Created',
				},
				{
					id: '222',
					bookingOptionRequestId: '222',
					expectedCommodity: '222',
					departureLocation: '22',
					arrivalLocation: '222',
					latestStatus: 'Shared',
				},
			];

			service.loadAllData(mockSearchParam).subscribe((response) => {
				expect(response).toEqual(mockResponse);
				expect(response.length).toBe(2);
				expect(response[0].arrivalLocation).toBe('111');
				expect(response[1].latestStatus).toBe('Shared');
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-option-management`);
			expect(req.request.method).toBe('GET');
			expect(req.request.params.get('productDescription')).toBe('GPU');

			req.flush(mockResponse);
		});
	});

	describe('getBookingOptionDetail', () => {
		it('should return  booking request detail', () => {
			const mockResponse: BookingOptionRequestDetailObj = {
				bookingShipmentDetails: {
					id: '',
					pieceGroups: {
						id: '',
						pieceGroupCount: 0,
					},
					totalGrossWeight: {
						currencyUnit: '',
						numericalValue: 0,
					},
					chargeableWeight: {
						currencyUnit: '',
						numericalValue: 0,
					},
					dimensions: {
						length: 0,
						width: 0,
						height: 2,
					},
					expectedCommodity: '',
					specialHandlingCodes: ['aaa'],
					textualHandlingInstructions: '',
				},
				transportLegs: [],
				bookingPreference: [],
				timePreferences: {
					earliestAcceptanceTime: '',
					latestAcceptanceTime: '',
					latestArrivalTime: '',
					timeOfAvailability: '',
				},
				unitsPreference: {
					currency: {
						currencyUnit: 'CNY',
					},
				},
				involvedParties: [],
				bookingOptionList: [],
				shareList: [],
			};
			const mockListObj: BookingOptionRequestListObj = {
				id: '',
				bookingOptionRequestId: '111',
				expectedCommodity: '',
				departureLocation: '',
				arrivalLocation: '',
				latestStatus: '',
			};

			service.getBookingOptionDetail(mockListObj.bookingOptionRequestId).subscribe((response) => {
				expect(response).toEqual(mockResponse);
				expect(response.bookingShipmentDetails.dimensions.height).toBe(2);
				expect(response.unitsPreference.currency.currencyUnit).toBe('CNY');
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-option-management/info`);
			expect(req.request.method).toBe('GET');
			expect(req.request.params.get('id')).toBe('111');

			req.flush(mockResponse);
		});
	});

	describe('createBookingOptionRequest', () => {
		it('should create booking option successfully', () => {
			const mockDetail: BookingOptionRequestDetailObj = {
				bookingShipmentDetails: {
					id: '',
					pieceGroups: {
						id: '',
						pieceGroupCount: 3,
					},
					totalGrossWeight: {
						currencyUnit: '',
						numericalValue: 0,
					},
					chargeableWeight: {
						currencyUnit: '',
						numericalValue: 0,
					},
					dimensions: {
						length: 0,
						width: 0,
						height: 2,
					},
					expectedCommodity: '',
					specialHandlingCodes: ['aaa'],
					textualHandlingInstructions: '',
				},
				transportLegs: [],
				bookingPreference: [],
				timePreferences: {
					earliestAcceptanceTime: '',
					latestAcceptanceTime: '',
					latestArrivalTime: '',
					timeOfAvailability: '',
				},
				unitsPreference: {
					currency: {
						currencyUnit: 'CNY',
					},
				},
				involvedParties: [],
			};
			const mockResponse = 'success';

			service.createBookingOptionRequest(mockDetail).subscribe((response) => {
				expect(response).toEqual(mockResponse);
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-option-management`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body.bookingShipmentDetails.pieceGroups.pieceGroupCount).toBe(3);
			expect(req.request.body.bookingShipmentDetails.specialHandlingCodes.length).toBe(1);

			req.flush(mockResponse);
		});
	});

	describe('getCommodityCodes', () => {
		it('should get commodity codes successfully', () => {
			const mockResponse: CodeName[] = [
				{
					code: '1',
					name: 'name1',
				},
				{
					code: '2',
					name: 'name2',
				},
			];

			service.getCodeByType(CodeType.COMMODITY_CODE).subscribe((response) => {
				expect(response).toEqual(mockResponse);
				expect(response.length).toBe(2);
				expect(response[0].code).toBe('1');
				expect(response[1].name).toBe('name2');
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/sys-management/enums/commodityCode`);
			expect(req.request.method).toBe('GET');

			req.flush(mockResponse);
		});
	});

	describe('getHandlingCodes', () => {
		it('should get handling codes successfully', () => {
			const mockResponse: CodeName[] = [
				{
					code: '1',
					name: 'name1',
				},
				{
					code: '2',
					name: 'name2',
				},
			];

			service.getCodeByType(CodeType.HANDLING_CODE).subscribe((response) => {
				expect(response).toEqual(mockResponse);
				expect(response.length).toBe(2);
				expect(response[0].code).toBe('1');
				expect(response[1].name).toBe('name2');
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/sys-management/enums/specialHandlingCode`);
			expect(req.request.method).toBe('GET');

			req.flush(mockResponse);
		});
	});

	describe('shareOption', () => {
		it('should share option successfully when fromBooking is false', () => {
			const bookingOptionRequestId = '111';
			const mockResponse = true;

			service.shareOption(bookingOptionRequestId, 'airline1', false).subscribe((response) => {
				expect(response).toEqual(true);
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-option-management/share`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body.bookingOptionRequestId).toBe('111');
			expect(req.request.body.orgId).toBe('airline1');

			req.flush(mockResponse);
		});

		it('should share option successfully when fromBooking is true', () => {
			const bookingRequestId = '111';
			const mockResponse = true;

			service.shareOption(bookingRequestId, 'airline1', true).subscribe((response) => {
				expect(response).toEqual(true);
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/share`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body.bookingRequestId).toBe('111');
			expect(req.request.body.orgId).toBe('airline1');

			req.flush(mockResponse);
		});
	});
});
