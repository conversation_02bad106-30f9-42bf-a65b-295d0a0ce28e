import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AddUserDialogComponent } from './add-user-dialog.component';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { UserMgmtRequestService } from '../../services/user-mgmt-request.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { of } from 'rxjs';
import { Organization } from '@shared/models/organization.model';
import { SecondaryUserPanelComponent } from '../secondary-user-panel/secondary-user-panel.component';
import { SecondaryUser } from '../../models/secondary-user.model';
import { UserListObject } from '../../models/user-list-object.model';

describe('AddUserDialogComponent', () => {
	let component: AddUserDialogComponent;
	let fixture: ComponentFixture<AddUserDialogComponent>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<AddUserDialogComponent>>;
	let userMgmtRequestServiceSpy: jasmine.SpyObj<UserMgmtRequestService>;
	let orgMgmtRequestServiceSpy: jasmine.SpyObj<OrgMgmtRequestService>;

	const mockOrganizations: Organization[] = [
		{ id: '1', name: 'Organization 1', orgType: 'type1' },
		{ id: '2', name: 'Organization 2', orgType: 'type2' },
		{ id: '3', name: 'Test Org', orgType: 'type3' },
	];

	const mockUserInfo: UserListObject = {
		userId: '123',
		firstName: 'John',
		lastName: 'Doe',
		email: '<EMAIL>',
		orgId: '1',
		orgName: 'Organization 1',
		primaryOrgId: '1',
		primaryOrgName: 'Organization 1',
		userType: '1',
		secondaryOrgIds: [{ orgId: '2', orgName: 'Organization 2', userType: '2' }],
	};

	beforeEach(async () => {
		userMgmtRequestServiceSpy = jasmine.createSpyObj('UserMgmtRequestService', ['getUserInfo', 'createUser', 'updateUser']);
		orgMgmtRequestServiceSpy = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList']);

		mockDialogRef = jasmine.createSpyObj<MatDialogRef<AddUserDialogComponent>>('MatDialogRef', ['close']);
		await TestBed.configureTestingModule({
			imports: [AddUserDialogComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: {} },
				{ provide: UserMgmtRequestService, useValue: userMgmtRequestServiceSpy },
				{ provide: OrgMgmtRequestService, useValue: orgMgmtRequestServiceSpy },
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		}).compileComponents();

		orgMgmtRequestServiceSpy.getOrgList.and.returnValue(of(mockOrganizations));

		fixture = TestBed.createComponent(AddUserDialogComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should call dialogRef.close when onCancel is called', () => {
		component.onCancel();
		expect(mockDialogRef.close).toHaveBeenCalled();
	});

	it('should call dialogRef.close with no arguments', () => {
		component.onCancel();
		expect(mockDialogRef.close).toHaveBeenCalledWith();
	});

	it('should not call dialogRef.close if form is invalid when onOk is called', () => {
		component.addUserForm.markAllAsTouched();
		component.addUserForm.patchValue({
			firstName: '',
			lastName: '',
			email: '',
			orgName: '',
			primaryOrgId: '',
			userType: '',
		});
		component.onOk();
		expect(mockDialogRef.close).not.toHaveBeenCalled();
	});

	it('should call createUser and dialogRef.close if form is valid and no userId in data', () => {
		const userPayload = {
			firstName: 'John',
			lastName: 'Doe',
			email: '<EMAIL>',
			orgName: 'Org1',
			primaryOrgId: '1',
			userType: 'admin',
			secondaryOrgIds: [],
		};
		component.addUserForm.setValue({
			firstName: userPayload.firstName,
			lastName: userPayload.lastName,
			email: userPayload.email,
			orgName: userPayload.orgName,
			primaryOrgId: userPayload.primaryOrgId,
			userType: userPayload.userType,
		});
		userMgmtRequestServiceSpy.createUser.and.returnValue(of('success'));
		component.getSecondaryUserList = jasmine.createSpy().and.returnValue([]);
		component.onOk();
		expect(userMgmtRequestServiceSpy.createUser).toHaveBeenCalledWith(jasmine.objectContaining(userPayload));
		// simulate async
		fixture.detectChanges();
		expect(mockDialogRef.close).toHaveBeenCalled();
	});

	it('should call updateUser and dialogRef.close if form is valid and userId in data', () => {
		const userPayload = {
			firstName: 'Jane',
			lastName: 'Smith',
			email: '<EMAIL>',
			orgName: 'Org2',
			primaryOrgId: '2',
			userType: 'user',
			secondaryOrgIds: [],
			userId: '123',
		};
		component.data.userId = '123';
		component.addUserForm.setValue({
			firstName: userPayload.firstName,
			lastName: userPayload.lastName,
			email: userPayload.email,
			orgName: userPayload.orgName,
			primaryOrgId: userPayload.primaryOrgId,
			userType: userPayload.userType,
		});
		userMgmtRequestServiceSpy.updateUser.and.returnValue(of('success'));
		component.getSecondaryUserList = jasmine.createSpy().and.returnValue([]);
		component.onOk();
		expect(userMgmtRequestServiceSpy.updateUser).toHaveBeenCalledWith(jasmine.objectContaining(userPayload));
		// simulate async
		fixture.detectChanges();
		expect(mockDialogRef.close).toHaveBeenCalled();
	});

	describe('Component Initialization', () => {
		it('should initialize with default values', () => {
			expect(component.isSecondary).toBe(false);
			expect(component.orgList).toEqual(mockOrganizations);
			expect(component.filteredOrgList).toEqual([]);
			expect(component.filteredPrimaryOrgList).toEqual(mockOrganizations); // This gets populated by setupAutocomplete
			expect(component.secondaryUserList).toEqual([]);
			expect(component.userTypes).toBeDefined();
		});

		it('should initialize form with required validators', () => {
			const form = component.addUserForm;
			expect(form.get('firstName')?.hasError('required')).toBe(true);
			expect(form.get('lastName')?.hasError('required')).toBe(true);
			expect(form.get('email')?.hasError('required')).toBe(true);
			expect(form.get('orgName')?.hasError('required')).toBe(true);
			expect(form.get('primaryOrgId')?.hasError('required')).toBe(true);
			expect(form.get('userType')?.hasError('required')).toBe(true);
		});

		it('should validate email format', () => {
			const emailControl = component.addUserForm.get('email');
			emailControl?.setValue('invalid-email');
			expect(emailControl?.hasError('email')).toBe(true);

			emailControl?.setValue('<EMAIL>');
			expect(emailControl?.hasError('email')).toBe(false);
		});

		it('should call initRefData on ngOnInit', () => {
			spyOn<any>(component, 'initRefData');
			component.ngOnInit();
			expect(component['initRefData']).toHaveBeenCalled();
		});
	});

	describe('Organization Management', () => {
		it('should filter organizations correctly', () => {
			const result = component.filterOrgs('org');
			expect(result.length).toBe(3);
			expect(result.every((org) => org.name.toLowerCase().includes('org'))).toBe(true);
		});

		it('should filter organizations case-insensitively', () => {
			const result = component.filterOrgs('ORG');
			expect(result.length).toBe(3);
		});

		it('should filter organizations with partial match', () => {
			const result = component.filterOrgs('Test');
			expect(result.length).toBe(1);
			expect(result[0].name).toBe('Test Org');
		});

		it('should return empty array when no organizations match filter', () => {
			const result = component.filterOrgs('nonexistent');
			expect(result.length).toBe(0);
		});

		it('should handle empty search string', () => {
			const result = component.filterOrgs('');
			expect(result.length).toBe(3);
		});

		it('should display organization name correctly', () => {
			const orgName = component.displayOrgName('1');
			expect(orgName).toBe('Organization 1');
		});

		it('should return empty string for non-existent organization id', () => {
			const orgName = component.displayOrgName('999');
			expect(orgName).toBe('');
		});
	});

	describe('User Information Management', () => {
		beforeEach(() => {
			component.data.userId = '123';
			userMgmtRequestServiceSpy.getUserInfo.and.returnValue(of(mockUserInfo));
		});

		it('should fill user info when userId is provided', () => {
			component['fillUserInfo']('123');
			expect(userMgmtRequestServiceSpy.getUserInfo).toHaveBeenCalledWith('123');
		});

		it('should patch form with user info', () => {
			component['fillUserInfo']('123');
			expect(component.addUserForm.get('firstName')?.value).toBe(mockUserInfo.firstName);
			expect(component.addUserForm.get('lastName')?.value).toBe(mockUserInfo.lastName);
			expect(component.addUserForm.get('email')?.value).toBe(mockUserInfo.email);
		});

		it('should set isSecondary to true when user has secondary organizations', () => {
			component['fillUserInfo']('123');
			expect(component.isSecondary).toBe(true);
			expect(component.secondaryUserList).toEqual(mockUserInfo.secondaryOrgIds || []);
		});

		it('should disable email field when editing existing user', () => {
			component['initRefData']();
			expect(component.addUserForm.get('email')?.disabled).toBe(true);
		});
	});

	describe('Form Validation', () => {
		it('should mark form as invalid when required fields are empty', () => {
			component.addUserForm.patchValue({
				firstName: '',
				lastName: '',
				email: '',
				orgName: '',
				primaryOrgId: '',
				userType: '',
			});
			expect(component.addUserForm.invalid).toBe(true);
		});

		it('should mark form as valid when all required fields are filled', () => {
			component.addUserForm.patchValue({
				firstName: 'John',
				lastName: 'Doe',
				email: '<EMAIL>',
				orgName: 'Test Org',
				primaryOrgId: '1',
				userType: '1',
			});
			expect(component.addUserForm.valid).toBe(true);
		});

		it('should validate email format in form', () => {
			component.addUserForm.patchValue({
				firstName: 'John',
				lastName: 'Doe',
				email: 'invalid-email',
				orgName: 'Test Org',
				primaryOrgId: '1',
				userType: '1',
			});
			expect(component.addUserForm.invalid).toBe(true);
			expect(component.addUserForm.get('email')?.hasError('email')).toBe(true);
		});
	});

	describe('Autocomplete Functionality', () => {
		it('should setup autocomplete for primary organization', () => {
			spyOn<any>(component, 'setupAutocomplete');
			component['initRefData']();
			expect(component['setupAutocomplete']).toHaveBeenCalled();
		});

		it('should filter primary organization list on value changes', () => {
			component.addUserForm.get('primaryOrgId')?.setValue('Test');
			expect(component.filteredPrimaryOrgList.length).toBe(1);
			expect(component.filteredPrimaryOrgList[0].name).toBe('Test Org');
		});

		it('should show all organizations when search is empty', () => {
			component.addUserForm.get('primaryOrgId')?.setValue('');
			expect(component.filteredPrimaryOrgList.length).toBe(3);
		});

		it('should handle whitespace in search terms', () => {
			const result = component.filterOrgs('  test  ');
			expect(result.length).toBe(1);
			expect(result[0].name).toBe('Test Org');
		});
	});

	describe('Secondary User Management', () => {
		let mockSecondaryUserPanel: jasmine.SpyObj<SecondaryUserPanelComponent>;

		beforeEach(() => {
			component.isSecondary = true;
			mockSecondaryUserPanel = jasmine.createSpyObj('SecondaryUserPanelComponent', ['getSecondaryUserList']);
			component.secondaryUserPanel = mockSecondaryUserPanel;
		});

		it('should get secondary user list from panel component', () => {
			const mockSecondaryUsers: SecondaryUser[] = [
				{ orgId: '2', orgName: 'Org 2', userType: '1' },
				{ orgId: '3', orgName: 'Org 3', userType: '2' },
			];
			mockSecondaryUserPanel.getSecondaryUserList.and.returnValue(mockSecondaryUsers);

			const result = component.getSecondaryUserList();
			expect(result).toEqual(mockSecondaryUsers);
		});

		it('should return empty array when secondary user panel is undefined', () => {
			component.secondaryUserPanel = undefined;
			const result = component.getSecondaryUserList();
			expect(result).toEqual([]);
		});

		it('should include secondary users in user payload when creating user', () => {
			const mockSecondaryUsers: SecondaryUser[] = [{ orgId: '2', orgName: 'Org 2', userType: '1' }];
			mockSecondaryUserPanel.getSecondaryUserList.and.returnValue(mockSecondaryUsers);

			component.addUserForm.patchValue({
				firstName: 'John',
				lastName: 'Doe',
				email: '<EMAIL>',
				orgName: 'Test Org',
				primaryOrgId: '1',
				userType: '1',
			});

			userMgmtRequestServiceSpy.createUser.and.returnValue(of('success'));
			component.onOk();

			expect(userMgmtRequestServiceSpy.createUser).toHaveBeenCalledWith(
				jasmine.objectContaining({
					secondaryOrgIds: mockSecondaryUsers,
				})
			);
		});
	});

	describe('User Detail Request', () => {
		it('should call createUser when no userId is provided', () => {
			const userPayload: UserListObject = {
				userId: '',
				firstName: 'John',
				lastName: 'Doe',
				email: '<EMAIL>',
				orgId: '1',
				orgName: 'Test Org',
				primaryOrgId: '1',
				primaryOrgName: 'Test Org',
				userType: '1',
			};

			userMgmtRequestServiceSpy.createUser.and.returnValue(of('success'));
			component['userDetailRequest'](userPayload).subscribe();

			expect(userMgmtRequestServiceSpy.createUser).toHaveBeenCalledWith(userPayload);
			expect(userMgmtRequestServiceSpy.updateUser).not.toHaveBeenCalled();
		});

		it('should call updateUser when userId is provided', () => {
			component.data.userId = '123';
			const userPayload: UserListObject = {
				userId: '',
				firstName: 'John',
				lastName: 'Doe',
				email: '<EMAIL>',
				orgId: '1',
				orgName: 'Test Org',
				primaryOrgId: '1',
				primaryOrgName: 'Test Org',
				userType: '1',
			};

			userMgmtRequestServiceSpy.updateUser.and.returnValue(of('success'));
			component['userDetailRequest'](userPayload).subscribe();

			expect(userMgmtRequestServiceSpy.updateUser).toHaveBeenCalledWith({
				...userPayload,
				userId: '123',
			});
			expect(userMgmtRequestServiceSpy.createUser).not.toHaveBeenCalled();
		});
	});

	describe('Edge Cases', () => {
		it('should handle null or undefined organization search', () => {
			expect(() => component.filterOrgs(null as any)).toThrow();
			expect(() => component.filterOrgs(undefined as any)).toThrow();
		});

		it('should handle empty organization list', () => {
			component.orgList = [];
			const result = component.filterOrgs('test');
			expect(result).toEqual([]);
		});

		it('should handle user info without secondary organizations', () => {
			const userInfoWithoutSecondary = { ...mockUserInfo, secondaryOrgIds: undefined };
			userMgmtRequestServiceSpy.getUserInfo.and.returnValue(of(userInfoWithoutSecondary));

			component['fillUserInfo']('123');
			expect(component.isSecondary).toBe(false);
			expect(component.secondaryUserList).toEqual([]);
		});

		it('should handle user info with empty secondary organizations array', () => {
			const userInfoWithEmptySecondary = { ...mockUserInfo, secondaryOrgIds: [] };
			userMgmtRequestServiceSpy.getUserInfo.and.returnValue(of(userInfoWithEmptySecondary));

			component['fillUserInfo']('123');
			expect(component.isSecondary).toBe(false);
		});
	});
});
