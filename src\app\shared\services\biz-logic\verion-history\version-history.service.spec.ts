import { TestBed } from '@angular/core/testing';
import { VersionHistoryService } from './version-history.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { RequestStatus, RequestStatusChangeAction, VersionHistoryObj } from '@shared/models/biz-logic/version-history.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { environment } from '@environments/environment';

const baseUrl = environment.baseApi;

describe('VersionHistoryService', () => {
	let service: VersionHistoryService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [VersionHistoryService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(VersionHistoryService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	describe('loadAllData', () => {
		it('should return empty array if loId is not provided', () => {
			const result = service.loadAllData({ loId: '', type: 'doc' });
			result.subscribe((data) => {
				expect(data).toEqual([]);
			});
		});

		it('should call getData with correct endpoint and params when loId exists', () => {
			const expectedUrl = `${baseUrl}/action-request-management/get-lo-history-action-request`;
			const mockResponse: VersionHistoryObj[] = [
				{
					loType: '',
					loId: '',
					actionRequestUri: '',
					version: '',
					actionRequestDate: '',
					updateOrgName: '',
					requestStatus: RequestStatus.REQUEST_ACCEPTED,
				},
			];

			service.loadAllData({ loId: '123', type: 'sli' }).subscribe((data) => {
				expect(data).toEqual(mockResponse);
			});

			const req = httpMock.expectOne(expectedUrl + '?loId=123&type=sli');
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});
	});

	describe('getDataPerPage', () => {
		it('should call getData with efg endpoint and pagination params', () => {
			const expectedUrl = `${baseUrl}/action-request-management/get-lo-history-action-request`;
			const mockResponse: PaginationResponse<VersionHistoryObj> = { rows: [], total: 0 };
			const params = {
				pageNum: 1,
				pageSize: 10,
				shareType: '',
			};

			service.getDataPerPage(params).subscribe((data) => {
				expect(data).toEqual(mockResponse);
			});

			const req = httpMock.expectOne(expectedUrl + '?pageNum=1&pageSize=10&shareType=');
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});

		it('should call getData with change request list endpoint and pagination params', () => {
			const expectedUrl = `${baseUrl}/action-request-management/list-change-request`;
			const mockResponse: PaginationResponse<VersionHistoryObj> = { rows: [], total: 0 };
			const params = {
				pageNum: 1,
				pageSize: 10,
				shareType: 'change-request',
			};

			service.getDataPerPage(params).subscribe((data) => {
				expect(data).toEqual(mockResponse);
			});

			const req = httpMock.expectOne(expectedUrl);
			expect(req.request.method).toBe('POST');
			req.flush(mockResponse);
		});
	});

	describe('getVersionHistoryDetail', () => {
		it('should call getData with detail endpoint and url param', () => {
			const actionUrl = 'https://api.com/version/1';
			const expectedUrl = `${baseUrl}/action-request-management/get-action-request-details`;
			const mockResponse = {
				id: '3333',
				hasLogisticsObject: true,
				hasOperation: [{ id: '3343-1', loType: 'aaaa', property: 'test', oldValue: 'old', newValue: 'new' }],
				actionRequestDate: '2025-07-30 10:33:00',
				updateOrgName: 'meng',
				requestStatus: RequestStatus.REQUEST_ACCEPTED,
				description: '33333',
				isOwner: 'isOwner',
			};

			service.getVersionHistoryDetail(actionUrl).subscribe((data) => {
				expect(data).toEqual(mockResponse);
			});

			const req = httpMock.expectOne((req) => req.url === expectedUrl && req.params.get('actionRequestUri') === actionUrl);
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});
	});

	describe('update request status', () => {
		it('should send PATCH request to correct endpoint with url and status', () => {
			const mockUri = 'https://api.com/action/123';
			const mockStatus: RequestStatusChangeAction = RequestStatusChangeAction.REQUEST_ACCEPTED;
			const expectedUrl = `${baseUrl}/action-request-management/update-action-request`;
			const expectedBody = { actionRequestUri: mockUri, status: mockStatus };

			service.updateRequestStatus(mockUri, mockStatus).subscribe((result) => {
				expect(result).toBeTrue();
			});

			const req = httpMock.expectOne(expectedUrl);

			expect(req.request.method).toBe('PATCH');
			expect(req.request.body).toEqual(expectedBody);
			expect(req.request.headers.get('Content-Type')).not.toBe('application/x-www-form-urlencoded');

			req.flush(true);
		});

		it('should return false when server returns false', () => {
			const mockUri = 'https://api.com/action/456';
			const mockStatus: RequestStatusChangeAction = RequestStatusChangeAction.REQUEST_ACCEPTED;

			service.updateRequestStatus(mockUri, mockStatus).subscribe((result) => {
				expect(result).toBeFalse();
			});
			const expectedUrl = `${baseUrl}/action-request-management/update-action-request`;

			const req = httpMock.expectOne(expectedUrl);
			req.flush(false);
		});

		it('should handle error response', () => {
			const mockUri = 'https://api.com/action/789';
			const mockStatus: RequestStatusChangeAction = RequestStatusChangeAction.REQUEST_REVOKED;

			service.updateRequestStatus(mockUri, mockStatus).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toEqual(500);
				},
			});

			const expectedUrl = `${baseUrl}/action-request-management/update-action-request`;
			const req = httpMock.expectOne(expectedUrl);
			req.flush('Server error', { status: 500, statusText: 'Internal Server Error' });
		});

		it('should only make one request', () => {
			const mockUri = 'https://api.com/action/1';
			const mockStatus: RequestStatusChangeAction = RequestStatusChangeAction.REQUEST_ACCEPTED;

			service.updateRequestStatus(mockUri, mockStatus).subscribe();
			const expectedUrl = `${baseUrl}/action-request-management/update-action-request`;

			httpMock.expectOne(expectedUrl);
			httpMock.verify();
		});
	});
});
