import { Injectable } from '@angular/core';
import { ApiService } from '@shared/services/api.service';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ActivityStream, UserStatisticsChart } from '../models/home-dashboard.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';

@Injectable({
	providedIn: 'root',
})
export class HomeDashboardService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getUserStatistics(): Observable<UserStatisticsChart[]> {
		return this.getData('dashboard/user-statistics');
	}

	getActivityStream(): Observable<PaginationResponse<ActivityStream>> {
		return this.getData('dashboard/activity-stream');
	}

	downloadFile(fileName: string): Observable<Blob> {
		return this.getData(`dashboard/download/${fileName}`, null, { responseType: 'blob' });
	}

	cleanActivityStream(): Observable<boolean> {
		return this.postData('dashboard/housekeeping/activity', null);
	}

	cleanSystemData(): Observable<boolean> {
		return this.postData('dashboard/housekeeping/system', null);
	}
}
