import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BookingOptionDetailComponent } from './booking-option-detail.component';
import {
	BookingOptionInfo,
	BookingOptionRequestDetailObj,
	BookingOptionRequestListObj,
	BookingOptionTransportObj,
	BookingRequestObj,
	OptionRequestDetail,
} from '../../models/booking.model';
import { BookingAirlineDetailObj } from '@shared/models/booking-option.model';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { UserProfileService } from '@shared/services/user-profile.service';
import { of, throwError } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { ShipmentParty } from 'src/app/modules/sli-mgmt/models/shipment-party.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { BookingRequestService } from '../../services/booking-request.service';
import { By } from '@angular/platform-browser';
import { UserProfile } from '@shared/models/user-profile.model';
import { BookingOptionRequestDetailComponent } from '../booking-option-request-detail/booking-option-request-detail.component';
import { Component, CUSTOM_ELEMENTS_SCHEMA, Input } from '@angular/core';

@Component({
	selector: 'orll-booking-option-request-detail',
	template: '<div>Mocked Option Request Detail</div>',
	standalone: true,
})
class FakeSubComponent {
	@Input() quote = false;
	@Input() detailInfo!: OptionRequestDetail;
	getCurrentUser() {
		return of(true);
	}
}

function createMockUserProfile(overrides: Partial<UserProfile> = {}): UserProfile {
	return {
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'ORG_TYPE',
		userType: '1',
		menuList: [],
		permissionList: [],
		orgList: [],
		...overrides,
	};
}

const mockTrans1: BookingOptionTransportObj = {
	departureLocation: 'Shanghai',
	arrivalLocation: 'Beijing',
	departureDate: '2025-01-01T08:00:00',
	arrivalDate: '2025-01-01T10:00:00',
	transportIdentifier: 'CA123',
	legNumber: 1,
	airlineCode: '1111',
};

const mockTrans2: BookingOptionTransportObj = {
	departureLocation: 'Beijing',
	arrivalLocation: 'Guangzhou',
	departureDate: '2025-01-02T09:00:00',
	arrivalDate: '2025-01-02T11:00:00',
	transportIdentifier: 'CZ456',
	legNumber: 2,
	airlineCode: '2222',
};

const mockOption1: BookingOptionInfo = {
	id: 'OPT-001',
	productDescription: 'Air China',
	grandTotal: 1200,
	priceList: [
		{
			chargeType: '11',
			rateClassCode: '11',
			subTotal: 0,
			chargePaymentType: '11',
			entitlement: '11',
		},
	],
	transportLegsList: [mockTrans1],
	offerValidFrom: '',
	offerValidTo: '',
	isChoose: false,
	bookingOptionRequestId: '111',
	carrier: { companyName: 'Air China' } as ShipmentParty,
};

const mockOption2: BookingOptionInfo = {
	id: 'OPT-002',
	productDescription: 'China Sout',
	grandTotal: 1500.0,
	priceList: [
		{
			chargeType: '23',
			rateClassCode: '23',
			subTotal: 0,
			chargePaymentType: '23',
			entitlement: '23',
		},
	],
	transportLegsList: [mockTrans1, mockTrans2],
	offerValidFrom: '',
	offerValidTo: '',
	isChoose: true,
	bookingOptionRequestId: '222',
	carrier: { companyName: 'Air China' } as ShipmentParty,
};

const mockRequestDetail: BookingOptionRequestDetailObj = {
	bookingShipmentDetails: {
		id: '',
		pieceGroups: {
			id: '',
			pieceGroupCount: 0,
		},
		totalGrossWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		chargeableWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		dimensions: {
			length: 0,
			width: 0,
			height: 0,
		},
		expectedCommodity: '',
		specialHandlingCodes: [],
		textualHandlingInstructions: '',
	},
	transportLegs: [],
	bookingPreference: [],
	timePreferences: {
		earliestAcceptanceTime: '',
		latestAcceptanceTime: '',
		latestArrivalTime: '',
		timeOfAvailability: '',
	},
	unitsPreference: {
		currency: {
			currencyUnit: '',
		},
	},
	involvedParties: [],
	bookingOptionList: [mockOption1, mockOption2],
	shareList: [
		{
			orgId: '111',
			orgName: '111',
			requestTime: '2025-08-11 11:30:00',
			airlineCode: '',
		},
		{
			orgId: '111',
			orgName: '111',
			requestTime: '2025-08-11 11:30:00',
			airlineCode: '',
		},
	],
};

const mockDetail1: BookingAirlineDetailObj = {
	id: 'OPT-001',
	totalPrice: {
		numericalValue: 0,
		currencyUnit: '',
	},
	transportVoList: [],
};
const mockDetail2: BookingAirlineDetailObj = {
	id: 'OPT-002',
	totalPrice: {
		numericalValue: 0,
		currencyUnit: '',
	},
	transportVoList: [],
};
const mockDetail3: BookingAirlineDetailObj = {
	id: 'OPT-003',
	totalPrice: {
		numericalValue: 0,
		currencyUnit: '',
	},
	transportVoList: [],
};

const mockOption4: BookingOptionInfo = {
	id: 'OPT-001',
	isChoose: false,
	bookingOptionRequestId: '',
	grandTotal: 0,
	priceList: [],
	transportLegsList: [],
	productDescription: '',
	offerValidFrom: '',
	offerValidTo: '',
	carrier: { companyName: 'Air China' } as ShipmentParty,
};
const mockOption5: BookingOptionInfo = {
	id: 'OPT-002',
	isChoose: true,
	bookingOptionRequestId: '',
	grandTotal: 0,
	priceList: [],
	transportLegsList: [],
	productDescription: '',
	offerValidFrom: '',
	offerValidTo: '',
	carrier: { companyName: 'Air China' } as ShipmentParty,
};
const mockOption6: BookingOptionInfo = {
	id: 'OPT-003',
	isChoose: false,
	bookingOptionRequestId: '',
	grandTotal: 0,
	priceList: [],
	transportLegsList: [],
	productDescription: '',
	offerValidFrom: '',
	offerValidTo: '',
	carrier: { companyName: 'Air China' } as ShipmentParty,
};

const mockBookingOptionDetail: BookingOptionRequestDetailObj = {
	bookingOptionList: [],
	bookingShipmentDetails: {
		id: '',
		pieceGroups: {
			id: '',
			pieceGroupCount: 0,
		},
		totalGrossWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		chargeableWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		dimensions: {
			length: 0,
			width: 0,
			height: 0,
		},
		expectedCommodity: '',
		specialHandlingCodes: [],
		textualHandlingInstructions: '',
	},
	transportLegs: [],
	bookingPreference: [],
	timePreferences: {
		earliestAcceptanceTime: '',
		latestAcceptanceTime: '',
		latestArrivalTime: '',
		timeOfAvailability: '',
	},
	unitsPreference: {
		currency: {
			currencyUnit: '',
		},
	},
	involvedParties: [],
};

const mockBookingOptionDetailForSelection: BookingOptionRequestDetailObj = {
	bookingOptionList: [mockOption4, mockOption5, mockOption6],
	bookingShipmentDetails: {
		id: '',
		pieceGroups: {
			id: '',
			pieceGroupCount: 0,
		},
		totalGrossWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		chargeableWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		dimensions: {
			length: 0,
			width: 0,
			height: 0,
		},
		expectedCommodity: '',
		specialHandlingCodes: [],
		textualHandlingInstructions: '',
	},
	transportLegs: [],
	bookingPreference: [],
	timePreferences: {
		earliestAcceptanceTime: '',
		latestAcceptanceTime: '',
		latestArrivalTime: '',
		timeOfAvailability: '',
	},
	unitsPreference: {
		currency: {
			currencyUnit: '',
		},
	},
	involvedParties: [],
};

const mockDialogData: BookingOptionRequestListObj = {
	id: '',
	bookingOptionRequestId: '1111',
	expectedCommodity: '',
	departureLocation: '',
	arrivalLocation: '',
	latestStatus: '',
};

describe('BookingOptionDetailComponent', () => {
	let component: BookingOptionDetailComponent;
	let fixture: ComponentFixture<BookingOptionDetailComponent>;
	let mockService: jasmine.SpyObj<BookingOptionRequestService>;
	let mockRequestService: jasmine.SpyObj<BookingRequestService>;
	let mockProfileService: jasmine.SpyObj<UserProfileService>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<BookingOptionDetailComponent>>;

	beforeEach(async () => {
		mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
		mockService = jasmine.createSpyObj('BookingOptionRequestService', ['getDataPerPage', 'getCodeByType', 'getBookingOptionDetail']);
		mockService.getCodeByType.and.returnValue(of([]));
		mockService.getBookingOptionDetail.and.returnValue(of(mockBookingOptionDetail));
		mockRequestService = jasmine.createSpyObj('BookingRequestService', ['createBookingRequest']);

		mockProfileService = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		mockProfileService.hasPermission.and.returnValue(of(true));
		mockProfileService.hasSomeRole.and.returnValue(of(true));
		await TestBed.configureTestingModule({
			imports: [BookingOptionDetailComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: BookingOptionRequestService, useValue: mockService },
				{ provide: BookingRequestService, useValue: mockRequestService },
				{ provide: UserProfileService, useValue: mockProfileService },
				{ provide: MAT_DIALOG_DATA, useValue: mockDialogData },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		})
			.overrideComponent(BookingOptionDetailComponent, {
				remove: { imports: [BookingOptionRequestDetailComponent] },
				add: { imports: [FakeSubComponent] },
			})
			.compileComponents();

		fixture = TestBed.createComponent(BookingOptionDetailComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();

		const mockUser = createMockUserProfile({ primaryOrgId: 'org123' });
		spyOn(component, 'getCurrentUser').and.returnValue(of(mockUser));
		const childDebugElement = fixture.debugElement.query(By.css('orll-booking-option-request-detail'));
		if (childDebugElement) {
			const childComponent = childDebugElement.componentInstance;

			spyOn(childComponent, 'getCurrentUser').and.returnValue(of(mockUser));
		}
	});

	afterEach(() => {
		fixture.destroy();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('createBooking', () => {
		beforeEach(() => {
			// Set up component state for testing createBooking
			component.selectedOption = {
				id: 'OPT-001',
				airlinesName: 'Air China',
				totalPrice: {
					numericalValue: 1200,
					currencyUnit: 'USD',
				},
				pieceList: [],
				transportVoList: [
					{
						departureLocation: 'Shanghai',
						arrivalLocation: 'Beijing',
						departureDate: new Date('2025-01-01 08:00:00').toISOString(),
						arrivalDate: new Date('2025-01-01 10:00:00').toISOString(),
						carrier: 'CA123',
						sequenceNumber: 1,
					},
				],
			} as BookingAirlineDetailObj;

			component.bookingOptionDetail = {
				bookingShipmentDetails: {
					id: 'SHIP-001',
					pieceGroups: {
						id: '',
						pieceGroupCount: 0,
					},
					totalGrossWeight: {
						currencyUnit: '',
						numericalValue: 0,
					},
					chargeableWeight: {
						currencyUnit: '',
						numericalValue: 0,
					},
					dimensions: {
						length: 0,
						width: 0,
						height: 0,
					},
					expectedCommodity: 'Electronics',
					specialHandlingCodes: [],
					textualHandlingInstructions: '',
				},
				transportLegs: [],
				bookingPreference: [],
				timePreferences: {
					earliestAcceptanceTime: '',
					latestAcceptanceTime: '',
					latestArrivalTime: '',
					timeOfAvailability: '',
				},
				unitsPreference: {
					currency: {
						currencyUnit: 'USD',
					},
				},
				involvedParties: [],
			};

			component.bookingRequestDetail = {
				totalGrossWeight: '100',
				chargeableWeight: '100',
				dimLength: '10',
				dimWidth: '10',
				dimHeight: '10',
				pieceGroupCount: '1',
				expectedCommodity: 'Electronics',
				specialHandlingCodes: '',
				textualHandlingInstructions: '',
				departureLocation: 'Shanghai',
				arrivalLocation: 'Beijing',
				maxSegments: '1',
				preferredTransportId: '',
				earliestAcceptanceTime: '',
				latestAcceptanceTime: '',
				latestArrivalTime: '',
				timeOfAvailability: '',
				currency: 'USD',
				iataCargoAgentCode: '',
			};
		});

		it('should create a booking request successfully', () => {
			const expectedPayload: BookingRequestObj = {
				waybillPrefix: '',
				waybillNumber: '',
				bookingOptionId: 'OPT-001',
				bookingShipmentId: 'SHIP-001',
				airlineName: 'Air China',
				requestedProduct: 'Electronics',
				requestedFlight: 'CA123',
				departureLocation: 'Shanghai',
				arrivalLocation: 'Beijing',
				flightDate: '2025-01-01 08:00:00',
			};

			mockRequestService.createBookingRequest.and.returnValue(of('success'));

			component.dataLoading = true;
			component.createBooking();

			expect(mockRequestService.createBookingRequest).toHaveBeenCalledWith(expectedPayload);
			expect(mockDialogRef.close).toHaveBeenCalledWith(true);
			expect(component.dataLoading).toBeFalse();
		});

		it('should handle booking request error', () => {
			mockRequestService.createBookingRequest.and.returnValue(throwError(() => new Error('Failed')));

			component.dataLoading = true;
			component.createBooking();

			expect(mockRequestService.createBookingRequest).toHaveBeenCalled();
			expect(mockDialogRef.close).toHaveBeenCalledWith(false);
			expect(component.dataLoading).toBeFalse();
		});
	});

	describe('transInfo', () => {
		it('should transform booking options and populate bookingAirlineDetails', () => {
			(component as any).transBookingOpitonInfo(mockRequestDetail, 'CNY');

			expect(component.bookingAirlineDetails.length).toBe(2);

			const [detail1, detail2] = component.bookingAirlineDetails;

			expect(detail1.id).toBe('OPT-001');
			expect(detail1.airlinesName).toBe('Air China');
			expect(detail1.totalPrice.numericalValue).toBe(1200);
			expect(detail1.totalPrice.currencyUnit).toBe('CNY');
			expect(detail1.transportVoList.length).toBe(1);
			expect(detail1.transportVoList[0]).toEqual({
				departureLocation: 'Shanghai',
				arrivalLocation: 'Beijing',
				departureDate: '2025-01-01T08:00:00',
				arrivalDate: '2025-01-01T10:00:00',
				carrier: 'CA123',
				sequenceNumber: 1,
			});

			expect(detail2.id).toBe('OPT-002');
			expect(detail2.totalPrice.numericalValue).toBe(1500);
			expect(detail2.totalPrice.currencyUnit).toBe('CNY');
			expect(detail2.transportVoList.length).toBe(2);
		});

		it('should set selectedOption to the item where isChoose is true', () => {
			(component as any).transBookingOpitonInfo(mockRequestDetail, 'USD');

			expect(component.selectedOption).toBeDefined();
			expect(component.selectedOption).not.toBeNull();
			if (component.selectedOption) {
				expect(component.selectedOption.id).toBe('OPT-002');
			}
		});

		it('should not add to bookingAirlineDetails if transList is missing', () => {
			const detailWithoutTrans = {
				bookingOptionList: [{ ...mockOption1, transportLegsList: undefined }],
			};

			(component as any).transBookingOpitonInfo(detailWithoutTrans as unknown as BookingOptionRequestDetailObj, 'CNY');

			expect(component.bookingAirlineDetails.length).toBe(0);
			expect(component.selectedOption).toBeNull();
		});

		it('should not process if bookingOptionList is missing or empty', () => {
			const detailEmpty: BookingOptionRequestDetailObj = {
				bookingOptionList: [],
				bookingShipmentDetails: {
					id: '',
					pieceGroups: {
						id: '',
						pieceGroupCount: 0,
					},
					totalGrossWeight: {
						currencyUnit: '',
						numericalValue: 0,
					},
					chargeableWeight: {
						currencyUnit: '',
						numericalValue: 0,
					},
					dimensions: {
						length: 0,
						width: 0,
						height: 0,
					},
					expectedCommodity: '',
					specialHandlingCodes: [],
					textualHandlingInstructions: '',
				},
				transportLegs: [],
				bookingPreference: [],
				timePreferences: {
					earliestAcceptanceTime: '',
					latestAcceptanceTime: '',
					latestArrivalTime: '',
					timeOfAvailability: '',
				},
				unitsPreference: {
					currency: {
						currencyUnit: '',
					},
				},
				involvedParties: [],
			};
			const detailNull = {};
			(component as any).transBookingOpitonInfo(detailEmpty, 'CNY');
			expect(component.bookingAirlineDetails.length).toBe(0);

			(component as any).transBookingOpitonInfo(detailNull, 'CNY');
			expect(component.bookingAirlineDetails.length).toBe(0);
		});

		it('should stop processing after finding the first isChoose item', () => {
			const mockOption3: BookingOptionInfo = {
				id: 'OPT-003',
				productDescription: 'Test Airline',
				grandTotal: 1000,
				transportLegsList: [mockTrans1],
				isChoose: true,
				bookingOptionRequestId: '',
				priceList: [],
				offerValidFrom: '',
				offerValidTo: '',
				carrier: { companyName: 'Air China' } as ShipmentParty,
			};

			const detailWithMultipleChoose = {
				bookingOptionList: [mockOption2, mockOption3],
			};

			(component as any).transBookingOpitonInfo(detailWithMultipleChoose as BookingOptionRequestDetailObj, 'CNY');

			expect(component.bookingAirlineDetails.length).toBe(1);
		});
	});

	describe('setBookingSelectedOption', () => {
		beforeEach(() => {
			component.bookingAirlineDetails = [mockDetail1, mockDetail2, mockDetail3];
			component.bookingOptionDetail = mockBookingOptionDetailForSelection;
		});

		it('should set selected=true for matching id in bookingAirlineDetails', () => {
			component.setBookingSelectedOption('OPT-002');

			const found = component.bookingAirlineDetails.find((d) => d.id === 'OPT-002');
			expect(found).toBeTruthy();
			expect(found?.selected).toBeTrue();
		});

		it('should set selected=false for non-matching ids in bookingAirlineDetails', () => {
			component.setBookingSelectedOption('OPT-002');

			const item1 = component.bookingAirlineDetails.find((d) => d.id === 'OPT-001');
			const item3 = component.bookingAirlineDetails.find((d) => d.id === 'OPT-003');

			expect(item1?.selected).toBeFalse();
			expect(item3?.selected).toBeFalse();
		});

		it('should update isChoose=true for matching id in bookingOptionDetail.bookingOptionList', () => {
			component.setBookingSelectedOption('OPT-003');

			const option = component.bookingOptionDetail?.bookingOptionList?.find((o) => o.id === 'OPT-003');
			expect(option?.isChoose).toBeTrue();
		});

		it('should set isChoose=false for non-matching ids in bookingOptionDetail.bookingOptionList', () => {
			component.setBookingSelectedOption('OPT-001');

			const option2 = component.bookingOptionDetail?.bookingOptionList?.find((o) => o.id === 'OPT-002');
			const option3 = component.bookingOptionDetail?.bookingOptionList?.find((o) => o.id === 'OPT-003');

			expect(option2?.isChoose).toBeFalse();
			expect(option3?.isChoose).toBeFalse();
		});
	});
});
