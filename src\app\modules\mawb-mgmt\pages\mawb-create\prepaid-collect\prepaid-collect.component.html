<div class="orll-prepaid-collect">
	<div class="d-flex justify-content-between">
		<h3>{{ 'hawb.prepaidAndCollect.prepaidTitle' | translate }}</h3>
		<h3>{{ 'hawb.prepaidAndCollect.collectTitle' | translate }}</h3>
	</div>

	<div>
		<form [formGroup]="prepaidForm">
			<div class="row mb-20-px">
				<div class="col-12 relative">
					<div class="cool-input-container d-flex flex-row">
						<span class="cool-label">{{ 'hawb.prepaidAndCollect.weightCharge' | translate }}</span>
						<div class="divider">
							<div class="divider-content"></div>
						</div>
						<div class="width-half">
							<input type="number" formControlName="weightChargePrepaid"
								class="cool-input rounded-l-md left disabled"
								[placeholder]="">
						</div>
						<div class="width-half">
							<input type="number" formControlName="weightChargeCollect"
								class="cool-input rounded-r-md right disabled"
								[placeholder]="">
						</div>
					</div>
				</div>
			</div>
			<div class="row mb-20-px">
				<div class="col-12 relative">
					<div class="cool-input-container d-flex flex-row">
						<span class="cool-label">{{ 'hawb.prepaidAndCollect.valuationCharge' | translate }}</span>
						<div class="divider">
							<div class="divider-content"></div>
						</div>
						<div class="width-half">
							<input type="number" formControlName="valuationChargePrepaid"
								class="cool-input rounded-l-md left disabled"
								[placeholder]="">
						</div>
						<div class="width-half">
							<input type="number" formControlName="valuationChargeCollect"
								class="cool-input rounded-r-md right disabled"
								[placeholder]="">
						</div>
					</div>
				</div>
			</div>

			<div class="row mb-20-px">
				<div class="col-12 relative">
					<div class="cool-input-container d-flex flex-row">
						<span class="cool-label">{{ 'hawb.prepaidAndCollect.tax' | translate }}</span>
						<div class="divider">
							<div class="divider-content"></div>
						</div>
						<div class="width-half">
							<input type="number" formControlName="taxPrepaid"
								class="cool-input rounded-l-md left disabled"
								[placeholder]="">
						</div>
						<div class="width-half">
							<input type="number" formControlName="taxCollect"
								class="cool-input rounded-r-md right disabled"
								[placeholder]="">
						</div>
					</div>
				</div>
			</div>
			<div class="row mb-20-px">
				<div class="col-12 relative">
					<div class="cool-input-container d-flex flex-row">
						<span
							class="cool-label">{{ 'hawb.prepaidAndCollect.totalOtherChargesDueAgent' | translate }}</span>
						<div class="divider">
							<div class="divider-content"></div>
						</div>
						<div class="width-half">
							<input type="text" formControlName="totalOtherChargesDueAgentPrepaid"
								class="cool-input rounded-l-md left disabled"
								[placeholder]="">
						</div>
						<div class="width-half">
							<input type="text" formControlName="totalOtherChargesDueAgentCollect"
								class="cool-input rounded-r-md right disabled"
								[placeholder]="">
						</div>
					</div>
				</div>
			</div>
			<div class="row mb-20-px">
				<div class="col-12 relative">
					<div class="cool-input-container d-flex flex-row">
						<span
							class="cool-label">{{ 'hawb.prepaidAndCollect.totalOtherChargesDueCarrier' | translate }}</span>
						<div class="divider">
							<div class="divider-content"></div>
						</div>
						<div class="width-half">
							<input type="text" formControlName="totalOtherChargesDueCarrierPrepaid"
								class="cool-input rounded-l-md left disabled"
								[placeholder]="">
						</div>
						<div class="width-half">
							<input type="text" formControlName="totalOtherChargesDueCarrierCollect"
								class="cool-input rounded-r-md right disabled"
								[placeholder]="">
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<mat-form-field appearance="outline" class="col-6" floatLabel="always">
					<mat-label>{{ 'hawb.prepaidAndCollect.totalPrepaid' | translate }}</mat-label>
					<input matInput formControlName="totalPrepaid" />
					@if (prepaidForm.get('totalPrepaid')?.hasError('required')) {
						<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.prepaidAndCollect.totalPrepaid' | translate } }}</mat-error>
					}
				</mat-form-field>
				<mat-form-field appearance="outline" class="col-6" floatLabel="always">
					<mat-label>{{ 'hawb.prepaidAndCollect.totalCollect' | translate }}</mat-label>
					<input matInput formControlName="totalCollect" />
					@if (prepaidForm.get('totalCollect')?.hasError('required')) {
						<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.prepaidAndCollect.totalCollect' | translate } }}</mat-error>
					}
				</mat-form-field>
			</div>
		</form>
	</div>
</div>
