import { Component, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { OrllCopyDirective } from './orll-copy.directive';
import { CommonService } from '@shared/services/common.service';

@Component({
	template: `
		<button [orllCopy]="textToCopy">Copy <PERSON>ton</button>
		<div [orllCopy]="'static content'">Copy Div</div>
		<span [orllCopy]="null">Null Content</span>
		<a [orllCopy]="emptyText">Empty Text</a>
	`,
	imports: [OrllCopyDirective],
})
class TestComponent {
	textToCopy = 'test content';
	emptyText = '';
}

describe('OrllCopyDirective', () => {
	let fixture: ComponentFixture<TestComponent>;
	let component: TestComponent;
	let debugElements: DebugElement[];
	let directiveInstances: OrllCopyDirective[];
	let commonService: jasmine.SpyObj<CommonService>;

	const originalClipboard = navigator.clipboard;

	beforeEach(async () => {
		const commonServiceSpy = jasmine.createSpyObj('CommonService', ['showWarning']);

		await TestBed.configureTestingModule({
			imports: [TestComponent, OrllCopyDirective],
			providers: [{ provide: CommonService, useValue: commonServiceSpy }],
		}).compileComponents();

		fixture = TestBed.createComponent(TestComponent);
		component = fixture.componentInstance;

		debugElements = fixture.debugElement.queryAll(By.directive(OrllCopyDirective));
		directiveInstances = debugElements.map((de) => de.injector.get(OrllCopyDirective));

		commonService = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;

		Object.defineProperty(navigator, 'clipboard', {
			value: {
				writeText: jasmine.createSpy('writeText'),
			},
			writable: true,
		});

		fixture.detectChanges();
	});

	afterEach(() => {
		Object.defineProperty(navigator, 'clipboard', {
			value: originalClipboard,
			writable: true,
		});
	});

	it('should create directive instances', () => {
		expect(directiveInstances.length).toBe(4);
		directiveInstances.forEach((instance) => {
			expect(instance).toBeTruthy();
		});
	});

	it('should set textToCopy from input binding', () => {
		expect(directiveInstances[0].textToCopy).toBe('test content');
		expect(directiveInstances[1].textToCopy).toBe('static content');
		expect(directiveInstances[2].textToCopy).toBeNull();
		expect(directiveInstances[3].textToCopy).toBe('');
	});

	it('should show warning when text is empty', fakeAsync(() => {
		debugElements[3].nativeElement.click();
		tick();

		expect(commonService.showWarning).toHaveBeenCalledWith('common.copy.no.text');
		expect(navigator.clipboard.writeText).not.toHaveBeenCalled();
	}));

	it('should show warning when text is null', fakeAsync(() => {
		debugElements[2].nativeElement.click();
		tick();

		expect(commonService.showWarning).toHaveBeenCalledWith('common.copy.no.text');
		expect(navigator.clipboard.writeText).not.toHaveBeenCalled();
	}));

	it('should copy text successfully and show success message', fakeAsync(() => {
		(navigator.clipboard.writeText as jasmine.Spy).and.returnValue(Promise.resolve());

		debugElements[0].nativeElement.click();
		tick();

		expect(navigator.clipboard.writeText).toHaveBeenCalledWith('test content');
		expect(commonService.showWarning).toHaveBeenCalledWith('common.copy.success');
		expect(commonService.showWarning).toHaveBeenCalledTimes(1);
	}));

	it('should show failure message when copy fails', fakeAsync(() => {
		(navigator.clipboard.writeText as jasmine.Spy).and.returnValue(
			(async () => {
				throw new Error('Clipboard error');
			})()
		);

		debugElements[0].nativeElement.click();
		tick();

		expect(navigator.clipboard.writeText).toHaveBeenCalledWith('test content');
		expect(commonService.showWarning).toHaveBeenCalledWith('common.copy.failed');
		expect(commonService.showWarning).toHaveBeenCalledTimes(1);
	}));

	it('should handle different text content for different elements', fakeAsync(() => {
		(navigator.clipboard.writeText as jasmine.Spy).and.returnValue(Promise.resolve());

		debugElements[0].nativeElement.click();
		tick();

		expect(navigator.clipboard.writeText).toHaveBeenCalledWith('test content');
		expect(commonService.showWarning).toHaveBeenCalledWith('common.copy.success');

		debugElements[1].nativeElement.click();
		tick();

		expect(navigator.clipboard.writeText).toHaveBeenCalledWith('static content');
		expect(commonService.showWarning).toHaveBeenCalledWith('common.copy.success');
		expect(commonService.showWarning).toHaveBeenCalledTimes(2);
	}));

	it('should update textToCopy when input changes', () => {
		expect(directiveInstances[0].textToCopy).toBe('test content');

		component.textToCopy = 'updated content';
		fixture.detectChanges();

		expect(directiveInstances[0].textToCopy).toBe('updated content');
	});

	it('should handle multiple quick clicks', fakeAsync(() => {
		(navigator.clipboard.writeText as jasmine.Spy).and.returnValue(Promise.resolve());

		debugElements[0].nativeElement.click();
		debugElements[0].nativeElement.click();
		debugElements[0].nativeElement.click();
		tick();

		expect(navigator.clipboard.writeText).toHaveBeenCalledTimes(3);
		expect(commonService.showWarning).toHaveBeenCalledTimes(3);
	}));

	it('should work with HostListener decorator', fakeAsync(() => {
		(navigator.clipboard.writeText as jasmine.Spy).and.returnValue(Promise.resolve());

		const onClickSpy = spyOn(directiveInstances[0], 'onClick' as any).and.callThrough();

		debugElements[0].nativeElement.click();
		tick();

		expect(onClickSpy).toHaveBeenCalled();
		expect(navigator.clipboard.writeText).toHaveBeenCalledWith('test content');
	}));
});
