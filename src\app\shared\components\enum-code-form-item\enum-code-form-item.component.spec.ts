import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { EnumCodeFormItemComponent } from './enum-code-form-item.component';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FocusMonitor, _IdGenerator } from '@angular/cdk/a11y';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { EnumCodeTypeModel } from './enum-code-type.model';
import { CodeName } from '@shared/models/code-name.model';
import { of } from 'rxjs';

describe('EnumCodeFormItemComponent', () => {
	let component: EnumCodeFormItemComponent;
	let fixture: ComponentFixture<EnumCodeFormItemComponent>;
	let mockOrgMgmtService: jasmine.SpyObj<OrgMgmtRequestService>;
	let mockFocusMonitor: jasmine.SpyObj<FocusMonitor>;
	let mockIdGenerator: jasmine.SpyObj<_IdGenerator>;

	const mockEnumCodes: CodeName[] = [
		{ code: 'AIR', name: 'Airline' },
		{ code: 'APT', name: 'Airport' },
		{ code: 'AGT', name: 'Agent' },
		{ code: 'FWD', name: 'Forwarder' },
		{ code: 'SHP', name: 'Shipper' },
	];

	beforeEach(async () => {
		mockOrgMgmtService = jasmine.createSpyObj('OrgMgmtRequestService', ['getEnumCode']);
		mockFocusMonitor = jasmine.createSpyObj('FocusMonitor', ['monitor']);
		mockIdGenerator = jasmine.createSpyObj('_IdGenerator', ['getId']);

		// Reset spies before each test
		mockOrgMgmtService.getEnumCode.calls.reset();
		mockFocusMonitor.monitor.calls.reset();
		mockIdGenerator.getId.calls.reset();

		mockOrgMgmtService.getEnumCode.and.returnValue(of(mockEnumCodes));
		mockFocusMonitor.monitor.and.returnValue(of(null));
		mockIdGenerator.getId.and.returnValue('mat-input-test-id');

		await TestBed.configureTestingModule({
			imports: [EnumCodeFormItemComponent, NoopAnimationsModule],
			providers: [
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				{ provide: OrgMgmtRequestService, useValue: mockOrgMgmtService },
				{ provide: FocusMonitor, useValue: mockFocusMonitor },
				{ provide: _IdGenerator, useValue: mockIdGenerator },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(EnumCodeFormItemComponent);
		component = fixture.componentInstance;
		component.enumType = EnumCodeTypeModel.ORG_TYPE;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('Component Initialization', () => {
		it('should initialize with default values', () => {
			expect(component.enumCode).toBe('');
			expect(component.focused).toBe(false);
			expect(component.disabled).toBe(false);
			// After ngOnInit, these arrays are populated from the service
			expect(component.allEnumCodes).toEqual(mockEnumCodes);
			expect(component.filteredEnumCodes).toEqual(mockEnumCodes);
		});

		it('should have required enumType input', () => {
			expect(component.enumType).toBe(EnumCodeTypeModel.ORG_TYPE);
		});

		it('should generate unique id', () => {
			// The component uses a private uid property for the id, which is set in constructor
			// Check if the idGenerator was called
			expect(mockIdGenerator.getId).toHaveBeenCalledWith('mat-input-');

			// The id property might be undefined initially, but the uid should be set
			// Let's test the actual behavior
			expect(component['uid']).toBe('mat-input-test-id');
		});

		it('should allow setting custom id', () => {
			const customId = 'custom-id';
			component.id = customId;
			expect(component.id).toBe(customId);
		});

		it('should fallback to uid when id is set to undefined', () => {
			component.id = undefined;
			expect(component.id).toBeDefined();
		});
	});

	describe('ngOnInit', () => {
		it('should setup focus monitoring', () => {
			expect(mockFocusMonitor.monitor).toHaveBeenCalledWith(component['elementRef'], true);
		});

		it('should load enum codes on initialization', () => {
			expect(mockOrgMgmtService.getEnumCode).toHaveBeenCalledWith(EnumCodeTypeModel.ORG_TYPE);
			expect(component.allEnumCodes).toEqual(mockEnumCodes);
			expect(component.filteredEnumCodes).toEqual(mockEnumCodes);
		});

		it('should handle different enum types', () => {
			component.enumType = EnumCodeTypeModel.CONTACT_DETAIL_TYPE;
			mockOrgMgmtService.getEnumCode.calls.reset();

			component.ngOnInit();

			expect(mockOrgMgmtService.getEnumCode).toHaveBeenCalledWith(EnumCodeTypeModel.CONTACT_DETAIL_TYPE);
		});

		it('should handle service error gracefully', () => {
			// Test that the component can handle empty enum codes (simulating service error)
			component.allEnumCodes = [];
			component.filteredEnumCodes = [];

			expect(() => {
				component.onInputChange('test');
				component['filter']('test');
			}).not.toThrow();

			expect(component.enumCode).toBe('test');
			expect(component.filteredEnumCodes).toEqual([]);
		});

		it('should call onTouched when focus is lost', fakeAsync(() => {
			const onTouchedSpy = jasmine.createSpy('onTouched');
			component.onTouched = onTouchedSpy;

			mockFocusMonitor.monitor.and.returnValue(of(null));
			component.ngOnInit();
			tick();

			expect(onTouchedSpy).toHaveBeenCalled();
		}));

		it('should not call onTouched when element gains focus', () => {
			const onTouchedSpy = jasmine.createSpy('onTouched');
			component.onTouched = onTouchedSpy;

			mockFocusMonitor.monitor.and.returnValue(of('keyboard'));
			component.ngOnInit();

			expect(onTouchedSpy).not.toHaveBeenCalled();
		});
	});

	describe('ControlValueAccessor Implementation', () => {
		describe('writeValue', () => {
			it('should set enumCode when value is provided', () => {
				const testValue = 'AIR';

				component.writeValue(testValue);

				expect(component.enumCode).toBe(testValue);
			});

			it('should handle empty string', () => {
				component.writeValue('');

				expect(component.enumCode).toBe('');
			});

			it('should handle null value', () => {
				component.writeValue(null as any);

				expect(component.enumCode).toBeNull();
			});

			it('should handle undefined value', () => {
				component.writeValue(undefined as any);

				expect(component.enumCode).toBeUndefined();
			});
		});

		describe('registerOnChange', () => {
			it('should register onChange callback', () => {
				const mockCallback = jasmine.createSpy('onChange');

				component.registerOnChange(mockCallback);

				expect(component.onChanges).toBe(mockCallback);
			});
		});

		describe('registerOnTouched', () => {
			it('should register onTouched callback', () => {
				const mockCallback = jasmine.createSpy('onTouched');

				component.registerOnTouched(mockCallback);

				expect(component.onTouched).toBe(mockCallback);
			});
		});

		describe('setDisabledState', () => {
			it('should set disabled state to true', () => {
				component.setDisabledState!(true);

				expect(component.disabled).toBe(true);
			});

			it('should set disabled state to false', () => {
				component.disabled = true;

				component.setDisabledState!(false);

				expect(component.disabled).toBe(false);
			});
		});
	});

	describe('MatFormFieldControl Implementation', () => {
		describe('setDescribedByIds', () => {
			it('should set aria-describedby attribute when ids are provided', () => {
				const testIds = ['id1', 'id2', 'id3'];

				component.setDescribedByIds(testIds);

				const element = component['elementRef'].nativeElement;
				expect(element.getAttribute('aria-describedby')).toBe('id1 id2 id3');
			});

			it('should remove aria-describedby attribute when no ids are provided', () => {
				const element = component['elementRef'].nativeElement;
				element.setAttribute('aria-describedby', 'existing-id');

				component.setDescribedByIds([]);

				expect(element.hasAttribute('aria-describedby')).toBe(false);
			});

			it('should handle single id', () => {
				const testIds = ['single-id'];

				component.setDescribedByIds(testIds);

				const element = component['elementRef'].nativeElement;
				expect(element.getAttribute('aria-describedby')).toBe('single-id');
			});
		});

		describe('onContainerClick', () => {
			let mockInputElement: jasmine.SpyObj<HTMLInputElement>;

			beforeEach(() => {
				// Mock the input element
				mockInputElement = jasmine.createSpyObj('HTMLInputElement', ['focus']);
				// Use Object.defineProperty to override the readonly property
				Object.defineProperty(component, 'inputElementRef', {
					value: { nativeElement: mockInputElement },
					writable: true,
					configurable: true,
				});
			});

			it('should focus input element when not focused', () => {
				component.focused = false;

				component.onContainerClick();

				expect(mockInputElement.focus).toHaveBeenCalled();
			});

			it('should not focus input element when already focused', () => {
				component.focused = true;

				component.onContainerClick();

				expect(mockInputElement.focus).not.toHaveBeenCalled();
			});

			it('should handle missing input element gracefully', () => {
				Object.defineProperty(component, 'inputElementRef', {
					value: { nativeElement: null },
					writable: true,
					configurable: true,
				});

				expect(() => component.onContainerClick()).toThrow();
			});
		});
	});

	describe('Input Handling', () => {
		describe('onInputChange', () => {
			beforeEach(() => {
				component.allEnumCodes = mockEnumCodes;
				component.filteredEnumCodes = mockEnumCodes;
			});

			it('should call onChanges callback with new value', () => {
				const onChangesSpy = jasmine.createSpy('onChanges');
				component.onChanges = onChangesSpy;
				const testValue = 'AIR';

				component.onInputChange(testValue);

				expect(onChangesSpy).toHaveBeenCalledWith(testValue);
			});

			it('should update enumCode with new value', () => {
				const testValue = 'APT';

				component.onInputChange(testValue);

				expect(component.enumCode).toBe(testValue);
			});

			it('should filter enum codes based on input', () => {
				component.onInputChange('AI');

				expect(component.filteredEnumCodes).toEqual([{ code: 'AIR', name: 'Airline' }]);
			});

			it('should handle case-insensitive filtering', () => {
				component.onInputChange('air');

				expect(component.filteredEnumCodes).toEqual([{ code: 'AIR', name: 'Airline' }]);
			});

			it('should return empty array when no matches found', () => {
				component.onInputChange('XYZ');

				expect(component.filteredEnumCodes).toEqual([]);
			});

			it('should handle empty input', () => {
				component.onInputChange('');

				expect(component.filteredEnumCodes).toEqual(mockEnumCodes);
			});

			it('should handle null input', () => {
				// The component's filter method expects a string, so null will cause an error
				expect(() => component.onInputChange(null)).toThrow();
			});

			it('should handle undefined input', () => {
				// The component's filter method expects a string, so undefined will cause an error
				expect(() => component.onInputChange(undefined)).toThrow();
			});
		});

		describe('displayEnumName', () => {
			it('should return the input value as is', () => {
				const testValue = 'AIR';

				const result = component.displayEnumName(testValue);

				expect(result).toBe(testValue);
			});

			it('should handle empty string', () => {
				const result = component.displayEnumName('');

				expect(result).toBe('');
			});

			it('should handle special characters', () => {
				const testValue = 'TEST@#$';

				const result = component.displayEnumName(testValue);

				expect(result).toBe(testValue);
			});
		});
	});

	describe('Private Methods', () => {
		describe('filter', () => {
			beforeEach(() => {
				component.allEnumCodes = mockEnumCodes;
			});

			it('should filter codes containing the search value', () => {
				const result = component['filter']('A');

				expect(result).toEqual([
					{ code: 'AIR', name: 'Airline' },
					{ code: 'APT', name: 'Airport' },
					{ code: 'AGT', name: 'Agent' },
				]);
			});

			it('should perform case-insensitive filtering', () => {
				const result = component['filter']('air');

				expect(result).toEqual([{ code: 'AIR', name: 'Airline' }]);
			});

			it('should return all codes for empty search', () => {
				const result = component['filter']('');

				expect(result).toEqual(mockEnumCodes);
			});

			it('should return empty array for no matches', () => {
				const result = component['filter']('NOMATCH');

				expect(result).toEqual([]);
			});

			it('should handle partial matches', () => {
				const result = component['filter']('PT');

				expect(result).toEqual([{ code: 'APT', name: 'Airport' }]);
			});

			it('should handle special characters in search', () => {
				component.allEnumCodes = [...mockEnumCodes, { code: 'TEST@', name: 'Test Special' }];

				const result = component['filter']('@');

				expect(result).toEqual([{ code: 'TEST@', name: 'Test Special' }]);
			});
		});
	});

	describe('Edge Cases and Integration', () => {
		it('should handle complete workflow from initialization to selection', fakeAsync(() => {
			const onChangesSpy = jasmine.createSpy('onChanges');
			component.onChanges = onChangesSpy;

			// Initialize component
			component.ngOnInit();
			tick();

			expect(component.allEnumCodes).toEqual(mockEnumCodes);
			expect(component.filteredEnumCodes).toEqual(mockEnumCodes);

			// Filter codes
			component.onInputChange('AI');
			expect(component.filteredEnumCodes.length).toBe(1);
			expect(onChangesSpy).toHaveBeenCalledWith('AI');

			// Select a value
			component.writeValue('AIR');
			expect(component.enumCode).toBe('AIR');
		}));

		it('should handle service error and continue functioning', () => {
			// Test that the component continues to function even when service fails
			// We'll simulate this by testing the component with empty enum codes
			component.allEnumCodes = [];
			component.filteredEnumCodes = [];

			expect(() => {
				component.onInputChange('test');
				component['filter']('test');
			}).not.toThrow();

			expect(component.enumCode).toBe('test');
			expect(component.filteredEnumCodes).toEqual([]);
		});

		it('should handle disabled state correctly', () => {
			component.setDisabledState!(true);
			expect(component.disabled).toBe(true);

			// Component should still function when disabled
			component.onInputChange('AIR');
			expect(component.enumCode).toBe('AIR');
		});

		it('should handle different enum types correctly', () => {
			const enumTypes = [
				EnumCodeTypeModel.ORG_TYPE,
				EnumCodeTypeModel.CONTACT_DETAIL_TYPE,
				EnumCodeTypeModel.RATE_CLASS_CODE,
				EnumCodeTypeModel.OTHER_CHARGE_CODE,
				EnumCodeTypeModel.ENTITLEMENT_CODE,
				EnumCodeTypeModel.SERVICE_CODE,
				EnumCodeTypeModel.CHARGE_CODE,
			];

			enumTypes.forEach((enumType) => {
				component.enumType = enumType;
				mockOrgMgmtService.getEnumCode.calls.reset();

				component.ngOnInit();

				expect(mockOrgMgmtService.getEnumCode).toHaveBeenCalledWith(enumType);
			});
		});
	});
});
