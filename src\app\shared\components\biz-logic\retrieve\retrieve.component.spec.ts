import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { RetrieveComponent } from './retrieve.component';
import { CommonService } from '@shared/services/common.service';
import { RetrieveService } from '@shared/services/biz-logic/obj-retrieve/retrieve.service';
import { LogisticObjType } from '@shared/models/share-type.model';
import { of, throwError } from 'rxjs';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';

describe('RetrieveComponent', () => {
	let component: RetrieveComponent;
	let fixture: ComponentFixture<RetrieveComponent>;
	let commonService: jasmine.SpyObj<CommonService>;
	let service: jasmine.SpyObj<RetrieveService>;
	let router: jasmine.SpyObj<any>;
	let dialogRef: jasmine.SpyObj<MatDialogRef<RetrieveComponent>>;

	beforeEach(async () => {
		commonService = jasmine.createSpyObj('CommonService', ['showFormInvalid', 'showWarning']);
		service = jasmine.createSpyObj('RetrieveService', ['getObjDetail']);
		router = jasmine.createSpyObj('Router', ['navigate']);
		dialogRef = jasmine.createSpyObj<MatDialogRef<RetrieveComponent>>('MatDialogRef', ['close']);

		await TestBed.configureTestingModule({
			imports: [RetrieveComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: RetrieveService, useValue: service },
				{ provide: CommonService, useValue: commonService },
				{ provide: Router, useValue: router },
				{ provide: MatDialogRef, useValue: dialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: { loId: '1', loType: LogisticObjType.HAWB } },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(RetrieveComponent);
		component = fixture.componentInstance;
		component.retrieveForm = new FormGroup({
			uri: new FormControl<string>('', [Validators.required]),
		});
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('retrieve', () => {
		it('should show form invalid message when form is invalid', () => {
			component.retrieveForm.get('uri')?.setValue('');

			component.retrieveObj();
			expect(commonService.showFormInvalid).toHaveBeenCalled();
		});

		it('should call service and navigate to SLI edit page when data is LogisticObjType.SLI', fakeAsync(() => {
			component.retrieveForm.get('uri')?.setValue('testId');
			component.retrieveForm.markAllAsTouched();
			component.retrieveForm.get = jasmine.createSpy().and.returnValue({ value: 'testId' });
			component.data = LogisticObjType.SLI;
			service.getObjDetail.and.returnValue(of(true));

			component.retrieveObj();
			tick();

			expect(component.dataLoading).toBeFalse();
			expect(service.getObjDetail).toHaveBeenCalledWith({ loId: 'testId', type: LogisticObjType.SLI.toLocaleLowerCase() });
			expect(router.navigate).toHaveBeenCalledWith(['sli', 'edit', 'testId', 0]);
			expect(component.dataLoading).toBeFalse();
		}));

		it('should call service and navigate to generic edit page when data is not LogisticObjType.SLI', fakeAsync(() => {
			component.retrieveForm.get('uri')?.setValue('testId');
			component.retrieveForm.markAllAsTouched();
			component.retrieveForm.get = jasmine.createSpy().and.returnValue({ value: 'testId' });
			component.data = LogisticObjType.MAWB;
			service.getObjDetail.and.returnValue(of(true));

			component.retrieveObj();
			tick();

			expect(component.dataLoading).toBeFalse();
			expect(service.getObjDetail).toHaveBeenCalledWith({ loId: 'testId', type: LogisticObjType.MAWB.toLocaleLowerCase() });
			expect(router.navigate).toHaveBeenCalledWith(['mawb', 'edit', 'testId']);
			expect(component.dataLoading).toBeFalse();
		}));

		it('should handle 403 error and show warning', fakeAsync(() => {
			component.retrieveForm.get('uri')?.setValue('testId');
			component.retrieveForm.markAllAsTouched();
			component.data = LogisticObjType.HAWB;
			component.retrieveForm.get = jasmine.createSpy().and.returnValue({ value: 'testId' });
			const error = { status: 403 };
			service.getObjDetail.and.returnValue(throwError(() => error));

			component.retrieveObj();
			tick();

			expect(component.dataLoading).toBeFalse();
			expect(service.getObjDetail).toHaveBeenCalled();
			expect(commonService.showWarning).toHaveBeenCalledWith('common.no.access.error');
			expect(component.dataLoading).toBeFalse();
		}));

		it('should handle other errors without showing delegation warning', fakeAsync(() => {
			component.retrieveForm.get('uri')?.setValue('testId');
			component.retrieveForm.markAllAsTouched();
			component.data = LogisticObjType.HAWB;
			component.retrieveForm.get = jasmine.createSpy().and.returnValue({ value: 'testId' });
			const error = { status: 500 };
			service.getObjDetail.and.returnValue(throwError(() => error));

			component.retrieveObj();
			tick();

			expect(component.dataLoading).toBeFalse();
			expect(service.getObjDetail).toHaveBeenCalled();
			expect(commonService.showWarning).not.toHaveBeenCalled();
			expect(component.dataLoading).toBeFalse();
		}));
	});
});
