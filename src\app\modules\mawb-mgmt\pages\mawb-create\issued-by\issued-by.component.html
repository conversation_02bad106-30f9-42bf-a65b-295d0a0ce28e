<div class="orll-issued-by">
	<div class="orll-issued-by-form__title-container">
		<h2 class="mat-display-2 orll-issued-by-form__title-container__title">{{ 'hawb.issuedBy.title' | translate }}</h2>

		@if (issuedByInfo?.id) {
			<mat-icon
				color="primary"
				[orllCopy]="shipmentParty?.id"
				(keydown.enter)="$event.stopPropagation()"
				class="orll-issued-by-form__title-container__contact-button"
				>content_copy</mat-icon
			>
		}
	</div>
	@if (!issuedByInfo) {
		<div class="issued-by-content d-flex justify-content-center align-items-center">
			<span>{{ 'hawb.issuedBy.content' | translate }}</span>
		</div>
	} @else {
		<div class="issued-by-content">
			<div class="row">
				<div class="col-6">
					<div class="orll-issued-by__label">{{ 'sli.mgmt.company.companyName' | translate }}</div>
					<div>{{ issuedByInfo?.companyName }}</div>
				</div>
				<div class="col-6">
					<div class="orll-issued-by__label">{{ 'mawb.mgmt.airlineCode' | translate }}</div>
					<div>{{ issuedByInfo?.airlineCode }}</div>
				</div>
			</div>
			<div class="row">
				<div class="col-4">
					<div class="orll-issued-by__label">{{ 'sli.mgmt.company.country' | translate }}</div>
					<div>{{ issuedByInfo?.countryCode }}</div>
				</div>

				<div class="col-4">
					<div class="orll-issued-by__label">{{ 'sli.mgmt.company.province' | translate }}</div>
					<div>{{ issuedByInfo?.regionCode }}</div>
				</div>
				<div class="col-4">
					<div class="orll-issued-by__label">{{ 'sli.mgmt.company.city' | translate }}</div>
					<div>{{ issuedByInfo?.cityCode }}</div>
				</div>
			</div>
			<div class="row">
				<div class="col-12">
					<div class="orll-issued-by__label">{{ 'sli.mgmt.company.address' | translate }}</div>
					<div>{{ issuedByInfo?.locationName }}</div>
				</div>
			</div>
		</div>
	}
</div>
