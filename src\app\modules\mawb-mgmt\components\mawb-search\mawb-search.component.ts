import { ChangeDetectionStrategy, Component, EventEmitter, Output, QueryList, ViewChildren } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { provideNativeDateAdapter } from '@angular/material/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { CommonModule, DatePipe } from '@angular/common';
import { AutocompleteComponent } from '@shared/components/autocomplete/autocomplete.component';
import { CodeName } from '@shared/models/code-name.model';
import { SearchType } from '@shared/models/search-type.model';
import { MawbSearchPayload } from '../../models/mawb-search-payload.model';
import { MawbSearchRequestService } from '../../services/mawb-search-request.service';

const DATE_FORMAT = 'yyyy-MM-dd';

@Component({
	selector: 'orll-mawb-search',
	templateUrl: './mawb-search.component.html',
	styleUrls: ['./mawb-search.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [provideNativeDateAdapter(), DatePipe],
	imports: [
		MatInputModule,
		MatIconModule,
		MatChipsModule,
		MatButtonModule,
		MatDatepickerModule,
		TranslateModule,
		ReactiveFormsModule,
		MatAutocompleteModule,
		CommonModule,
		AutocompleteComponent,
	],
})
export class MawbSearchComponent extends DestroyRefComponent {
	@Output() searchMawb = new EventEmitter<MawbSearchPayload>();
	@ViewChildren('airlineCodeAutocomplete,latestStatusAutocomplete,departureAutocomplete,arrivalAutocomplete,mawbNumberAutocomplete')
	autocompleteList!: QueryList<AutocompleteComponent<any>>;

	mawbSearchForm: FormGroup = new FormGroup({
		goodsDescription: new FormControl<string>(''),
		startDate: new FormControl<Date | null>(null),
		endDate: new FormControl<Date | null>(null),
		eventStartDate: new FormControl<Date | null>(null),
		eventEndDate: new FormControl<Date | null>(null),
	});

	selectedAirlines: CodeName[] = [];
	selectedLatestStatus: CodeName[] = [];
	selectedDepartureLocations: CodeName[] = [];
	selectedArrivalLocations: CodeName[] = [];
	selectedMawbNumbers: CodeName[] = [];

	constructor(
		public readonly mawbSearchRequestService: MawbSearchRequestService,
		private readonly datePipe: DatePipe
	) {
		super();
	}

	// eslint-disable-next-line
	selectedItems(item: CodeName[], keyword: string): void {
		switch (keyword) {
			case SearchType.AIRLINE_CODE: {
				this.selectedAirlines = item;
				break;
			}

			case SearchType.LATEST_STATUS: {
				this.selectedLatestStatus = item;
				break;
			}

			case SearchType.ORIGIN: {
				this.selectedDepartureLocations = item;
				break;
			}

			case SearchType.DESTINATION: {
				this.selectedArrivalLocations = item;
				break;
			}

			case SearchType.MAWB: {
				this.selectedMawbNumbers = item;
				break;
			}

			default:
				break;
		}
	}

	onSearch(): void {
		const payload: MawbSearchPayload = {
			goodsDescription: this.mawbSearchForm.value.goodsDescription ?? '',
			createDateStart: this.datePipe.transform(this.mawbSearchForm.value.startDate, DATE_FORMAT) ?? null,
			createDateEnd: this.datePipe.transform(this.mawbSearchForm.value.endDate, DATE_FORMAT) ?? null,
			eventDateStart: this.datePipe.transform(this.mawbSearchForm.value.eventStartDate, DATE_FORMAT) ?? null,
			eventDateEnd: this.datePipe.transform(this.mawbSearchForm.value.eventEndDate, DATE_FORMAT) ?? null,
			airlineCodeList: this.selectedAirlines.map((airline) => airline.name),
			latestStatusList: this.selectedLatestStatus.map((latestStatus) => latestStatus.name),
			departureLocationList: this.selectedDepartureLocations.map((location) => location.code),
			arrivalLocationList: this.selectedArrivalLocations.map((location) => location.code),
			mawbNumberList: this.selectedMawbNumbers.map((mawbNumber) => mawbNumber.code),
		};
		this.searchMawb.emit(payload);
	}

	onReset(event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		this.mawbSearchForm.reset();
		this.selectedAirlines = [];
		this.selectedLatestStatus = [];
		this.selectedDepartureLocations = [];
		this.selectedArrivalLocations = [];
		this.selectedMawbNumbers = [];
		this.autocompleteList.forEach((autocomplete) => autocomplete.eraseValue(event));
	}
}
