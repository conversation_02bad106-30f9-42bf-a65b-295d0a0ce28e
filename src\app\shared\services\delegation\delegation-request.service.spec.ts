import { TestBed } from '@angular/core/testing';
import { HttpClient } from '@angular/common/http';
import { DelegationRequestService } from './delegation-request.service';
import { DelegationRequest } from '@shared/models/delegation-request';
import { of } from 'rxjs';

describe('DelegationRequestService', () => {
	let service: DelegationRequestService;
	let httpClientSpy: jasmine.SpyObj<HttpClient>;

	const mockSuccessResponse = {
		code: 200,
		msg: 'success',
		data: '123',
	};

	beforeEach(async () => {
		httpClientSpy = jasmine.createSpyObj('HttpClient', ['get', 'post', 'patch']);

		await TestBed.configureTestingModule({
			providers: [DelegationRequestService, { provide: HttpClient, useValue: httpClientSpy }],
		});
	});

	beforeEach(() => {
		service = TestBed.inject(DelegationRequestService);
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('#getDelegationList should retrieve delegation list with pagination', (done: DoneFn) => {
		const loId = 'id-123';
		const pageParams = { pageNum: 0, pageSize: 10, orderByColumn: 'id', isAsc: 'true' };
		const mockDelegationList: DelegationRequest = {
			isRequestedFor: ['123'],
			isRequestedBy: 'abc',
			isRequestedAt: '2023-01-01',
			requestStatus: 'REQUEST_PENDING',
			hasDescription: '123456',
			hasPermission: ['234'],
			hasLogisticsObject: ['345'],
		};
		const mockResponse = {
			rows: [mockDelegationList],
			total: 1,
		};
		httpClientSpy.get.and.returnValue(of(mockResponse));

		service.getDelegationList(pageParams, loId).subscribe({
			next: (response) => {
				expect(response).toEqual(mockResponse);
				expect(httpClientSpy.get).toHaveBeenCalledWith(jasmine.stringContaining('access-delegations/list'), {
					params: jasmine.any(Object),
				});
				done();
			},
			error: done.fail,
		});
	});

	it('#requestDelegation should request delegation', (done: DoneFn) => {
		const delegationRequest: DelegationRequest = {
			isRequestedFor: ['123'],
			hasDescription: '123456',
			hasPermission: ['234'],
			hasLogisticsObject: ['345'],
		};

		httpClientSpy.post.and.returnValue(of(mockSuccessResponse));

		service.requestDelegation(delegationRequest).subscribe({
			next: (response) => {
				expect(response).toEqual(mockSuccessResponse);
				expect(httpClientSpy.post).toHaveBeenCalledWith(
					jasmine.stringContaining('access-delegations'),
					jasmine.objectContaining(delegationRequest)
				);
				done();
			},
			error: done.fail,
		});
	});

	it('#getDelegationDetail should get delegation detail', (done: DoneFn) => {
		const actionRequestId = '123';
		const mockResponse = {
			id: '1',
			isRequestedFor: ['123'],
			isRequestedBy: 'abc',
			isRequestedAt: '2023-01-01',
			requestStatus: 'REQUEST_PENDING',
			hasDescription: '123456',
			hasPermission: ['234'],
			hasLogisticsObject: ['345'],
		};
		httpClientSpy.get.and.returnValue(of(mockResponse));

		service.getDelegationDetail(actionRequestId).subscribe({
			next: (response) => {
				expect(response).toEqual(mockResponse);
				expect(httpClientSpy.get).toHaveBeenCalledWith(jasmine.stringContaining('access-delegations/detail'), {
					params: jasmine.any(Object),
				});
				done();
			},
			error: done.fail,
		});
	});

	it('#approveDelegation should approve delegation', (done: DoneFn) => {
		const requestId = '123';
		httpClientSpy.patch.and.returnValue(of(mockSuccessResponse));

		service.approveDelegation(requestId).subscribe({
			next: (response) => {
				expect(response).toEqual(mockSuccessResponse);
				expect(httpClientSpy.patch).toHaveBeenCalledWith(
					jasmine.stringContaining('access-delegations/accept'),
					jasmine.objectContaining({ requestId })
				);
				done();
			},
			error: done.fail,
		});
	});

	it('#rejectDelegation should reject delegation', (done: DoneFn) => {
		const requestId = '123';
		httpClientSpy.patch.and.returnValue(of(mockSuccessResponse));

		service.rejectDelegation(requestId).subscribe({
			next: (response) => {
				expect(response).toEqual(mockSuccessResponse);
				expect(httpClientSpy.patch).toHaveBeenCalledWith(
					jasmine.stringContaining('access-delegations/reject'),
					jasmine.objectContaining({ requestId })
				);
				done();
			},
			error: done.fail,
		});
	});

	it('#revokeDelegation should revoke delegation', (done: DoneFn) => {
		const requestId = '123';
		httpClientSpy.patch.and.returnValue(of(mockSuccessResponse));

		service.revokeDelegation(requestId).subscribe({
			next: (response) => {
				expect(response).toEqual(mockSuccessResponse);
				expect(httpClientSpy.patch).toHaveBeenCalledWith(
					jasmine.stringContaining('access-delegations/revoke'),
					jasmine.objectContaining({ requestId })
				);
				done();
			},
			error: done.fail,
		});
	});
});
