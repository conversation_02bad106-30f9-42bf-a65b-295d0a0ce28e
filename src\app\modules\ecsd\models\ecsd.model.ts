import { Modules } from '@shared/models/user-role.model';

export interface EcsdObj {
	id?: string;
	securityStatus: string;
	receivedFrom: string;
	whetherExemptedForScreening?: string;
	screeningMethod: string;
	groundsForExemption: string;
	issuedBy: string;
	employeeId: string;
	issuedOn: string;
	additionalSecurityInfo: string;
	loId: string;
	loType: string;
	regulatedEntityCategory1: string;
	regulatedEntityIdentifier1: string;
	regulatedEntityCategory: string;
	regulatedEntityIdentifier: string;
	pieceList?: PieceListObj[];
	hawbList?: HawbListObj[];
	createdBy?: string;
}

export interface EcsdDialogObj {
	ecsdObj?: EcsdObj;
	loId: string;
	loType: Modules;
}

export interface EcsdSearchReq {
	securityStatus: string;
	receivedFrom: string;
	whetherExemptedForScreening: string;
	screeningMethod: string;
	issuedBy: string;
	employeeId: string;
	regulatedEntityCategory: string;
	regulatedEntityIdentifier: string;
	loId: string;
	loType: string;
	orgId: string;
	createdBy: string;
}

export interface PieceListObj {
	id: string;
	pieceId: string;
	productDescription: string;
	packageType: string;
	grossWeight: string;
	pieceQuantity: number;
	dimensions: {
		length: string;
		height: string;
		width: string;
	};
}

export interface EcsdDetailObj {
	pieceList: PieceListObj[];
}

export interface SubObjSearchReq {
	loId: string;
	loType: string;
}

export interface HawbListObj {
	id: string;
	hawbId: string;
	waybillNumber: string;
	shipper: string;
	consignee: string;
	goodsDescription: string;
	origin: string;
	destination: string;
	weight: string;
	slac: number;
}

export interface SubObj {
	pieceList: PieceListObj[];
	hawbList: HawbListObj[];
}

export const EXEMPTED_SCREEN = 'whetherExemptedForScreening';
export const SCREEM_METHOD = 'screeningMethod';
export const GROUND_EXEMPTION = 'groundsForExemption';
