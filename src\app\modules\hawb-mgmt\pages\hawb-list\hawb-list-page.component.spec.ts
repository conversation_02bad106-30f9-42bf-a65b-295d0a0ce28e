import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { MawbSearchRequestService } from '../../../mawb-mgmt/services/mawb-search-request.service';
import { of, throwError } from 'rxjs';
import { PageEvent } from '@angular/material/paginator';
import { PaginationResponse } from '@shared/models/pagination-response.model';
// eslint-disable-next-line @typescript-eslint/naming-convention
import HawbListPageComponent from './hawb-list-page.component';
import { TranslateModule } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';
import { HawbSearchPayload } from '../../models/hawb-search-payload.model';
import { HawbListObject } from '../../models/hawb-list-object.model';
import { Sort } from '@angular/material/sort';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { UserProfileService } from '@shared/services/user-profile.service';
import { ShareDialogComponent } from '@shared/components/share-dialog/share-dialog.component';
import { ShareType } from '@shared/models/share-type.model';
import { MatDialog } from '@angular/material/dialog';

describe('HawbListPageComponent', () => {
	let component: HawbListPageComponent;
	let fixture: ComponentFixture<HawbListPageComponent>;
	let hawbSearchRequestServiceSpy: jasmine.SpyObj<HawbSearchRequestService>;
	let cdrSpy: jasmine.SpyObj<ChangeDetectorRef>;
	let mockHawbRecords: any[];
	let mockPaginationResponse: PaginationResponse<any>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;
	let mockDialog: jasmine.SpyObj<MatDialog>;

	beforeEach(() => {
		// Create spies for all required services
		hawbSearchRequestServiceSpy = jasmine.createSpyObj('HawbSearchRequestService', ['getHawbList']);
		cdrSpy = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);

		// Setup mock data
		mockHawbRecords = [{ id: 1 }, { id: 2 }, { id: 3 }];
		mockPaginationResponse = {
			rows: mockHawbRecords,
			total: mockHawbRecords.length,
			pageNum: 1,
		} as PaginationResponse<any>;

		// Configure default spy behavior
		hawbSearchRequestServiceSpy.getHawbList.and.returnValue(of(mockPaginationResponse));
	});

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [HawbListPageComponent, TranslateModule.forRoot()],
			providers: [
				{
					provide: ActivatedRoute,
					useClass: class ActivatedRouteMock {},
				},
				{
					provide: HawbSearchRequestService,
					useValue: hawbSearchRequestServiceSpy,
				},
				{
					provide: ChangeDetectorRef,
					useValue: cdrSpy,
				},
				{
					provide: UserProfileService,
					useValue: userProfileServiceSpy,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				{
					provide: MatDialog,
					useValue: mockDialog,
				},
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(HawbListPageComponent);
		component = fixture.componentInstance;
		component['hawbSearchPayload'] = {} as HawbSearchPayload;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should retrieve the hawb list page with default pagination parameters when not in FHL mode', () => {
			spyOn<HawbListPageComponent, any>(component, 'getHawbListPage');
			component.isFHL = false;

			component.ngOnInit();

			expect(component['getHawbListPage']).toHaveBeenCalledWith({ pageNum: 1, pageSize: 10 });
		});

		it('should not retrieve hawb list page when in FHL mode', () => {
			spyOn<HawbListPageComponent, any>(component, 'getHawbListPage');
			component.isFHL = true;

			component.ngOnInit();

			expect(component['getHawbListPage']).not.toHaveBeenCalled();
		});
	});

	describe('ngOnChanges', () => {
		it('should update hawbSearchPayload with hawbIdList when selectedTabIndex changes to HAWB tab and hawbIdList is provided', () => {
			spyOn<HawbListPageComponent, any>(component, 'getHawbListPage');
			component.isFHL = true;
			component.hawbIdList = ['HAWB1', 'HAWB2'];
			component.selectedTabIndex = 1; // HAWB_TAB_INDEX

			component.ngOnChanges({
				selectedTabIndex: {
					currentValue: 1,
					previousValue: 0,
					firstChange: true,
					isFirstChange: () => true,
				},
			});

			expect(component.hawbSearchPayload).toEqual({ neone: true, hawbIdList: ['HAWB1', 'HAWB2'] });
			expect(component['getHawbListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should not update hawbSearchPayload when selectedTabIndex changes to non-HAWB tab', () => {
			spyOn<HawbListPageComponent, any>(component, 'getHawbListPage');
			component.isFHL = true;
			component.mawbId = 'MAWB123';
			component.selectedTabIndex = 0; // Non-HAWB tab

			component.ngOnChanges({
				selectedTabIndex: {
					currentValue: 0,
					previousValue: 1,
					firstChange: false,
					isFirstChange: () => false,
				},
			});

			expect(component['getHawbListPage']).not.toHaveBeenCalled();
		});
	});

	describe('onHawbListPageChange', () => {
		it('should update pagination parameters and retrieve the requested page', () => {
			spyOn<HawbListPageComponent, any>(component, 'getHawbListPage');
			const pageEvent: PageEvent = { pageIndex: 1, previousPageIndex: 0, pageSize: 10, length: 30 };

			component.onHawbListPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(2);
			expect(component.pageParams.pageSize).toBe(10);
			expect(component['getHawbListPage']).toHaveBeenCalledWith({ pageNum: 2, pageSize: 10 });
		});
	});

	describe('onSearch', () => {
		it('should update search payload and retrieve hawb list with current pagination', () => {
			spyOn<HawbListPageComponent, any>(component, 'getHawbListPage');
			const mockSearchPayload: HawbSearchPayload = { searchTerm: 'test' } as HawbSearchPayload;

			component.onSearch(mockSearchPayload);

			expect(component.hawbSearchPayload).toBe(mockSearchPayload);
			expect(component['getHawbListPage']).toHaveBeenCalledWith(component.pageParams);
		});
	});

	describe('onHawbListSortChange', () => {
		it('should update sort parameters when sort direction is provided', () => {
			const sortEvent: Sort = { active: 'createdAt', direction: 'asc' };

			component.onHawbListSortChange(sortEvent);

			expect(component.pageParams.orderByColumn).toBe('createdAt');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should clear sort parameters when sort direction is empty', () => {
			// First set some values
			component.pageParams.orderByColumn = 'createdAt';
			component.pageParams.isAsc = 'asc';

			const sortEvent: Sort = { active: 'createdAt', direction: '' };

			component.onHawbListSortChange(sortEvent);

			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});
	});

	describe('getHawbListPage', () => {
		let mawbSearchRequestServiceSpy: jasmine.SpyObj<MawbSearchRequestService>;

		beforeEach(() => {
			mawbSearchRequestServiceSpy = TestBed.inject(MawbSearchRequestService) as jasmine.SpyObj<MawbSearchRequestService>;
			spyOn(mawbSearchRequestServiceSpy, 'getTotalPieceQuantity').and.returnValue(
				of({
					totalQuantity: 100,
					totalSlac: 50,
				})
			);
		});

		it('should set dataLoading to true and clear hawbList before making the request', () => {
			// Setup
			component.dataLoading = false;
			component.hawbList = [{} as any];

			// We need to modify the spy to capture the state before the observable completes
			hawbSearchRequestServiceSpy.getHawbList.and.callFake(() => {
				// Check state right after the method call but before observable completes
				expect(component.dataLoading).toBeTrue();
				expect(component.hawbList.length).toBe(0);
				return of(mockPaginationResponse);
			});

			// Execute
			component['getHawbListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(hawbSearchRequestServiceSpy.getHawbList).toHaveBeenCalled();
		});

		it('should retrieve a page of hawb list and update component properties on success', () => {
			// Execute
			component['getHawbListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(hawbSearchRequestServiceSpy.getHawbList).toHaveBeenCalledWith({ pageNum: 1, pageSize: 10 }, {});
			expect(component.hawbList).toEqual(mockHawbRecords);
			expect(component.hawbListTotalRecords).toEqual(mockHawbRecords.length);
			expect(component.dataLoading).toBeFalse();
		});

		it('should emit hawbListChange and fetch total quantities when in FHL mode', () => {
			// Setup
			component.isFHL = true;
			component.mawbId = 'MAWB123';
			spyOn(component.hawbListChange, 'emit');

			// Execute
			component['getHawbListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(component.hawbListChange.emit).toHaveBeenCalledWith(mockHawbRecords);
			expect(mawbSearchRequestServiceSpy.getTotalPieceQuantity).toHaveBeenCalledWith('MAWB123');
			expect(component.totalQuantity).toBe(100);
			expect(component.totalSlac).toBe(50);
			expect(component.dataLoading).toBeFalse();
		});

		it('should not fetch total quantities when in FHL mode but no mawbId is provided', () => {
			// Setup
			component.isFHL = true;
			component.mawbId = '';
			spyOn(component.hawbListChange, 'emit');

			// Execute
			component['getHawbListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(component.hawbListChange.emit).toHaveBeenCalledWith(mockHawbRecords);
			expect(mawbSearchRequestServiceSpy.getTotalPieceQuantity).not.toHaveBeenCalled();
			expect(component.dataLoading).toBeFalse();
		});

		it('should set existMawb to false in hawbSearchPayload when fromCreateMawb is true and no mawbId or hawbIdList', () => {
			// Setup
			component.fromCreateMawb = true;
			component.mawbId = '';
			component.hawbIdList = [];

			// Execute
			component['getHawbListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(hawbSearchRequestServiceSpy.getHawbList).toHaveBeenCalledWith({ pageNum: 1, pageSize: 10 }, { existMawb: false });
		});

		it('should handle errors and display a snackbar notification', () => {
			const errorResponse = { message: 'something went wrong' };
			hawbSearchRequestServiceSpy.getHawbList.and.returnValue(throwError(() => errorResponse));

			component['getHawbListPage']({ pageNum: 1, pageSize: 10 });

			expect(component.dataLoading).toBeFalse();
		});

		it('should handle 403 error and trigger delegation request', () => {
			const errorResponse = { status: 403, error: { code: 403, msg: 'Unauthorized', data: '123' } };
			hawbSearchRequestServiceSpy.getHawbList.and.returnValue(throwError(() => errorResponse));
			spyOn(component, 'delegationRequest').and.returnValue(of(true));
			spyOn(component.refreshDelegationRequest, 'emit');

			component['getHawbListPage']({ pageNum: 1, pageSize: 10 });

			expect(component.dataLoading).toBeFalse();
			expect(component.delegationRequest).toHaveBeenCalledWith(errorResponse.error, component.mawbId);
			expect(component.refreshDelegationRequest.emit).toHaveBeenCalled();
		});

		it('should not emit refreshDelegationRequest when delegation request returns false', () => {
			const errorResponse = { status: 403, error: { code: 403, msg: 'Unauthorized', data: '123' } };
			hawbSearchRequestServiceSpy.getHawbList.and.returnValue(throwError(() => errorResponse));
			spyOn(component, 'delegationRequest').and.returnValue(of(false));
			spyOn(component.refreshDelegationRequest, 'emit');

			component['getHawbListPage']({ pageNum: 1, pageSize: 10 });

			expect(component.delegationRequest).toHaveBeenCalledWith(errorResponse.error, component.mawbId);
			expect(component.refreshDelegationRequest.emit).not.toHaveBeenCalled();
		});
	});

	describe('onHawbListObjectShare', () => {
		it('should open share dialog with correct configuration', () => {
			const mockHawbObject = { hawbId: 'HAWB123' } as HawbListObject;
			const mockTranslation = 'Share HAWB';
			spyOn(component['translate'], 'instant').and.returnValue(mockTranslation);

			component.onHawbListObjectShare(mockHawbObject);

			expect(mockDialog.open).toHaveBeenCalledWith(ShareDialogComponent, {
				width: '60vw',
				autoFocus: false,
				data: {
					title: mockTranslation,
					shareType: ShareType.HAWB,
					param: mockHawbObject.hawbId,
				},
			});
		});

		it('should use correct translation key for dialog title', () => {
			const mockHawbObject = { hawbId: 'HAWB123' } as HawbListObject;
			spyOn(component['translate'], 'instant');

			component.onHawbListObjectShare(mockHawbObject);

			expect(component['translate'].instant).toHaveBeenCalledWith('hawb.share.title');
		});
	});
});
