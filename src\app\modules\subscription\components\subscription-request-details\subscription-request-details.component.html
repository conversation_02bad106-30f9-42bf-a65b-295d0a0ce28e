<div class="orll-subscription-request-details">
	<h2 mat-dialog-title>
		<span>{{ 'subscription.request.detail' | translate }}</span>
		<mat-icon class="orll-subscription-request-details__clear_dialog" [matDialogClose]="'cancel'">clear_round</mat-icon>
	</h2>
	<mat-dialog-content class="iata-box">
		<div class="row">
			<div class="col-2">
				<mat-label>{{ 'subscription.request.detail' | translate }}:</mat-label>
			</div>
			<div class="col-10">
				<mat-label>{{
					subscriptionDetails?.subscriberOrgId ? data.orgMap?.get(subscriptionDetails.subscriberOrgId) : ''
				}}</mat-label>
			</div>
		</div>

		<div class="row">
			<div class="col-2">
				<mat-label>{{ 'subscription.request.table.topicType' | translate }}:</mat-label>
			</div>
			<div class="col-10">
				<mat-label>{{ subscriptionDetails?.topicType }}</mat-label>
			</div>
		</div>

		<div class="row">
			<div class="col-2">
				<mat-label>{{ 'subscription.request.table.topic' | translate }}:</mat-label>
			</div>
			<div class="col-10">
				<mat-label>{{ subscriptionDetails?.topic ?? '' }}</mat-label>
			</div>
		</div>

		<div class="row">
			<div class="col-2">
				<mat-label>Event Type:</mat-label>
			</div>
			<div class="col-10">
				@for (item of eventList; track $index) {
					<div class="orll-subscription-request-details__tag-item">
						<mat-icon [style.color]="'green'"> check_circle </mat-icon>
						<span>{{ item.eventName }}</span>
					</div>
				}
			</div>
		</div>

		<div class="row">
			<div class="col-2">
				<mat-label>{{ 'subscription.request.table.status' | translate }}:</mat-label>
			</div>
			<div class="col-10">
				<mat-label>{{ subscriptionDetails?.status ?? '' }}</mat-label>
			</div>
		</div>

		<div class="row">
			<div class="col-2">
				<mat-label>{{ 'subscription.request.table.requestBy' | translate }}:</mat-label>
			</div>
			<div class="col-4">
				<mat-label>{{ subscriptionDetails?.subscriberOrgName ?? '' }}</mat-label>
			</div>

			<div class="col-2">
				<mat-label>{{ 'subscription.request.table.requestAt' | translate }}:</mat-label>
			</div>
			<div class="col-4">
				<mat-label>{{ subscriptionDetails?.isRequestedAt | date: 'yyyy.MM.dd HH:mm' }}</mat-label>
			</div>
		</div>

		<mat-divider></mat-divider>
		<div class="mat-dialog-actions">
			@if (
				(subscriptionDetails?.status === requestStatus.PENDING || subscriptionDetails?.status === requestStatus.APPROVED) &&
				(isPublisher || isSubscriber)
			) {
				<ng-container>
					<div class="left-button">
						<button mat-stroked-button color="primary" (click)="updateSubscription('revoked')">
							<mat-icon>u_turn_left</mat-icon>
							{{ 'common.dialog.revoke' | translate }}
						</button>
					</div>
				</ng-container>
			}

			<div class="orll-subscription-request-details__btn_container">
				@if (!isPublisher || subscriptionDetails?.status !== requestStatus.PENDING) {
					<ng-container>
						<button
							mat-flat-button
							color="primary"
							[matDialogClose]="'cancel'"
							class="orll-subscription-request-details__reverse-icon">
							<mat-icon>done</mat-icon>
							{{ 'common.dialog.ok' | translate }}
						</button>
					</ng-container>
				}

				@if (subscriptionDetails?.status === requestStatus.PENDING && isPublisher) {
					<ng-container>
						<button color="primary" mat-button (click)="updateSubscription('rejected')">
							{{ 'common.dialog.reject' | translate }}
						</button>
						<button
							mat-flat-button
							color="primary"
							class="orll-subscription-request-details__reverse-icon"
							(click)="updateSubscription('approved')">
							<mat-icon>done</mat-icon>
							{{ 'common.dialog.approve' | translate }}
						</button>
					</ng-container>
				}
			</div>
		</div>
	</mat-dialog-content>
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
