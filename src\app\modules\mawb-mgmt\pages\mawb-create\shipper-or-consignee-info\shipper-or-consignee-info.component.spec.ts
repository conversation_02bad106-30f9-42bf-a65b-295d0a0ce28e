import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { SimpleChanges } from '@angular/core';
import { of } from 'rxjs';

import { ShipperOrConsigneeInfoComponent } from './shipper-or-consignee-info.component';
import { SliCreateRequestService } from 'src/app/modules/sli-mgmt/services/sli-create-request.service';
import { MawbCreateRequestService } from '../../../services/mawb-create-request.service';
import { Country } from 'src/app/modules/sli-mgmt/models/country.model';
import { Province } from 'src/app/modules/sli-mgmt/models/province.model';
import { CodeName } from '@shared/models/code-name.model';
import { ShipmentParty } from 'src/app/modules/sli-mgmt/models/shipment-party.model';
import { OrgInfo } from '@shared/models/org-info.model';
import { OrgType } from '@shared/models/org-type.model';
import { HawbListObject } from 'src/app/modules/hawb-mgmt/models/hawb-list-object.model';
import { HawbCreateDto } from 'src/app/modules/hawb-mgmt/models/hawb-create.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';

describe('ShipperOrConsigneeInfoComponent', () => {
	let component: ShipperOrConsigneeInfoComponent;
	let fixture: ComponentFixture<ShipperOrConsigneeInfoComponent>;
	let sliCreateRequestServiceMock: jasmine.SpyObj<SliCreateRequestService>;
	let mawbCreateRequestServiceMock: jasmine.SpyObj<MawbCreateRequestService>;

	// Mock data
	const mockCountries: Country[] = [
		{ code: 'US', name: 'United States', provinces: [] },
		{ code: 'CA', name: 'Canada', provinces: [] },
		{ code: 'CN', name: 'China', provinces: [] },
	];

	const mockProvinces: Province[] = [
		{ code: 'NY', name: 'New York', cities: [] },
		{ code: 'CA', name: 'California', cities: [] },
	];

	const mockCities: CodeName[] = [
		{ code: 'NYC', name: 'New York City' },
		{ code: 'BUF', name: 'Buffalo' },
	];

	const mockShipmentParty: ShipmentParty = {
		companyName: 'Test Company',
		contactName: 'John Doe',
		countryCode: 'US',
		regionCode: 'NY',
		cityCode: 'NYC',
		textualPostCode: '10001',
		locationName: '123 Test Street',
		phoneNumber: '+1234567890',
		emailAddress: '<EMAIL>',
		companyType: OrgType.SHIPPER,
	};

	const mockOrgInfo: OrgInfo = {
		id: '1',
		companyName: 'Test Org',
		partyRole: OrgType.SHIPPER,
		countryCode: 'US',
		locationName: '456 Org Street',
		regionCode: 'CA',
		textualPostCode: '90210',
		cityCode: 'LAX',
		persons: [
			{
				contactName: 'Jane Smith',
				contactRole: OrgType.CUSTOMER_CONTACT,
				jobTitle: 'Manager',
				employeeId: 'EMP001',
				phoneNumber: '+0987654321',
				emailAddress: '<EMAIL>',
			},
		],
	};

	const mockHawbList: HawbListObject[] = [
		{
			hawbId: 'hawb1',
			shipper: 'Shipper A',
			consignee: 'Consignee A',
			hawbNumber: 'HAWB001',
			goodsDescription: 'Test Goods A',
			origin: 'NYC',
			destination: 'LAX',
			mawbNumber: 'MAWB001',
			createDate: '2024-01-01',
			orgId: 'org1',
			sliId: 'sli1',
			mawbId: 'mawb1',
		},
		{
			hawbId: 'hawb2',
			shipper: 'Shipper B',
			consignee: 'Consignee B',
			hawbNumber: 'HAWB002',
			goodsDescription: 'Test Goods B',
			origin: 'LAX',
			destination: 'NYC',
			mawbNumber: 'MAWB002',
			createDate: '2024-01-02',
			orgId: 'org2',
			sliId: 'sli2',
			mawbId: 'mawb2',
		},
	];

	const mockHawbCreateDto: HawbCreateDto = {
		orgId: 'test-org',
		waybillPrefix: '123',
		waybillNumber: '********',
		partyList: [],
		sliPartyList: [
			{
				companyName: 'HAWB Company',
				contactName: 'HAWB Contact',
				countryCode: 'CA',
				regionCode: 'ON',
				cityCode: 'TOR',
				textualPostCode: 'M5V 3A8',
				locationName: '789 HAWB Street',
				phoneNumber: '+**********',
				emailAddress: '<EMAIL>',
				companyType: OrgType.SHIPPER,
				iataCargoAgentCode: 'IATA123',
			},
		],
		accountingInformation: 'Test accounting',
		departureLocation: 'YYZ',
		arrivalLocation: 'LAX',
		insuredAmount: { currencyUnit: 'USD', numericalValue: 1000 },
		weightValuationIndicator: 'P',
		otherChargesIndicator: 'P',
		declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 500 },
		declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 500 },
		textualHandlingInstructions: 'Handle with care',
		totalGrossWeight: 100,
		rateClassCode: 'N',
		totalVolumetricWeight: 50,
		rateCharge: { currencyUnit: 'USD', numericalValue: 200 },
		goodsDescriptionForRate: 'General cargo',
		otherChargeList: [],
		carrierDeclarationDate: '2024-01-01',
		carrierDeclarationPlace: 'Toronto',
		consignorDeclarationSignature: 'Consignor',
		carrierDeclarationSignature: 'Carrier',
	};

	beforeEach(async () => {
		const sliCreateRequestServiceSpy = jasmine.createSpyObj('SliCreateRequestService', ['getCountries', 'getProvinces', 'getCities']);
		const mawbCreateRequestServiceSpy = jasmine.createSpyObj('MawbCreateRequestService', ['getHawbDetail']);

		await TestBed.configureTestingModule({
			imports: [ShipperOrConsigneeInfoComponent],
			providers: [
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceSpy },
				{ provide: MawbCreateRequestService, useValue: mawbCreateRequestServiceSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(ShipperOrConsigneeInfoComponent);
		component = fixture.componentInstance;
		sliCreateRequestServiceMock = TestBed.inject(SliCreateRequestService) as jasmine.SpyObj<SliCreateRequestService>;
		mawbCreateRequestServiceMock = TestBed.inject(MawbCreateRequestService) as jasmine.SpyObj<MawbCreateRequestService>;

		// Setup default mock returns
		sliCreateRequestServiceMock.getCountries.and.returnValue(of(mockCountries));
		sliCreateRequestServiceMock.getProvinces.and.returnValue(of(mockProvinces));
		sliCreateRequestServiceMock.getCities.and.returnValue(of(mockCities));
		mawbCreateRequestServiceMock.getHawbDetail.and.returnValue(of(mockHawbCreateDto));
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('Component Initialization', () => {
		it('should initialize form with correct default values', () => {
			expect(component.shipperConsigneeForm).toBeDefined();
			expect(component.shipperConsigneeForm.get('companyName')?.value).toBe('');
			expect(component.shipperConsigneeForm.get('accountingNoteText')?.value).toBe('');
			expect(component.shipperConsigneeForm.get('contactName')?.value).toBe('');
			expect(component.shipperConsigneeForm.get('countryCode')?.value).toBe('');
			expect(component.shipperConsigneeForm.get('regionCode')?.value).toBe('');
			expect(component.shipperConsigneeForm.get('cityCode')?.value).toBe('');
			expect(component.shipperConsigneeForm.get('textualPostCode')?.value).toBe('');
			expect(component.shipperConsigneeForm.get('locationName')?.value).toBe('');
			expect(component.shipperConsigneeForm.get('phoneNumber')?.value).toBe('');
			expect(component.shipperConsigneeForm.get('emailAddress')?.value).toBe('');
		});

		it('should initialize form with correct validators', () => {
			const form = component.shipperConsigneeForm;

			expect(form.get('companyName')?.hasError('required')).toBeTruthy();
			expect(form.get('countryCode')?.hasError('required')).toBeTruthy();
			expect(form.get('regionCode')?.hasError('required')).toBeTruthy();
			expect(form.get('cityCode')?.hasError('required')).toBeTruthy();
			expect(form.get('locationName')?.hasError('required')).toBeTruthy();
			expect(form.get('phoneNumber')?.hasError('required')).toBeTruthy();

			// Test email validator
			form.get('emailAddress')?.setValue('invalid-email');
			expect(form.get('emailAddress')?.hasError('email')).toBeTruthy();

			form.get('emailAddress')?.setValue('<EMAIL>');
			expect(form.get('emailAddress')?.hasError('email')).toBeFalsy();
		});

		it('should initialize arrays and properties', () => {
			expect(component.countries).toEqual([]);
			expect(component.provinces).toEqual([]);
			expect(component.cities).toEqual([]);
			expect(component.filteredCountries).toEqual([]);
			expect(component.filteredProvinces).toEqual([]);
			expect(component.filteredCities).toEqual([]);
			expect(component.isOpen).toBeFalse();
		});

		it('should call initRefData and setupAutocomplete on ngOnInit', () => {
			spyOn(component as any, 'initRefData');
			spyOn(component as any, 'setupAutocomplete');

			component.ngOnInit();

			expect((component as any).initRefData).toHaveBeenCalled();
			expect((component as any).setupAutocomplete).toHaveBeenCalled();
		});
	});

	describe('Reference Data Loading', () => {
		it('should load countries on initialization', () => {
			fixture.detectChanges();

			expect(sliCreateRequestServiceMock.getCountries).toHaveBeenCalled();
			expect(component.countries).toEqual(mockCountries);
			expect(component.filteredCountries).toEqual(mockCountries);
		});
	});

	describe('Input Changes', () => {
		it('should call fillShipmentInfo when shipmentParty input changes', () => {
			spyOn(component, 'fillShipmentInfo');

			const changes: SimpleChanges = {
				shipmentParty: {
					currentValue: mockShipmentParty,
					previousValue: null,
					firstChange: true,
					isFirstChange: () => true,
				},
			};

			component.ngOnChanges(changes);

			expect(component.fillShipmentInfo).toHaveBeenCalled();
		});

		it('should not call fillShipmentInfo when other inputs change', () => {
			spyOn(component, 'fillShipmentInfo');

			const changes: SimpleChanges = {
				title: {
					currentValue: 'shipper',
					previousValue: '',
					firstChange: true,
					isFirstChange: () => true,
				},
			};

			component.ngOnChanges(changes);

			expect(component.fillShipmentInfo).not.toHaveBeenCalled();
		});
	});

	describe('Unique Parties', () => {
		it('should return unique parties for shipper', () => {
			const result = component.getUniqueParties(mockHawbList, 'shipper');

			expect(result.length).toBe(2);
			expect(result[0].shipper).toBe('Shipper A');
			expect(result[1].shipper).toBe('Shipper B');
		});

		it('should return unique parties for consignee', () => {
			const result = component.getUniqueParties(mockHawbList, 'consignee');

			expect(result.length).toBe(2);
			expect(result[0].consignee).toBe('Consignee A');
			expect(result[1].consignee).toBe('Consignee B');
		});

		it('should filter out duplicate parties', () => {
			const duplicateList: HawbListObject[] = [
				...mockHawbList,
				{
					...mockHawbList[0],
					hawbId: 'hawb3',
				},
			];

			const result = component.getUniqueParties(duplicateList, 'shipper');

			expect(result.length).toBe(2);
		});
	});

	describe('Party Selection', () => {
		it('should select party and fill form data', () => {
			spyOn(component, 'fillShipmentInfo');

			component.onSelectParty('hawb1', 'shipper');

			expect(mawbCreateRequestServiceMock.getHawbDetail).toHaveBeenCalledWith('hawb1');
			expect(component.shipmentParty).toEqual(mockHawbCreateDto.sliPartyList![0]);
			expect(component.fillShipmentInfo).toHaveBeenCalled();
			expect(component.isOpen).toBeFalse();
		});

		it('should handle empty sliPartyList', () => {
			const emptyHawbDto: HawbCreateDto = {
				...mockHawbCreateDto,
				sliPartyList: [],
			};
			mawbCreateRequestServiceMock.getHawbDetail.and.returnValue(of(emptyHawbDto));

			component.onSelectParty('hawb1', 'shipper');

			expect(component.shipmentParty).toEqual(null);
		});

		it('should handle null sliPartyList', () => {
			const nullHawbDto: HawbCreateDto = {
				...mockHawbCreateDto,
				sliPartyList: undefined,
			};
			mawbCreateRequestServiceMock.getHawbDetail.and.returnValue(of(nullHawbDto));

			component.onSelectParty('hawb1', 'shipper');

			expect(component.isOpen).toBeFalse();
		});
	});

	describe('Type Guards and Data Transformation', () => {
		it('should identify OrgInfo correctly', () => {
			const isOrgInfo = (component as any).isOrgInfo(mockOrgInfo);
			expect(isOrgInfo).toBeTruthy();
		});

		it('should identify ShipmentParty correctly', () => {
			const isOrgInfo = (component as any).isOrgInfo(mockShipmentParty);
			expect(isOrgInfo).toBeFalsy();
		});

		it('should transform OrgInfo to ShipmentParty', () => {
			const result = (component as any).getShipmentInfo(mockOrgInfo);

			expect(result.companyName).toBe(mockOrgInfo.companyName);
			expect(result.contactName).toBe(mockOrgInfo.persons[0].contactName);
			expect(result.countryCode).toBe(mockOrgInfo.countryCode);
			expect(result.regionCode).toBe(mockOrgInfo.regionCode);
			expect(result.cityCode).toBe(mockOrgInfo.cityCode);
			expect(result.textualPostCode).toBe(mockOrgInfo.textualPostCode);
			expect(result.locationName).toBe(mockOrgInfo.locationName);
			expect(result.phoneNumber).toBe(mockOrgInfo.persons[0].phoneNumber);
			expect(result.emailAddress).toBe(mockOrgInfo.persons[0].emailAddress);
			expect(result.companyType).toBe(mockOrgInfo.partyRole);
		});

		it('should handle OrgInfo without customer contact', () => {
			const orgInfoWithoutContact: OrgInfo = {
				...mockOrgInfo,
				persons: [
					{
						contactName: 'Other Contact',
						contactRole: 'OTHER_ROLE',
						jobTitle: 'Manager',
						employeeId: 'EMP002',
						phoneNumber: '+1111111111',
						emailAddress: '<EMAIL>',
					},
				],
			};

			const result = (component as any).getShipmentInfo(orgInfoWithoutContact);

			expect(result.contactName).toBe('');
			expect(result.phoneNumber).toBe('');
			expect(result.emailAddress).toBe('');
		});

		it('should return ShipmentParty as is', () => {
			const result = (component as any).getShipmentInfo(mockShipmentParty);

			expect(result).toEqual(mockShipmentParty);
		});
	});

	describe('Form Data Filling', () => {
		it('should fill form with shipment party data', () => {
			component.shipmentParty = mockShipmentParty;
			component.countries = mockCountries;

			spyOn(component as any, 'setupCountryValueChange');
			spyOn(component as any, 'setupRegionValueChange');

			component.fillShipmentInfo();

			expect((component as any).setupCountryValueChange).toHaveBeenCalledWith(mockShipmentParty.countryCode);
			expect((component as any).setupRegionValueChange).toHaveBeenCalledWith(mockShipmentParty.regionCode);
			expect(component.shipperConsigneeForm.get('companyName')?.value).toBe(mockShipmentParty.companyName);
			expect(component.shipperConsigneeForm.get('contactName')?.value).toBe(mockShipmentParty.contactName);
			expect(component.shipperConsigneeForm.get('countryCode')?.value).toBe(mockShipmentParty.countryCode);
		});

		it('should not fill form when shipmentParty is null', () => {
			component.shipmentParty = null;

			spyOn(component as any, 'setupCountryValueChange');
			spyOn(component as any, 'setupRegionValueChange');

			component.fillShipmentInfo();

			expect((component as any).setupCountryValueChange).not.toHaveBeenCalled();
			expect((component as any).setupRegionValueChange).not.toHaveBeenCalled();
		});

		it('should fill form with OrgInfo data', () => {
			component.shipmentParty = mockOrgInfo;
			component.countries = mockCountries;

			spyOn(component as any, 'setupCountryValueChange');
			spyOn(component as any, 'setupRegionValueChange');

			component.fillShipmentInfo();

			expect(component.shipperConsigneeForm.get('companyName')?.value).toBe(mockOrgInfo.companyName);
			expect(component.shipperConsigneeForm.get('contactName')?.value).toBe(mockOrgInfo.persons[0].contactName);
		});
	});

	describe('Autocomplete Functionality', () => {
		beforeEach(() => {
			component.countries = mockCountries;
			fixture.detectChanges();
		});

		it('should filter countries based on search input', () => {
			const countryControl = component.shipperConsigneeForm.get('countryCode');

			countryControl?.setValue('United');

			expect(component.filteredCountries.length).toBe(1);
			expect(component.filteredCountries[0].name).toBe('United States');
		});

		it('should show all countries when search is empty', () => {
			const countryControl = component.shipperConsigneeForm.get('countryCode');

			countryControl?.setValue('');

			expect(component.filteredCountries.length).toBe(mockCountries.length);
		});

		it('should handle case insensitive search', () => {
			const countryControl = component.shipperConsigneeForm.get('countryCode');

			countryControl?.setValue('CANADA');

			expect(component.filteredCountries.length).toBe(1);
			expect(component.filteredCountries[0].name).toBe('Canada');
		});

		it('should handle null search value', () => {
			const countryControl = component.shipperConsigneeForm.get('countryCode');

			countryControl?.setValue(null);

			expect(component.filteredCountries.length).toBe(mockCountries.length);
		});
	});

	describe('Display Functions', () => {
		beforeEach(() => {
			component.countries = mockCountries;
			component.provinces = mockProvinces;
			component.cities = mockCities;
		});

		it('should display country name by code', () => {
			const result = component.displayCountryName('US');
			expect(result).toBe('United States');
		});

		it('should return empty string for unknown country code', () => {
			const result = component.displayCountryName('UNKNOWN');
			expect(result).toBe('');
		});

		it('should display province name by code', () => {
			const result = component.displayProvinceName('NY');
			expect(result).toBe('New York');
		});

		it('should return empty string for unknown province code', () => {
			const result = component.displayProvinceName('UNKNOWN');
			expect(result).toBe('');
		});

		it('should display city name by code', () => {
			const result = component.displayCityName('NYC');
			expect(result).toBe('New York City');
		});

		it('should return empty string for unknown city code', () => {
			const result = component.displayCityName('UNKNOWN');
			expect(result).toBe('');
		});
	});

	describe('Country Value Change', () => {
		beforeEach(() => {
			component.countries = mockCountries;
		});

		it('should setup provinces when country changes', () => {
			const mockEvent = {
				option: { value: 'US' },
			} as MatAutocompleteSelectedEvent;

			component.countryValueChange(mockEvent);

			expect(sliCreateRequestServiceMock.getProvinces).toHaveBeenCalled();
			expect(component.shipperConsigneeForm.get('regionCode')?.value).toBe('');
			expect(component.shipperConsigneeForm.get('cityCode')?.value).toBe('');
		});

		it('should handle empty event', () => {
			component.countryValueChange();

			expect(component.shipperConsigneeForm.get('regionCode')?.value).toBe('');
			expect(component.shipperConsigneeForm.get('cityCode')?.value).toBe('');
		});

		it('should setup province autocomplete after loading provinces', () => {
			component.countries = mockCountries;

			(component as any).setupCountryValueChange('US');

			expect(sliCreateRequestServiceMock.getProvinces).toHaveBeenCalled();
			expect(component.provinces).toEqual(mockProvinces);
			expect(component.filteredProvinces).toEqual(mockProvinces);
		});
	});

	describe('Region Value Change', () => {
		beforeEach(() => {
			component.provinces = mockProvinces;
		});

		it('should setup cities when region changes', () => {
			const mockEvent = {
				option: { value: 'NY' },
			} as MatAutocompleteSelectedEvent;

			component.regionValueChange(mockEvent);

			expect(sliCreateRequestServiceMock.getCities).toHaveBeenCalled();
		});

		it('should handle empty event', () => {
			component.provinces = mockProvinces;

			component.regionValueChange();

			expect(sliCreateRequestServiceMock.getCities).toHaveBeenCalled();
		});

		it('should setup city autocomplete after loading cities', () => {
			component.provinces = mockProvinces;

			(component as any).setupRegionValueChange('NY');

			expect(sliCreateRequestServiceMock.getCities).toHaveBeenCalled();
			expect(component.cities).toEqual(mockCities);
			expect(component.filteredCities).toEqual(mockCities);
		});
	});

	describe('Form Data Retrieval', () => {
		beforeEach(() => {
			component.shipperConsigneeForm.patchValue({
				companyName: 'Test Company',
				contactName: 'John Doe',
				countryCode: 'US',
				regionCode: 'NY',
				cityCode: 'NYC',
				textualPostCode: '10001',
				locationName: '123 Test Street',
				phoneNumber: '+1234567890',
				emailAddress: '<EMAIL>',
			});
		});

		it('should return form data when form is valid', () => {
			component.title = 'shipper';

			const result = component.getFormData();

			expect(result).toBeTruthy();
			expect(result?.companyName).toBe('Test Company');
			expect(result?.contactName).toBe('John Doe');
			expect(result?.countryCode).toBe('US');
			expect(result?.regionCode).toBe('NY');
			expect(result?.cityCode).toBe('NYC');
			expect(result?.textualPostCode).toBe('10001');
			expect(result?.locationName).toBe('123 Test Street');
			expect(result?.phoneNumber).toBe('+1234567890');
			expect(result?.emailAddress).toBe('<EMAIL>');
			expect(result?.companyType).toBe(OrgType.SHIPPER);
		});

		it('should return null when form is invalid and ignore is false', () => {
			component.shipperConsigneeForm.get('companyName')?.setValue('');

			const result = component.getFormData(false);

			expect(result).toBeNull();
		});

		it('should return form data when form is invalid but ignore is true', () => {
			component.shipperConsigneeForm.get('companyName')?.setValue('');
			component.title = 'consignee';

			const result = component.getFormData(true);

			expect(result).toBeTruthy();
			expect(result?.companyType).toBe(OrgType.CONSIGNEE);
		});

		it('should set correct company type for shipper', () => {
			component.title = 'shipper';

			const result = component.getFormData();

			expect(result?.companyType).toBe(OrgType.SHIPPER);
		});

		it('should set correct company type for consignee', () => {
			component.title = 'consignee';

			const result = component.getFormData();

			expect(result?.companyType).toBe(OrgType.CONSIGNEE);
		});
	});

	describe('Province and City Filtering', () => {
		beforeEach(() => {
			component.provinces = mockProvinces;
			component.cities = mockCities;
		});

		it('should filter provinces based on search input', () => {
			component.filteredProvinces = mockProvinces;

			// Simulate province search
			const regionControl = component.shipperConsigneeForm.get('regionCode');
			regionControl?.setValue('New');

			// This would be triggered by the valueChanges subscription
			component.filteredProvinces = component.provinces.filter((province) => province.name.toLowerCase().includes('new'));

			expect(component.filteredProvinces.length).toBe(1);
			expect(component.filteredProvinces[0].name).toBe('New York');
		});

		it('should filter cities based on search input', () => {
			component.filteredCities = mockCities;

			// Simulate city search
			const cityControl = component.shipperConsigneeForm.get('cityCode');
			cityControl?.setValue('New');

			// This would be triggered by the valueChanges subscription
			component.filteredCities = component.cities.filter((city) => city.name.toLowerCase().includes('new'));

			expect(component.filteredCities.length).toBe(1);
			expect(component.filteredCities[0].name).toBe('New York City');
		});
	});

	describe('Component Properties', () => {
		it('should have correct input properties', () => {
			component.title = 'shipper';
			component.shipmentParty = mockShipmentParty;
			component.hawbList = mockHawbList;

			expect(component.title).toBe('shipper');
			expect(component.shipmentParty).toBe(mockShipmentParty);
			expect(component.hawbList).toBe(mockHawbList);
		});

		it('should toggle isOpen property', () => {
			expect(component.isOpen).toBeFalse();

			component.isOpen = true;
			expect(component.isOpen).toBeTrue();

			component.isOpen = false;
			expect(component.isOpen).toBeFalse();
		});
	});
});
