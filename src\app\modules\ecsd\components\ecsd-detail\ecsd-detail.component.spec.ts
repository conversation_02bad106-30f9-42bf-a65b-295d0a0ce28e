import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EcsdDetailComponent } from './ecsd-detail.component';
import { EcsdDialogObj, EcsdObj } from '../../models/ecsd.model';
import { of } from 'rxjs';
import { EcsdService } from '../../service/ecsd.service';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Modules } from '@shared/models/user-role.model';
import { TranslateModule } from '@ngx-translate/core';

const mockListObj: EcsdDialogObj = {
	loId: '111',
	loType: Modules.SLI,
	ecsdObj: {
		id: '111',
		securityStatus: '',
		receivedFrom: '',
		whetherExemptedForScreening: '',
		screeningMethod: '',
		groundsForExemption: '',
		issuedBy: '',
		employeeId: '',
		issuedOn: '',
		additionalSecurityInfo: '',
		loId: '',
		loType: '',
		regulatedEntityCategory1: '',
		regulatedEntityIdentifier1: '',
		regulatedEntityCategory: '',
		regulatedEntityIdentifier: '',
	},
};

const ecsdDetail: EcsdObj = {
	securityStatus: '',
	receivedFrom: '',
	whetherExemptedForScreening: '',
	screeningMethod: '',
	groundsForExemption: '',
	issuedBy: '',
	employeeId: '',
	issuedOn: '',
	additionalSecurityInfo: '',
	loId: '',
	loType: '',
	regulatedEntityCategory1: '',
	regulatedEntityIdentifier1: '',
	regulatedEntityCategory: '',
	regulatedEntityIdentifier: '',
	pieceList: [],
};

describe('EcsdDetailComponent', () => {
	let component: EcsdDetailComponent;
	let fixture: ComponentFixture<EcsdDetailComponent>;
	let mockDialog: jasmine.SpyObj<MatDialog>;
	let mockEcsdService: jasmine.SpyObj<EcsdService>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<EcsdDetailComponent>>;

	beforeEach(async () => {
		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		mockEcsdService = jasmine.createSpyObj('EcsdService', ['getEcsd']);
		mockEcsdService.getEcsd.and.returnValue(of(ecsdDetail));
		mockDialogRef = jasmine.createSpyObj<MatDialogRef<EcsdDetailComponent>>('MatDialogRef', ['close']);
		await TestBed.configureTestingModule({
			imports: [EcsdDetailComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: EcsdService, useValue: mockEcsdService },
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: MAT_DIALOG_DATA, useValue: mockListObj },
				{ provide: MatDialogRef, useValue: mockDialogRef },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(EcsdDetailComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	afterEach(() => {
		mockEcsdService.getEcsd.calls.reset();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should call getEcsd when data.obj exists', () => {
			const mockResponse: EcsdObj = {
				securityStatus: '',
				receivedFrom: '',
				whetherExemptedForScreening: '',
				screeningMethod: '',
				groundsForExemption: '',
				issuedBy: '',
				employeeId: '',
				issuedOn: '',
				additionalSecurityInfo: '',
				loId: '',
				loType: '',
				regulatedEntityCategory1: '',
				regulatedEntityIdentifier1: '',
				regulatedEntityCategory: '',
				regulatedEntityIdentifier: '',
				pieceList: [
					{
						id: '11',
						pieceId: '11',
						productDescription: 'p1',
						packageType: 'normal',
						grossWeight: '11',
						pieceQuantity: 0,
						dimensions: {
							length: '1',
							height: '1',
							width: '1',
						},
					},
					{
						id: '22',
						pieceId: '22',
						productDescription: 'p2',
						packageType: 'normal',
						grossWeight: '22',
						pieceQuantity: 0,
						dimensions: {
							length: '1',
							height: '1',
							width: '1',
						},
					},
				],
			};
			component.data.ecsdObj = {
				securityStatus: '',
				receivedFrom: '',
				whetherExemptedForScreening: '',
				screeningMethod: '',
				groundsForExemption: '',
				issuedBy: '',
				employeeId: '',
				issuedOn: '',
				additionalSecurityInfo: '',
				loId: '',
				loType: '',
				regulatedEntityCategory1: '',
				regulatedEntityIdentifier1: '',
				regulatedEntityCategory: '',
				regulatedEntityIdentifier: '',
			};
			mockEcsdService.getEcsd.and.returnValue(of(mockResponse));

			component.ngOnInit();
			fixture.detectChanges();

			expect(mockEcsdService.getEcsd).toHaveBeenCalledWith(component.data.ecsdObj);
			expect(component.ecsdDetail).toEqual(mockResponse);
			expect(component.dataSource.data).toEqual(mockResponse.pieceList ?? []);
			expect(component.dataLoading).toBeFalse();
		});

		it('should use hawbList if pieceList is not present', () => {
			component.data.loType = Modules.MAWB;
			const mockResponse: EcsdObj = {
				securityStatus: '',
				receivedFrom: '',
				whetherExemptedForScreening: '',
				screeningMethod: '',
				groundsForExemption: '',
				issuedBy: '',
				employeeId: '',
				issuedOn: '',
				additionalSecurityInfo: '',
				loId: '',
				loType: '',
				regulatedEntityCategory1: '',
				regulatedEntityIdentifier1: '',
				regulatedEntityCategory: '',
				regulatedEntityIdentifier: '',
				hawbList: [
					{
						id: '',
						hawbId: '',
						waybillNumber: '',
						shipper: '',
						consignee: '',
						goodsDescription: '',
						origin: '',
						destination: '',
						weight: '',
						slac: 0,
					},
				],
			};
			mockEcsdService.getEcsd.and.returnValue(of(mockResponse));

			component.ngOnInit();
			fixture.detectChanges();

			expect(component.dataSource.data).toEqual(mockResponse.hawbList ?? []);
		});

		it('should set empty array if both pieceList and hawbList are null', () => {
			const mockResponse: EcsdObj = {
				securityStatus: '',
				receivedFrom: '',
				whetherExemptedForScreening: '',
				screeningMethod: '',
				groundsForExemption: '',
				issuedBy: '',
				employeeId: '',
				issuedOn: '',
				additionalSecurityInfo: '',
				loId: '',
				loType: '',
				regulatedEntityCategory1: '',
				regulatedEntityIdentifier1: '',
				regulatedEntityCategory: '',
				regulatedEntityIdentifier: '',
			};
			mockEcsdService.getEcsd.and.returnValue(of(mockResponse));

			component.ngOnInit();
			fixture.detectChanges();

			expect(component.dataSource.data).toEqual([]);
		});
	});
});
