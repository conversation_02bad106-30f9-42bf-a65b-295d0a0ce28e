import { TestBed } from '@angular/core/testing';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { BookingRequestService } from './booking-request.service';
import { BookingRequestListObj, BookingRequestObj, BookingRequestSearchParam } from '../models/booking.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { environment } from 'src/environments/environment';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('BookingRequestService', () => {
	let service: BookingRequestService;
	let httpMock: HttpTestingController;
	const baseUrl = environment.baseApi;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [BookingRequestService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(BookingRequestService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	describe('Service Initialization', () => {
		it('should be created', () => {
			expect(service).toBeTruthy();
		});

		it('should extend ApiService', () => {
			expect(service).toBeInstanceOf(BookingRequestService);
		});

		it('should implement GenericTableService interface', () => {
			expect(service.getDataPerPage).toBeDefined();
			expect(service.loadAllData).toBeDefined();
		});
	});

	describe('getDataPerPage', () => {
		it('should return paginated booking request data', () => {
			const mockSearchParam: BookingRequestSearchParam = {
				requestedProduct: 'SLI',
				requestedStatus: 'REQUESTED',
				departureLocation: 'LAX',
				arrivalLocation: 'JFK',
			};

			const mockResponse: PaginationResponse<BookingRequestListObj> = {
				total: 2,
				rows: [
					{
						id: '1',
						bookingRequestId: '123',
						canConfirm: true,
						canShare: true,
						requestedBy: 'John Doe',
						requestedProduct: 'SLI',
						requestedFlight: 'AA123',
						requestedStatus: 'REQUESTED',
						departureLocation: 'LAX',
						arrivalLocation: 'JFK',
						flightDate: '2023-12-01T10:00:00Z',
					},
					{
						id: '2',
						bookingRequestId: '456',
						canConfirm: true,
						canShare: true,
						requestedBy: 'Jane Smith',
						requestedProduct: 'MAWB',
						requestedFlight: 'BA456',
						requestedStatus: 'CONFIRMED',
						departureLocation: 'LHR',
						arrivalLocation: 'CDG',
						flightDate: '2023-12-02T14:30:00Z',
					},
				],
			};

			service.getDataPerPage(mockSearchParam).subscribe((response) => {
				expect(response).toEqual(mockResponse);
				expect(response.total).toBe(2);
				expect(response.rows.length).toBe(2);
				expect(response.rows[0].requestedBy).toBe('John Doe');
				expect(response.rows[1].requestedProduct).toBe('MAWB');
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/list`);
			expect(req.request.method).toBe('GET');
			expect(req.request.params.get('requestedProduct')).toBe('SLI');
			expect(req.request.params.get('requestedStatus')).toBe('REQUESTED');
			expect(req.request.params.get('departureLocation')).toBe('LAX');
			expect(req.request.params.get('arrivalLocation')).toBe('JFK');
			req.flush(mockResponse);
		});

		it('should handle empty search parameters', () => {
			const mockSearchParam: BookingRequestSearchParam = {};
			const mockResponse: PaginationResponse<BookingRequestListObj> = {
				total: 0,
				rows: [],
			};

			service.getDataPerPage(mockSearchParam).subscribe((response) => {
				expect(response).toEqual(mockResponse);
				expect(response.total).toBe(0);
				expect(response.rows.length).toBe(0);
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/list`);
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});

		it('should handle partial search parameters', () => {
			const mockSearchParam: BookingRequestSearchParam = {
				requestedProduct: 'SLI',
			};

			const mockResponse: PaginationResponse<BookingRequestListObj> = {
				total: 1,
				rows: [
					{
						id: '1',
						bookingRequestId: '123',
						canConfirm: true,
						canShare: true,
						requestedBy: 'Test User',
						requestedProduct: 'SLI',
						requestedFlight: 'TK789',
						requestedStatus: 'REQUESTED',
						departureLocation: 'IST',
						arrivalLocation: 'DXB',
						flightDate: '2023-12-03T08:15:00Z',
					},
				],
			};

			service.getDataPerPage(mockSearchParam).subscribe((response) => {
				expect(response.total).toBe(1);
				expect(response.rows[0].requestedProduct).toBe('SLI');
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/list`);
			expect(req.request.method).toBe('GET');
			expect(req.request.params.get('requestedProduct')).toBe('SLI');
			expect(req.request.params.get('requestedStatus')).toBeNull();
			req.flush(mockResponse);
		});

		it('should handle HTTP errors gracefully', () => {
			const mockSearchParam: BookingRequestSearchParam = {
				requestedProduct: 'SLI',
			};

			service.getDataPerPage(mockSearchParam).subscribe({
				next: () => fail('Expected an error'),
				error: (error) => {
					expect(error.status).toBe(500);
					expect(error.statusText).toBe('Internal Server Error');
				},
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/list`);
			req.flush('Server Error', { status: 500, statusText: 'Internal Server Error' });
		});
	});

	describe('loadAllData', () => {
		it('should return all booking request data without pagination', () => {
			const mockSearchParam: BookingRequestSearchParam = {
				requestedStatus: 'CONFIRMED',
			};

			const mockResponse: BookingRequestListObj[] = [
				{
					id: '1',
					bookingRequestId: '123',
					canConfirm: true,
					canShare: true,
					requestedBy: 'Alice Johnson',
					requestedProduct: 'HAWB',
					requestedFlight: 'LH890',
					requestedStatus: 'CONFIRMED',
					departureLocation: 'FRA',
					arrivalLocation: 'NRT',
					flightDate: '2023-12-04T16:45:00Z',
				},
				{
					id: '2',
					bookingRequestId: '456',
					canConfirm: true,
					canShare: true,
					requestedBy: 'Bob Wilson',
					requestedProduct: 'SLI',
					requestedFlight: 'AF234',
					requestedStatus: 'CONFIRMED',
					departureLocation: 'CDG',
					arrivalLocation: 'LAX',
					flightDate: '2023-12-05T12:20:00Z',
				},
			];

			service.loadAllData(mockSearchParam).subscribe((response) => {
				expect(response).toEqual(mockResponse);
				expect(response.length).toBe(2);
				expect(response[0].requestedStatus).toBe('CONFIRMED');
				expect(response[1].requestedStatus).toBe('CONFIRMED');
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/list`);
			expect(req.request.method).toBe('GET');
			expect(req.request.params.get('requestedStatus')).toBe('CONFIRMED');
			req.flush(mockResponse);
		});

		it('should handle empty response', () => {
			const mockSearchParam: BookingRequestSearchParam = {
				requestedProduct: 'NONEXISTENT',
			};

			const mockResponse: BookingRequestListObj[] = [];

			service.loadAllData(mockSearchParam).subscribe((response) => {
				expect(response).toEqual([]);
				expect(response.length).toBe(0);
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/list`);
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});

		it('should handle network errors', () => {
			const mockSearchParam: BookingRequestSearchParam = {};

			service.loadAllData(mockSearchParam).subscribe({
				next: () => fail('Expected an error'),
				error: (error) => {
					expect(error.status).toBe(0);
					expect(error.statusText).toBe('Unknown Error');
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/booking-request/list`);
			req.error(new ErrorEvent('Network error'), { status: 0, statusText: 'Unknown Error' });
		});
	});

	describe('API Integration', () => {
		it('should use correct base URL from environment', () => {
			const mockSearchParam: BookingRequestSearchParam = {};

			service.getDataPerPage(mockSearchParam).subscribe();

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/list`);
			expect(req.request.url).toBe(`${baseUrl}/booking-request/list`);
			req.flush({ total: 0, rows: [] });
		});

		it('should pass all search parameters as query params', () => {
			const mockSearchParam: BookingRequestSearchParam = {
				requestedProduct: 'SLI',
				requestedStatus: 'REQUESTED',
				departureLocation: 'LAX',
				arrivalLocation: 'JFK',
			};

			service.getDataPerPage(mockSearchParam).subscribe();

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/list`);
			const params = req.request.params;

			expect(params.get('requestedProduct')).toBe('SLI');
			expect(params.get('requestedStatus')).toBe('REQUESTED');
			expect(params.get('departureLocation')).toBe('LAX');
			expect(params.get('arrivalLocation')).toBe('JFK');

			req.flush({ total: 0, rows: [] });
		});
	});

	describe('detail, transport and confirm', () => {
		it('getBookingRequestDetail should GET booking request detail by id', () => {
			const bookingRequestId = '123';
			const mockDetail = { id: '123', someField: 'value' } as any;

			service.getBookingRequestDetail(bookingRequestId).subscribe((res) => {
				expect(res).toEqual(mockDetail);
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/info`);
			expect(req.request.method).toBe('GET');
			expect(req.request.params.get('bookingRequestId')).toBe(bookingRequestId);
			req.flush(mockDetail);
		});

		it('getBookingTransportInfo should GET transport info by bookingId', () => {
			const bookingId = 'bk-1';
			const mockTrans = [{ transportId: 't1' }] as any;

			service.getBookingTransportInfo(bookingId).subscribe((res) => {
				expect(res).toEqual(mockTrans);
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/booking/info`);
			expect(req.request.method).toBe('GET');
			expect(req.request.params.get('bookingId')).toBe(bookingId);
			req.flush(mockTrans);
		});

		it('confirmBookingRequest should send update request and return string on success', () => {
			const bookingRequestId = 'confirm-1';

			service.confirmBookingRequest(bookingRequestId).subscribe((res) => {
				expect(res).toBe('ok');
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/confirm`);
			// updateData is expected to use PUT
			expect(req.request.method).toBe('PUT');
			expect(req.request.params.get('bookingRequestId')).toBe(bookingRequestId);
			req.flush('ok');
		});

		it('confirmBookingRequest should propagate error on failure', () => {
			const bookingRequestId = 'confirm-fail';

			service.confirmBookingRequest(bookingRequestId).subscribe({
				next: () => fail('Expected error'),
				error: (err) => {
					expect(err.status).toBe(500);
				},
			});

			const req = httpMock.expectOne((request) => request.url === `${baseUrl}/booking-request/confirm`);
			req.flush('Server Error', { status: 500, statusText: 'Internal Server Error' });
		});
	});

	describe('createBookingRequest', () => {
		it('should make a POST request to create a booking request', () => {
			const mockBookingRequest: BookingRequestObj = {
				bookingOptionId: 'OPT-001',
				bookingShipmentId: 'SHIP-001',
				airlineName: 'Air China',
				requestedProduct: 'Electronics',
				requestedFlight: 'CA123',
				departureLocation: 'Shanghai',
				arrivalLocation: 'Beijing',
				flightDate: '2023-12-01T08:00:00Z',
			};

			service.createBookingRequest(mockBookingRequest).subscribe({
				next: (response) => {
					expect(response).toBe('success');
				},
				error: () => fail('Expected to succeed'),
			});

			const req = httpMock.expectOne(`${baseUrl}/booking-request/create`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toEqual(mockBookingRequest);

			req.flush('success');
		});

		it('should handle HTTP errors gracefully', () => {
			const mockBookingRequest: BookingRequestObj = {
				bookingOptionId: 'OPT-001',
				bookingShipmentId: 'SHIP-001',
				airlineName: 'Air China',
				requestedProduct: 'Electronics',
				requestedFlight: 'CA123',
				departureLocation: 'Shanghai',
				arrivalLocation: 'Beijing',
				flightDate: '2023-12-01T08:00:00Z',
			};

			service.createBookingRequest(mockBookingRequest).subscribe({
				next: () => fail('Expected an error'),
				error: (error) => {
					expect(error.status).toBe(500);
					expect(error.statusText).toBe('Internal Server Error');
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/booking-request/create`);
			req.flush('Server Error', { status: 500, statusText: 'Internal Server Error' });
		});

		it('should send correct request body structure', () => {
			const mockBookingRequest: BookingRequestObj = {
				bookingOptionId: 'OPT-001',
				bookingShipmentId: 'SHIP-001',
				airlineName: 'Air China',
				requestedProduct: 'Electronics',
				requestedFlight: 'CA123',
				departureLocation: 'Shanghai',
				arrivalLocation: 'Beijing',
				flightDate: '2023-12-01T08:00:00Z',
			};

			service.createBookingRequest(mockBookingRequest).subscribe();

			const req = httpMock.expectOne(`${baseUrl}/booking-request/create`);
			expect(req.request.body.bookingOptionId).toBe('OPT-001');
			expect(req.request.body.bookingShipmentId).toBe('SHIP-001');
			expect(req.request.body.airlineName).toBe('Air China');
			expect(req.request.body.requestedProduct).toBe('Electronics');
			expect(req.request.body.requestedFlight).toBe('CA123');
			expect(req.request.body.departureLocation).toBe('Shanghai');
			expect(req.request.body.arrivalLocation).toBe('Beijing');
			expect(req.request.body.flightDate).toBe('2023-12-01T08:00:00Z');

			req.flush('success');
		});
	});
});
