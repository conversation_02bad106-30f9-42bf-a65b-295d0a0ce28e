import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiService } from '@shared/services/api.service';
import {
	HAWBEventListObj,
	HawbStatusPageRequest,
	MAWBEventListObj,
	MAWBEventObj,
	MawbStatusPageRequest,
	MAWBUpdateEventObj,
	PieceEventListObj,
	StatusHistory,
	StatusUpdateLog,
} from '../models/mawb-event.model';
import { Observable } from 'rxjs';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { GenericTableService } from '@shared/services/table/orll-table.interface';

@Injectable({
	providedIn: 'root',
})
export class MawbStatusService extends ApiService implements GenericTableService<StatusUpdateLog> {
	constructor(http: HttpClient) {
		super(http);
	}

	getDataPerPage(param: { mawbId: string }): Observable<PaginationResponse<StatusUpdateLog>> {
		return super.getData<PaginationResponse<StatusUpdateLog>>('event-management/get-modification-log', param);
	}

	loadAllData(param: any): Observable<StatusUpdateLog[]> {
		return super.getData<StatusUpdateLog[]>('event-management/get-modification-log-items', param);
	}

	getEventList(pageParams: MAWBEventObj): Observable<string[]> {
		return super.postData<string[]>('event-management/get-choose-status', pageParams);
	}

	getMawbStatus(param: { loId: string; type: string }): Observable<MAWBEventListObj> {
		return super.getData<MAWBEventListObj>('event-management/get-lo-latest-status', param);
	}

	listHawbStatusByMawb(param: MawbStatusPageRequest): Observable<PaginationResponse<HAWBEventListObj>> {
		return super.getData<PaginationResponse<HAWBEventListObj>>('event-management/list-hawb-status-by-mawb', param);
	}

	listPieceStatusByHawb(param: HawbStatusPageRequest): Observable<PaginationResponse<PieceEventListObj>> {
		return super.getData<PaginationResponse<PieceEventListObj>>('event-management/list-piece-status-by-hawb', param);
	}

	updateEventStatus(param: MAWBUpdateEventObj): Observable<string> {
		return super.postData<string>('event-management/update-choose-status', param);
	}

	getStatusHistoryList(loId: string, type: string): Observable<StatusHistory[]> {
		return super.getData<StatusHistory[]>('event-management/get-lo-history-status', { loId, type });
	}

	getAllEvents(): Observable<string[]> {
		return super.getData('sys-management/enums/statusCode');
	}
}
