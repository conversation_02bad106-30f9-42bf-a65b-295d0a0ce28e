import { ChangeDetectionStrategy, Component } from '@angular/core';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';

@Component({
	selector: 'orll-dialog',
	imports: [MatDialogModule, MatDividerModule, MatIconModule],
	templateUrl: './dialog-template.component.html',
	styleUrl: './dialog-template.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export default class OrllDialogComponent {}
