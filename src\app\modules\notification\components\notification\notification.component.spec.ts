import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { NotificationComponent } from './notification.component';
import { NotificationService } from '../../services/notification.service';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { LogisticObjType } from '@shared/models/share-type.model';
import { NotificationListObj, NotificationEventType } from '../../models/notification.model';
import { of, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SimpleChange } from '@angular/core';

const mockPagedResponse: PaginationResponse<NotificationListObj> = {
	total: 15,
	rows: [
		{
			id: '111',
			eventType: NotificationEventType.LOGISTICS_EVENT_RECEIVED,
			dataType: LogisticObjType.SLI,
			companyName: 'Company A',
			logisticsObject: 'http://10.10.11.10:8090/logistics-objects/47e85903-8f7d-457e-a498-d8b2418ef45b',
			hasRead: false,
			createTime: '2025-01-01 11:30',
			waybillNumber: '1111',
		},
		{
			id: '222',
			eventType: NotificationEventType.LOGISTICS_OBJECT_UPDATED,
			dataType: LogisticObjType.SLI,
			companyName: 'Company A',
			logisticsObject: 'http://10.10.11.10:8090/logistics-objects/47e85903-8f7d-457e-a498-d8b2418ef45b',
			hasRead: false,
			createTime: '2025-01-01 11:30',
			waybillNumber: '2222',
		},
	],
};

describe('NotificationComponent', () => {
	let component: NotificationComponent;
	let fixture: ComponentFixture<NotificationComponent>;
	let mockNotificationService: jasmine.SpyObj<NotificationService>;
	let routerSpy: jasmine.SpyObj<Router>;

	beforeEach(async () => {
		mockNotificationService = jasmine.createSpyObj('NotificationService', ['getNotificationPerPage']);
		mockNotificationService.getNotificationPerPage.and.returnValue(of(mockPagedResponse));
		routerSpy = jasmine.createSpyObj('Router', ['navigate']);

		await TestBed.configureTestingModule({
			imports: [NotificationComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: NotificationService, useValue: mockNotificationService },
				{ provide: Router, useValue: routerSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(NotificationComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with correct default values', () => {
		expect(component.dataLoading).toBeFalse();
		expect(component.totalRecords).toBe(0);
		expect(component.pageSize).toBe(10);
		expect(component.pageIndex).toBe(0);
		expect(component.tablePageSizes).toEqual([10, 50, 100]);
		expect(component.notifications).toEqual([]);
		expect(component.showUnread).toBeFalse();
		expect(component.refreshNotification).toBeFalse();
	});

	it('should call loadData when unRead input changes', () => {
		const spyLoad = spyOn(component as any, 'loadData');
		component.showUnread = true;
		component.ngOnChanges({ showUnread: new SimpleChange(false, true, false) });
		expect(spyLoad).toHaveBeenCalled();
	});

	it('should call loadData when refresh input changes', () => {
		const spyLoad = spyOn(component as any, 'loadData');
		component.refreshNotification = true;
		component.ngOnChanges({ refreshNotification: new SimpleChange(false, true, false) });
		expect(spyLoad).toHaveBeenCalled();
	});

	it('should load data successfully and update properties', fakeAsync(() => {
		mockNotificationService.getNotificationPerPage.and.returnValue(of(mockPagedResponse));

		(component as any).loadData();
		fixture.detectChanges();
		tick();

		expect(mockNotificationService.getNotificationPerPage).toHaveBeenCalledWith({ pageNum: 1, pageSize: 10 }, false);
		expect(component.dataLoading).toBeFalse();
		expect(component.notifications).toEqual(mockPagedResponse.rows);
		expect(component.totalRecords).toBe(15);
	}));

	it('should handle error and set dataLoading to false', fakeAsync(() => {
		mockNotificationService.getNotificationPerPage.and.returnValue(throwError(() => new Error('Network error')));

		(component as any).loadData();
		fixture.detectChanges();
		tick();

		expect(mockNotificationService.getNotificationPerPage).toHaveBeenCalled();
		expect(component.dataLoading).toBeFalse();
		expect(component.notifications).toEqual([]);
		expect(component.totalRecords).toBe(0);
	}));

	it('should update pageIndex and pageSize and call loadData on page change', () => {
		const spyLoad = spyOn(component as any, 'loadData');
		const event = { pageIndex: 2, pageSize: 50 };

		component.onPageChange(event);

		expect(component.pageIndex).toBe(2);
		expect(component.pageSize).toBe(50);
		expect(spyLoad).toHaveBeenCalled();
	});

	it('should navigate to correct path for SLI type', () => {
		const row: NotificationListObj = {
			dataType: LogisticObjType.SLI,
			logisticsObject: 'SLI789',
			id: '',
			eventType: '',
			companyName: '',
			hasRead: false,
			createTime: '',
			waybillNumber: '',
		};
		spyOn(component.showOpen, 'emit');

		component.goToDetail(row);

		expect(component.showOpen.emit).toHaveBeenCalledWith(false);
		expect(routerSpy.navigate).toHaveBeenCalledWith(['/sli/edit', 'SLI789', 0]);
	});

	it('should navigate to correct path for non-SLI type', () => {
		const row: NotificationListObj = {
			dataType: LogisticObjType.MAWB,
			logisticsObject: 'INV001',
			id: '',
			eventType: '',
			companyName: '',
			hasRead: false,
			createTime: '',
			waybillNumber: '',
		};
		spyOn(component.showOpen, 'emit');

		component.goToDetail(row);

		expect(component.showOpen.emit).toHaveBeenCalledWith(false);
		expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb/edit', 'INV001']);
	});

	it('should navigate to correct path for BOOKING_OPTION_REQUEST type', () => {
		const row: NotificationListObj = {
			dataType: LogisticObjType.BOOKING_OPTION_REQUEST,
			logisticsObject: 'BOOKING_OPTION_REQUEST001',
			id: '',
			eventType: '',
			companyName: '',
			hasRead: false,
			createTime: '',
			waybillNumber: '',
		};
		spyOn(component.showOpen, 'emit');

		component.goToDetail(row);

		expect(component.showOpen.emit).toHaveBeenCalledWith(false);
		expect(routerSpy.navigate).toHaveBeenCalledWith(['/quote']);
	});

	it('should navigate to correct path for BOOKING_REQUEST type', () => {
		const row: NotificationListObj = {
			dataType: LogisticObjType.BOOKING_REQUEST,
			logisticsObject: 'BOOKING_REQUEST001',
			id: '',
			eventType: '',
			companyName: '',
			hasRead: false,
			createTime: '',
			waybillNumber: '',
		};
		spyOn(component.showOpen, 'emit');

		component.goToDetail(row);

		expect(component.showOpen.emit).toHaveBeenCalledWith(false);
		expect(routerSpy.navigate).toHaveBeenCalledWith(['/booking']);
	});
});
