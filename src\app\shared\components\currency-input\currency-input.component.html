<ng-container [formGroup]="currencyForm">
	<mat-form-field class="numerical width-100 negative-mr-1" appearance="outline" floatLabel="always">
		@if (formLabel) {
			<mat-label>{{ formLabel | translate }}</mat-label>
		}
		<input matInput formControlName="numericalValue" />
		@if (currencyForm.get('numericalValue')?.hasError('required')) {
			<mat-error>{{ 'validators.required' | translate: { field: fieldName | translate } }}</mat-error>
		}
		@if (currencyForm.get('numericalValue')?.hasError('pattern')) {
			<mat-error>{{ `validators.maxDecimal${pattern}` | translate }}</mat-error>
		}
	</mat-form-field>
	@if (!hiddenUnit) {
		<mat-form-field appearance="outline" class="width-80" floatLabel="always">
			<input type="text" matInput formControlName="currencyUnit" [matAutocomplete]="autoCurrency" />
			<mat-autocomplete #autoCurrency="matAutocomplete">
				@for (currency of filteredCurrency; track currency) {
					<mat-option [value]="currency">{{ currency }}</mat-option>
				}
			</mat-autocomplete>
			<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
			@if (currencyForm.get('currencyUnit')?.hasError('required')) {
				<mat-error>{{ 'validators.required' | translate: { field: currencyUnit | translate } }}</mat-error>
			}
		</mat-form-field>
	}
</ng-container>
