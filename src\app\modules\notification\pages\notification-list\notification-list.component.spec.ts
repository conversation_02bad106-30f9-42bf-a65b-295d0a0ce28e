import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import NotificationListComponent from './notification-list.component';
import { TranslateModule } from '@ngx-translate/core';
import { NotificationService } from '../../services/notification.service';
import { LogisticObjType } from '@shared/models/share-type.model';
import { NotificationEventType, NotificationListObj } from '../../models/notification.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { of } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { By } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';

const mockPagedResponse: PaginationResponse<NotificationListObj> = {
	total: 15,
	rows: [
		{
			id: '111',
			eventType: NotificationEventType.LOGISTICS_EVENT_RECEIVED,
			dataType: LogisticObjType.SLI,
			companyName: 'Company A',
			logisticsObject: 'http://***********:8090/logistics-objects/47e85903-8f7d-457e-a498-d8b2418ef45b',
			hasRead: false,
			createTime: '2025-01-01 11:30',
			waybillNumber: '1111',
		},
		{
			id: '222',
			eventType: NotificationEventType.LOGISTICS_OBJECT_UPDATED,
			dataType: LogisticObjType.SLI,
			companyName: 'Company A',
			logisticsObject: 'http://***********:8090/logistics-objects/47e85903-8f7d-457e-a498-d8b2418ef45b',
			hasRead: false,
			createTime: '2025-01-01 11:30',
			waybillNumber: '2222',
		},
	],
};

describe('NotificationListComponent', () => {
	let component: NotificationListComponent;
	let fixture: ComponentFixture<NotificationListComponent>;
	let mockNotificationService: jasmine.SpyObj<NotificationService>;

	beforeEach(async () => {
		mockNotificationService = jasmine.createSpyObj('NotificationService', ['getNotificationPerPage']);
		mockNotificationService.getNotificationPerPage.and.returnValue(of(mockPagedResponse));
		await TestBed.configureTestingModule({
			imports: [NotificationListComponent, TranslateModule.forRoot(), FormsModule],
			providers: [{ provide: NotificationService, useValue: mockNotificationService }],
			schemas: [NO_ERRORS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(NotificationListComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should bind slide toggle state to showUnread property', () => {
		const slideToggleDebugEl = fixture.debugElement.query(By.css('mat-slide-toggle'));

		slideToggleDebugEl.triggerEventHandler('change', { checked: true });
		fixture.detectChanges();

		expect(component.showUnread).toBe(true);

		slideToggleDebugEl.triggerEventHandler('change', { checked: false });
		fixture.detectChanges();

		expect(component.showUnread).toBe(false);
	});
});
