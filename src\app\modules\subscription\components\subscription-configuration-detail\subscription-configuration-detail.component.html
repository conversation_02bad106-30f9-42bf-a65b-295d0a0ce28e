<div class="orll-subscription-detail">
	<h2 mat-dialog-title>
		@if (isEdit) {
			<span>{{ 'subscription.edit.title' | translate }}</span>
		} @else {
			<span>{{ 'subscription.create.title' | translate }}</span>
		}

		<mat-icon class="orll-subscription-detail__clear_dialog" [matDialogClose]="'cancel'">clear_round</mat-icon>
	</h2>
	<mat-dialog-content>
		<div class="container mt-4">
			<form [formGroup]="configurationForm">
				<div class="row">
					<mat-form-field class="col-6">
						<mat-label>{{ 'subscription.request.table.topicType' | translate }}</mat-label>
						<mat-select
							class="width-100"
							formControlName="topicType"
							(selectionChange)="configurationForm.get('topic')?.patchValue('')">
							@for (item of topicTypeOptions; track item.name) {
								<mat-option [value]="item.code">
									{{ item.code }}
								</mat-option>
							}
						</mat-select>
						@if (configurationForm.get('topicType')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'subscription.request.table.topicType' | translate }
							}}</mat-error>
						}
					</mat-form-field>

					<mat-form-field class="col-6">
						<mat-label>{{ 'subscription.request.table.topic' | translate }}</mat-label>
						@if (configurationForm.get('topicType')?.value.includes('LOGISTICS_OBJECT_TYPE')) {
							<mat-select class="width-100" formControlName="topic">
								@for (item of topicOptions; track item) {
									<mat-option [value]="item">
										{{ item }}
									</mat-option>
								}
							</mat-select>
						} @else {
							<input matInput formControlName="topic" placeholder="input URI in here..." />
						}
						@if (configurationForm.get('topic')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'subscription.request.table.topic' | translate }
							}}</mat-error>
						}
					</mat-form-field>
				</div>

				<div class="row align-items-center">
					<div class="col-4">
						<span class="orll-subscription-detail__form-label">{{ 'subscription.form.event' | translate }} *</span>
					</div>
					<div class="col-8">
						<form [formGroup]="configurationForm.get('subscriptionEventType')" class="checkbox-form">
							@for (eventType of eventTypeOptions; track eventType?.code) {
								<mat-checkbox [formControlName]="eventType.code" value="false">{{ eventType?.name }}</mat-checkbox>
							}
							@if (configurationForm.get('subscriptionEventType')?.hasError('atLeastOneRequired')) {
								<mat-error class="error-msg">{{
									'validators.required' | translate: { field: 'subscription.form.event' | translate }
								}}</mat-error>
							}
						</form>
					</div>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-12" floatLabel="always">
						<mat-label>{{ 'common.dialog.delegation.request.description' | translate }}</mat-label>
						<textarea matInput formControlName="description"></textarea>
						@if (configurationForm.get('description')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'common.dialog.delegation.request.description' | translate }
							}}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'subscription.config.table.expiersAt' | translate }}</mat-label>
						<input matInput formControlName="expiresAt" [matDatepicker]="picker" />
						<mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
						<mat-datepicker #picker></mat-datepicker>
						@if (configurationForm.get('expiresAt')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'subscription.config.table.expiersAt' | translate }
							}}</mat-error>
						}
					</mat-form-field>
				</div>
			</form>
		</div>
		<mat-divider class="row"></mat-divider>
	</mat-dialog-content>
	<div class="orll-subscription-detail__btn d-flex">
		<button mat-stroked-button [mat-dialog-close]="'cancel'" color="primary" class="orll-subscription-detail__cancel_btn">
			{{ 'common.dialog.cancel' | translate }}
		</button>
		<button mat-flat-button color="primary" (click)="saveConfiguration()">
			<mat-icon>check</mat-icon>
			{{ 'common.dialog.ok' | translate }}
		</button>
	</div>
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
