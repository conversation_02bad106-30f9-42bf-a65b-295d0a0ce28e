import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '../destroy-observable/destroy-ref.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { AbstractControl, FormControl, FormGroup, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { AutocompleteComponent } from '../autocomplete/autocomplete.component';
import { SliSearchRequestService } from 'src/app/modules/sli-mgmt/services/sli-search-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { DelegationPermission, DelegationRequestStatus } from '@shared/models/delegation-permission';
import { DelegationRequest } from '@shared/models/delegation-request';
import { DelegationRequestService } from '@shared/services/delegation/delegation-request.service';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';

@Component({
	selector: 'orll-delegation-request-dialog',
	templateUrl: './delegation-request-dialog.component.html',
	styleUrl: './delegation-request-dialog.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatDialogModule,
		MatButtonModule,
		MatIconModule,
		TranslateModule,
		MatCheckboxModule,
		MatButtonModule,
		MatInputModule,
		MatFormField,
		ReactiveFormsModule,
		MatAutocompleteModule,
		AutocompleteComponent,
		IataDateFormatPipe,
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class DelegationRequestDialogComponent extends DestroyRefComponent implements OnInit {
	selectedOrgs: CodeName[] = [];
	selectedPermissions: string[] = [];
	submitted = false;
	delegationDetail: DelegationRequest | null = null;
	delegationRequestStatus = DelegationRequestStatus;
	requestStatus = '';
	requestId = '';
	isApprovedBy = '';

	delegationPermissionList: CodeName[] = [
		{
			code: DelegationPermission.GET_LOGISTICS_EVENT,
			name: 'common.delegation.permission.GET_LOGISTICS_EVENT',
		},
		{
			code: DelegationPermission.GET_LOGISTICS_OBJECT,
			name: 'common.delegation.permission.GET_LOGISTICS_OBJECT',
		},
		{
			code: DelegationPermission.POST_LOGISTICS_EVENT,
			name: 'common.delegation.permission.POST_LOGISTICS_EVENT',
		},
		{
			code: DelegationPermission.PATCH_LOGISTICS_OBJECT,
			name: 'common.delegation.permission.PATCH_LOGISTICS_OBJECT',
		},
	];

	constructor(
		public readonly sliSearchRequestService: SliSearchRequestService,
		private readonly delegationRequestService: DelegationRequestService,
		public dialogRef: MatDialogRef<DelegationRequestDialogComponent>,
		@Inject(MAT_DIALOG_DATA) public data: any
	) {
		super();
	}

	delegationRequestForm: FormGroup = new FormGroup({
		hasDescription: new FormControl<string>('', [Validators.required]),
		hasLogisticsObject: new FormControl<string>('', [Validators.required]),
		hasPermission: new FormGroup(
			{
				[DelegationPermission.GET_LOGISTICS_EVENT]: new FormControl<boolean>(false),
				[DelegationPermission.GET_LOGISTICS_OBJECT]: new FormControl<boolean>(false),
				[DelegationPermission.POST_LOGISTICS_EVENT]: new FormControl<boolean>(false),
				[DelegationPermission.PATCH_LOGISTICS_OBJECT]: new FormControl<boolean>(false),
			},
			[this.atLeastOneRequiredValidator()]
		),
	});

	private atLeastOneRequiredValidator(): ValidatorFn {
		return (control: AbstractControl): ValidationErrors | null => {
			const group = control as FormGroup;
			const hasSelected = Object.values(group.controls).some((c) => c.value === true);
			return hasSelected ? null : { atLeastOneRequired: true };
		};
	}

	ngOnInit(): void {
		this.isApprovedBy = this.data.isApprovedBy ?? '';
		this.delegationDetail = this.data.record ?? null;
		this.requestId = this.delegationDetail?.requestId ?? '';
		this.requestStatus = this.delegationDetail?.requestStatus ?? '';
		if (!this.delegationDetail) {
			this.delegationRequestForm.patchValue({
				hasLogisticsObject: this.data.loId,
			});
		}
	}

	onCancel(): void {
		this.dialogRef.close(false);
	}

	onOk(): void {
		this.delegationRequestForm.markAllAsTouched();
		this.submitted = true;

		if (this.delegationRequestForm.invalid) {
			return;
		}

		this.submitted = false;
		this.selectedPermissions = [];

		const hasPermissions = this.delegationRequestForm.value.hasPermission;
		for (const key in hasPermissions) {
			if (hasPermissions[key]) {
				this.selectedPermissions.push(key);
			}
		}

		this.delegationRequestService
			.requestDelegation({
				documentId: this.data.documentId,
				isRequestedFor: this.selectedOrgs.map((org) => org.code),
				hasPermission: this.selectedPermissions,
				hasDescription: this.delegationRequestForm.value.hasDescription,
				hasLogisticsObject: this.delegationRequestForm.value.hasLogisticsObject.split(','),
			})
			.subscribe({
				next: () => {
					this.dialogRef.close(true);
				},
			});
	}

	onReject(): void {
		if (this.requestId) {
			this.delegationRequestService.rejectDelegation(this.requestId).subscribe({
				next: () => {
					this.dialogRef.close(true);
				},
			});
		}
	}

	onApprove(): void {
		if (this.requestId) {
			this.delegationRequestService.approveDelegation(this.requestId).subscribe({
				next: () => {
					this.dialogRef.close(true);
				},
			});
		}
	}

	onRevoke(): void {
		if (this.requestId) {
			this.delegationRequestService.revokeDelegation(this.requestId).subscribe({
				next: () => {
					this.dialogRef.close(true);
				},
			});
		}
	}
}
