import { Currency, Id, OtherChargeList, PartyList } from '../../mawb-mgmt/models/mawb-create.model';

export interface HawbCreateDto {
	id?: Id;
	sliId?: string;
	orgId: string;
	bookingId?: string;
	sliNumber?: string;
	waybillPrefix: string;
	waybillNumber: string;
	pieceIdList?: string[];
	partyList: PartyList[];
	sliPartyList?: PartyList[];
	accountingInformation: string;
	departureLocation: string;
	arrivalLocation: string;
	requestedFlight?: string;
	requestedDate?: string;
	toFirst?: string;
	toSecond?: string;
	toThird?: string;
	byFirstCarrier?: string;
	bySecondCarrier?: string;
	byThirdCarrier?: string;
	insuredAmount: Currency;
	weightValuationIndicator: string;
	otherChargesIndicator: string;
	declaredValueForCarriage: Currency;
	declaredValueForCustoms: Currency;
	textualHandlingInstructions: string;
	totalGrossWeight: number | null;
	volume?: number | null;
	rateClassCode: string | null;
	totalVolumetricWeight: number;
	rateCharge: Currency;
	goodsDescriptionForRate: string;
	otherChargeList: OtherChargeList[];
	carrierDeclarationDate: string;
	carrierDeclarationPlace: string;
	consignorDeclarationSignature: string;
	carrierDeclarationSignature: string;
	promptMessage?: string | null;
}
