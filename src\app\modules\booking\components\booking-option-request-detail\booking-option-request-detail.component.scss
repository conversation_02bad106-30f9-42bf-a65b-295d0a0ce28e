.booking-option-request-detail {
	color: var(--iata-grey-600);
	&__sub-title {
		padding: 10px 0px 15px 0px;
	}
	.detail-title {
		font-size: 20px;
		font-weight: 400;
		color: var(--iata-grey-500);
	}
	.title-from {
		padding-left: 10px;
		font-weight: 500;
		color: var(--iata-grey-600);
	}

	&_quote {
		border: 1px solid var(--iata-grey-200);
		border-radius: 8px;
		margin-top: 20px;

		.value {
			padding: 10px 0px 10px 10px !important;
		}
		.divider {
			margin: 0px 15px;
		}
	}

	.row {
		gap: 10px;
		margin-left: 0px;
		margin-right: 0px;
	}
	.dimensions {
		width: 50%;
		max-width: 100%;
	}
	.lwh {
		width: 33%;
	}
	.width-100 {
		width: 100%;
	}
	.width-20 {
		width: 20%;
	}
	.width-35 {
		width: 35%;
	}
	.width-55 {
		width: 55%;
	}
	.label {
		color: var(--iata-grey-400);
		font-size: 12px;
		padding-left: 10px;
	}
	.value {
		display: flex;
		gap: 4rem;
		padding: 20px 0px 20px 10px;
	}
	.dimension-value {
		display: flex;
		gap: 2rem;
		padding: 20px 0px 20px 10px;
	}
}
