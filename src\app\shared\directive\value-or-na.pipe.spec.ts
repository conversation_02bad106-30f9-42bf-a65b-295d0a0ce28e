import { ValueOrNAPipe } from './value-or-na.pipe';

describe('ValueOrNAPipe', () => {
	let pipe: ValueOrNAPipe;

	beforeEach(() => {
		pipe = new ValueOrNAPipe();
	});

	it('create an instance', () => {
		expect(pipe).toBeTruthy();
	});

	it('should return default value ("N/A") when value is undefined', () => {
		expect(pipe.transform()).toBe('N/A');
	});

	it('should return default value ("N/A") when value is null', () => {
		expect(pipe.transform(null)).toBe('N/A');
	});

	it('should return default value ("N/A") when value is an empty string', () => {
		expect(pipe.transform('')).toBe('N/A');
	});

	it('should return the string value when it is a non-empty string', () => {
		const testString = 'Hello, World!';
		expect(pipe.transform(testString)).toBe(testString);
	});

	it('should return "0" when value is number 0', () => {
		expect(pipe.transform(0)).toBe('0');
	});

	it('should return string representation of a number when value is a number', () => {
		const testNumber = 42;
		expect(pipe.transform(testNumber)).toBe('42');
	});

	it('should return "true" when value is boolean true', () => {
		expect(pipe.transform(true)).toBe('true');
	});

	it('should return "false" when value is boolean false', () => {
		expect(pipe.transform(false)).toBe('false');
	});

	it('should use custom default value when provided', () => {
		const customDefault = 'Not Available';
		expect(pipe.transform(null, customDefault)).toBe(customDefault);
		expect(pipe.transform('', customDefault)).toBe(customDefault);
		expect(pipe.transform(undefined, customDefault)).toBe(customDefault);
	});

	it('should be created', () => {
		expect(pipe).toBeTruthy();
	});
});
