import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import ServerManagementComponent from './server-management.component';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { of } from 'rxjs';
import { ServerManagementService } from '../../services/server-management.service';
import { Organization } from '@shared/models/organization.model';
import { TranslateModule } from '@ngx-translate/core';
import { SliCreateRequestService } from 'src/app/modules/sli-mgmt/services/sli-create-request.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { OrgInfo, ServerType } from '../../models/mgmt-server.model';
import { CodeName } from '@shared/models/code-name.model';

const orgs: Organization[] = [
	{
		id: '111',
		name: '111',
		orgType: '',
	},
];

const orgInfo: OrgInfo = {
	id: '',
	companyName: '',
	partyRole: '',
	countryCode: '',
	locationName: '',
	regionCode: '',
	textualPostCode: '',
	cityCode: '',
	iataCargoAgentCode: '',
	airlineCode: '',
	persons: [],
	server: {
		id: '',
		hasDataHolder: '',
		hasServerEndpoint: '',
		hasSupportedContentType: [],
		hasSupportedLanguage: '',
	},
	orgProperties: {
		userName: '',
		password: '',
		graphDbUrl: '',
		neOneUrl: '',
		keycloakUrl: '',
		grantType: '',
		clientId: '',
		clientSecret: '',
		logisticsAgentUri: '',
	},
};

const mockCode: CodeName[] = [
	{
		code: '',
		name: '',
	},
];

describe('ServerManagementComponent', () => {
	let component: ServerManagementComponent;
	let fixture: ComponentFixture<ServerManagementComponent>;
	let mockCreateReqService: jasmine.SpyObj<SliCreateRequestService>;
	let mockMgmtService: jasmine.SpyObj<ServerManagementService>;
	let mockOrgService: jasmine.SpyObj<OrgMgmtRequestService>;

	beforeEach(async () => {
		mockCreateReqService = jasmine.createSpyObj('SliCreateRequestService', ['getProvinces', 'getCountries', 'getEnumCode']);
		mockCreateReqService.getCountries.and.returnValue(of([]));
		mockCreateReqService.getProvinces.and.returnValue(of([]));
		mockMgmtService = jasmine.createSpyObj('ServerManagementService', ['getOrgInfo', 'getOrgList']);
		mockMgmtService.getOrgList.and.returnValue(of([]));
		mockMgmtService.getOrgInfo.and.returnValue(of(orgInfo));

		mockOrgService = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList', 'getEnumCode']);
		mockOrgService.getOrgList.and.returnValue(of(orgs));
		mockOrgService.getEnumCode.and.returnValue(of(mockCode));

		await TestBed.configureTestingModule({
			imports: [ServerManagementComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: SliCreateRequestService, useValue: mockCreateReqService },
				{ provide: ServerManagementService, useValue: mockMgmtService },
				{ provide: OrgMgmtRequestService, useValue: mockOrgService },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(ServerManagementComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit()', () => {
		it('should initialize organizations and set first item as selected', fakeAsync(() => {
			// Mock response data
			const mockOrgs: Organization[] = [
				{
					id: 'org 1',
					name: 'org 1',
					orgType: '',
				},
				{
					id: 'org 2',
					name: 'org 2',
					orgType: '',
				},
				{
					id: 'org 3',
					name: 'org 3',
					orgType: '',
				},
			];

			mockMgmtService.getOrgList.and.returnValue(of(mockOrgs));

			// Call ngOnInit
			component.ngOnInit();

			// Verify service call
			expect(mockMgmtService.getOrgList).toHaveBeenCalledWith('Internal');

			// Wait for async operation
			tick();

			// Verify data setup
			expect(component.organizations).toEqual(mockOrgs);
			expect(component.selectedItem).toEqual(mockOrgs[0]);
		}));

		it('should handle empty organizations array', fakeAsync(() => {
			mockMgmtService.getOrgList.and.returnValue(of([]));

			component.ngOnInit();
			tick();

			expect(component.organizations).toEqual([]);
			expect(component.selectedItem).toBeNull();
		}));
	});

	describe('onItemClick()', () => {
		it('should set the selected item', () => {
			const testItem = { id: 5, name: 'Test Org' };

			component.onItemClick(testItem);

			expect(component.selectedItem).toEqual(testItem);
		});

		it('should replace previously selected item', () => {
			const initialItem = { id: 1, name: 'Initial' };
			const newItem = { id: 2, name: 'New' };

			component.selectedItem = initialItem;
			component.onItemClick(newItem);

			expect(component.selectedItem).toEqual(newItem);
			expect(component.selectedItem).not.toEqual(initialItem);
		});
	});

	describe('onTabChanged()', () => {
		it('should set isOrll to true and load RESIDENT organizations when first tab is selected', fakeAsync(() => {
			const mockEvent = { index: 0, tab: { textLabel: 'ORLL Resident' } } as MatTabChangeEvent;
			const mockOrgs: Organization[] = [
				{
					name: 'RESIDENT Org',
					orgType: '',
					id: '1',
				},
			];

			mockMgmtService.getOrgList.and.returnValue(of(mockOrgs));

			component.onTabChanged(mockEvent);
			tick();

			expect(mockMgmtService.getOrgList).toHaveBeenCalledWith('Internal');
			expect(component.organizations).toEqual(mockOrgs);
		}));

		it('should set isOrll to false and load external organizations when second tab is selected', fakeAsync(() => {
			const mockEvent = { index: 1, tab: { textLabel: 'External Servers' } } as MatTabChangeEvent;
			const mockOrgs: Organization[] = [
				{
					id: '2',
					name: 'External Org',
					orgType: '',
				},
			];

			mockMgmtService.getOrgList.and.returnValue(of(mockOrgs));

			component.onTabChanged(mockEvent);
			tick();

			expect(mockMgmtService.getOrgList).toHaveBeenCalledWith('External');
			expect(component.organizations).toEqual(mockOrgs);
		}));
	});

	// Integration test: verify tab switching behavior
	describe('Tab Switching Integration', () => {
		it('should correctly switch between RESIDENT and external organizations', fakeAsync(() => {
			const mockOrgs: Organization[] = [
				{
					id: '1',
					name: 'RESIDENT',
					orgType: '',
				},
			];
			const externalOrgs: Organization[] = [
				{
					id: '2',
					name: 'External',
					orgType: '',
				},
			];
			const translateServiceSpy = jasmine.createSpyObj('TranslateService', ['instant']);

			// Replace the translate service
			(component as any).translateService = translateServiceSpy;

			// First call: RESIDENT orgs
			mockMgmtService.getOrgList.withArgs('Internal').and.returnValue(of(mockOrgs));
			// Second call: external orgs
			mockMgmtService.getOrgList.withArgs('External').and.returnValue(of(externalOrgs));
			translateServiceSpy.instant.and.returnValue('ORLL Resident');

			// Switch to RESIDENT tab
			component.onTabChanged({ index: 0, tab: { textLabel: 'ORLL Resident' } } as MatTabChangeEvent);
			tick();

			expect(component.organizations).toEqual(mockOrgs);

			// Switch to external tab
			component.onTabChanged({ index: 1, tab: { textLabel: 'External Servers' } } as MatTabChangeEvent);
			tick();

			expect(component.organizations).toEqual(externalOrgs);

			// Verify service was called with correct parameters
			expect(mockMgmtService.getOrgList).toHaveBeenCalledWith('Internal');
			expect(mockMgmtService.getOrgList).toHaveBeenCalledWith('External');
			expect(mockMgmtService.getOrgList).toHaveBeenCalledTimes(3);
		}));
	});

	describe('newServer()', () => {
		it('should set selectedItem to undefined', () => {
			component.selectedItem = { id: 'new-server', name: 'new-server' };

			component.newServer();

			expect(component.selectedItem).toEqual({id: 'new-server'});
		});

		it('should set currentServerType to EXTERNAL', () => {
			component.currentServerType = ServerType.RESIDENT;

			component.newServer();

			expect(component.currentServerType).toBe(ServerType.RESIDENT);
		});

		it('should reset both selectedItem and currentServerType', () => {
			component.selectedItem = { id: 'new-server', name: 'new-server' };
			component.currentServerType = ServerType.RESIDENT;

			component.newServer();

			expect(component.selectedItem).toEqual({id: 'new-server'});
			expect(component.currentServerType).toBe(ServerType.RESIDENT);
		});
	});

	describe('refreshList()', () => {
		const mockOrganizations: Organization[] = [
			{ id: '1', name: 'Org 1', orgType: '' },
			{ id: '2', name: 'Org 2', orgType: '' },
		];

		it('should call getOrgList with provided ServerType', () => {
			mockMgmtService.getOrgList.and.returnValue(of(mockOrganizations));

			component.refreshList(ServerType.RESIDENT);

			expect(mockMgmtService.getOrgList).toHaveBeenCalledWith(ServerType.RESIDENT);
		});

		it('should update organizations with response from getOrgList', () => {
			mockMgmtService.getOrgList.and.returnValue(of(mockOrganizations));

			component.refreshList(ServerType.RESIDENT);

			expect(component.organizations).toEqual(mockOrganizations);
		});

		it('should handle different ServerType values', () => {
			mockMgmtService.getOrgList.and.returnValue(of(mockOrganizations));

			component.refreshList(ServerType.RESIDENT);
			expect(mockMgmtService.getOrgList).toHaveBeenCalledWith(ServerType.RESIDENT);

			component.refreshList(ServerType.EXTERNAL);
			expect(mockMgmtService.getOrgList).toHaveBeenCalledWith(ServerType.EXTERNAL);
		});

		it('should handle empty response from getOrgList', () => {
			mockMgmtService.getOrgList.and.returnValue(of([]));

			component.refreshList(ServerType.RESIDENT);

			expect(component.organizations).toEqual([]);
		});
	});
});
