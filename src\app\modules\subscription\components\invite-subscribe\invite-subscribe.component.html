<div class="orll-invite-subscribe">
	<h2 mat-dialog-title>
		<span> {{ 'subscription.btn.invite' | translate }}</span>
		<mat-icon class="orll-invite-subscribe__clear_dialog" [mat-dialog-close]="'cancel'">clear_round</mat-icon>
	</h2>
	<mat-dialog-content>
		<form [formGroup]="inviteForm" class="form-container">
			<div class="row">
				<mat-form-field appearance="outline" class="col-6">
					<mat-label>{{ 'subscription.request.table.topicType' | translate }}</mat-label>
					<mat-select formControlName="topicType">
						@for (type of topicTypes; track type.name) {
							<mat-option [value]="type.name">{{ type.code }}</mat-option>
						}
					</mat-select>
					@if (inviteForm.get('topicType')?.hasError('required')) {
						<mat-error>{{
							'validators.required' | translate: { field: 'subscription.request.table.topicType' | translate }
						}}</mat-error>
					}
				</mat-form-field>

				<mat-form-field appearance="outline" floatLabel="always" class="col-6">
					<mat-label>{{ 'subscription.request.table.topic' | translate }}</mat-label>
					@if (inviteForm.get('topicType')?.value?.includes('LOGISTICS_OBJECT_IDENTIFIER')) {
						<input matInput formControlName="topic" placeholder="input URI in here..." />
					} @else {
						<mat-select formControlName="topic" required>
							@for (topic of topics; track topic) {
								<mat-option [value]="topic">{{ topic }}</mat-option>
							}
						</mat-select>
					}
					@if (inviteForm.get('topic')?.hasError('required')) {
						<mat-error>{{
							'validators.required' | translate: { field: 'subscription.request.table.topic' | translate }
						}}</mat-error>
					}
				</mat-form-field>
			</div>

			<div class="row">
				<mat-form-field class="col-6">
					<mat-label>{{ 'subscription.request.table.subscriber' | translate }}</mat-label>
					<mat-select formControlName="subscribers" multiple>
						@for (partner of partners; track partner.id) {
							<mat-option [value]="partner.id">{{ partner.name }}</mat-option>
						}
					</mat-select>
					@if (inviteForm.get('subscribers')?.hasError('required')) {
						<mat-error>{{
							'validators.required' | translate: { field: 'subscription.request.table.subscriber' | translate }
						}}</mat-error>
					}
				</mat-form-field>
			</div>
		</form>
		<mat-divider></mat-divider>
	</mat-dialog-content>
	<mat-dialog-actions>
		<ng-container>
			<div class="orll-invite-subscribe__btn">
				<button mat-stroked-button [mat-dialog-close]="'cancel'" color="primary" class="orll-subscription-detail__cancel_btn">
					{{ 'common.dialog.cancel' | translate }}
				</button>
				<button mat-flat-button color="primary" class="reverse-icon" (click)="inviteToSubscribe()">
					<mat-icon>input</mat-icon>
					{{ 'subscription.btn.invite.confirm' | translate }}
				</button>
			</div>
		</ng-container>
	</mat-dialog-actions>
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
