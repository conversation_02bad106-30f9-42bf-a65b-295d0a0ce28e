<div class="orll-booking-transport">
	<div class="transport-detail">
		<div class="summary iata-box">
			<div class="row">
				<div class="airline">{{ bookingRequestDetails?.airlinesName ?? bookingAirlineDetail?.airlinesName ?? '' }}</div>
				<div class="col-1"></div>
			</div>
			<div class="row">
				<div class="col-9">
					<div class="flight-info">
						<div class="departure">
							<div>
								<div>
									<span class="time">{{ firstTransport?.departureDate | iataDateFormat: 'dd MM yyyy' }}</span>
									<span>{{ firstTransport?.departureDate | iataDateFormat: 'HH:mm' }}</span>
								</div>
								<div class="mt-10">
									<span class="location">{{ firstTransport?.departureLocation }}</span>
								</div>
							</div>
						</div>
						<div class="flight-icon flex-40">
							<svg width="90" height="5" viewBox="0 0 90 5" fill="none" xmlns="http://www.w3.org/2000/svg">
								<mask id="path-1-inside-1_5356_29238" fill="white">
									<path
										d="M54.5 0C55.7095 2.25745e-07 56.7186 0.85886 56.9502 2H85.0498C85.2814 0.85886 86.2905 0 87.5 0C88.8807 0 90 1.11929 90 2.5C90 3.88071 88.8807 5 87.5 5C86.2905 5 85.2814 4.14114 85.0498 3H56.9502C56.7186 4.14114 55.7095 5 54.5 5H35.5C34.2905 5 33.2814 4.14114 33.0498 3H4.9502C4.71858 4.14114 3.70949 5 2.5 5C1.11929 5 0 3.88071 0 2.5C0 1.11929 1.11929 0 2.5 0C3.70949 0 4.71858 0.85886 4.9502 2H33.0498C33.2814 0.85886 34.2905 0 35.5 0H54.5Z" />
								</mask>
								<path
									d="M54.5 0C55.7095 2.25745e-07 56.7186 0.85886 56.9502 2H85.0498C85.2814 0.85886 86.2905 0 87.5 0C88.8807 0 90 1.11929 90 2.5C90 3.88071 88.8807 5 87.5 5C86.2905 5 85.2814 4.14114 85.0498 3H56.9502C56.7186 4.14114 55.7095 5 54.5 5H35.5C34.2905 5 33.2814 4.14114 33.0498 3H4.9502C4.71858 4.14114 3.70949 5 2.5 5C1.11929 5 0 3.88071 0 2.5C0 1.11929 1.11929 0 2.5 0C3.70949 0 4.71858 0.85886 4.9502 2H33.0498C33.2814 0.85886 34.2905 0 35.5 0H54.5Z"
									fill="white" />
								<path
									d="M54.5 0V-1V0ZM56.9502 2L55.9702 2.19892L56.1328 3H56.9502V2ZM85.0498 2V3H85.8672L86.0298 2.19892L85.0498 2ZM85.0498 3L86.0298 2.80108L85.8672 2H85.0498V3ZM56.9502 3V2H56.1328L55.9702 2.80108L56.9502 3ZM35.5 5V6V5ZM33.0498 3L34.0298 2.80108L33.8672 2H33.0498V3ZM4.9502 3V2H4.13278L3.97018 2.80108L4.9502 3ZM4.9502 2L3.97018 2.19892L4.13278 3H4.9502V2ZM33.0498 2V3H33.8672L34.0298 2.19892L33.0498 2ZM54.5 0V1C55.2247 1 55.8313 1.51477 55.9702 2.19892L56.9502 2L57.9302 1.80108C57.6058 0.20295 56.1943 -1 54.5 -1V0ZM56.9502 2V3H85.0498V2V1H56.9502V2ZM85.0498 2L86.0298 2.19892C86.1687 1.51477 86.7753 1 87.5 1V0V-1C85.8057 -1 84.3942 0.20295 84.0698 1.80108L85.0498 2ZM87.5 0V1C88.3284 1 89 1.67157 89 2.5H90H91C91 0.567003 89.433 -1 87.5 -1V0ZM90 2.5H89C89 3.32843 88.3284 4 87.5 4V5V6C89.433 6 91 4.433 91 2.5H90ZM87.5 5V4C86.7753 4 86.1687 3.48523 86.0298 2.80108L85.0498 3L84.0698 3.19892C84.3942 4.79705 85.8057 6 87.5 6V5ZM85.0498 3V2H56.9502V3V4H85.0498V3ZM56.9502 3L55.9702 2.80108C55.8313 3.48523 55.2247 4 54.5 4V5V6C56.1943 6 57.6058 4.79705 57.9302 3.19892L56.9502 3ZM54.5 5V4H35.5V5V6H54.5V5ZM35.5 5V4C34.7753 4 34.1687 3.48523 34.0298 2.80108L33.0498 3L32.0698 3.19892C32.3942 4.79705 33.8057 6 35.5 6V5ZM33.0498 3V2H4.9502V3V4H33.0498V3ZM4.9502 3L3.97018 2.80108C3.83132 3.48523 3.22465 4 2.5 4V5V6C4.19433 6 5.60583 4.79705 5.93021 3.19892L4.9502 3ZM2.5 5V4C1.67157 4 1 3.32843 1 2.5H0H-1C-1 4.433 0.567003 6 2.5 6V5ZM0 2.5H1C1 1.67157 1.67157 1 2.5 1V0V-1C0.567003 -1 -1 0.567003 -1 2.5H0ZM2.5 0V1C3.22465 1 3.83132 1.51477 3.97018 2.19892L4.9502 2L5.93021 1.80108C5.60583 0.20295 4.19433 -1 2.5 -1V0ZM4.9502 2V3H33.0498V2V1H4.9502V2ZM33.0498 2L34.0298 2.19892C34.1687 1.51477 34.7753 1 35.5 1V0V-1C33.8057 -1 32.3942 0.20295 32.0698 1.80108L33.0498 2ZM35.5 0V1H54.5V0V-1H35.5V0Z"
									fill="#1E32FA"
									mask="url(#path-1-inside-1_5356_29238)" />
							</svg>
						</div>
						<div class="arrival">
							<div>
								<div>
									<span class="time">{{ lastTransport?.arrivalDate | iataDateFormat: 'dd MM yyyy' }}</span>
									<span>{{ lastTransport?.arrivalDate | iataDateFormat: 'HH:mm' }}</span>
								</div>
								<div class="mt-10">
									<span class="location">{{ lastTransport?.arrivalLocation }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="border-right"></div>
				<div class="col-3 ml-1" class="price-col">
					<div class="total-price {{ bookingRequestDetails ? 'pb-30' : '' }}" [class.pirce-expand]="this.showPieceDetail">
						<div>
							<div>
								<span class="price">{{
									bookingRequestDetails?.totalPrice?.numericalValue ??
										bookingAirlineDetail?.totalPrice?.numericalValue ??
										'0'
								}}</span>
								<span>{{
									bookingRequestDetails?.totalPrice?.currencyUnit ?? bookingAirlineDetail?.totalPrice?.currencyUnit ?? ''
								}}</span>
							</div>
							<div>{{ 'booking.request.totalPrice' | translate }}</div>
							@if (bookingAirlineDetail) {
								<div>
									@if (showPieceDetail) {
										<mat-icon
											(click)="showPieceDetail = !showPieceDetail"
											(keydown.enter)="$event.stopPropagation()"
											color="primary"
											class="price-btn"
											>expand_less</mat-icon
										>
									} @else {
										<mat-icon
											(click)="showPieceDetail = !showPieceDetail"
											(keydown.enter)="$event.stopPropagation()"
											color="primary"
											class="price-btn"
											>expand_more</mat-icon
										>
									}
								</div>
							}
						</div>
					</div>
					@if (showPieceDetail) {
						<div class="orll-booking-transport__piece-detail">
							<div class="orll-booking-transport__piece-detail-table">
								<div class="row table-title">
									<div class="col-2">
										{{ 'booking.option.form.charge.type' | translate }}
									</div>
									<div class="col-2">
										{{ 'booking.option.form.charge.rate.class' | translate }}
									</div>
									<div class="col-2">
										{{ 'booking.option.form.rate' | translate }}
									</div>
									<div class="col-2">
										{{ 'booking.option.form.payment.type' | translate }}
									</div>
									<div class="col-2">
										{{ 'booking.option.form.entitlement' | translate }}
									</div>
								</div>
								@if (priceList) {
									@for (price of priceList; track price.chargeType) {
										<div class="row">
											<div class="col-2">
												{{ price.chargeType ?? '' }}
											</div>
											<div class="col-2">
												{{ price.rateClassCode ?? '' }}
											</div>
											<div class="col-2">
												{{ price.subTotal ?? '' }}
											</div>
											<div class="col-2">
												{{ price.chargePaymentType ?? '' }}
											</div>
											<div class="col-2">
												{{ price.entitlement ?? '' }}
											</div>
										</div>
									}
								}
							</div>
						</div>
					}
				</div>
			</div>
		</div>

		<div class="detail iata-box">
			<div class="transport-list">
				@for (
					transport of bookingRequestDetails?.transportVoList ?? bookingAirlineDetail?.transportVoList ?? [];
					track transport.sequenceNumber
				) {
					<div class="row">
						<div class="col-3 center">
							<div class="flight-icon center">
								<svg width="9" height="43" viewBox="0 0 9 43" fill="none" xmlns="http://www.w3.org/2000/svg">
									<mask id="path-1-inside-1_5356_29243" fill="white">
										<path
											d="M9 38.5C9 40.9853 6.98528 43 4.5 43C2.01472 43 -1.79152e-06 40.9853 -1.68289e-06 38.5C-1.58164e-06 36.1838 1.75007 34.2771 4 34.0283L4 8.97168C1.75007 8.72291 -2.97948e-07 6.81624 -1.96701e-07 4.5C-8.80662e-08 2.01472 2.01472 -3.05337e-07 4.5 -1.96702e-07C6.98528 -6.44422e-08 9 2.01472 9 4.5C9 6.81624 7.24993 8.72291 5 8.97168L5 34.0283C7.24993 34.2771 9 36.1838 9 38.5Z" />
									</mask>
									<path
										d="M9 38.5C9 40.9853 6.98528 43 4.5 43C2.01472 43 -1.79152e-06 40.9853 -1.68289e-06 38.5C-1.58164e-06 36.1838 1.75007 34.2771 4 34.0283L4 8.97168C1.75007 8.72291 -2.97948e-07 6.81624 -1.96701e-07 4.5C-8.80662e-08 2.01472 2.01472 -3.05337e-07 4.5 -1.96702e-07C6.98528 -6.44422e-08 9 2.01472 9 4.5C9 6.81624 7.24993 8.72291 5 8.97168L5 34.0283C7.24993 34.2771 9 36.1838 9 38.5Z"
										fill="white" />
									<path
										d="M4.5 43L4.5 44L4.5 44L4.5 43ZM4 34.0283L4.10989 35.0223L5 34.9238L5 34.0283L4 34.0283ZM4 8.97168L5 8.97168L5 8.07615L4.1099 7.97774L4 8.97168ZM4.5 -1.96702e-07L4.5 -1L4.5 -1.96702e-07ZM5 8.97168L4.8901 7.97774L4 8.07615L4 8.97168L5 8.97168ZM5 34.0283L4 34.0283L4 34.9238L4.8901 35.0223L5 34.0283ZM9 38.5L8 38.5C8 40.433 6.43299 42 4.5 42L4.5 43L4.5 44C7.53756 44 10 41.5376 10 38.5L9 38.5ZM4.5 43L4.5 42C2.567 42 0.999998 40.433 0.999998 38.5L-1.68289e-06 38.5L-1 38.5C-1 41.5376 1.46243 44 4.5 44L4.5 43ZM-1.68289e-06 38.5L0.999998 38.5C0.999998 36.6994 2.36075 35.2157 4.10989 35.0223L4 34.0283L3.8901 33.0344C1.13939 33.3385 -1 35.6681 -1 38.5L-1.68289e-06 38.5ZM4 34.0283L5 34.0283L5 8.97168L4 8.97168L3 8.97168L3 34.0283L4 34.0283ZM4 8.97168L4.1099 7.97774C2.36075 7.78434 1 6.30059 1 4.5L-1.96701e-07 4.5L-1 4.5C-1 7.3319 1.13939 9.66149 3.8901 9.96562L4 8.97168ZM-1.96701e-07 4.5L1 4.5C1 2.567 2.567 1 4.5 1L4.5 -1.96702e-07L4.5 -1C1.46243 -1 -1 1.46243 -1 4.5L-1.96701e-07 4.5ZM4.5 -1.96702e-07L4.5 1C6.433 1 8 2.567 8 4.5L9 4.5L10 4.5C10 1.46243 7.53757 -1 4.5 -1L4.5 -1.96702e-07ZM9 4.5L8 4.5C8 6.30059 6.63925 7.78434 4.8901 7.97774L5 8.97168L5.1099 9.96562C7.86061 9.66149 10 7.3319 10 4.5L9 4.5ZM5 8.97168L4 8.97168L4 34.0283L5 34.0283L6 34.0283L6 8.97168L5 8.97168ZM5 34.0283L4.8901 35.0223C6.63925 35.2157 8 36.6994 8 38.5L9 38.5L10 38.5C10 35.6681 7.86061 33.3385 5.10989 33.0344L5 34.0283Z"
										fill="#1E32FA"
										mask="url(#path-1-inside-1_5356_29243)" />
								</svg>
							</div>
							<div>
								<div>
									<span class="flight-time">{{ transport?.departureDate | iataDateFormat: 'dd MM yyyy HH:mm' }}</span>
								</div>
								<div class="mt-10">
									<span class="flight-time">{{ transport?.arrivalDate | iataDateFormat: 'dd MM yyyy HH:mm' }}</span>
								</div>
							</div>
						</div>
						<div class="col-3">
							<div>
								<span class="location small">{{ transport?.departureLocation }}</span>
							</div>
							<div class="mt-10">
								<span class="location small">{{ transport?.arrivalLocation }}</span>
							</div>
						</div>
						<div class="col-6 center">
							<div>
								<span class="carrier small">{{ transport?.flightNumber }}</span>
							</div>
						</div>
					</div>
					<div class="border-bottom"></div>
				}
			</div>
		</div>
	</div>
	@if (selectable) {
		<div
			(click)="selectOption()"
			(keydown.enter)="$event.stopPropagation()"
			class="transport-select iata-box"
			[class.transport-selected]="bookingRequestDetails?.selected ?? bookingAirlineDetail?.selected">
			<mat-icon>check</mat-icon>
		</div>
	}
</div>
