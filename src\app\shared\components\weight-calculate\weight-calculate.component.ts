import { WeightCalculateRequestService } from '../../services/biz-logic/weight-calculate/weight-calculate-request.service';
import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '../destroy-observable/destroy-ref.component';
import { MatTable, MatTableDataSource, MatTableModule } from '@angular/material/table';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortHeader, MatSortModule } from '@angular/material/sort';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { CommonModule, Location } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import { MatButtonModule, MatButton } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatLabel, MatInputModule } from '@angular/material/input';
import { Piece, WeightInfoListObj } from '@shared/models/weight-info';
import { REGX_NUMBER_1_DECIMAL } from '@shared/models/constant';
import { startWith } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { roundGrossWeightVolume } from '@shared/utils/common.utils';

@Component({
	selector: 'orll-weight-calculate',
	imports: [
		SpinnerComponent,
		MatSortHeader,
		MatSortModule,
		MatPaginatorModule,
		MatTableModule,
		TranslateModule,
		RouterModule,
		MatButtonModule,
		MatIcon,
		MatButton,
		MatLabel,
		FormsModule,
		CommonModule,
		ReactiveFormsModule,
		MatFormFieldModule,
		MatInputModule,
		MatTable,
		MatPaginatorModule,
	],
	templateUrl: './weight-calculate.component.html',
	styleUrl: './weight-calculate.component.scss',
})
export default class WeightCalculateComponent extends DestroyRefComponent implements OnInit {
	@Input() sliNumber?: string;
	@Output() pagination = new EventEmitter<PageEvent & { sortField?: string; sortDirection?: string }>();

	dataSource = new MatTableDataSource<Piece>([]);
	data!: WeightInfoListObj;
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};

	dataLoading = false;
	totalRecords = 0;
	pageSize = 10;
	pageIndex = 0;
	readonly tablePageSizes: number[] = [10, 50, 100];

	weightCalculateForm: FormGroup = new FormGroup({
		totalGrossWeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		volume: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		chargeableWeight: new FormControl<string>({ value: '', disabled: true }, [
			Validators.required,
			Validators.pattern(/^([1-9]\d*(\.5)?|0\.5)$/),
		]),
	});
	orgMap: Map<string, string> = new Map<string, string>();
	state: any;
	displayedColumns: string[] = ['productDescription', 'dimensions', 'grossWeight', 'slac'];

	constructor(
		private readonly router: Router,
		private readonly weightCalculateRequestService: WeightCalculateRequestService,
		private readonly location: Location
	) {
		super();
		const navigation = this.router.getCurrentNavigation();
		if (navigation?.extras.state) {
			this.state = navigation.extras.state;
		}
		this.setupVolumeListener('totalGrossWeight');
		this.setupVolumeListener('volume');
	}
	private setupVolumeListener(fieldName: string) {
		this.weightCalculateForm
			.get(fieldName)
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe(() => {
				const grossWeightValue = Number(this.weightCalculateForm.get('totalGrossWeight')?.value);
				const volume = Number(this.weightCalculateForm.get('volume')?.value);
				if (
					!Number.isNaN(volume) &&
					!Number.isNaN(grossWeightValue) &&
					volume !== null &&
					grossWeightValue !== null &&
					volume !== undefined &&
					grossWeightValue !== undefined
				) {
					const result = roundGrossWeightVolume(volume);
					if (grossWeightValue !== undefined && grossWeightValue !== null && result > grossWeightValue) {
						this.weightCalculateForm.patchValue({ chargeableWeight: result });
					} else {
						this.weightCalculateForm.patchValue({ chargeableWeight: grossWeightValue });
					}
				}
			});
	}

	ngOnInit(): void {
		this.loadData();
	}

	private loadData() {
		const request: PaginationRequest = {
			pageNum: this.pageIndex + 1,
			pageSize: this.pageSize,
		};
		this.dataLoading = true;

		if (this.sliNumber) {
			this.weightCalculateRequestService.getSliData(request, this.sliNumber).subscribe({
				next: (res) => {
					this.data = res;
					this.dataSource.data = res.pieces;
					this.weightCalculateForm.patchValue({
						chargeableWeight: res.chargableWeight,
						totalGrossWeight: res.totalGrossWeight,
						volume: res.volume,
					});
					this.dataLoading = false;
				},
				error: (error) => {
					console.error(error);
					this.dataLoading = false;
				},
			});
		} else if (this.state) {
			this.weightCalculateRequestService
				.getHawbData(
					request,
					this.state.selectedHawbs.map((item: { hawbId: any }) => item.hawbId)
				)
				.subscribe({
					next: (res) => {
						this.data = res;
						this.dataSource.data = res.pieces;
						this.weightCalculateForm.patchValue({
							chargeableWeight: res.chargableWeight,
							totalGrossWeight: res.totalGrossWeight,
							volume: res.volume,
						});
						this.dataLoading = false;
					},
					error: (error) => {
						console.error(error);
						this.dataLoading = false;
					},
				});
		}
	}

	onCancel() {
		this.location.back();
	}

	onSave() {
		if (this.weightCalculateForm.invalid) {
			this.weightCalculateForm.markAllAsTouched();
			return;
		}

		if (this.sliNumber) {
			this.router.navigate(['/hawb/create', this.sliNumber, 'detail'], {
				state: this.getState(),
			});
		} else if (this.state) {
			this.router.navigate(['/mawb/create/detail'], {
				state: {
					...this.getState(),
					selectedHawbs: this.state.selectedHawbs,
					bookingId: this.state.bookingId,
					volume: this.weightCalculateForm.get('volume')?.value,
				},
			});
		}
	}

	private getState() {
		return {
			grossWeight: this.weightCalculateForm.get('totalGrossWeight')?.value,
			chargeableWeight: this.weightCalculateForm.get('chargeableWeight')?.value,
			volume: this.weightCalculateForm.get('volume')?.value,
		};
	}
}
