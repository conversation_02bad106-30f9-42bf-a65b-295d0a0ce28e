export enum UserRole {
	SHIPPER = 'SHP',
	CONSIGNEE = 'CNE',
	FORWARDER = 'FFW',
	CARRIER = 'AIR',
	CUSTOM = 'CTM',
	AGENT = 'GHA',
	SECURITY = 'APT',
	SUPER_USER = '3',
}

export enum UserPermission {
	CREATE = 'create',
	UPDATE = 'update',
	DELETE = 'delete',
	QUERY = 'query',
	SHARE = 'share',
	CHANGE_REQUEST = 'req:change',
}

export enum Modules {
	SLI = 'sli',
	HAWB = 'hawb',
	MAWB = 'mawb',
	BOOKING = 'booking',
}
