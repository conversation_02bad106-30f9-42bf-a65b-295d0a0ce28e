import { TestBed } from '@angular/core/testing';

import { ServerManagementService } from './server-management.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { environment } from '@environments/environment';
import { OrgInfo, PostOrgInfo, Server } from '../models/mgmt-server.model';
import { Organization } from '@shared/models/organization.model';

const baseUrl = environment.baseApi;

describe('ServerManagementService', () => {
	let service: ServerManagementService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [ServerManagementService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(ServerManagementService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should getOrg', () => {
		const mockResponse: OrgInfo = {
			id: 'A',
			companyName: 'A',
			partyRole: '',
			countryCode: '',
			locationName: '',
			regionCode: '',
			textualPostCode: '',
			cityCode: '',
			iataCargoAgentCode: '',
			airlineCode: '',
			persons: [],
			server: {
				id: '',
				hasDataHolder: '',
				hasServerEndpoint: '',
				hasSupportedContentType: [],
				hasSupportedLanguage: '',
			},
			orgProperties: {
				userName: '',
				password: '',
				graphDbUrl: '',
				neOneUrl: '',
				keycloakUrl: '',
				grantType: '',
				clientId: '',
				clientSecret: '',
				logisticsAgentUri: '',
			},
		};
		const id = '111';

		service.getOrgInfo(id).subscribe((res) => {
			expect(res).toEqual(mockResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/org/org?orgId=111`);
		expect(req.request.method).toBe('GET');
		req.flush(mockResponse);
	});

	it('should retrieveServerInformation', () => {
		const mockResponse: Server = {
			id: '',
			hasDataHolder: '',
			hasServerEndpoint: '',
			hasSupportedContentType: [],
			hasSupportedLanguage: '',
		};
		const id = '111';

		service.retrieveServerInformation(id).subscribe((res) => {
			expect(res).toEqual(mockResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/org/org/retrieveServerInformation`);
		expect(req.request.method).toBe('POST');
		req.flush(mockResponse);
	});

	it('should saveOrg no param.id', () => {
		const param: PostOrgInfo = {
			id: null,
			orgStatus: '',
			orgServerType: '',
			organization: {
				name: '',
				basedAtLocation: {
					streetAddressLines: '',
					cityCode: '',
					regionCode: '',
					country: '',
				},
			},
			orgProperties: {
				graphDbUrl: '',
				userName: '',
				password: '',
				neOneUrl: '',
				keycloakUrl: '',
				grantType: '',
				clientId: '',
				clientSecret: '',
				logisticsAgentUri: '',
			},
			party: {
				partyRole: '',
			},
			persons: [],
			orgType: '',
			name: '',
			idStr: '',
		};
		const mockResponse = 'success';
		service.saveOrg(param).subscribe((res) => {
			expect(res).toEqual(mockResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/org/org`);
		expect(req.request.method).toBe('POST');
		req.flush(mockResponse);
	});

	it('should saveOrg with param.id', () => {
		const param: PostOrgInfo = {
			id: '111',
			orgStatus: '',
			orgServerType: '',
			organization: {
				name: '',
				basedAtLocation: {
					streetAddressLines: '',
					cityCode: '',
					regionCode: '',
					country: '',
				},
			},
			orgProperties: {
				graphDbUrl: '',
				userName: '',
				password: '',
				neOneUrl: '',
				keycloakUrl: '',
				grantType: '',
				clientId: '',
				clientSecret: '',
				logisticsAgentUri: '',
			},
			party: {
				partyRole: '',
			},
			persons: [],
			orgType: '',
			name: '',
			idStr: '',
		};
		const mockResponse = 'success';
		service.saveOrg(param).subscribe((res) => {
			expect(res).toEqual(mockResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/org/org`);
		expect(req.request.method).toBe('POST');
		req.flush(mockResponse);
	});

	it('should getOrgList ', () => {
		const mockResponse: Organization[] = [
			{
				id: '',
				name: '',
				orgType: '',
			},
		];
		service.getOrgList('111').subscribe((res) => {
			expect(res).toEqual(mockResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/org/org/list?serverType=111`);
		expect(req.request.method).toBe('GET');
		req.flush(mockResponse);
	});

	it('delete org ', () => {
		const mockResponse = true;
		service.deleteServer('111').subscribe((res) => {
			expect(res).toEqual(mockResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/org/org/deleteServer`);
		expect(req.request.method).toBe('DELETE');
		req.flush(mockResponse);
	});
});
