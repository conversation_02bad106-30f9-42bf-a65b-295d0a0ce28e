import { Injectable } from '@angular/core';
import { ApiService } from '@shared/services/api.service';
import { BookingInfo, BookingOptionDetail, BookingOptionRequestDetailObj, BookingOptionRequestListObj } from '../models/booking.model';
import { GenericTableService } from '@shared/services/table/orll-table.interface';
import { HttpClient } from '@angular/common/http';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { map, Observable, of } from 'rxjs';
import { CodeName } from '@shared/models/code-name.model';
import { AIRPORTS } from '../../sli-mgmt/ref-data/airports.data';
import { Airport } from '../../sli-mgmt/models/airport.model';
import { CURRENCIES } from '../../sli-mgmt/ref-data/currencies.data';

@Injectable({
	providedIn: 'root',
})
export class BookingOptionRequestService extends ApiService implements GenericTableService<BookingOptionRequestListObj> {
	constructor(http: HttpClient) {
		super(http);
	}

	getDataPerPage(param: any): Observable<PaginationResponse<BookingOptionRequestListObj>> {
		return this.getData('booking-option-management', param);
	}

	loadAllData(param: any): Observable<BookingOptionRequestListObj[]> {
		return this.getData('booking-option-management', param);
	}

	getBookingOptionDetail(id: string): Observable<BookingOptionRequestDetailObj> {
		return this.getData('booking-option-management/info', { id });
	}

	getAirports(): Observable<CodeName[]> {
		return of(AIRPORTS).pipe(
			map((airports: Airport[]) => {
				return airports.map((airport: Airport) => {
					return { code: airport.code, name: `${airport.code} - ${airport.name}` };
				});
			})
		);
	}

	getCurrencies(): Observable<string[]> {
		return of(CURRENCIES).pipe(
			map((currencies: string[]) => {
				return currencies.map((currency: string) => {
					return currency;
				});
			})
		);
	}

	createBookingOptionRequest(data: BookingOptionRequestDetailObj): Observable<string> {
		if (data.id) {
			return this.postData('booking-option-management/update-booking-option-request', data);
		}
		return this.postData('booking-option-management', data);
	}

	shareOption(id: string, airline: string, fromBooking: boolean): Observable<boolean> {
		if (fromBooking) {
			return this.postData('booking-request/share', { bookingRequestId: id, orgId: airline });
		}
		return this.postData('booking-option-management/share', { bookingOptionRequestId: id, orgId: airline });
	}

	sendBookingOptonsToForwarder(param: BookingOptionDetail[]) {
		return this.postData('booking-option-management/add-booking-option', param);
	}

	createBookingRequest(data: BookingInfo): Observable<string> {
		return this.postData('booking-request/direct-create', data);
	}
}
