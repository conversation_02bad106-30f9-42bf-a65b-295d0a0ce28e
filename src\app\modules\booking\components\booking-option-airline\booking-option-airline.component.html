<orll-dialog class="booking-option-request-airline">
	<ng-container dialog-title>
		<span>{{ 'booking.option.select.airline' | translate }}</span>
	</ng-container>

	<div dialog-content>
		<mat-form-field class="width-100">
			<mat-select (selectionChange)="selectAirline($event)">
				@for (airline of airlines; track airline.id) {
					<mat-option [value]="airline.id">{{ airline.name }}</mat-option>
				}
			</mat-select>
		</mat-form-field>
		@if (dataLoading) {
			<iata-spinner></iata-spinner>
		}
	</div>
	<ng-container dialog-actions>
		<button mat-stroked-button [mat-dialog-close]="'cancel'" color="primary">
			{{ 'common.dialog.cancel' | translate }}
		</button>
		<button mat-flat-button color="primary" (click)="sendRequest()" [disabled]="involvedParties.length === 0">
			<mat-icon>check</mat-icon>
			{{ 'common.dialog.ok' | translate }}
		</button>
	</ng-container>
</orll-dialog>
