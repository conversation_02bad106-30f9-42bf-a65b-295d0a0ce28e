import { TestBed } from '@angular/core/testing';
import { CanDeactivateGuard, CanComponentDeactivate } from './can-deactivate.guard';
import { Observable, of, BehaviorSubject } from 'rxjs';

describe('CanDeactivateGuard', () => {
	let guard: CanDeactivateGuard;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [CanDeactivateGuard],
		});
		guard = TestBed.inject(CanDeactivateGuard);
	});

	it('should be created', () => {
		expect(guard).toBeTruthy();
	});

	describe('canDeactivate', () => {
		it('should return true when component has no canDeactivate method', () => {
			const component = {} as CanComponentDeactivate;

			const result = guard.canDeactivate(component);

			expect(result).toBe(true);
		});

		it('should return true when component canDeactivate method is undefined', () => {
			const component = {
				canDeactivate: undefined,
			} as any;

			const result = guard.canDeactivate(component);

			expect(result).toBe(true);
		});

		it('should return true when component canDeactivate method is null', () => {
			const component = {
				canDeactivate: null,
			} as any;

			const result = guard.canDeactivate(component);

			expect(result).toBe(true);
		});

		it('should call component canDeactivate method and return boolean result', () => {
			const mockCanDeactivate = jasmine.createSpy('canDeactivate').and.returnValue(true);
			const component: CanComponentDeactivate = {
				canDeactivate: mockCanDeactivate,
			};

			const result = guard.canDeactivate(component);

			expect(mockCanDeactivate).toHaveBeenCalled();
			expect(result).toBe(true);
		});

		it('should call component canDeactivate method and return false result', () => {
			const mockCanDeactivate = jasmine.createSpy('canDeactivate').and.returnValue(false);
			const component: CanComponentDeactivate = {
				canDeactivate: mockCanDeactivate,
			};

			const result = guard.canDeactivate(component);

			expect(mockCanDeactivate).toHaveBeenCalled();
			expect(result).toBe(false);
		});

		it('should handle Observable<boolean> return type', () => {
			const mockCanDeactivate = jasmine.createSpy('canDeactivate').and.returnValue(of(true));
			const component: CanComponentDeactivate = {
				canDeactivate: mockCanDeactivate,
			};

			const result = guard.canDeactivate(component);

			expect(mockCanDeactivate).toHaveBeenCalled();
			expect(result).toBeInstanceOf(Observable);

			// Test the observable value
			(result as Observable<boolean>).subscribe((value) => {
				expect(value).toBe(true);
			});
		});

		it('should handle Observable<boolean> return type with false value', () => {
			const mockCanDeactivate = jasmine.createSpy('canDeactivate').and.returnValue(of(false));
			const component: CanComponentDeactivate = {
				canDeactivate: mockCanDeactivate,
			};

			const result = guard.canDeactivate(component);

			expect(mockCanDeactivate).toHaveBeenCalled();
			expect(result).toBeInstanceOf(Observable);

			// Test the observable value
			(result as Observable<boolean>).subscribe((value) => {
				expect(value).toBe(false);
			});
		});

		it('should handle Promise<boolean> return type', async () => {
			const mockCanDeactivate = jasmine.createSpy('canDeactivate').and.returnValue(Promise.resolve(true));
			const component: CanComponentDeactivate = {
				canDeactivate: mockCanDeactivate,
			};

			const result = guard.canDeactivate(component);

			expect(mockCanDeactivate).toHaveBeenCalled();
			expect(result).toBeInstanceOf(Promise);

			// Test the promise value
			const value = await (result as Promise<boolean>);
			expect(value).toBe(true);
		});

		it('should handle Promise<boolean> return type with false value', async () => {
			const mockCanDeactivate = jasmine.createSpy('canDeactivate').and.returnValue(Promise.resolve(false));
			const component: CanComponentDeactivate = {
				canDeactivate: mockCanDeactivate,
			};

			const result = guard.canDeactivate(component);

			expect(mockCanDeactivate).toHaveBeenCalled();
			expect(result).toBeInstanceOf(Promise);

			// Test the promise value
			const value = await (result as Promise<boolean>);
			expect(value).toBe(false);
		});

		it('should handle rejected Promise', async () => {
			const error = new Error('Deactivation check failed');
			const mockCanDeactivate = jasmine.createSpy('canDeactivate').and.returnValue(Promise.reject(error));
			const component: CanComponentDeactivate = {
				canDeactivate: mockCanDeactivate,
			};

			const result = guard.canDeactivate(component);

			expect(mockCanDeactivate).toHaveBeenCalled();
			expect(result).toBeInstanceOf(Promise);

			// Test the promise rejection
			try {
				await (result as Promise<boolean>);
				fail('Promise should have been rejected');
			} catch (err) {
				expect(err).toBe(error);
			}
		});

		it('should handle Observable error', () => {
			const error = new Error('Observable error');
			const errorObservable = new Observable<boolean>((subscriber) => {
				subscriber.error(error);
			});
			const mockCanDeactivate = jasmine.createSpy('canDeactivate').and.returnValue(errorObservable);
			const component: CanComponentDeactivate = {
				canDeactivate: mockCanDeactivate,
			};

			const result = guard.canDeactivate(component);

			expect(mockCanDeactivate).toHaveBeenCalled();
			expect(result).toBeInstanceOf(Observable);

			// Test the observable error
			(result as Observable<boolean>).subscribe({
				next: () => fail('Observable should have errored'),
				error: (err) => {
					expect(err).toBe(error);
				},
			});
		});
	});

	describe('Integration Tests', () => {
		it('should work with a real component implementation', () => {
			class TestComponent implements CanComponentDeactivate {
				private hasUnsavedChanges = false;

				setUnsavedChanges(hasChanges: boolean) {
					this.hasUnsavedChanges = hasChanges;
				}

				canDeactivate(): boolean {
					return !this.hasUnsavedChanges;
				}
			}

			const component = new TestComponent();

			// Test when no unsaved changes
			component.setUnsavedChanges(false);
			expect(guard.canDeactivate(component)).toBe(true);

			// Test when has unsaved changes
			component.setUnsavedChanges(true);
			expect(guard.canDeactivate(component)).toBe(false);
		});

		it('should work with async component implementation', async () => {
			class AsyncTestComponent implements CanComponentDeactivate {
				private shouldAllow = true;

				setShouldAllow(allow: boolean) {
					this.shouldAllow = allow;
				}

				async canDeactivate(): Promise<boolean> {
					// Simulate async operation (e.g., showing confirmation dialog)
					await new Promise((resolve) => setTimeout(resolve, 10));
					return this.shouldAllow;
				}
			}

			const component = new AsyncTestComponent();

			// Test async allow
			component.setShouldAllow(true);
			const allowResult = await (guard.canDeactivate(component) as Promise<boolean>);
			expect(allowResult).toBe(true);

			// Test async deny
			component.setShouldAllow(false);
			const denyResult = await (guard.canDeactivate(component) as Promise<boolean>);
			expect(denyResult).toBe(false);
		});

		it('should work with Observable component implementation', () => {
			class ObservableTestComponent implements CanComponentDeactivate {
				private allowSubject = new BehaviorSubject<boolean>(true);

				setAllow(allow: boolean) {
					this.allowSubject.next(allow);
				}

				canDeactivate(): Observable<boolean> {
					return this.allowSubject.asObservable();
				}
			}

			const component = new ObservableTestComponent();
			const result = guard.canDeactivate(component) as Observable<boolean>;

			expect(result).toBeInstanceOf(Observable);

			// Test initial value
			let receivedValue: boolean;
			result.subscribe((value) => {
				receivedValue = value;
			});
			expect(receivedValue!).toBe(true);

			// Test changed value
			component.setAllow(false);
			// Subscribe again to get the new value
			result.subscribe((value) => {
				receivedValue = value;
			});
			expect(receivedValue!).toBe(false);
		});
	});
});
