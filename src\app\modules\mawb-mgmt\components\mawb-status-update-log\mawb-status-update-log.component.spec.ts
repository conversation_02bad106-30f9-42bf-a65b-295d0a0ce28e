import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MawbStatusUpdateLogComponent } from './mawb-status-update-log.component';
import { TranslateModule } from '@ngx-translate/core';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { OperationStatus } from '../../models/mawb-event.model';

describe('MawbStatusUpdateLogComponent', () => {
	let component: MawbStatusUpdateLogComponent;
	let fixture: ComponentFixture<MawbStatusUpdateLogComponent>;

	const mockDialogData = '123';
	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [MawbStatusUpdateLogComponent, TranslateModule.forRoot()],
			providers: [{ provide: MAT_DIALOG_DATA, useValue: mockDialogData }, provideHttpClientTesting(), provideHttpClient()],
		}).compileComponents();

		fixture = TestBed.createComponent(MawbStatusUpdateLogComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('ngOnInit should set updateLogParam.mawbId from dialog data', () => {
		expect(component.updateLogParam.bachId).toBe(mockDialogData);
	});

	it('showStatus should map known OperationStatus value to key', () => {
		const someKey = Object.keys(OperationStatus)[0];
		const value = (OperationStatus as any)[someKey];
		expect(component.showStatus(value)).toBe(someKey);
	});

	it('showStatus should return input when not found in OperationStatus', () => {
		expect(component.showStatus('UNKNOWN_STATUS')).toBe('UNKNOWN_STATUS');
	});
});
