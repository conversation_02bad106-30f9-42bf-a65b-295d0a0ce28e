.orll-server-management {
	.iata-box {
		flex: 1;
	}

	ul {
		list-style: none;
		padding: 0;
		margin: 0;
		width: 245px;
		display: flex;
		flex-direction: column;
		background-color: var(--iata-grey-50);
		border-radius: 4px;
		box-shadow: 0 2px 10px var(--iata-grey-900);
		transition: box-shadow 0.3s ease;
		&:hover {
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		}
	}

	.iata-main-header__secondary-nav__dropdown-list__item {
		position: relative;
		padding: 0 24px;
		height: 48px;
		cursor: pointer;
		box-sizing: border-box;
		width: 100%;
		text-align: left;
		display: flex;
		align-items: center;
		white-space: nowrap;
		transition: all 0.3s cubic-bezier(0.35, 0, 0.25, 1);
		background-color: transparent;

		border-bottom: 1px solid rgba(0, 0, 0, 0.08);

		&:last-child {
			border-bottom: none;
		}

		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 8px;
			bottom: 8px;
			width: 2px;
			background-color: var(--iata-red-500);
			border-radius: 1px;
			transform: scaleX(0);
			transition: transform 0.3s cubic-bezier(0.35, 0, 0.25, 1);
		}

		&:hover {
			opacity: 1;
			background-color: rgba(0, 0, 0, 0.04);
			border-bottom-color: rgba(0, 0, 0, 0.12);
		}

		&:active,
		&.selected {
			opacity: 1;
			background-color: var(--iata-white) !important;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

			&::before {
				transform: scaleX(1.5);
			}
		}

		&.active {
			opacity: 1;
			background-color: rgba(63, 81, 181, 0.1);

			&::after {
				content: '';
				position: absolute;
				right: 0;
				top: 0;
				bottom: 0;
				width: 2px;
				background-color: var(--iata-blue-500);
				transform: scaleY(1);
				transition: all 0.3s cubic-bezier(0.35, 0, 0.25, 1);
			}
		}

	}

	.iata-main-header__secondary-nav__dropdown-list__item:last-child:not(.selected) {
		color: var(--iata-white);
		background-color: var(--iata-blue-900);
	}

	.role-name {
		overflow: hidden;
		text-overflow: ellipsis;
		width: 100%;
		padding-left: 8px;
	}

	a {
		text-decoration: none;
		color: inherit;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
	}

	.add-server-wrapper {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 8px;
	}

}
