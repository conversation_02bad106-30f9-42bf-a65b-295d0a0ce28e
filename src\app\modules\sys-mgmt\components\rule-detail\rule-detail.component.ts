import { Component, Inject, OnInit } from '@angular/core';
import { RuleService } from '../../services/rule.service';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AbstractControl, FormControl, FormGroup, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { Organization } from '@shared/models/organization.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { RuleDetailObj, RuleListObj } from '../../models/rule.model';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';

@Component({
	selector: 'orll-rule-detail',
	imports: [
		MatDialogModule,
		MatInputModule,
		MatDividerModule,
		TranslateModule,
		MatIconModule,
		MatButtonModule,
		MatRadioModule,
		MatFormFieldModule,
		ReactiveFormsModule,
		MatSelectModule,
	],
	templateUrl: './rule-detail.component.html',
	styleUrl: './rule-detail.component.scss',
})
export class RuleDetailComponent extends DestroyRefComponent implements OnInit {
	requestTypes = [
		{ code: '1', name: 'Change Request' },
		{ code: '2', name: 'Subscription Request' },
		{ code: '3', name: 'Access Delegation Request' },
	];
	ruleForm: FormGroup = new FormGroup(
		{
			holder: new FormControl<string[]>([], []),
			requestType: new FormControl<string>('', [Validators.required]),
			request: new FormControl<string[]>([], []),
			action: new FormControl<string>('', [Validators.required]),
		},
		{
			validators: [this.multiSelectValidator()],
		}
	);

	isEdit = false;
	isSave = false;
	dataLoading = false;
	orgList: Organization[] = [];
	requestList: Organization[] = [];

	constructor(
		private readonly ruleService: RuleService,
		private readonly orgService: OrgMgmtRequestService,
		private readonly dialog: MatDialog,
		private readonly translateService: TranslateService,
		private readonly dialogRef: MatDialogRef<RuleDetailComponent>,
		@Inject(MAT_DIALOG_DATA) public data: RuleListObj
	) {
		super();
	}

	ngOnInit() {
		this.orgService.getOrgList().subscribe((res) => {
			this.orgList = res;
			this.requestList = res;
		});

		if (this.data?.id) {
			this.dataLoading = true;
			this.ruleService.getRule(this.data).subscribe({
				next: (res) => {
					this.ruleForm.patchValue(res);
					this.isEdit = true;
					this.dataLoading = false;
				},
				error: () => (this.dataLoading = false),
			});
		}

		this.ruleForm
			.get('holder')
			?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((value: string[]) => {
				this.requestList = this.orgList.filter((item) => !value.includes(item.id));
				this.ruleForm.patchValue({
					request: this.ruleForm
						.get('request')
						?.value.filter((item: string) => !this.ruleForm.get('holder')?.value.includes(item)),
				});
			});
	}

	getFormData(): RuleDetailObj {
		const formData: RuleDetailObj = {
			holder: this.ruleForm.value.holder ?? [],
			requestType: this.ruleForm.value.requestType ?? '',
			request: this.ruleForm.value.request ?? [],
			action: this.ruleForm.value.action ?? '',
		};
		if (this.data?.id) {
			formData.id = this.data.id;
		}
		return formData;
	}

	saveRule() {
		this.isSave = true;
		this.ruleForm.markAllAsTouched();
		if (this.ruleForm.invalid) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				autoFocus: false,
				data: {
					content: this.translateService.instant('common.dialog.form.validate'),
				},
			});
			return;
		}

		this.dataLoading = true;
		this.ruleService.saveRule(this.getFormData()).subscribe({
			next: () => {
				this.dataLoading = false;
				this.dialogRef.close(true);
			},
			error: () => {
				this.dataLoading = false;
			},
		});
	}

	private multiSelectValidator(): ValidatorFn {
		return (control: AbstractControl): ValidationErrors | null => {
			const group = control as FormGroup;
			const holder: any[] = group.get('holder')?.value || [];
			const requester: any[] = group.get('request')?.value || [];

			if (holder.length >= 2) {
				if (requester.length > 0) {
					return { requestEmpty: true };
				}

				return null;
			}

			if (holder.length === 1) {
				const holderVal = holder[0];
				if (requester.includes(holderVal)) {
					return { requestIncludeHolder: true, conflictValue: holderVal };
				}
			}

			return null;
		};
	}
}
