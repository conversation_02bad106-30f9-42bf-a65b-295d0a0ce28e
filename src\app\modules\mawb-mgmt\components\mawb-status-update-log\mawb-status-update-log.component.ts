import { ChangeDetectionStrategy, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { OrllTableComponent } from '@shared/components/orll-table/orll-table.component';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { MawbStatusService } from '../../services/mawb-status.service';
import { OrllColumnDef } from '@shared/models/orlll-common-table';
import { OperationStatus, StatusUpdateLog } from '../../models/mawb-event.model';
import { MatIconModule } from '@angular/material/icon';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';

@Component({
	selector: 'orll-mawb-status-update-log',
	templateUrl: './mawb-status-update-log.component.html',
	styleUrl: './mawb-status-update-log.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatDialogModule, MatIconModule, TranslateModule, OrllTableComponent, SpinnerComponent],
	providers: [IataDateFormatPipe],
})
export class MawbStatusUpdateLogComponent implements OnInit {
	dataLoading = false;
	updateLogParam = {
		bachId: '',
	};
	columns: OrllColumnDef<StatusUpdateLog>[] = [
		{
			key: 'operationStatus',
			header: 'mawb.event.update.log.operationStatus',
			transform: (val) => this.showStatus(val),
		},
		{
			key: 'bizId',
			header: 'mawb.event.update.log.loId',
		},
		{
			key: 'type',
			header: 'mawb.event.update.log.type',
		},
		{
			key: 'newValue',
			header: 'mawb.event.update.log.newValue',
		},
		{
			key: 'errorMsg',
			header: 'mawb.event.update.log.errorMsg',
		},
		{
			key: 'createDate',
			header: 'mawb.event.update.log.createDate',
			transform: (val) => this.iataDateFormatPipe.transform(val),
		},
		{
			key: 'userName',
			header: 'mawb.event.update.log.userName',
		},
	];

	constructor(
		public readonly mawbStatusService: MawbStatusService,
		private readonly iataDateFormatPipe: IataDateFormatPipe,
		@Inject(MAT_DIALOG_DATA) public batchId: string
	) {}

	ngOnInit(): void {
		this.updateLogParam = {
			bachId: this.batchId,
		};
	}

	showStatus(val: string): string {
		for (const key in OperationStatus) {
			if (OperationStatus[key as keyof typeof OperationStatus] === val) {
				return key;
			}
		}
		return val;
	}
}
