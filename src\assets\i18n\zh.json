{"validators": {"required": "{{field}} is required", "email": "Please enter a valid email address", "number": "Please enter number", "pattern": "Please enter a valid {{requiredPattern}} value in {{field}}", "minlength": "Please enter at least {{minLength}} characters", "maxlength": "Please enter no more than {{maxLength}} characters", "min": "Please enter a value greater than or equal to {{min}}", "max": "Please enter a value less than or equal to {{max}}", "minDate": "Please enter a value greater than or equal to {{minDate}}", "maxOneDecimal": "{{field}} is positive number with maximum 1 decimal", "maxTwoDecimal": "{{field}} is positive number with maximum 2 decimal", "maxDecimal1": "positive number with maximum 1 decimal", "maxDecimal2": "positive number with maximum 2 decimal", "maxOneDecimalRound5": "{{field}} is positive number with maximum 1 decimal and round up to 0.5", "positiveNumber": "{{field}} is positive number"}, "common": {"mainHeader.app.title": "ONE Record", "mainHeader.app.subtitle": "Living Lab", "mainHeader.mainNav.shipment": "货物管理", "mainHeader.mainNav.sli": "SLI", "mainHeader.mainNav.sli.piece": "单件列表", "mainHeader.mainNav.hawb": "HAWB", "mainHeader.mainNav.mawb": "MAWB", "mainHeader.mainNav.changeRequests": "Change Requests", "mainHeader.mainNav.mawb.fhl": "House Manifest", "mainHeader.mainNav.upload": "Upload", "mainHeader.mainNav.payment": "Payment", "mainHeader.mainNav.fileBrowser": "File Browser", "mainHeader.mainNav.checkin": "Check-in", "mainHeader.mainNav.mf": "MF", "mainHeader.mainNav.users": "User", "mainHeader.mainNav.subscription": "订阅", "mainHeader.mainNav.distribution": "Distribution", "mainHeader.mainNav.distribution.quote": "Quote", "mainHeader.mainNav.distribution.booking": "Booking", "mainHeader.language.english": "English", "mainHeader.language.chinese": "中文", "mainHeader.settings": "Settings", "mainHeader.account": "Account", "mainHeader.userid": "User ID", "mainHeader.currentRole": "Current Role", "mainHeader.resources": "Resources", "mainHeader.logout": "注销", "mainHeader.mainNav.partner": "Partner", "mainHeader.mainNav.partner.permission": "权限", "mainHeader.mainNav.partner.subscription": "订阅", "mainHeader.mainNav.system": "System", "mainHeader.userlogin.success": "登录成功", "mainHeader.userlogin.fail": "登录失败", "mainFooter.title": "Our mission is to represent,", "mainFooter.subtitle": "lead and serve the airline industry", "mainFooter.support": "Support", "mainFooter.services": "Services", "mainFooter.store": "IATA store", "mainFooter.privacy": "Privacy", "mainFooter.legal": "Legal", "breadcrumb.home": "Home", "dialog.next": "下一步", "dialog.cancel": "取消", "dialog.later": "稍后", "dialog.ok": "确定", "dialog.revoke": "撤销", "dialog.approve": "批准", "dialog.reject": "拒绝", "dialog.alert.title": "注意", "dialog.alert.content.piece": "No permission granted on Pieces", "dialog.alert.content.403": "403 error", "dialog.request.delegation": "Request Access Delegation", "dialog.delegation.request.title": "Delegation Access Request", "dialog.delegation.request.description": "Description", "dialog.delegation.request.for": "Is Requested For", "dialog.delegation.request.by": "请求发起人", "dialog.delegation.request.at": "请求时间", "dialog.delegation.request.status": "请求状态", "dialog.delegation.request.permissions": "Permissions", "dialog.delegation.request.logisticsObject": "Logistics Object", "dialog.delegation.request.create": "Raise Delegation Request", "dialog.delegation.request.send": "Send", "dialog.delegation.request.approve": "批准", "dialog.delegation.request.reject": "拒绝", "delegation.permission.GET_LOGISTICS_EVENT": "GET_LOGISTICS_EVENT", "delegation.permission.GET_LOGISTICS_OBJECT": "GET_LOGISTICS_OBJECT", "delegation.permission.POST_LOGISTICS_EVENT": "POST_LOGISTICS_EVENT", "delegation.permission.PATCH_LOGISTICS_OBJECT": "PATCH_LOGISTICS_OBJECT", "delegation.request.tab": "权限请求", "dialog.cancel.content": "Are you sure to cancel?", "dialog.delete.content": "Are you sure to delete?", "dialog.unConsolidate.conent": "Are you sure to break down?", "selectOrg.title": "Select One from Below", "confirm.title": "Confirm", "dialog.form.validate": "Please fill out all the required and valid data.", "dialog.orglist.fail": "Get organization list failed", "dialog.orginfo.fail": "Get organization info failed", "change.request": {"object.type": "Logistic Object Type", "object.url": "Logistic Object URI", "version": "Version", "date": "Date", "changed.by": "Changed by", "status": "Status", "title": "Version History", "request.number": "MAWB Number", "dialog.title": "Change Request", "request.by": "Request By", "description": "Describe your change to Data Holder", "btn.revoke": "撤销", "btn.reject": "拒绝", "btn.approve": "批准", "detail.table.obj": "Logistic Object", "detail.table.property": "Property", "detail.table.oldValue": "Initial Value", "detail.table.newValue": "New Value", "no.permission": "No permission to retrieve version info"}, "table.action": "", "copy.success": "URI is copied", "copy.failed": "URI copy failed", "copy.no.text": "No text to be copied", "retrieve.btn": "Retrieve", "enter.uri.btn": "Enter URI to Retrieve", "enter.uri.title": "Enter URI to Retrieve a ", "no.access.error": "Not authorized to perform action error then it can follow the Request Access Delegation flow to request access"}, "sli": {"mgmt.title": "Shipper's Letter of Instructions (SLI) Management", "mgmt.goodsDescription": "货物描述", "mgmt.sliCode": "SLI Code", "mgmt.shipper": "Shipper Company Name", "mgmt.consignee": "Consignee Company Name", "mgmt.departureLocation": "始发站", "mgmt.arrivalLocation": "目的站", "mgmt.hawbNumber": "关联分运单", "mgmt.createDate": "Created Date Range", "mgmt.search": "搜索", "mgmt.reset": "重置", "mgmt.create": "创建托书", "mgmt.createHawb": "Create", "mgmt.edit": "Edit SLI", "mgmt.cancel": "取消", "mgmt.save": "保存", "mgmt.add.piece": "Piece", "mgmt.delete.piece": "Delete", "mgmt.list": "<PERSON><PERSON>'s Letter of Instructions (SLI) List", "mgmt.list.total.quantity": "单件总数", "mgmt.list.total.slac": "SLAC总数", "mgmt.company": {"companyName": "公司名称", "contactName": "联系人姓名", "country": "国家", "province": "省份", "city": "城市名称", "textualPostCode": "邮政编码", "address": "地址", "phoneNumber": "电话号码", "emailAddress": "邮箱", "shipper": "托运人", "consignee": "收货人", "alsoNotify": "其他通知方", "companyName.required": "Company Name is required", "contactName.required": "Contact Name is required", "country.required": "Country is required", "province.required": "Province is required", "city.required": "City is required", "address.required": "Address is required", "phoneNumber.required": "Phone Number is required", "pattern": {"number": "Please enter number", "email": "Please enter a valid email address"}}, "mgmt.routing": {"departureLocation": "始发站", "arrivalLocation": "目的站", "departureLocation.required": "Airport of Departure is required", "arrivalLocation.required": "Airport of Destination is required", "shippingInfo": "Accounting Information"}, "mgmt.pieceList": {"goodsDescription": "货物描述", "totalGrossWeight": "总毛重", "totalDimensions": "尺寸", "dimLength": "长", "dimWidth": "宽", "dimHeight": "高", "goodsDescription.required": "Description of Goods is required", "totalGrossWeight.required": "Total Gross Weight is required", "dimLength.required": "Length is required", "dimWidth.required": "Width is required", "dimHeight.required": "Height is required", "declaredValueForCustoms": "供海关用声明价值", "declaredValueForCarriage": "供运输用声明价值", "insuredAmount": "保险价值", "textualHandlingInstructions": "Handling Information Remarks", "weightValuationIndicator": "预付/到付", "incoterms": "Incoterms", "pattern": {"positiveNumber": "positive number", "decimalNumber1": "positive number with maximum 1 decimal", "decimalNumber2": "positive number with maximum 2 decimals", "decimalNumber2NIL": "positive number with maximum 2 decimals or NIL", "decimalNumber2NVD": "positive number with maximum 2 decimals or NVD", "decimalNumber2NCV": "positive number with maximum 2 decimals or NCV"}}, "piece": {"table.column.productDescription": "品名", "table.column.packagingType": "Packaging Type", "table.column.grossWeight": "Gross Weight(KG)", "table.column.dimensions": "尺寸", "table.column.pieceQuantity": "Piece Quantity", "table.column.slac": "SLAC", "table.column.actions": "Actions", "table.column.latestStatus": "最新事件", "addDialog.title": "选择单件类型", "addDialog.subtitle": "Please select piece type of you want to add", "addDialog.general": "普通货物", "addDialog.dg": "危险品货物", "addDialog.la": "活体动物", "addDialog.pieceType.required": "Piece type is required", "grossWeight.required": "Gross Weight is required", "packagingType.required": "Packaging Type is required", "productDescription.required": "Product Description is required", "add": "Add Piece", "edit": "Edit Piece", "add.pieceIn": "Add", "title": "Piece", "productDescription": "品名", "grossWeight": "Gross Weight", "dimensions": "尺寸", "upid": "件级货物编号", "packages": "Packages", "packagingType": "包装类型", "packagedIdentifier": "Packaged Identifier", "hsCommodityDescription": "税号", "nvdForCustoms": "是否有海关用声明价值", "nvdForCarriage": "是否有运输用声明价值", "shippingMarks": "运输标记", "textualHandlingInstructions": "其它操作要求", "pieceQuantity": "Piece Quantity", "pieceQuantity.required": "Piece Quantity is required", "done": "完成", "item.title": "Do you want to create Contained Pieces in this Piece?", "item.add": "添加内含小件", "contained.yes": "Yes", "contained.no": "No", "contained.title": "Contained Pieces", "item.description": "内含小件描述", "item.weight": "Weight", "item.quantity": "Quantity", "item.total": "内含小件总数", "slac.total": "SLAC", "create.success": "Create piece successfully", "create.fail": "Create piece failed", "update.success": "Update piece successfully", "update.fail": "Update piece failed", "detail.fail": "Get piece detail failed", "list.fail": "Get piece list failed", "no.sli.fail": "Please create SLI first", "consolidate.title": "拼装单件", "consolidate.btn": "拼装"}, "dgPiece": {"title": "危险品单件", "formItem": {"productDescription": "品名", "typeOfPackage": "包装类型", "packagedIdentifier": "Packaged Identifier", "whetherHaveDeclaredValueForCustoms": "是否有海关用声明价值", "whetherHaveDeclaredValueForCarriage": "是否有运输用声明价值", "specialProvisionId": "特殊条款编号", "explosiveCompatibilityGroupCode": "爆炸品相容性组别代码", "packagingDangerLevelCode": "包装危险等级代码", "technicalName": "技术名称", "unNumber": "UN编号", "shippersDeclaration": "<PERSON><PERSON>’s Declaration", "handlingInformation": "Handling Information", "allPackedInOne": "All Packed in One", "qValueNumeric": "放射性物质Q值", "upid": "件级货物编号", "shippingMarks": "运输标记", "grossWeight": "Gross Weight", "dimensions": "尺寸", "hsCommodityDescription": "税号", "properShippingName": "规范运输名称", "textualHandlingInstructions": "Textual Handling Instructions", "hazardClassificationId": "Hazard Classification ID", "additionalHazardClassificationId": "Additional Hazard Classification ID", "packingInstructionNumber": "包装指令编号", "complianceDeclaration": "符合性声明", "exclusiveUseIndicator": "专用标识", "authorizationInformation": "授权信息", "aircraftLimitationInformation": "机型限制信息", "item.productDescription": "品名", "item.quantity": "Quantity", "item.weight": "Weight", "item.title": "<PERSON><PERSON>", "item.add": "Add"}}, "liveAnimalPiece": {"title": "活体动物单件", "formItem": {"productDescription": "品名", "typeOfPackage": "包装类型", "packagedIdentifier": "Packaged Identifier", "speciesCommonName": "物种通用名", "speciesScientificName": "物种学名", "specimenDescription": "个体描述", "animalQuantity": "动物数量", "shippingMarks": "运输标记", "upid": "件级货物编号", "grossWeight": "Gross Weight", "dimensions": "尺寸", "whetherHaveDeclaredValueForCustoms": "是否有海关用声明价值", "whetherHaveDeclaredValueForCarriage": "是否有运输用声明价值", "textualHandlingInstructions": "Textual Handling Instructions"}}, "table.column.sliCode": "SLI Code", "table.column.shipper": "托运人", "table.column.consignee": "收货人", "table.column.goodsDescription": "Goods Description", "table.column.departureLocation": "始发站", "table.column.arrivalLocation": "目的站", "table.column.slac": "Piece Quantity", "table.column.createDate": "创建日期", "table.column.receivedFrom": "接收来源", "table.column.hawbNumber": "关联分运单", "table.column.share": "分享", "dialog.create.success": "Create SLI successfully", "dialog.create.fail": "Create SLI failed", "dialog.update.success": "Update SLI successfully", "dialog.update.fail": "Update SLI failed", "dialog.detail.fail": "Get SLI detail failed", "dialog.list.fail": "Get SLI list failed", "dialog.shipper.fail": "Get shipper info failed", "share.title": "Share SLI", "create.pieces.confirm": "Please finalize SLI creation later to associate these pieces"}, "shared": {"table.column.name": "公司名称", "table.column.orgType": "公司类型", "button.name": "分享"}, "hawb": {"mgmt.title": "分运单", "mgmt.hawbNumber": "分运单编号", "mgmt.create": "根据SLI创建分运单", "mgmt.create.fromSli": "Create House Air Waybill from SLI", "mgmt.create.fromSliDetail": "Create House Air Waybill", "mgmt.edit": "Edit HAWB", "mgmt.fhl.total": "Total HAWB", "mgmt.fhl.total.slac": "SLAC总数", "mgmt.fhl.total.piece": "Total Piece", "mgmt.piecelist": "分运单单件清单", "mgmt.total.piece": "单件总数", "mgmt.associated.sli": "关联托书", "mgmt.latest.status": "最新事件", "table.column.hawbNumber": "分运单编号", "table.column.shipper": "托运人", "table.column.consignee": "收货人", "table.column.goodsDescription": "Goods Description", "table.column.origin": "始发站", "table.column.destination": "目的站", "table.column.pieceQuantity": "Piece Quantity", "table.column.createDate": "创建日期", "table.column.latestStatus": "最新事件", "table.column.eventDate": "事件日期", "table.column.mawbNumber": "关联主运单", "table.column.share": "分享", "table.column.copy": "Copy", "table.column.totalGrossWeight": "Weight", "table.column.pieceNumber": "Number of Pieces", "table.column.slac": "SLAC", "table.column.textualHandlingInformation": "Special Handing Code", "table.column.countryCode": "Custom Information ISO Country Code", "table.column.contentCode": "Customs, Security and Regulatory Control Information Identifier", "table.column.otherCustomsInformation": "Supplementary Customs, Security and Regulatory Control Information", "dialog.list.fail": "Get HAWB list failed", "createHawb.success": "Create HAWB Success", "preview.awb": "Preview AWB", "updateHawb.success": "HAWB is successfully updated", "updateHawb.error": "HAWB update fails due to server unavailability, please try again", "share.title": "分享分运单", "carrierAgent": {"title": "承运人代理", "company": "公司名称", "agentIataCode": "代理IATA代码", "country": "国家", "province": "省份", "cityName": "城市名称", "textualPostCode": "邮政编码", "address": "地址", "phoneNumber": "电话号码", "email": "邮箱", "formInvalid": "Carrier's Agent Form Invalid"}, "issuedBy": {"title": "发放人员姓名", "content": "To be populated when MAWB is created"}, "formItem": {"hawbPrefix": "分运单前缀", "hawbNumber": "分运单编号", "accountingInformation": "Accounting Information", "handingInformation": "Handing Information", "noOfPiecesRcp": "No. of Pieces RCP", "grossWeight": "Gross Weight", "volume": "体积", "rateClass": "Rate Class", "chargeableWeight": "计费重量", "rateCharge": "Rate/Charge", "total": "Total", "natureAndQuantityOfGoods": "Nature and Quantity of Goods (including Dimensions or Volume)", "date": "Date", "atPlace": "At(Place)", "signatureOfShipperOrHisAgent": "Signature of <PERSON><PERSON> or his Agent", "signatureOfCarrierOrItsAgent": "Signature of Carrier or its Agent"}, "airportInfo": {"departureAndRequestedRouting": "起运机场及要求路线", "departureAndRequestedRouting.required": "Airport of Departure and Requested Routing is required", "airportOfDestination": "Airport Of Destination", "amountOfInsurance": "Amount Of Insurance", "flight": "航班", "to": "To", "toBy2ndCarrier": "第二承运人目的地", "toBy3rdCarrier": "第三承运人目的地", "date": "Date", "byFirstCarrier": "第一承运人", "by2ndCarrier": "第二承运人", "by3rdCarrier": "By 3rd carrier", "wtOrVal": "WT/VAL", "other": "Other", "declaredValueForCarriage": "Declared Value For Carriage", "declaredValueForCustoms": "Declared Value For Customs"}, "prepaidAndCollect": {"prepaidTitle": "Prepaid", "collectTitle": "Collect", "weightCharge": "Weight Charge", "valuationCharge": "Valuation Charge", "tax": "Tax", "totalOtherChargesDueAgent": "Total Other Charges Due Agent", "totalOtherChargesDueCarrier": "Total Other Charges Due Carrier", "totalPrepaid": "Total Prepaid", "totalCollect": "Total Collect"}, "otherCharges": {"title": "Other Charges", "chargePaymentType": "Charge Payment Type", "entitlement": "Entitlement", "otherChargeCode": "Other Charge Code", "otherChargeAmount": "Other Charge Amount", "addButton": "Other Charges", "formInvalid": "Carrier's Agent Form Invalid"}}, "mawb": {"mgmt.title": "Master Air Waybill", "mgmt.mawbNumber": "MAWB Number", "mgmt.create": "Create MAWB from HAWBs", "mgmt.create.fromHawb": "Create MAWB from my HAWBs", "mgmt.create.fromHawbDetail": "Master Air Waybill", "mgmt.create.fromSelectedHawb": "Select to create MAWB", "mgmt.edit": "Edit MAWB", "mgmt.export": "Export PDF", "mgmt.close": "Close", "mgmt.exporting": "Exporting...", "exportpdf.success": "Export PDF successfully", "exportpdf.failed": "Export PDF failed", "exportpdf.generating": "Generating PDF...", "mgmt.airlineCode": "Airline Code", "mgmt.latestStatus": "最新事件", "mgmt.btn.post.request": "Post Change Request", "mgmt.accountingNoteText.shipper": "<PERSON><PERSON>'s Account Number", "mgmt.accountingNoteText.consignee": "Consignee's Account Number", "mgmt.shipment.shipper": "New Shipper", "mgmt.shipment.consignee": "New Consignee", "table.column.share": "分享", "table.column.copy": "Copy", "table.column.mawbNumber": "MAWB Number", "table.column.airlineCode": "Airline Code", "table.column.goodsDescription": "Goods Description", "table.column.origin": "始发站", "table.column.destination": "目的站", "table.column.latestStatus": "最新事件", "table.column.eventDate": "事件日期", "table.column.createDate": "创建日期", "share.title": "Share MAWB", "createMawb.success": "Create MAWB Success", "preview.awb": "Preview AWB", "updateMawb.success": "MAWB is successfully updated", "post.success": "Post Change Request has been successfully sent", "updateMawb.error": "MAWB update fails due to server unavailability, please try again", "dialog.weight.validate": "selected HAWBs have different WT/VAL value", "dialog.other.validate": "selected HAWBs have different Other charge type value", "carrierAgent": {"title": "承运人代理", "company": "公司名称", "agentIataCode": "代理IATA代码", "accountingNoteText": "Account No.", "country": "国家", "province": "省份", "cityName": "城市名称", "textualPostCode": "邮政编码", "address": "地址", "phoneNumber": "电话号码", "email": "邮箱", "formInvalid": "Carrier's Agent Form Invalid"}, "issuedBy": {"title": "发放人员姓名", "content": "To be populated when MAWB is created"}, "formItem": {"mawbPrefix": "MAWB Prefix", "mawbNumber": "MAWB Number", "mawbNumber.checkLength": "MAWB Number must be 8 digits", "mawbNumber.checkDigit": "MAWB Number's 8th digit should be the mod7 of the first 7 digits, which is ", "accountingInformation": "Accounting Information", "handingInformation": "Handing Information", "noOfPiecesRcp": "No. of Pieces RCP", "grossWeight": "Gross Weight", "volume": "体积", "serviceCode": "Service Code", "rateClass": "Rate Class", "chargeableWeight": "计费重量", "rateCharge": "Rate/Charge", "total": "Total", "natureAndQuantityOfGoods": "Nature and Quantity of Goods (including Dimensions or Volume)", "destinationCurrencyRate": "Currency Conversion Rate", "destinationCollectCharges": "Collect Charges in Destination Currency", "totalCollectCharges": "Total Collect Charges", "destinationCharges": "Charges at Destination", "shippingInfo": "Optional Shipping Information", "shippingRefNo": "Reference Number", "date": "Date", "atPlace": "At(Place)", "signatureOfShipperOrHisAgent": "Signature of <PERSON><PERSON> or his Agent", "signatureOfCarrierOrItsAgent": "Signature of Carrier or its Agent"}, "airportInfo": {"departureAndRequestedRouting": "Airport of Departure and Requested Routing", "airportOfDestination": "Airport Of Destination", "amountOfInsurance": "Amount Of Insurance", "chargesCode": "Charges Code", "flight": "航班", "to": "To", "toBy2ndCarrier": "第二承运人目的地", "toBy3rdCarrier": "第三承运人目的地", "date": "Date", "byFirstCarrier": "第一承运人", "by2ndCarrier": "第二承运人", "by3rdCarrier": "By 3rd carrier", "wtOrVal": "WT/VAL", "other": "Other", "declaredValueForCarriage": "Declared Value For Carriage", "declaredValueForCustoms": "Declared Value For Customs"}, "prepaidAndCollect": {"prepaidTitle": "Prepaid", "collectTitle": "Collect", "weightCharge": "Weight Charge", "valuationCharge": "Valuation Charge", "tax": "Tax", "totalOtherChargesDueAgent": "Total Other Charges Due Agent", "totalOtherChargesDueCarrier": "Total Other Charges Due Carrier", "totalPrepaid": "Total Prepaid", "totalCollect": "Total Collect"}, "otherCharges": {"title": "Other Charges", "chargePaymentType": "Charge Payment Type", "entitlement": "Entitlement", "otherChargeCode": "Other Charge Code", "otherChargeAmount": "Other Charge Amount", "addButton": "Other Charges", "formInvalid": "Carrier's Agent Form Invalid"}, "event": {"update.title": "Event Update", "milestone.date": "Specify if the milestone took place the other day", "planned.milestone": "This is a planned milestone", "partial.milestone": "This is a partially reached milestone", "update.btn": "更新", "bulk.update.btn": "Bulk Update", "update.log.btn": "Update Log", "update.log.title": "Log Items", "update.log.operationStatus": "Operation Status", "update.log.loId": "Logistics Objects Id", "update.log.type": "Type", "update.log.newValue": "Milestone", "update.log.errorMsg": "Error <PERSON>", "update.log.createDate": "Create Date", "update.log.userName": "Update By", "update.log.orgName": "Org Name", "time.type.error": "empty event time type", "update.time.error": "empty update time", "empty.error": "Please select the event", "choose.mawb": "Please select MAWB/HAWB/PIECE", "tracking.title": "Event Tracking", "status.history": "Status History", "history.table.event": "Event", "history.table.time": "Update Time", "history.table.user": "Update By", "history.table.event.time.type": "Event Time Type", "history.table.partial.event": "Partial Event", "no.permission": "No permission to retrieve Logistic Events", "back.btn": "Back"}}, "partner": {"mgmt.head": "Partner Access", "mgmt.title": "Please note following rules have been pre-configured as such:", "mgmt.title1": "1. Airlines can only access MAWBs if MAWB Prefix = Airline Settlement Code with full permissions granted", "mgmt.title2": "2. Shippers can only access HAWBs created from his company's SLIs with full permissions granted.", "mgmt.mawbNumber": "MAWB Number", "mgmt.addPartner": "Add Partner", "mgmt.edit": "Edit", "mgmt.save": "保存", "mgmt.latestStatus": "最新事件", "table.column.businessData": "Business Data", "table.column.partner": "Partner", "table.column.GET_LOGISTICS_OBJECT": "GET_LOGISTICS_OBJECT", "table.column.PATCH_LOGISTICS_OBJECT": "PATCH_LOGISTICS_OBJECT", "table.column.POST_LOGISTICS_EVENT": "POST_LOGISTICS_EVENT", "table.column.GET_LOGISTICS_EVENT": "GET_LOGISTICS_EVENT", "tab.configuration": "Configuration", "tab.request": "Request"}, "upload": {"browser.dragAndDrop": "Drag & Drop file(s) here", "browser.or": "or", "browser.browse": "Browse for file(s)", "progress.lastModified": "Last modified at:", "progress.fileSize": "File size:"}, "filesManagement": {"category": "File category", "table.column.name": "Name", "table.column.size": "Size", "table.column.createdAt": "Created at", "table.column.actions": "Actions", "table.action.view": "View", "table.action.download": "Download", "table.action.delete": "Delete", "noFilesFound": "Sorry, we didn't find any files. You might need to upload them first!", "selectedFile.header": "Selected file view"}, "ifgPayment": {"paymentInitFailed": "Payment initiation failed!", "renderFailed": "Payment screen rendering failed!"}, "checkin": {"passenger": {"title": "Passenger details", "name": "Full Name", "gender": "Gender", "nationality": "Nationality", "nationality.placeholder": "Select Nationality", "birthDate": "Birth Date"}, "flight": {"title": "Flight details", "carrier": "Operating Carrier", "carrier.placeholder": "Select Carrier", "departureAirport": "Departure Airport", "departureAirport.placeholder": "Select Airport", "departureDate": "Departure Date", "arrivalAirport": "Arrival Airport", "arrivalAirport.placeholder": "Select Airport", "arrivalDate": "Arrival Date", "flightNumber": "Flight Number", "addLeg": "Add Leg", "deleteLeg": "Delete Leg", "checkin": "Check-in"}, "boardingPass": {"title": "Here is your boarding pass!", "subTitle": "Send boarding pass to your e-mail address:", "email": "邮箱", "send": "Send"}}, "users": {"mgmt.list": "User Management", "mgmt.keyword": "Please input keyword to search", "mgmt.search": "搜索", "table.column.userName": "User Name", "table.column.email": "User Email", "table.column.firstName": "First Name", "table.column.lastName": "Last Name", "table.column.orgName": "Company/Organization", "table.column.primaryOrgName": "Primary Resident Company", "table.column.userType": "User Type", "table.column.roles": "Roles", "table.column.status": "Status", "table.column.lastAccessed": "Last Accessed At", "table.column.actions": "Actions", "table.action.updateUser": "Update User", "buttons.create": "Add User", "noDataFound": "We didn't find any existing user!", "mgmt.create.title": "Create User", "mgmt.create.firstName": "First Name", "mgmt.create.firstName.required": "First Name is required", "mgmt.create.lastName": "Last Name", "mgmt.create.lastName.required": "Last Name is required", "mgmt.create.email": "邮箱", "mgmt.create.email.required": "Email is required", "mgmt.create.orgName": "Company/Organization", "mgmt.create.orgName.required": "Company/Organization is required", "mgmt.create.primaryOrgName": "Primary Resident Company", "mgmt.create.userType": "User Type", "mgmt.create.userType.required": "User Type are required", "mgmt.create.primaryOrgName.required": "Primary Resident Company is required", "mgmt.create.secondary": "Secondary Resident Companies", "mgmt.create.secondary.orgName": "Resident Company", "mgmt.create.secondary.add": "Add", "mgmt.create.email.iataEmail": "Email should be @iata.org or @external.iata.org", "create.email.duplicatedUser": "User with such email already exists", "create.roles": "Roles", "create.roles.required": "Roles are required", "create.status": "Status", "create.status.required": "Status is required", "create.cancel": "取消", "create.submit": "保存", "update.title": "Update User", "update.submit": "保存"}, "pagination": {"itemsPerPage": "Records per page", "nextPage": "Next page", "previousPage": "Previous page", "firstPage": "First page", "lastPage": "Last page", "rangeEmpty": "0 of 0", "rangeLabel": "{{start}} - {{end}} of {{length}}"}, "subscription": {"title": "订阅", "booking.requset.text": "Airlines can only subscribe to Booking Requests raised for its own Booking Options", "request.table.subscriber": "订阅方", "request.table.topicType": "主题类型", "request.table.topic": "主题", "request.table.requestBy": "请求发起人", "request.table.requestAt": "请求时间", "request.table.status": "请求状态", "request.detail": "请求详情", "detail.permissions": "Permissions", "tab.configuration": "Configuration", "tab.request": "Request", "config.table.eventType": "Subscription Event Type", "config.table.expiersAt": "Expires At", "btn.new": "New Subscription", "btn.invite": "Invite to Subscribe", "create.title": "Create Subscription", "edit.title": "Edit Subscription", "form.event": "Subscription Event Type", "btn.invite.confirm": "Invite", "topic.placeholder": "input URI in here..."}, "notifications": {"title": "通知", "view.more": "Read all Notifications", "toggle.read": "Only Unread", "btn.mark": "<PERSON> as <PERSON>", "share.content": " has shared ", "updated": " has been updated ", "created": " has been created ", "event.content": " has been added an event"}, "system": {"title": "系统管理", "rule": {"title": "规则", "table.holder": "Holder", "table.request.type": "Request Type", "table.requester": "Requester", "table.action": "Action", "info": "All Change Requests on any Logistic Objects as part of a MAWB are auto-rejected if the MAWB (Shipment:Master) has RCS event posted", "approve": "Auto Approve", "reject": "Auto Reject", "create.title": "Create Rule", "edit.title": "Edit Rule", "requester.error1": "holders are more than one, requesters must be empty", "requester.error2": "requester can not include holders", "booking.info": "Booking Options patched to my Booking Option Request by any Airlines are auto-approved so that Forwarder can always see received Booking Options."}, "server": {"title": "Server", "matpanel.title.server": "Server", "matpanel.label.uri": "URI", "matpanel.label.endpoint": "Endpoint", "matpanel.label.apiVersion": "API Version", "matpanel.label.contentType": "Content Type", "matpanel.label.encoding": "Encoding", "matpanel.label.language": "Language", "matpanel.label.ontology": "Ontology", "matpanel.label.ontologyVersion": "Ontology Version", "matpanel.title.organization": "Organization", "matpanel.label.residentsType": "Residents Type", "matpanel.label.companyName": "公司名称", "matpanel.label.forwarderIATACode": "Forwarder IATA Code", "matpanel.label.airlineCode": "Airline Code", "matpanel.label.airlinePrefix": "Airline Prefix", "matpanel.label.country": "国家", "matpanel.label.province": "省份", "matpanel.label.city": "城市名称", "matpanel.label.address": "地址", "matpanel.label.postCode": "Post Code", "matpanel.title.keycloak": "Keycloak", "matpanel.label.graphDbUrl": "GraphDb Url", "matpanel.label.neOneUrl": "NeOne Url", "matpanel.label.keycloakUrl": "Keycloak Url", "matpanel.label.grantType": "GrantType", "matpanel.label.clientId": "Client Id", "matpanel.label.clientSecret": "Client Secret", "matpanel.label.logisticsAgentUri": "Logistics Agent <PERSON><PERSON>", "matpanel.title.contact": "Contact", "matpanel.label.contactRole": "Contact Role", "matpanel.label.jobTitle": "Job Title", "matpanel.label.contactName": "联系人姓名", "matpanel.label.contactDetailType": "Contact Detail Type", "matpanel.label.textualValue": "Textual Value", "button.retrieveServerInfo": "Retrieve Server Info", "button.retrieveOrganizationInfo": "Retrieve Organization Info", "button.retrieveContactInfo": "Retrieve Contact Info", "button.save": "保存", "button.cancel": "取消", "button.addNewExternalServer": "Add New External Server", "button.deleteExternalServer": "Delete External Server", "tab.oRLLResident": "ORLL Resident", "tab.externalServers": "External Servers", "contact.new": "Add Contact", "retrive.org.btn": "Retrieve Organization Info", "retrive.contact.btn": "Retrieve Contact Info", "uri": "URI", "matpanel.title.permission": "Menu Permission"}}, "booking": {"option": {"table.product": "Requested Product", "table.departure": "Requested Departure", "table.arrival": "Requested Arrival", "table.status": "Status", "create.btn": "Raise Booking Option Request", "view.title": "Booking Option Request", "chargeable.weight": "计费重量", "commodity.code": "Commodity Code", "handling.code": "Special Handling Codes", "handling.instruction": "其它操作要求", "subTitle.itinerary": "Itinerary", "departure.location": "Departure Location", "arrival.location": "Arrival Location", "max.segment": "Max Segments", "transport.id": "Preferred Transport ID", "earliest.acceptance.time": "Earliest Acceptance Time", "latest.acceptance.time": "Latest Acceptance Time", "latest.arrival.time": "Latest Arrival Time", "shipment.available.date": "Shipment Available Date", "currency": "<PERSON><PERSON><PERSON><PERSON>", "subTitle.preference": "Preference", "subTitle.booking.shipment": "Booking Shipment", "select.airline": "Select an Airline", "request.title": "Booking Option Request from ", "to.forwarder": "Send back to Forwarder", "request.booking": "Request Booking ", "from": " - IATA Code #", "form.departure": "始发站", "form.arrival": "Airport of Arrival", "form.airline.code": "Operating Carrier ( Airline Code)", "form.flight.number": "Flight Number", "form.departure.time": "Departure Date/Time", "form.arrival.time": "Arrival Date/Time", "form.charge.type": "Charge Type", "form.charge.rate.class": "Charge Rate Class", "form.rate": "Rate/Weight Charge", "form.payment.type": "Payment Type", "form.entitlement": "Entitlement", "form.product.code": "Product Code", "form.offer.valid.from": "Offer <PERSON><PERSON>", "form.offer.valid.to": "Offer <PERSON><PERSON>", "add.charge.btn": "Add Charge", "del.itinerary": "Remove Itinerary", "add.itinerary": "Add Itinerary", "send.back.btn": "Send back to Forwarder", "add.booking.option.btn": "Add Booking Option", "departure.check.warning": "Departure location should match previous arrival location", "price.title": "Price", "grand.total": "Grand Total =", "share.history": "Share History", "request.to": "Requested To", "request.date": "Requested Date", "created.succ": "Raise booking option request for mawb successfully", "created.failed": "Raise booking option request for mawb failed", "select": "Please select one booking option", "iata.code": "Code  "}, "mgmt.requestedBy": "Requested By", "mgmt.requestedProduct": "Requested Product", "mgmt.requestedFlight": "Requested Flight", "mgmt.requestedStatus": "Requested Status", "mgmt.departureLocation": "Departure Location", "mgmt.arrivalLocation": "Arrival Location", "mgmt.flightDate": "Flight Date", "mgmt.mawbNumber": "关联主运单", "request.pieceGroupCount": "Piece Quantity", "request.totalGrossWeight": "Gross Weight", "request.chargeableWeight": "计费重量", "request.totalDimensions": "尺寸", "request.expectedCommodity": "Commodity Code", "request.specialHandlingCodes": "Special Handling Codes", "request.textualHandlingInstructions": "其它操作要求", "request.totalPrice": "Price Grand Total", "dialog.title.option": "Booking Option", "dialog.title.request": "Booking Request", "dialog.button.request": "Request Booking", "dialog.button.confirm": "Confirm Booking", "dialog.button.create.master": "Create MAWB", "dialog.button.update.master": "Update MAWB", "dialog.button.mawb": "Create MAWB"}, "ecsd": {"title": "安保声明", "create.btn": "创建安保声明", "edit.btn": "Edit eCSD", "view.btn": "View eCSD", "table.status": "安保状态", "table.method": "安检方法", "table.ground": "免于安检原因", "table.from": "接收来源", "table.issue.by": "发放人员姓名", "table.issue.on": "发放时间", "hawb.table.number": "分运单编号", "hawb.table.shipper": "托运人", "hawb.table.consignee": "收货人", "hawb.table.description": "Goods Descripiton", "hawb.table.origin": "始发站", "hawb.table.destination": "目的站", "hawb.table.weight": "Weight", "hawb.table.slac": "SLAC", "info.regulated": "管制实体类别和标识符（发放安保状态的管制方", "info.reason": "发放安保状态理由", "info.accepted": "管制实体类别和标识符（已接受另一管制代理人为托运货物提供的安保状态的管制方）", "form.category": "管制实体类别", "form.identifier": "管制实体标识符", "form.status": "安保状态", "form.screen": "是否免于安检", "form.method": "安检方法", "form.ground": "免于安检原因", "form.from": "接收来源", "form.issue.by": "发放人员姓名/雇员ID", "form.issue.on": "发放时间", "form.information": "附加安保信息", "table.piece.number": "Piece Number", "table.hawb.number": "分运单编号", "table.mawb.number": "MAWB Number", "table.air.code": "Airline Code", "table.good.description": "Goods Description", "table.package.type": "Packaging Type", "table.gross.weight": "Gross Weight", "table.dimensions": "尺寸", "yes": "Yes", "no": "No", "save.btn": "保存"}, "calculate": {"title": "Calculate Volume and Chargeable Weight", "column.productDescription": "品名", "column.dimensions": "尺寸", "column.grossWeight": "Gross Weight", "column.slac": "slac", "label.pieceRCP": "实际接收件数:", "label.pieceGrossWeight": "单件毛重:", "label.totalSlac": "Total Slac:", "label.totalGrossWeight": "总毛重", "label.volume": "体积", "label.chargeableWeight": "计费重量", "btn.save": "保存", "btn.cancel": "取消"}, "home": {"dashboard": "控制台", "dashboard.title": "欢迎来到 ONE Record 创新应用实验室，这是一个用于探索国际航空运输协会（IATA）ONE Record 功能的演示环境", "dashboard.links": "实用链接", "dashboard.links.standard.title": "ONE Record Standard Introduction", "dashboard.links.standard": "Introduction to ONE Record, a standard for data sharing and creates a single record view of the shipment", "dashboard.links.api.title": "ONE Record API Specification", "dashboard.links.api": "This ONE Record API specification is part of the ONE Record standard. It defines a standard, programming language-agnostic interface for the interaction with the ONE Record Web API.", "dashboard.links.dataModel.title": "ONE Record Data Model Specification", "dashboard.links.dataModel": "This ONE Record data model specification is part of the ONE Record standard. It details core concepts of the data model described in the ONE Record cargo and ONE Record core code lists ontologies. It aims to provide explanations and examples for usage of the data model for implementers.", "dashboard.chart.weeklyTotal": "新增用户总数/周", "dashboard.chart.weeklyExternal": "外部新增用户/周", "dashboard.chart.weeklyActive": "周活用户", "dashboard.chart.weeklyTotalUser": "注册用户总数/周", "dashboard.chart.weeklyTotalExternalUser": "Weekly Total External User", "dashboard.chart.weeklyTotalActiveUser": "周活用户总数", "dashboard.chart.userNumber": "User Number", "dashboard.chart.growthNumber": "增长数量", "dashboard.chart.growthRate": "增长率", "dashboard.activity.stream": "活动日志", "dashboard.activity.stream.clean": "Clean Up Activity Stream", "dashboard.activity.system.clean": "Clean Up System Data", "dashboard.activity.stream.clean.msg": "Are you sure to clean up all the Activity Stream logs? system data such as Shipment records(SLI/HAWB/MAWB) are not impacted.", "dashboard.activity.system.clean.msg": "Are you sure to clean up all the system data such as Shipment records(SLI/HAWB/MAWB), Change/Subscription/Access Delegation Requests, Subscriptions, Quote/Bookings and Notifications?", "dashboard.activity.clean": "Clean", "dashboard.activity.stream.download": "下载JSON文件"}}