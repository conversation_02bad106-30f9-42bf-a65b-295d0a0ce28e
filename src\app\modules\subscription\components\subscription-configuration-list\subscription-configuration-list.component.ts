import { Component } from '@angular/core';
import { SubscriptionConfigRequest, SubscriptionConfigurationListObj } from '../../models/subscription.model';
import { OrllColumnDef } from '@shared/models/orlll-common-table';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { SubscriptionConfigurationService } from '../../services/subscription-configuration.service';
import { OrllTableComponent } from '@shared/components/orll-table/orll-table.component';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { SubscriptionConfigurationDetailComponent } from '../subscription-configuration-detail/subscription-configuration-detail.component';
import { ShareDialogComponent } from '@shared/components/share-dialog/share-dialog.component';
import { ShareType } from '@shared/models/share-type.model';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { InviteSubscribeComponent } from '../invite-subscribe/invite-subscribe.component';
import { formatDateTime } from '@shared/utils/common.utils';

@Component({
	selector: 'orll-subscription-configuration-list',
	imports: [MatDialogModule, OrllTableComponent, MatInputModule, MatIconModule, TranslateModule, ReactiveFormsModule, MatButtonModule],
	templateUrl: './subscription-configuration-list.component.html',
	styleUrl: './subscription-configuration-list.component.scss',
})
export class SubscriptionConfigurationListComponent extends RolesAwareComponent {
	configrationSearchForm: FormGroup = new FormGroup({
		description: new FormControl<string>(''),
	});
	configurationParam: SubscriptionConfigRequest = {};

	columns: OrllColumnDef<SubscriptionConfigurationListObj>[] = [
		{
			key: 'description',
			header: 'common.dialog.delegation.request.description',
		},
		{
			key: 'topicType',
			header: 'subscription.request.table.topicType',
			transform: (val) => this.showTopicType(val),
		},
		{
			key: 'topic',
			header: 'subscription.request.table.topic',
		},
		{
			key: 'subscriptionEventType',
			header: 'subscription.config.table.eventType',
		},
		{
			key: 'expiresAt',
			header: 'subscription.config.table.expiersAt',
			transform: (val) => formatDateTime(val),
		},
		{
			key: 'actions',
			header: 'common.table.action',
			actions: [
				{
					iconKey: 'share',
					iconClickAction: (row: SubscriptionConfigurationListObj) => this.shareConfiguration(row),
				},
				{
					iconKey: 'edit',
					iconClickAction: (row: SubscriptionConfigurationListObj) => this.createOrUpdateConfiguration(row),
				},
				{
					iconKey: 'delete',
					iconClickAction: (row: SubscriptionConfigurationListObj) => this.deleteConfiguration(row),
				},
			],
		},
	];
	dataLoading = false;

	constructor(
		private readonly dialog: MatDialog,
		public readonly configurationService: SubscriptionConfigurationService,
		private readonly translateService: TranslateService
	) {
		super();
	}

	onSearch() {
		this.configurationParam = { ...this.configurationParam, description: this.configrationSearchForm.value.description ?? '' };
	}

	shareConfiguration(row: SubscriptionConfigurationListObj) {
		this.dialog.open(ShareDialogComponent, {
			width: '60vw',
			autoFocus: false,
			data: {
				shareType: ShareType.SUBSCRIPTION_CONFIG,
				param: row.id,
			},
		});
	}

	createOrUpdateConfiguration(row: SubscriptionConfigurationListObj | undefined) {
		const dialogRef = this.dialog.open(SubscriptionConfigurationDetailComponent, {
			width: '60vw',
			autoFocus: false,
			data: row,
		});

		dialogRef.afterClosed().subscribe((res) => {
			if (res === true) {
				this.configurationParam = { ...this.configurationParam };
			}
		});
	}

	deleteConfiguration(row: SubscriptionConfigurationListObj) {
		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translateService.instant('common.dialog.delete.content'),
			},
		});

		dialogRef.afterClosed().subscribe((res) => {
			if (res === true) {
				this.dataLoading = true;
				this.configurationService.deleteConfiguration(row.id).subscribe({
					next: () => {
						this.dataLoading = false;
						this.refreshList();
					},
					error: () => (this.dataLoading = false),
				});
			}
		});
	}

	inviteToSubscribe() {
		this.dialog.open(InviteSubscribeComponent, {
			width: '60vw',
			autoFocus: false,
		});
	}

	refreshList() {
		this.configurationParam = { ...this.configurationParam };
	}

	showTopicType(val: string) {
		return val ? val.replace(/#/g, '#\n') : val;
	}
}
