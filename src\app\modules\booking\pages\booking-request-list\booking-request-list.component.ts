import { ChangeDetectionStrategy, Component, OnInit, Query<PERSON>ist, ViewChildren } from '@angular/core';
import { ActionType, OrllColumnDef } from '@shared/models/orlll-common-table';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { OrllTableComponent } from '@shared/components/orll-table/orll-table.component';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { BookingRequestListObj, BookingRequestSearchParam } from '../../models/booking.model';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';
import { SliSearchRequestService } from 'src/app/modules/sli-mgmt/services/sli-search-request.service';
import { AutocompleteComponent } from '@shared/components/autocomplete/autocomplete.component';
import { CodeName } from '@shared/models/code-name.model';
import { BookingRequestService } from '../../services/booking-request.service';
import { DATE_TIME_MINS_FORMAT } from '@shared/models/constant';
import { BookingRequestDetailsComponent } from '../../components/booking-request-details/booking-request-details.component';
import { UserRole } from '@shared/models/user-role.model';
import { AsyncPipe } from '@angular/common';
import { Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { BookingOptionAirlineComponent } from '../../components/booking-option-airline/booking-option-airline.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
	selector: 'orll-booking-request-list',
	templateUrl: './booking-request-list.component.html',
	styleUrl: './booking-request-list.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatDialogModule,
		OrllTableComponent,
		MatInputModule,
		MatIconModule,
		MatSelectModule,
		MatButtonModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		AutocompleteComponent,
		AsyncPipe,
	],
	providers: [IataDateFormatPipe],
})
export default class BookingRequestListComponent extends RolesAwareComponent implements OnInit {
	@ViewChildren('departureAutocomplete,arrivalAutocomplete')
	autocompleteList!: QueryList<AutocompleteComponent<any>>;

	bookingRequestSearchForm: FormGroup = new FormGroup({
		requestedProduct: new FormControl<string>(''),
		requestedStatus: new FormControl<string>(''),
	});

	bookingRequestStatus: string[] = [DropDownType.REQUESTED_STATUS, DropDownType.CONFIRMED_STATUS];
	selectedDepartureLocations: CodeName[] = [];
	selectedArrivalLocations: CodeName[] = [];

	bookingRequestSearchParam: BookingRequestSearchParam = {};

	columns: OrllColumnDef<BookingRequestListObj>[] = [
		{
			key: 'requestedBy',
			header: 'booking.mgmt.requestedBy',
		},
		{
			key: 'requestedProduct',
			header: 'booking.mgmt.requestedProduct',
		},
		{
			key: 'requestedFlight',
			header: 'booking.mgmt.requestedFlight',
		},
		{
			key: 'flightDate',
			header: 'booking.mgmt.flightDate',
			transform: (val) => this.iataDateFormatPipe.transform(new Date(val).toISOString(), DATE_TIME_MINS_FORMAT),
		},
		{
			key: 'departureLocation',
			header: 'booking.mgmt.departureLocation',
		},
		{
			key: 'arrivalLocation',
			header: 'booking.mgmt.arrivalLocation',
		},
		{
			key: 'mawbNumber',
			header: 'booking.mgmt.mawbNumber',
			clickCell: (row: BookingRequestListObj) => this.linkMawb(row.mawbId ?? ''),
			noshowLink: (row: BookingRequestListObj) => !row.mawbId,
		},
		{
			key: 'requestedStatus',
			header: 'booking.mgmt.requestedStatus',
			clickCell: (row: BookingRequestListObj) => this.openRequestDetailDialog(row),
		},
	];
	dataLoading = false;

	readonly forwarder: string[] = [UserRole.FORWARDER];

	constructor(
		public readonly bookingRequestService: BookingRequestService,
		public readonly sliSearchRequestService: SliSearchRequestService,
		private readonly iataDateFormatPipe: IataDateFormatPipe,
		private readonly dialog: MatDialog,
		private readonly router: Router
	) {
		super();
	}

	ngOnInit() {
		this.hasSomeRole(this.forwarder)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((hasRole) => {
				const actions: ActionType<BookingRequestListObj>[] = [
					{
						iconKey: 'share',
						iconClickAction: (row: BookingRequestListObj) => this.shareBookingRequest(row),
						showCondition: (row: BookingRequestListObj) => row.canShare && hasRole,
					},
					{
						iconKey: 'content_copy',
						showCopy: (row: BookingRequestListObj) => {
							row.copyId = row.bookingId;
							return row.requestedStatus === DropDownType.CONFIRMED_STATUS;
						},
					},
				];

				this.columns = [
					...this.columns,
					{
						key: 'actions',
						header: 'common.table.action',
						actions: actions,
					},
				];
			});
	}

	onSearch() {
		this.bookingRequestSearchParam = {
			...this.bookingRequestSearchParam,
			...this.bookingRequestSearchForm.value,
			departureLocationList: this.selectedDepartureLocations.map((location) => location.code),
			arrivalLocationList: this.selectedArrivalLocations.map((location) => location.code),
		};
	}

	onCreate() {
		this.router.navigate(['booking/create']);
	}

	linkMawb(mawbId: string) {
		if (!mawbId) return;
		this.router.navigate(['mawb/edit', mawbId]);
	}

	openRequestDetailDialog(row: BookingRequestListObj) {
		const dialogRef = this.dialog.open(BookingRequestDetailsComponent, {
			width: '70vw',
			data: {
				title: 'booking.dialog.title.request',
				buttonName:
					row.requestedStatus === DropDownType.REQUESTED_STATUS
						? 'booking.dialog.button.confirm'
						: 'booking.dialog.button.create.master',
				bookingRequestId: row.bookingRequestId,
				canConfirm: row.canConfirm,
				confirmedStatus: row.requestedStatus === DropDownType.CONFIRMED_STATUS,
			},
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (result) this.onSearch();
		});
	}

	shareBookingRequest(row: BookingRequestListObj) {
		const dialogRef = this.dialog.open(BookingOptionAirlineComponent, {
			width: '30vw',
			autoFocus: false,
			data: { id: row.bookingRequestId, fromBooking: true },
		});
		dialogRef.afterClosed().subscribe((shareSucc) => {
			if (shareSucc) {
				this.onSearch();
			}
		});
	}

	onReset(event: Event) {
		event.preventDefault();
		event.stopPropagation();
		this.bookingRequestSearchForm.reset({
			requestedProduct: '',
			requestedStatus: '',
		});
		this.selectedDepartureLocations = [];
		this.selectedArrivalLocations = [];
		for (const autocomplete of this.autocompleteList) {
			autocomplete.eraseValue(event);
		}
	}
}
