import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MawbStatusUpdateComponent } from './mawb-status-update.component';
import { MawbStatusService } from '../../services/mawb-status.service';
import { HTTP_INTERCEPTORS, provideHttpClient } from '@angular/common/http';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { of, throwError, Subject } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { EventTimeType } from '../../models/mawb-event.model';
import { DatePipe } from '@angular/common';
import { UserProfileService } from '@shared/services/user-profile.service';

describe('MawbStatusUpdateComponent', () => {
	let component: MawbStatusUpdateComponent;
	let fixture: ComponentFixture<MawbStatusUpdateComponent>;
	const mockDialogData = {
		param: {
			mawbId: 'test1',
			choseAllHAWB: true,
			hawbIdList: [
				{
					hawbId: 'hawb1',
					choseAllPiece: false,
					pieceIdList: [],
				},
			],
		},
	};

	const mockStatusService = {
		getEventList: jasmine.createSpy('getEventList').and.returnValue(of(['3333', '3331'])),
		getMawbStatus: jasmine.createSpy('getMawbStatus').and.returnValue(of({ mawbId: '11' })),
		listHawbStatusByMawb: jasmine.createSpy('listHawbStatusByMawb').and.returnValue(of({ rows: [{ hawbId: '1' }] })),
		listPieceStatusByHawb: jasmine.createSpy('listPieceStatusByHawb').and.returnValue(of({ rows: [{ id: '1' }] })),
		updateEventStatus: jasmine.createSpy('updateEventStatus').and.returnValue(of('Success')),
		getAllEvents: jasmine.createSpy('getAllEvents').and.returnValue(of(['event1', 'event2'])),
	};

	const mockDialogRef = {
		close: jasmine.createSpy('close'),
	};

	const mockDatePipe = {
		transform: jasmine.createSpy('transform').and.returnValue('2023-12-01 10:30:00'),
	};

	const mockUserProfileService = {
		hasSomeRole: jasmine.createSpy('hasSomeRole').and.returnValue(of(true)),
		hasPermission: jasmine.createSpy('hasPermission').and.returnValue(of(true)),
		currentUser$: of({ id: 'user1', name: 'Test User' }),
		isSuperUser: jasmine.createSpy('isSuperUser').and.returnValue(true),
	};

	beforeEach(async () => {
		// Reset spies before each test
		mockStatusService.getEventList.calls.reset();
		mockStatusService.getAllEvents.calls.reset();
		mockStatusService.updateEventStatus.calls.reset();
		mockDialogRef.close.calls.reset();
		mockDatePipe.transform.calls.reset();

		await TestBed.configureTestingModule({
			imports: [MawbStatusUpdateComponent, MatDialogModule, TranslateModule.forRoot()],
			providers: [
				{ provide: MawbStatusService, useValue: mockStatusService },
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClientTesting(),
				provideHttpClient(),
				{ provide: takeUntilDestroyed, useValue: () => (source: any) => source },
				{ provide: MAT_DIALOG_DATA, useValue: mockDialogData },
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: DatePipe, useValue: mockDatePipe },
				{ provide: UserProfileService, useValue: mockUserProfileService },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(MawbStatusUpdateComponent);
		component = fixture.componentInstance;
		// Don't call detectChanges() here to avoid triggering ngOnInit automatically
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should set eventTimeType to EventTimeType[1] when checkbox is checked', () => {
		const event = { checked: true } as MatCheckboxChange;

		component.onEventTypeChange(event);

		expect(component.eventTimeType).toBe(EventTimeType[1]);
	});

	it('should set eventTimeType to EventTimeType[0] when checkbox is unchecked', () => {
		const event = { checked: false } as MatCheckboxChange;

		component.onEventTypeChange(event);

		expect(component.eventTimeType).toBe(EventTimeType[0]);
	});

	describe('ngOnInit', () => {
		beforeEach(() => {
			// Reset component state before each test
			component.dataLoading = false;
			component.events = [];
			component.allEvents = [];
		});

		it('should initialize data loading and call service methods', () => {
			// Arrange - Use a Subject to control when the observable completes
			const getEventListSubject = new Subject<string[]>();
			mockStatusService.getEventList.and.returnValue(getEventListSubject.asObservable());

			// Act
			component.ngOnInit();

			// Assert - dataLoading should be true before the observable completes
			expect(component.dataLoading).toBe(true);
			expect(mockStatusService.getEventList).toHaveBeenCalledWith(mockDialogData.param);
			expect(mockStatusService.getAllEvents).toHaveBeenCalled();

			// Complete the observable to finish the test
			getEventListSubject.next(['event1', 'event2']);
			getEventListSubject.complete();
		});

		it('should set events and stop loading when getEventList succeeds', () => {
			// Arrange
			const mockEvents = ['event1', 'event2'];
			mockStatusService.getEventList.and.returnValue(of(mockEvents));

			// Act
			component.ngOnInit();

			// Assert
			expect(component.events).toEqual(mockEvents);
			expect(component.dataLoading).toBe(false);
		});

		it('should set allEvents when getAllEvents succeeds', () => {
			// Arrange
			const mockAllEvents = ['event1', 'event2', 'event3'];
			mockStatusService.getAllEvents.and.returnValue(of(mockAllEvents));

			// Act
			component.ngOnInit();

			// Assert
			expect(component.allEvents).toEqual(mockAllEvents);
		});
	});

	describe('onEventChange', () => {
		it('should set selectedEvent to the provided value', () => {
			// Arrange
			const testEvent = 'TEST_EVENT';

			// Act
			component.onEventChange(testEvent);

			// Assert
			expect(component.selectedEvent).toBe(testEvent);
		});

		it('should handle empty string', () => {
			// Arrange
			const testEvent = '';

			// Act
			component.onEventChange(testEvent);

			// Assert
			expect(component.selectedEvent).toBe('');
		});
	});

	describe('constructor', () => {
		it('should initialize maxDate to yesterday', () => {
			// Arrange
			const today = new Date();
			const expectedMaxDate = new Date();
			expectedMaxDate.setDate(today.getDate() - 1);

			// Act & Assert
			expect(component.maxDate.getDate()).toBe(expectedMaxDate.getDate());
			expect(component.maxDate.getMonth()).toBe(expectedMaxDate.getMonth());
			expect(component.maxDate.getFullYear()).toBe(expectedMaxDate.getFullYear());
		});

		it('should initialize default values before ngOnInit', () => {
			// Create a fresh component without calling detectChanges
			const freshFixture = TestBed.createComponent(MawbStatusUpdateComponent);
			const freshComponent = freshFixture.componentInstance;

			// Assert initial values before ngOnInit
			expect(freshComponent.selectedEvent).toBe('');
			expect(freshComponent.partialEventIndicator).toBe(false);
			expect(freshComponent.eventTimeType).toBe(EventTimeType[0]);
			expect(freshComponent.milestoneTime).toBeNull();
			expect(freshComponent.dataLoading).toBe(false);
			expect(freshComponent.selectedEventsList).toEqual([]);
			expect(freshComponent.allEvents).toEqual([]);
			expect(freshComponent.events).toEqual([]);
		});
	});

	describe('updateEventStatus', () => {
		beforeEach(() => {
			// Reset component state and spies
			component.selectedEvent = 'TEST_EVENT';
			component.eventTimeType = EventTimeType[1];
			component.partialEventIndicator = true;
			component.milestoneTime = new Date('2023-12-01T10:30:00');
			component.dataLoading = false;

			// Reset service spy calls
			mockStatusService.updateEventStatus.calls.reset();
			mockDatePipe.transform.calls.reset();
			mockDialogRef.close.calls.reset();
		});

		it('should set dataLoading to true when called', () => {
			// Arrange - Use a Subject to control when the observable completes
			const updateEventStatusSubject = new Subject<string>();
			mockStatusService.updateEventStatus.and.returnValue(updateEventStatusSubject.asObservable());

			// Act
			component.updateEventStatus();

			// Assert - dataLoading should be true before the observable completes
			expect(component.dataLoading).toBe(true);

			// Complete the observable to finish the test
			updateEventStatusSubject.next('Success');
			updateEventStatusSubject.complete();
		});

		it('should call updateEventStatus service with correct parameters when milestoneTime is set', () => {
			// Act
			component.updateEventStatus();

			// Assert
			expect(mockStatusService.updateEventStatus).toHaveBeenCalled();
			const actualCall = mockStatusService.updateEventStatus.calls.mostRecent().args[0];
			expect(actualCall.mawbId).toBe(mockDialogData.param.mawbId);
			expect(actualCall.eventTimeType).toBe(EventTimeType[1]);
			expect(actualCall.status).toBe('TEST_EVENT');
			expect(actualCall.partialEventIndicator).toBe(true);
			expect(actualCall.updateTime).toBeDefined();
			expect(typeof actualCall.updateTime).toBe('string');
		});

		it('should use maxDate when milestoneTime is null', () => {
			// Arrange
			component.milestoneTime = null;

			// Act
			component.updateEventStatus();

			// Assert
			expect(mockStatusService.updateEventStatus).toHaveBeenCalled();
			const actualCall = mockStatusService.updateEventStatus.calls.mostRecent().args[0];
			expect(actualCall.eventTimeType).toBe(EventTimeType[1]);
			expect(actualCall.status).toBe('TEST_EVENT');
			expect(actualCall.partialEventIndicator).toBe(true);
			expect(actualCall.updateTime).toBeDefined();
			expect(typeof actualCall.updateTime).toBe('string');
		});

		it('should close dialog and set dataLoading to false on success', () => {
			// Arrange
			mockStatusService.updateEventStatus.and.returnValue(of('Success'));

			// Act
			component.updateEventStatus();

			// Assert
			expect(component.dataLoading).toBe(false);
			expect(mockDialogRef.close).toHaveBeenCalled();
		});

		it('should set dataLoading to false on error', () => {
			// Arrange
			const error = { status: 500, error: { msg: 'Server error' } };
			mockStatusService.updateEventStatus.and.returnValue(throwError(() => error));

			// Act
			component.updateEventStatus();

			// Assert
			expect(component.dataLoading).toBe(false);
		});

		it('should trigger delegation request on 403 error', () => {
			// Arrange
			const error = {
				status: 403,
				error: {
					code: 403,
					msg: 'Forbidden',
					data: 'delegation-id',
				},
			};
			mockStatusService.updateEventStatus.and.returnValue(throwError(() => error));
			spyOn(component, 'delegationRequest').and.returnValue(of(true));

			// Act
			component.updateEventStatus();

			// Assert
			expect(component.delegationRequest).toHaveBeenCalledWith(error.error, mockDialogData.param.mawbId);
		});

		it('should close dialog with true when delegation request succeeds', () => {
			// Arrange
			const error = {
				status: 403,
				error: {
					code: 403,
					msg: 'Forbidden',
					data: 'delegation-id',
				},
			};
			mockStatusService.updateEventStatus.and.returnValue(throwError(() => error));
			spyOn(component, 'delegationRequest').and.returnValue(of(true));

			// Act
			component.updateEventStatus();

			// Assert
			expect(mockDialogRef.close).toHaveBeenCalledWith(true);
		});

		it('should not close dialog when delegation request fails', () => {
			// Arrange
			const error = {
				status: 403,
				error: {
					code: 403,
					msg: 'Forbidden',
					data: 'delegation-id',
				},
			};
			mockStatusService.updateEventStatus.and.returnValue(throwError(() => error));
			spyOn(component, 'delegationRequest').and.returnValue(of(false));

			// Act
			component.updateEventStatus();

			// Assert
			expect(mockDialogRef.close).not.toHaveBeenCalledWith(true);
		});

		it('should not trigger delegation request for non-403 errors', () => {
			// Arrange
			const error = { status: 500, error: { msg: 'Server error' } };
			mockStatusService.updateEventStatus.and.returnValue(throwError(() => error));
			spyOn(component, 'delegationRequest');

			// Act
			component.updateEventStatus();

			// Assert
			expect(component.delegationRequest).not.toHaveBeenCalled();
		});

		it('should handle updateEventStatus with different eventTimeType values', () => {
			// Arrange
			component.selectedEvent = 'CUSTOM_EVENT';
			component.eventTimeType = EventTimeType[0]; // ACTUAL
			component.partialEventIndicator = false;
			component.milestoneTime = new Date('2023-11-15T14:20:00');

			// Act
			component.updateEventStatus();

			// Assert
			expect(mockStatusService.updateEventStatus).toHaveBeenCalled();
			const actualCall = mockStatusService.updateEventStatus.calls.mostRecent().args[0];
			expect(actualCall.eventTimeType).toBe(EventTimeType[0]);
			expect(actualCall.status).toBe('CUSTOM_EVENT');
			expect(actualCall.partialEventIndicator).toBe(false);
			expect(actualCall.updateTime).toBeDefined();
		});

		it('should handle updateEventStatus with partialEventIndicator true', () => {
			// Arrange
			component.selectedEvent = 'PARTIAL_EVENT';
			component.eventTimeType = EventTimeType[1]; // PLANNED
			component.partialEventIndicator = true;
			component.milestoneTime = null;

			// Act
			component.updateEventStatus();

			// Assert
			expect(mockStatusService.updateEventStatus).toHaveBeenCalled();
			const actualCall = mockStatusService.updateEventStatus.calls.mostRecent().args[0];
			expect(actualCall.eventTimeType).toBe(EventTimeType[1]);
			expect(actualCall.status).toBe('PARTIAL_EVENT');
			expect(actualCall.partialEventIndicator).toBe(true);
			expect(actualCall.updateTime).toBeDefined();
		});
	});

	describe('Inherited Methods from DelegationRequestComponent', () => {
		it('should have access to dialog property', () => {
			// Assert
			expect(component.dialog).toBeDefined();
		});

		it('should have access to inherited methods from RolesAwareComponent', () => {
			// Assert
			expect(component.hasSomeRole).toBeDefined();
			expect(component.hasPermission).toBeDefined();
			expect(component.getCurrentUser).toBeDefined();
			expect(component.isSuperUser).toBeDefined();
		});

		it('should have access to destroyRef from DestroyRefComponent', () => {
			// Assert
			expect(component['destroyRef']).toBeDefined();
		});
	});

	describe('Component Properties', () => {
		it('should have correct initial property values after ngOnInit', () => {
			// Trigger ngOnInit
			fixture.detectChanges();

			// Assert - after ngOnInit, some arrays will be populated by service calls
			expect(component.selectedEvent).toBe('');
			expect(component.partialEventIndicator).toBe(false);
			expect(component.eventTimeType).toBe(EventTimeType[0]);
			expect(component.milestoneTime).toBeNull();
			expect(component.selectedEventsList).toEqual([]);
			// events and allEvents will be populated by ngOnInit service calls
			expect(Array.isArray(component.events)).toBe(true);
			expect(Array.isArray(component.allEvents)).toBe(true);
		});

		it('should allow setting and getting property values', () => {
			// Arrange & Act
			component.selectedEvent = 'NEW_EVENT';
			component.partialEventIndicator = true;
			component.eventTimeType = EventTimeType[1];
			component.milestoneTime = new Date('2023-12-01');
			component.dataLoading = true;

			// Assert
			expect(component.selectedEvent).toBe('NEW_EVENT');
			expect(component.partialEventIndicator).toBe(true);
			expect(component.eventTimeType).toBe(EventTimeType[1]);
			expect(component.milestoneTime).toEqual(new Date('2023-12-01'));
			expect(component.dataLoading).toBe(true);
		});
	});
});
