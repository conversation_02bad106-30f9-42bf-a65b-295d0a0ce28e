import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import PartnerListComponent from './partner-list.component';
import { TranslateModule } from '@ngx-translate/core';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';

describe('PartnerListComponent', () => {
	let component: PartnerListComponent;
	let fixture: ComponentFixture<PartnerListComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [PartnerListComponent, TranslateModule.forRoot()],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(PartnerListComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('onTabChanged', () => {
		it('should update currentTabLabel and selectedTabIndex when tab changes', () => {
			// Initial values
			expect(component.selectedTabIndex).toBe(0);
			expect(component.currentTabLabel).toBe('');

			// Create mock event
			const mockTabChangeEvent = {
				index: 1,
				tab: { textLabel: 'Test Tab Label' },
			} as MatTabChangeEvent;

			// Call the method
			component.onTabChanged(mockTabChangeEvent);

			// Check if values were updated correctly
			expect(component.selectedTabIndex).toBe(1);
			expect(component.currentTabLabel).toBe('Test Tab Label');
		});
	});
});
