<div class="iata-box orll-weight-calculate">
	<div class="orll-weight-calculate__container">
		<table mat-table [dataSource]="dataSource" matSort class="orll-weight-calculate__table"
			aria-label="Company table">
			<ng-container matColumnDef="productDescription">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header
					class="orll-weight-calculate__mat-head">
					{{ 'calculate.column.productDescription' | translate }}
				</th>
				<td mat-cell *matCellDef="let row">
					{{ row.productDescription }}
				</td>
			</ng-container>

			<ng-container matColumnDef="dimensions">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header
					class="orll-weight-calculate__mat-head">
					{{ 'calculate.column.dimensions' | translate }}
				</th>
				<td mat-cell *matCellDef="let row" class="orll-weight-calculate__mat-cell">
					{{ `${row.dimensions.length}CM*${row.dimensions.width}CM*${row.dimensions.height}CM` }}
				</td>
			</ng-container>

			<ng-container matColumnDef="grossWeight">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header
					class="orll-weight-calculate__mat-head">
					{{ 'calculate.column.grossWeight' | translate }}
				</th>
				<td mat-cell *matCellDef="let row" class="orll-weight-calculate__mat-cell">
					{{ row.grossWeight }}
				</td>
			</ng-container>

			<ng-container matColumnDef="slac">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header
					class="orll-weight-calculate__mat-head">
					{{ 'calculate.column.slac' | translate }}
				</th>
				<td mat-cell *matCellDef="let row" class="orll-weight-calculate__mat-cell">
					{{ row.slac }}
				</td>
			</ng-container>

			<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
			<tr mat-row *matRowDef="let row; columns: displayedColumns" class="orll-mawb-table__row"></tr>
		</table>
		@if (dataLoading) {
		<iata-spinner></iata-spinner>
		}
	</div>

	<div class="right-aligned-container">
		<div class="horizontal-group">
			<div class="label-value-group">
				<mat-label>{{ 'calculate.label.pieceRCP' | translate }}</mat-label>
				<mat-label>{{ data?.noPieceRcp }}</mat-label>
			</div>

			<div class="label-value-group">
				<mat-label>{{ 'calculate.label.pieceGrossWeight' | translate }}</mat-label>
				<mat-label>{{ data?.pieceGrossWeight }}</mat-label>
			</div>

			<div class="label-value-group">
				<mat-label>{{ 'calculate.label.totalSlac' | translate }}</mat-label>
				<mat-label>{{ data?.totalSlac }}</mat-label>
			</div>
		</div>
	</div>


	<form [formGroup]="weightCalculateForm" class="row col-12">
		<div class="col-4">
			<mat-form-field appearance="outline" class="orll-weight-calculate__input">
				<mat-label>{{ 'calculate.label.totalGrossWeight' | translate }}</mat-label>
				<input matInput formControlName="totalGrossWeight" />
				<span matSuffix class="unit">KG</span>
			</mat-form-field>
			@if (weightCalculateForm.get('totalGrossWeight')?.hasError('pattern')) {
			<mat-error>{{
				'validators.maxOneDecimal'
				| translate
				: {
				field: 'mawb.formItem.grossWeight' | translate,
				}
				}}
			</mat-error>
			}
		</div>
		<div class="col-4">
			<mat-form-field appearance="outline" class="orll-weight-calculate__input">
				<mat-label>{{ 'calculate.label.volume' | translate }}</mat-label>
				<input matInput formControlName="volume" />
				<span matSuffix class="unit">M³</span>
			</mat-form-field>
			@if (weightCalculateForm.get('volume')?.hasError('pattern')) {
			<mat-error>{{
				'validators.maxOneDecimal'
				| translate
				: {
				field: 'mawb.formItem.volume' | translate,
				}
				}}
			</mat-error>
			}
		</div>
		<div class="col-4">
			<mat-form-field appearance="outline" class="orll-weight-calculate__input">
				<mat-label>{{ 'calculate.label.chargeableWeight' | translate }}</mat-label>
				<input matInput formControlName="chargeableWeight" />
				<span matSuffix class="unit">KG</span>
			</mat-form-field>
			@if (weightCalculateForm.get('chargeableWeight')?.hasError('pattern')) {
			<mat-error>{{
				'validators.maxOneDecimalRound5'
				| translate
				: {
				field: 'mawb.formItem.chargeableWeight' | translate,
				}
				}}
			</mat-error>
			}
		</div>
	</form>
	<div class="button-group">
		<button mat-button color="primary" (click)="onCancel()">{{ 'calculate.btn.cancel' | translate }}</button>
		<a mat-flat-button class="create-hawb-button" (click)="onSave()">
			{{ 'calculate.btn.save' | translate }}
			<mat-icon>done</mat-icon>
		</a>
	</div>
</div>
