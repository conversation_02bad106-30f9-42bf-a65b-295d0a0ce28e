import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BookingOptionRequestDetailComponent } from './booking-option-request-detail.component';
import { of } from 'rxjs';
import { UserProfileService } from '@shared/services/user-profile.service';
import { UserProfile } from '@shared/models/user-profile.model';

function createMockUserProfile(overrides: Partial<UserProfile> = {}): UserProfile {
	return {
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'ORG_TYPE',
		userType: '1',
		menuList: [],
		permissionList: [],
		orgList: [],
		...overrides,
	};
}

describe('BookingOptionRequestDetailComponent', () => {
	let component: BookingOptionRequestDetailComponent;
	let fixture: ComponentFixture<BookingOptionRequestDetailComponent>;
	let mockProfileService: jasmine.SpyObj<UserProfileService>;

	beforeEach(async () => {
		mockProfileService = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole']);
		mockProfileService.hasPermission.and.returnValue(of(true));
		mockProfileService.hasSomeRole.and.returnValue(of(true));
		await TestBed.configureTestingModule({
			imports: [BookingOptionRequestDetailComponent],
			providers: [{ provide: UserProfileService, useValue: mockProfileService }],
		}).compileComponents();

		fixture = TestBed.createComponent(BookingOptionRequestDetailComponent);
		component = fixture.componentInstance;
		const mockUser = createMockUserProfile({ primaryOrgId: 'org123' });
		spyOn(component, 'getCurrentUser').and.returnValue(of(mockUser));
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should have quote input set to true (or false)', () => {
		expect(component.quote).toBe(false);
	});
});
