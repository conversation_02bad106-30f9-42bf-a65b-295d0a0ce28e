import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MawbSearchComponent } from './mawb-search.component';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { provideTranslateService } from '@ngx-translate/core';
import { AutocompleteComponent } from '@shared/components/autocomplete/autocomplete.component';
import { QueryList } from '@angular/core';
import { SearchType } from '@shared/models/search-type.model';
import { MawbSearchRequestService } from '../../services/mawb-search-request.service';
import { DatePipe } from '@angular/common';

describe('MawbSearchComponent', () => {
	let component: MawbSearchComponent;
	let fixture: ComponentFixture<MawbSearchComponent>;
	let datePipe: DatePipe;

	const mockSearchService = {
		getOptions: jasmine.createSpy('getOptions'),
	};

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [MawbSearchComponent],
			providers: [
				{ provide: MawbSearchRequestService, useValue: mockSearchService },
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
				DatePipe,
			],
		}).compileComponents();

		fixture = TestBed.createComponent(MawbSearchComponent);
		component = fixture.componentInstance;
		datePipe = TestBed.inject(DatePipe);
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize form with empty values', () => {
		expect(component.mawbSearchForm.value).toEqual({
			goodsDescription: '',
			startDate: null,
			endDate: null,
			eventStartDate: null,
			eventEndDate: null,
		});
	});

	it('should handle different search types in selectedItems()', () => {
		const testItem = { code: 'TST', name: 'Test' };

		component.selectedItems([testItem], SearchType.AIRLINE_CODE);
		expect(component.selectedAirlines).toEqual([testItem]);

		component.selectedItems([testItem], SearchType.LATEST_STATUS);
		expect(component.selectedLatestStatus).toEqual([testItem]);

		component.selectedItems([testItem], SearchType.ORIGIN);
		expect(component.selectedDepartureLocations).toEqual([testItem]);

		component.selectedItems([testItem], SearchType.DESTINATION);
		expect(component.selectedArrivalLocations).toEqual([testItem]);

		component.selectedItems([testItem], SearchType.MAWB);
		expect(component.selectedMawbNumbers).toEqual([testItem]);
	});

	it('should emit complete search payload with transformed dates', () => {
		const emitSpy = spyOn(component.searchMawb, 'emit');
		const testDate = new Date('2023-01-15');

		component.mawbSearchForm.patchValue({
			goodsDescription: 'Electronics',
			startDate: testDate,
			endDate: testDate,
			eventStartDate: testDate,
			eventEndDate: testDate,
		});

		component.selectedAirlines = [{ code: 'AA', name: 'American Airlines' }];
		component.selectedDepartureLocations = [{ code: 'JFK', name: 'New York' }];

		component.onSearch();

		expect(emitSpy).toHaveBeenCalledWith({
			goodsDescription: 'Electronics',
			createDateStart: '2023-01-15',
			createDateEnd: '2023-01-15',
			eventDateStart: '2023-01-15',
			eventDateEnd: '2023-01-15',
			airlineCodeList: ['American Airlines'],
			latestStatusList: [],
			departureLocationList: ['JFK'],
			arrivalLocationList: [],
			mawbNumberList: [],
		});
	});

	it('should reset form and clear all selections', () => {
		const event = new MouseEvent('click');
		spyOn(event, 'preventDefault');
		spyOn(event, 'stopPropagation');

		// Set initial values
		component.mawbSearchForm.patchValue({
			goodsDescription: 'Test',
			startDate: new Date(),
			endDate: new Date(),
			eventStartDate: new Date(),
			eventEndDate: new Date(),
		});
		component.selectedAirlines = [{ code: 'DL', name: 'Delta' }];

		// Mock autocomplete reset
		component.autocompleteList = {
			forEach: jasmine.createSpy('forEach'),
		} as unknown as QueryList<AutocompleteComponent<any>>;

		component.onReset(event);

		expect(event.preventDefault).toHaveBeenCalled();
		expect(event.stopPropagation).toHaveBeenCalled();
		expect(component.mawbSearchForm.value).toEqual({
			goodsDescription: null,
			startDate: null,
			endDate: null,
			eventStartDate: null,
			eventEndDate: null,
		});
		expect(component.selectedAirlines).toEqual([]);
		expect(component.autocompleteList.forEach).toHaveBeenCalled();
	});

	it('should handle null dates in search payload', () => {
		const emitSpy = spyOn(component.searchMawb, 'emit');

		component.onSearch();

		expect(emitSpy).toHaveBeenCalledWith(
			jasmine.objectContaining({
				createDateStart: null,
				createDateEnd: null,
			})
		);
	});

	it('should transform dates to correct format', () => {
		const testDate = new Date('2023-12-31');
		const formatted = datePipe.transform(testDate, 'yyyy-MM-dd');
		expect(formatted).toBe('2023-12-31');
	});

	describe('selectedItems Edge Cases', () => {
		it('should handle unknown search type in selectedItems', () => {
			const testItem = { code: 'TST', name: 'Test' };
			const originalAirlines = [...component.selectedAirlines];

			component.selectedItems([testItem], 'UNKNOWN_TYPE' as any);

			// Should not change any selections for unknown type
			expect(component.selectedAirlines).toEqual(originalAirlines);
			expect(component.selectedLatestStatus).toEqual([]);
			expect(component.selectedDepartureLocations).toEqual([]);
			expect(component.selectedArrivalLocations).toEqual([]);
			expect(component.selectedMawbNumbers).toEqual([]);
		});

		it('should handle empty array in selectedItems', () => {
			component.selectedItems([], SearchType.AIRLINE_CODE);
			expect(component.selectedAirlines).toEqual([]);

			component.selectedItems([], SearchType.LATEST_STATUS);
			expect(component.selectedLatestStatus).toEqual([]);

			component.selectedItems([], SearchType.ORIGIN);
			expect(component.selectedDepartureLocations).toEqual([]);

			component.selectedItems([], SearchType.DESTINATION);
			expect(component.selectedArrivalLocations).toEqual([]);

			component.selectedItems([], SearchType.MAWB);
			expect(component.selectedMawbNumbers).toEqual([]);
		});

		it('should handle multiple items in selectedItems', () => {
			const testItems = [
				{ code: 'TST1', name: 'Test1' },
				{ code: 'TST2', name: 'Test2' },
				{ code: 'TST3', name: 'Test3' },
			];

			component.selectedItems(testItems, SearchType.AIRLINE_CODE);
			expect(component.selectedAirlines).toEqual(testItems);
			expect(component.selectedAirlines.length).toBe(3);
		});
	});

	describe('onSearch Edge Cases', () => {
		it('should handle search with all selections populated', () => {
			const emitSpy = spyOn(component.searchMawb, 'emit');
			const testDate = new Date('2023-06-15');

			component.mawbSearchForm.patchValue({
				goodsDescription: 'Test Goods',
				startDate: testDate,
				endDate: testDate,
				eventStartDate: testDate,
				eventEndDate: testDate,
			});

			component.selectedAirlines = [{ code: 'AA', name: 'American Airlines' }];
			component.selectedLatestStatus = [{ code: 'CREATED', name: 'Created' }];
			component.selectedDepartureLocations = [{ code: 'JFK', name: 'New York' }];
			component.selectedArrivalLocations = [{ code: 'LAX', name: 'Los Angeles' }];
			component.selectedMawbNumbers = [{ code: '123-456789', name: '123-456789' }];

			component.onSearch();

			expect(emitSpy).toHaveBeenCalledWith({
				goodsDescription: 'Test Goods',
				createDateStart: '2023-06-15',
				createDateEnd: '2023-06-15',
				eventDateStart: '2023-06-15',
				eventDateEnd: '2023-06-15',
				airlineCodeList: ['American Airlines'],
				latestStatusList: ['Created'],
				departureLocationList: ['JFK'],
				arrivalLocationList: ['LAX'],
				mawbNumberList: ['123-456789'],
			});
		});

		it('should handle search with empty form values', () => {
			const emitSpy = spyOn(component.searchMawb, 'emit');

			component.mawbSearchForm.patchValue({
				goodsDescription: '',
				startDate: null,
				endDate: null,
				eventStartDate: null,
				eventEndDate: null,
			});

			component.onSearch();

			expect(emitSpy).toHaveBeenCalledWith({
				goodsDescription: '',
				createDateStart: null,
				createDateEnd: null,
				eventDateStart: null,
				eventDateEnd: null,
				airlineCodeList: [],
				latestStatusList: [],
				departureLocationList: [],
				arrivalLocationList: [],
				mawbNumberList: [],
			});
		});

		it('should handle search with null goodsDescription', () => {
			const emitSpy = spyOn(component.searchMawb, 'emit');

			component.mawbSearchForm.patchValue({
				goodsDescription: null,
			});

			component.onSearch();

			expect(emitSpy).toHaveBeenCalledWith(
				jasmine.objectContaining({
					goodsDescription: '',
				})
			);
		});
	});

	describe('onReset Edge Cases', () => {
		it('should handle reset when autocompleteList is undefined', () => {
			const event = new MouseEvent('click');
			spyOn(event, 'preventDefault');
			spyOn(event, 'stopPropagation');

			// Set autocompleteList to undefined
			component.autocompleteList = undefined as any;

			// Should throw error because the component tries to call forEach on undefined
			expect(() => component.onReset(event)).toThrow();

			expect(event.preventDefault).toHaveBeenCalled();
			expect(event.stopPropagation).toHaveBeenCalled();
		});

		it('should reset all selection arrays to empty', () => {
			const event = new MouseEvent('click');

			// Set initial values
			component.selectedAirlines = [{ code: 'AA', name: 'American' }];
			component.selectedLatestStatus = [{ code: 'CREATED', name: 'Created' }];
			component.selectedDepartureLocations = [{ code: 'JFK', name: 'New York' }];
			component.selectedArrivalLocations = [{ code: 'LAX', name: 'Los Angeles' }];
			component.selectedMawbNumbers = [{ code: '123', name: '123-456789' }];

			// Mock autocomplete
			component.autocompleteList = {
				forEach: jasmine.createSpy('forEach'),
			} as unknown as QueryList<AutocompleteComponent<any>>;

			component.onReset(event);

			expect(component.selectedAirlines).toEqual([]);
			expect(component.selectedLatestStatus).toEqual([]);
			expect(component.selectedDepartureLocations).toEqual([]);
			expect(component.selectedArrivalLocations).toEqual([]);
			expect(component.selectedMawbNumbers).toEqual([]);
		});

		it('should call eraseValue on each autocomplete component', () => {
			const event = new MouseEvent('click');
			const mockAutocomplete1 = { eraseValue: jasmine.createSpy('eraseValue') };
			const mockAutocomplete2 = { eraseValue: jasmine.createSpy('eraseValue') };

			component.autocompleteList = {
				forEach: (callback: any) => {
					callback(mockAutocomplete1);
					callback(mockAutocomplete2);
				},
			} as unknown as QueryList<AutocompleteComponent<any>>;

			component.onReset(event);

			expect(mockAutocomplete1.eraseValue).toHaveBeenCalledWith(event);
			expect(mockAutocomplete2.eraseValue).toHaveBeenCalledWith(event);
		});
	});

	describe('Component Initialization', () => {
		it('should initialize with correct form structure', () => {
			expect(component.mawbSearchForm.get('goodsDescription')).toBeTruthy();
			expect(component.mawbSearchForm.get('startDate')).toBeTruthy();
			expect(component.mawbSearchForm.get('endDate')).toBeTruthy();
			expect(component.mawbSearchForm.get('eventStartDate')).toBeTruthy();
			expect(component.mawbSearchForm.get('eventEndDate')).toBeTruthy();
		});

		it('should initialize all selection arrays as empty', () => {
			expect(component.selectedAirlines).toEqual([]);
			expect(component.selectedLatestStatus).toEqual([]);
			expect(component.selectedDepartureLocations).toEqual([]);
			expect(component.selectedArrivalLocations).toEqual([]);
			expect(component.selectedMawbNumbers).toEqual([]);
		});

		it('should have mawbSearchRequestService injected', () => {
			expect(component.mawbSearchRequestService).toBeTruthy();
		});
	});

	describe('Date Handling', () => {
		it('should handle invalid dates gracefully', () => {
			const invalidDate = new Date('invalid-date');

			component.mawbSearchForm.patchValue({
				startDate: invalidDate,
				endDate: invalidDate,
				eventStartDate: invalidDate,
				eventEndDate: invalidDate,
			});

			// Should throw error because DatePipe cannot handle invalid dates
			expect(() => component.onSearch()).toThrow();
		});

		it('should handle edge case dates correctly', () => {
			const emitSpy = spyOn(component.searchMawb, 'emit');
			const leapYearDate = new Date('2024-02-29'); // Leap year date
			const endOfYearDate = new Date('2023-12-31');

			component.mawbSearchForm.patchValue({
				startDate: leapYearDate,
				endDate: endOfYearDate,
			});

			component.onSearch();

			expect(emitSpy).toHaveBeenCalledWith(
				jasmine.objectContaining({
					createDateStart: '2024-02-29',
					createDateEnd: '2023-12-31',
				})
			);
		});
	});
});
