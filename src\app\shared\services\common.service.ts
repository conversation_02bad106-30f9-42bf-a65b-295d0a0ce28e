import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';

@Injectable({
	providedIn: 'root',
})
export class CommonService {
	constructor(
		private readonly dialog: MatDialog,
		private readonly translate: TranslateService,
		private readonly router: Router
	) {}

	showFormInvalid() {
		this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translate.instant('common.dialog.form.validate'),
			},
		});
	}

	showWarning(info: string) {
		this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translate.instant(info),
			},
		});
	}

	showDeleteConfirm(): MatDialogRef<any> {
		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translate.instant('common.dialog.delete.content'),
			},
		});
		return dialogRef;
	}

	showCancelConfirm(path: string): void {
		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translate.instant('common.dialog.cancel.content'),
			},
		});

		dialogRef.afterClosed().subscribe((confirmed) => {
			if (confirmed) {
				this.router.navigate([path]);
			}
		});
	}
}
