import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnChanges,
	Output,
	SimpleChanges,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { SliPieceTableComponent } from '../sli-piece-table/sli-piece-table.component';
import { CommonModule } from '@angular/common';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { PieceList } from '../../models/piece/piece-list.model';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { map } from 'rxjs';
import { DelegationRequestComponent } from '@shared/components/delegation-request/delegation-request.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';

const PIECE_TAB_INDEX = 1;

@Component({
	selector: 'orll-sli-piece-list',
	templateUrl: './sli-piece-list.component.html',
	styleUrl: './sli-piece-list.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatInputModule,
		MatSelectModule,
		MatIconModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		CommonModule,
		MatAutocompleteModule,
		SliPieceTableComponent,
		SpinnerComponent,
	],
})
export class SliPieceListComponent extends DelegationRequestComponent implements OnChanges {
	pieceList: PieceList[] = [];
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};
	totalRecords = 0;
	totalQuantity = 0;
	totalSlac = 0;
	dataLoading = false;

	sliTemplateNum = '';

	@Input() sliNumber = '';
	@Input() selectedTabIndex = 0;
	@Input() isForHawb = false;
	@Input() disableUpdate = false;

	@Output() saveRequest = new EventEmitter<string>();
	@Output() refreshDelegationRequest = new EventEmitter<void>();

	constructor(
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly cdr: ChangeDetectorRef,
		private readonly router: Router,
		private readonly route: ActivatedRoute
	) {
		super();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['selectedTabIndex']) {
			this.refreshData();
		}
	}

	refreshData() {
		if (this.selectedTabIndex === PIECE_TAB_INDEX) {
			// for NE-ONE loop timer
			setTimeout(() => {
				this.getPieceListPageBySliTemplateNum();
			}, 2000);
		}
	}

	// eslint-disable-next-line
	getFormData(ignore?: boolean): {} | null {
		return {
			pieces: [], // update SLI only without piece list data
		};
	}

	onSortChange(sort: Sort): void {
		if (sort.direction === '') {
			this.pageParams.orderByColumn = '';
			this.pageParams.isAsc = '';
			return;
		}
		this.pageParams.orderByColumn = sort.active;
		this.pageParams.isAsc = sort.direction;
	}

	onPageChange(event: PageEvent & { sortField?: string; sortDirection?: string }): void {
		this.pageParams.pageNum = event.pageIndex + 1;
		this.pageParams.pageSize = event.pageSize;
		if (event.sortDirection) {
			this.pageParams.orderByColumn = event.sortField;
			this.pageParams.isAsc = event.sortDirection;
		}
		this.getPieceListPageBySliTemplateNum();
	}

	private getPieceListPageBySliTemplateNum() {
		if (this.sliNumber || this.sliTemplateNum) {
			this.sliTemplateNum = this.sliNumber || this.sliTemplateNum;
			this.getPieceListPage(this.pageParams);
		} else {
			this.sliCreateRequestService.getSliTemplate().subscribe((res) => {
				this.sliTemplateNum = res;
				this.getPieceListPage(this.pageParams);
			});
		}
	}

	private getPieceListPage(pageParams: PaginationRequest): void {
		this.dataLoading = true;
		this.pieceList = [];

		this.sliCreateRequestService
			.getPieceList(pageParams, this.sliTemplateNum)
			.pipe(
				map((response) => {
					const treeNodes = response.rows.map((item) => ({
						...item,
						level: 0,
						expanded: false,
						slac:
							item.containedPieces && item.containedPieces.length > 0
								? item.containedPieces.reduce((sum, subPiece) => sum + (subPiece.pieceQuantity ?? 0), 0)
								: 0,
					}));
					return {
						rows: treeNodes,
						total: response.total,
					};
				})
			)
			.subscribe({
				next: (res) => {
					this.pieceList = res.rows;
					this.totalRecords = res.total;
					this.dataLoading = false;
					if (this.sliTemplateNum) {
						this.sliCreateRequestService.getTotalPieceQuantity(this.sliTemplateNum).subscribe((res) => {
							this.totalQuantity = res.totalQuantity;
							this.totalSlac = res.totalSlac;
							this.cdr.markForCheck();
						});
					}

					this.cdr.markForCheck();
				},
				error: (err) => {
					this.dataLoading = false;

					// trigger delegation request when 403
					if (err.status === 403) {
						this.delegationRequest(err.error, this.sliNumber)
							.pipe(takeUntilDestroyed(this.destroyRef))
							.subscribe((result) => {
								if (result) {
									this.refreshDelegationRequest.emit();
								}
							});
					}
				},
			});
	}

	createOrUpdatePiece(pieceParams: { pieceType: string; pieceId?: string }) {
		let routePath = [];

		if (pieceParams.pieceId) {
			routePath = ['piece', pieceParams.pieceType, pieceParams.pieceId];
		} else {
			routePath = ['piece', pieceParams.pieceType];
		}

		this.router.navigate(routePath, { relativeTo: this.route });
	}
}
