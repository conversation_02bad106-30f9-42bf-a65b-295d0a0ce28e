import { Component, Inject, OnInit } from '@angular/core';
// eslint-disable-next-line @typescript-eslint/naming-convention
import OrllDialogComponent from '@shared/components/dialog-template/dialog-template.component';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { Organization } from '@shared/models/organization.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';

@Component({
	selector: 'orll-booking-option-airline',
	imports: [
		OrllDialogComponent,
		MatSelectModule,
		MatDialogModule,
		MatIconModule,
		TranslateModule,
		MatButtonModule,
		MatFormFieldModule,
		SpinnerComponent,
	],
	templateUrl: './booking-option-airline.component.html',
	styleUrl: './booking-option-airline.component.scss',
})
export class BookingOptionAirlineComponent implements OnInit {
	airlines: Organization[] = [];
	involvedParties = '';
	dataLoading = false;

	constructor(
		private readonly bookingService: BookingOptionRequestService,
		private readonly orgService: OrgMgmtRequestService,
		@Inject(MAT_DIALOG_DATA) public data: { id: string; fromBooking: boolean },
		private readonly dialogRef: MatDialogRef<BookingOptionAirlineComponent>
	) {}

	ngOnInit(): void {
		this.orgService.getOrgList('AIR').subscribe((res) => {
			this.airlines = res;
		});
	}

	selectAirline(event: MatSelectChange) {
		this.involvedParties = event.value;
	}

	sendRequest() {
		this.dataLoading = true;
		this.bookingService.shareOption(this.data.id, this.involvedParties, this.data.fromBooking).subscribe({
			next: () => {
				this.dataLoading = false;
				this.dialogRef.close(true);
			},
			error: () => {
				this.dataLoading = false;
				this.dialogRef.close(false);
			},
		});
	}
}
