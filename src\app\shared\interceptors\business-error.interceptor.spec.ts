import { TestBed } from '@angular/core/testing';
import { provideHttpClientTesting, HttpTestingController } from '@angular/common/http/testing';
import { HttpClient, HttpErrorResponse, HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { BusinessErrorInterceptor } from './business-error.interceptor';
import { NotificationService } from '@shared/services/notification.service';

describe('BusinessErrorInterceptor', () => {
	let httpClient: HttpClient;
	let httpTestingController: HttpTestingController;
	let mockNotificationService: jasmine.SpyObj<NotificationService>;
	let interceptor: BusinessErrorInterceptor;

	const testApiUrl = '/api/test';
	const testNonApiUrl = '/assets/config.json';

	beforeEach(() => {
		mockNotificationService = jasmine.createSpyObj('NotificationService', ['showSuccess', 'showError']);

		TestBed.configureTestingModule({
			providers: [
				BusinessErrorInterceptor,
				{ provide: NotificationService, useValue: mockNotificationService },
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		});

		httpClient = TestBed.inject(HttpClient);
		httpTestingController = TestBed.inject(HttpTestingController);
		interceptor = TestBed.inject(BusinessErrorInterceptor);

		// Reset spies before each test
		mockNotificationService.showSuccess.calls.reset();
		mockNotificationService.showError.calls.reset();
	});

	afterEach(() => {
		httpTestingController.verify();
	});

	it('should be created', () => {
		expect(interceptor).toBeTruthy();
	});

	describe('URL Pattern Matching', () => {
		it('should bypass interceptor for non-API URLs', () => {
			const testData = { message: 'test' };

			httpClient.get(testNonApiUrl).subscribe((response) => {
				expect(response).toEqual(testData);
			});

			const req = httpTestingController.expectOne(testNonApiUrl);
			expect(req.request.method).toBe('GET');
			req.flush(testData);

			// Should not call notification service for non-API URLs
			expect(mockNotificationService.showSuccess).not.toHaveBeenCalled();
			expect(mockNotificationService.showError).not.toHaveBeenCalled();
		});

		it('should process API URLs with /api pattern', () => {
			const responseData = { code: 200, data: { id: 1, name: 'test' } };

			httpClient.get(testApiUrl).subscribe((response) => {
				expect(response).toEqual({ id: 1, name: 'test' });
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.flush(responseData);
		});

		it('should process API URLs with full domain and /api pattern', () => {
			const fullApiUrl = 'https://example.com/api/test';
			const responseData = { code: 200, data: { id: 1, name: 'test' } };

			httpClient.get(fullApiUrl).subscribe((response) => {
				expect(response).toEqual({ id: 1, name: 'test' });
			});

			const req = httpTestingController.expectOne(fullApiUrl);
			req.flush(responseData);
		});

		it('should process API URLs with http protocol', () => {
			const httpApiUrl = 'http://localhost:8080/api/test';
			const responseData = { code: 200, data: { id: 1, name: 'test' } };

			httpClient.get(httpApiUrl).subscribe((response) => {
				expect(response).toEqual({ id: 1, name: 'test' });
			});

			const req = httpTestingController.expectOne(httpApiUrl);
			req.flush(responseData);
		});
	});

	describe('Successful Response Handling', () => {
		it('should extract data from successful response with code 200', () => {
			const responseData = { code: 200, data: { id: 1, name: 'test' } };

			httpClient.get(testApiUrl).subscribe((response) => {
				expect(response).toEqual({ id: 1, name: 'test' });
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.flush(responseData);
		});

		it('should show success notification for non-GET requests with message', () => {
			const responseData = {
				code: 200,
				data: { id: 1 },
				msg: 'Operation successful',
			};

			httpClient.post(testApiUrl, { name: 'test' }).subscribe((response) => {
				expect(response).toEqual({ id: 1 });
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.flush(responseData);

			expect(mockNotificationService.showSuccess).toHaveBeenCalledWith('Operation successful');
		});

		it('should show success notification using showMessage field', () => {
			const responseData = {
				code: 200,
				data: { id: 1 },
				showMessage: 'Data saved successfully',
			};

			httpClient.put(testApiUrl, { name: 'updated' }).subscribe((response) => {
				expect(response).toEqual({ id: 1 });
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.flush(responseData);

			expect(mockNotificationService.showSuccess).toHaveBeenCalledWith('Data saved successfully');
		});

		it('should not show success notification for GET requests', () => {
			const responseData = {
				code: 200,
				data: { id: 1 },
				msg: 'Data retrieved',
			};

			httpClient.get(testApiUrl).subscribe((response) => {
				expect(response).toEqual({ id: 1 });
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.flush(responseData);

			expect(mockNotificationService.showSuccess).not.toHaveBeenCalled();
		});

		it('should not show notification when no message is provided', () => {
			const responseData = { code: 200, data: { id: 1 } };

			httpClient.post(testApiUrl, { name: 'test' }).subscribe((response) => {
				expect(response).toEqual({ id: 1 });
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.flush(responseData);

			expect(mockNotificationService.showSuccess).not.toHaveBeenCalled();
		});

		it('should return original response when code is 200 but no data field', () => {
			const responseData = { code: 200, result: { id: 1, name: 'test' } };

			httpClient.get(testApiUrl).subscribe((response) => {
				expect(response).toEqual(responseData);
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.flush(responseData);
		});
	});

	describe('Business Error Handling', () => {
		it('should throw error when response code is not 200', () => {
			const errorResponse = { code: 400, msg: 'Bad request' };

			httpClient.get(testApiUrl).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.body).toEqual(errorResponse);
				},
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.flush(errorResponse);
		});

		it('should show specific error for code 100401 (unauthorized)', () => {
			const errorResponse = { code: 100401, msg: 'Unauthorized access' };

			httpClient.get(testApiUrl).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.body).toEqual(errorResponse);
				},
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.flush(errorResponse);

			expect(mockNotificationService.showError).toHaveBeenCalledWith('Unauthorized access');
		});

		it('should handle business errors with different error codes', () => {
			const errorResponse = { code: 500, msg: 'Internal server error' };

			httpClient.post(testApiUrl, { data: 'test' }).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.body).toEqual(errorResponse);
				},
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.flush(errorResponse);

			// Should not show notification for non-100401 business errors
			expect(mockNotificationService.showError).not.toHaveBeenCalled();
		});
	});

	describe('HTTP Error Handling', () => {
		it('should handle HttpErrorResponse with error message', () => {
			httpClient.get(testApiUrl).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error instanceof HttpErrorResponse).toBe(true);
				},
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.error(new ProgressEvent('Network error'), {
				status: 500,
				statusText: 'Internal Server Error',
			});

			expect(mockNotificationService.showError).toHaveBeenCalled();
		});

		it('should show error message from error.msg when available', () => {
			httpClient.get(testApiUrl).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error instanceof HttpErrorResponse).toBe(true);
				},
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.error(new ProgressEvent('Network error'), {
				status: 400,
				statusText: 'Bad Request',
				headers: undefined,
			});

			// Simulate the error with custom message
			httpClient.get(testApiUrl + '/custom').subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error instanceof HttpErrorResponse).toBe(true);
				},
			});

			const customReq = httpTestingController.expectOne(testApiUrl + '/custom');
			customReq.flush(
				{ msg: 'Custom error message' },
				{
					status: 400,
					statusText: 'Bad Request',
				}
			);

			expect(mockNotificationService.showError).toHaveBeenCalledWith('Custom error message');
		});

		it('should show default error message when no custom message available', () => {
			httpClient.get(testApiUrl).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error instanceof HttpErrorResponse).toBe(true);
				},
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.error(new ProgressEvent('Network error'), {
				status: 404,
				statusText: 'Not Found',
			});

			expect(mockNotificationService.showError).toHaveBeenCalled();
			// The exact message depends on the HttpErrorResponse.message property
			expect(mockNotificationService.showError).toHaveBeenCalledWith(jasmine.any(String));
		});
	});

	describe('Edge Cases and Integration', () => {
		it('should handle response without body', () => {
			httpClient.get(testApiUrl).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error).toBeDefined();
				},
			});

			const req = httpTestingController.expectOne(testApiUrl);
			req.flush(null);
		});

		it('should handle multiple API calls with different outcomes', () => {
			// First call - success
			httpClient.get(testApiUrl + '/success').subscribe((response) => {
				expect(response).toEqual({ id: 1 });
			});

			// Second call - business error
			httpClient.post(testApiUrl + '/error', {}).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.body.code).toBe(100401);
				},
			});

			// Third call - HTTP error
			httpClient.put(testApiUrl + '/http-error', {}).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error instanceof HttpErrorResponse).toBe(true);
				},
			});

			// Handle requests
			const successReq = httpTestingController.expectOne(testApiUrl + '/success');
			successReq.flush({ code: 200, data: { id: 1 } });

			const errorReq = httpTestingController.expectOne(testApiUrl + '/error');
			errorReq.flush({ code: 100401, msg: 'Unauthorized' });

			const httpErrorReq = httpTestingController.expectOne(testApiUrl + '/http-error');
			httpErrorReq.error(new ProgressEvent('Network error'), { status: 500 });

			expect(mockNotificationService.showError).toHaveBeenCalledWith('Unauthorized');
		});

		it('should preserve request headers and method information', () => {
			const headers = { Authorization: 'Bearer token123' };
			const requestData = { name: 'test' };

			httpClient.post(testApiUrl, requestData, { headers }).subscribe((response) => {
				expect(response).toEqual({ id: 1 });
			});

			const req = httpTestingController.expectOne(testApiUrl);
			expect(req.request.method).toBe('POST');
			expect(req.request.headers.get('Authorization')).toBe('Bearer token123');
			expect(req.request.body).toEqual(requestData);

			req.flush({ code: 200, data: { id: 1 } });
		});

		it('should handle concurrent requests independently', () => {
			const requests = [httpClient.get(testApiUrl + '/1'), httpClient.get(testApiUrl + '/2'), httpClient.get(testApiUrl + '/3')];

			requests.forEach((request, index) => {
				request.subscribe((response) => {
					expect(response).toEqual({ id: index + 1 });
				});
			});

			// Handle all requests
			for (let i = 1; i <= 3; i++) {
				const req = httpTestingController.expectOne(testApiUrl + `/${i}`);
				req.flush({ code: 200, data: { id: i } });
			}
		});
	});
});
