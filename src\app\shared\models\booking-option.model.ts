import { Currency } from 'src/app/modules/sli-mgmt/models/currency.model';
import { Dimensions } from 'src/app/modules/sli-mgmt/models/piece/dimensions.model';

export interface BookingAirlineDetailObj {
	id?: string;
	airlinesName?: string;
	totalPrice: Currency;
	transportVoList: Transport[];
	selected?: boolean;
	priceList?: BookingOptionPieceObj[];
}

export interface BookingRequestDetailObj extends BookingAirlineDetailObj {
	waybillPrefix?: string;
	waybillNumber?: string;
	bookingId: string;
	forwarderName: string;
	forwarderIataCode: string;
	pieceGroupCount: number;
	totalGrossWeight: number;
	chargeableWeight: number;
	totalDimensions: Dimensions;
	expectedCommodity: string;
	specialHandlingCodes: string[];
	textualHandlingInstructions: string;
}

export interface Transport {
	departureLocation: string;
	arrivalLocation: string;
	departureDate: string;
	arrivalDate: string;
	carrier: string;
	sequenceNumber: number;
	flightNumber?: string;
}

export interface BookingOptionPieceObj {
	chargeType: string;
	rateClassCode: string;
	subTotal: number;
	chargePaymentType: string;
	entitlement: string;
}
