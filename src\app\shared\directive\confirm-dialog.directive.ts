import { Directive, Output, EventEmitter, HostListener } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';

@Directive({
	selector: '[confirmDialog]',
})
export class ConfirmDialogDirective {
	@Output() confirm = new EventEmitter<void>();

	constructor(
		private readonly dialog: MatDialog,
		private readonly translateService: TranslateService
	) {}

	@HostListener('click', ['$event'])
	onClick(event: Event): void {
		event.preventDefault();
		event.stopPropagation();

		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translateService.instant('common.dialog.cancel.content'),
			},
		});

		dialogRef.afterClosed().subscribe((confirmed) => {
			if (confirmed) {
				this.confirm.emit();
			}
		});
	}
}
