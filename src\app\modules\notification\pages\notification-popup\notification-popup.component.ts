import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { NotificationComponent } from '../../components/notification/notification.component';
import { Router, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';

@Component({
	selector: 'orll-notification-popup',
	imports: [MatIconModule, MatSlideToggleModule, TranslateModule, NotificationComponent, RouterModule, FormsModule],
	templateUrl: './notification-popup.component.html',
	styleUrl: './notification-popup.component.scss',
})
export class NotificationPopupComponent {
	@Input() refreshNotification = true;

	@Output() showOpen = new EventEmitter<boolean>();

	showUnread = false;

	constructor(private readonly router: Router) {}

	goToNotification() {
		this.showOpen.emit(false);
		this.router.navigate(['/notification']);
	}

	onToggleChange(event: MatSlideToggleChange) {
		this.showUnread = event.checked;
	}
}
