import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@environments/environment';
import { CodeName } from '@shared/models/code-name.model';
import { CodeType } from '@shared/models/search-type.model';

@Injectable({
	providedIn: 'root',
})
export class ApiService {
	private readonly baseUrl = environment.baseApi;

	constructor(private readonly http: HttpClient) {}

	// GET
	getData<T>(endpoint: string, params?: any): Observable<T>;
	getData(endpoint: string, params: any, options: { responseType: 'blob' }): Observable<Blob>;
	getData<T>(endpoint: string, params?: any, options?: any): Observable<T | Blob> {
		let httpParams = new HttpParams();
		if (params) {
			Object.keys(params).forEach((key) => {
				httpParams = httpParams.set(key, params[key]);
			});
		}

		const requestOptions = {
			params: httpParams,
			...options,
		};

		return this.http.get(`${this.baseUrl}/${endpoint}`, requestOptions) as Observable<T | Blob>;
	}

	// POST
	postData<T>(endpoint: string, data: any): Observable<T> {
		return this.http.post<T>(`${this.baseUrl}/${endpoint}`, data);
	}

	// PUT
	updateData<T>(endpoint: string, data: any, isQueryParams?: boolean): Observable<T> {
		if (isQueryParams) {
			let httpParams = new HttpParams();
			if (data) {
				Object.keys(data).forEach((key) => {
					httpParams = httpParams.set(key, data[key]);
				});
			}

			return this.http.put<T>(`${this.baseUrl}/${endpoint}`, {}, { params: httpParams });
		}

		return this.http.put<T>(`${this.baseUrl}/${endpoint}`, data);
	}

	// PATCH
	updateDataPatch<T>(endpoint: string, data: any): Observable<T> {
		return this.http.patch<T>(`${this.baseUrl}/${endpoint}`, data);
	}

	// DELETE
	deleteData<T>(endpoint: string, data: any): Observable<T> {
		return this.http.delete<T>(`${this.baseUrl}/${endpoint}`, { body: data });
	}

	getCodeByType(codeType: CodeType): Observable<CodeName[]> {
		return this.getData(`sys-management/enums/${codeType}`);
	}
}
