import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IssuedByComponent } from './issued-by.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';

describe('IssuedByComponent', () => {
	let component: IssuedByComponent;
	let fixture: ComponentFixture<IssuedByComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [IssuedByComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(IssuedByComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('getData', () => {
		it('should return null if issuedByInfo is null', () => {
			component.issuedByInfo = null;
			expect(component.getData()).toBeNull();
		});

		it('should return correct data if issuedByInfo is not null', () => {
			const issuedByInfo = {
				id: null,
				companyName: 'Test Company',
				countryCode: 'US',
				regionCode: 'NY',
				cityCode: 'NYC',
				textualPostCode: '10001',
				locationName: '123 Test St',
				airlineCode: 'TEST123',
				persons: [
					{
						contactRole: 'CUSTOMER_CONTACT',
						phoneNumber: '**********',
						emailAddress: '<EMAIL>',
					},
				],
			} as any;

			component.issuedByInfo = issuedByInfo;
			const result = component.getData();

			expect(result).toEqual({
				id: null,
				companyName: 'Test Company',
				contactName: '',
				countryCode: 'US',
				regionCode: 'NY',
				cityCode: 'NYC',
				textualPostCode: '10001',
				locationName: '123 Test St',
				phoneNumber: '**********',
				emailAddress: '<EMAIL>',
				airlineCode: 'TEST123',
				companyType: 'AIR',
			});
		});
	});
});
