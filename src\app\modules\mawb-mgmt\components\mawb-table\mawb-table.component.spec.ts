import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { MawbTableComponent } from './mawb-table.component';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService, TranslateModule } from '@ngx-translate/core';
import { MawbListObject } from '../../models/mawb-list-object.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { MatTableDataSource } from '@angular/material/table';
import { By } from '@angular/platform-browser';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { UserProfileService } from '@shared/services/user-profile.service';
import { of } from 'rxjs';
import { Router } from '@angular/router';
import { SimpleChanges } from '@angular/core';

describe('MawbTableComponent', () => {
	let component: MawbTableComponent;
	let fixture: ComponentFixture<MawbTableComponent>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;
	let routerSpy: jasmine.SpyObj<Router>;

	const mockRecords: MawbListObject[] = [
		{
			mawbId: '1',
			mawbNumber: '123-456789',
			airlineCode: 'AA',
			goodsDescription: 'Electronics',
			origin: 'JFK',
			destination: 'LAX',
			pieceQuantity: '10',
			latestStatus: 'Created',
			createDate: '2023-01-01',
			orgId: 'org1',
			eventDate: '',
		},
		{
			mawbId: '2',
			mawbNumber: '987-654321',
			airlineCode: 'DL',
			goodsDescription: 'Clothing',
			origin: 'SFO',
			destination: 'ORD',
			pieceQuantity: '20',
			latestStatus: 'Shipped',
			createDate: '2023-01-02',
			orgId: 'org2',
			eventDate: '',
		},
	];

	const mockPageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};

	beforeEach(async () => {
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		routerSpy = jasmine.createSpyObj('Router', ['navigate']);

		await TestBed.configureTestingModule({
			imports: [MawbTableComponent, TranslateModule.forRoot()],
			providers: [
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				{
					provide: UserProfileService,
					useValue: userProfileServiceSpy,
				},
				{
					provide: Router,
					useValue: routerSpy,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(MawbTableComponent);
		component = fixture.componentInstance;

		component.records = mockRecords;
		component.dataSource = new MatTableDataSource<MawbListObject>(mockRecords);
		component.totalRecords = 2;
		component.pageParams = mockPageParams;

		fixture.detectChanges();
	});

	describe('Component Initialization', () => {
		it('should create', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with correct default values', () => {
			expect(component.records).toEqual(mockRecords);
			expect(component.totalRecords).toBe(2);
			expect(component.currentSort).toEqual({ active: '', direction: '' });
		});

		it('should initialize with correct permission constants', () => {
			expect(component.mawbModule).toBeDefined();
			expect(component.createPermission).toBeDefined();
			expect(component.sharePermission).toBeDefined();
		});

		it('should initialize dataSource correctly', () => {
			expect(component.dataSource).toBeDefined();
			expect(component.dataSource.data).toEqual(mockRecords);
		});

		it('should have correct table page sizes', () => {
			expect(component.tablePageSizes).toEqual([10, 50, 100]);
		});

		it('should initialize with correct displayed columns', () => {
			const expectedColumns = [
				'mawbNumber',
				'airlineCode',
				'goodsDescription',
				'origin',
				'destination',
				'latestStatus',
				'eventDate',
				'createDate',
			];

			// Check base columns (share column might be added by ngOnInit)
			expectedColumns.forEach((column) => {
				expect(component.displayedColumns).toContain(column);
			});
		});
	});

	it('should initialize dataSource with all fields', () => {
		expect(component.dataSource.data).toEqual(mockRecords);
		expect(component.dataSource.data[0].pieceQuantity).toBe('10');
		expect(component.dataSource.data[0].latestStatus).toBe('Created');
		expect(component.dataSource.data[0].orgId).toBe('org1');
	});

	it('should update dataSource when records input changes', () => {
		const newRecords = [
			...mockRecords,
			{
				mawbId: '3',
				mawbNumber: '111-222333',
				airlineCode: 'UA',
				goodsDescription: 'Books',
				origin: 'LHR',
				destination: 'CDG',
				pieceQuantity: '30',
				latestStatus: 'Delivered',
				createDate: '2023-01-03',
				orgId: 'org3',
				eventDate: '',
			},
		];

		component.records = newRecords;
		component.ngOnChanges({
			records: {
				currentValue: newRecords,
				previousValue: mockRecords,
				firstChange: false,
				isFirstChange: () => false,
			},
		});

		fixture.detectChanges();
		expect(component.dataSource.data.length).toBe(3);
		expect(component.dataSource.data[2].pieceQuantity).toBe('30');
	});

	it('should emit sortChange event with correct field', () => {
		spyOn(component.sortChange, 'emit');
		const sortEvent: Sort = { active: 'pieceQuantity', direction: 'asc' };

		component.onSortChange(sortEvent);

		expect(component.sortChange.emit).toHaveBeenCalledWith(sortEvent);
		expect(component.currentSort.active).toBe('pieceQuantity');
	});

	it('should emit pagination event with sort and pagination data', fakeAsync(() => {
		spyOn(component.pagination, 'emit');
		const pageEvent: PageEvent = {
			pageIndex: 1,
			pageSize: 50,
			length: 100,
		};

		component.currentSort = { active: 'latestStatus', direction: 'asc' };
		// @ts-expect-error private
		component.emitPaginationWithSort(pageEvent);
		tick();

		expect(component.pagination.emit).toHaveBeenCalledWith({
			...pageEvent,
			sortField: 'latestStatus',
			sortDirection: 'asc',
		});
	}));

	it('should emit shareHawb event with full record', () => {
		spyOn(component.shareMawb, 'emit');
		const mockRecord = mockRecords[0];

		component.shareMawb.emit(mockRecord);

		expect(component.shareMawb.emit).toHaveBeenCalledWith(mockRecord);
		expect(mockRecord.mawbNumber).toBe('123-456789');
	});

	it('should have unique trackBy identifier', () => {
		const mockRecord = mockRecords[0];
		const result = component.trackByMawbId(0, mockRecord);
		expect(result).toBe(`${mockRecord.mawbId}${mockRecord.createDate}`);
	});

	it('should render all fields in table rows', () => {
		const rows = fixture.debugElement.queryAll(By.css('tbody tr'));

		const firstRowCells = rows[0].queryAll(By.css('td'));
		expect(firstRowCells[0].nativeElement.textContent).toContain('123-456789');
		expect(firstRowCells[1].nativeElement.textContent).toContain('AA');
		expect(firstRowCells[2].nativeElement.textContent).toContain('Electronics');
		expect(firstRowCells[3].nativeElement.textContent).toContain('JFK');
		expect(firstRowCells[4].nativeElement.textContent).toContain('LAX');
		expect(firstRowCells[5].nativeElement.textContent).toContain('Created');
		expect(firstRowCells[7].nativeElement.textContent).toContain('2023-01-01');

		expect(firstRowCells[8]).toBeTruthy();
	});

	describe('Navigation Methods', () => {
		it('should navigate to create MAWB page', () => {
			component.createMawbFromHawb();

			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb/create']);
		});

		it('should navigate to edit MAWB page with correct ID', () => {
			const mawbId = 'test-mawb-123';
			component.bookingId = 'test-booking-123';

			component.editMawb(mawbId);

			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb/edit', mawbId], {
				state: {
					bookingId: 'test-booking-123',
				},
			});
		});

		it('should handle empty mawbId in editMawb', () => {
			component.bookingId = 'test-booking-123';

			component.editMawb('');

			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb/edit', ''], {
				state: {
					bookingId: 'test-booking-123',
				},
			});
		});

		it('should navigate to event tracking page when openEventTracking is called', () => {
			const mockRecord = mockRecords[0];
			const translateServiceSpy = jasmine.createSpyObj('TranslateService', ['instant']);
			translateServiceSpy.instant.and.returnValue('Event Tracking');

			// Replace the translate service
			(component as any).translateService = translateServiceSpy;

			component.openEventTracking(mockRecord);

			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb/edit', mockRecord.mawbId], {
				state: {
					selectedLabel: 'Event Tracking',
				},
			});
			expect(translateServiceSpy.instant).toHaveBeenCalledWith('mawb.event.tracking.title');
		});

		it('should handle openEventTracking with different record', () => {
			const mockRecord = mockRecords[1];
			const translateServiceSpy = jasmine.createSpyObj('TranslateService', ['instant']);
			translateServiceSpy.instant.and.returnValue('Seguimiento de Eventos');

			// Replace the translate service
			(component as any).translateService = translateServiceSpy;

			component.openEventTracking(mockRecord);

			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb/edit', mockRecord.mawbId], {
				state: {
					selectedLabel: 'Seguimiento de Eventos',
				},
			});
		});

		it('should handle navigation with null or undefined mawbId', () => {
			const recordWithNullId = { ...mockRecords[0], mawbId: null as any };
			component.bookingId = 'test-booking-123';

			component.editMawb(recordWithNullId.mawbId);

			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb/edit', null], {
				state: {
					bookingId: 'test-booking-123',
				},
			});
		});
	});

	describe('ngOnInit Permission Handling', () => {
		beforeEach(() => {
			// Reset displayedColumns to original state before each test
			component.displayedColumns.length = 0;
			component.displayedColumns.push(
				'mawbNumber',
				'airlineCode',
				'goodsDescription',
				'origin',
				'destination',
				'latestStatus',
				'eventDate',
				'createDate'
			);
		});

		it('should add share column when user has share permission', fakeAsync(() => {
			userProfileServiceSpy.hasPermission.and.returnValue(of(true));

			component.ngOnInit();
			tick();

			expect(component.displayedColumns).toContain('share');
			expect(component.displayedColumns[component.displayedColumns.length - 1]).toBe('share');
		}));

		it('should not add share column when user lacks share permission', fakeAsync(() => {
			userProfileServiceSpy.hasPermission.and.returnValue(of(false));

			component.ngOnInit();
			tick();

			expect(component.displayedColumns).not.toContain('share');
			expect(component.displayedColumns.length).toBe(9); // Only base columns
		}));

		it('should handle permission check errors gracefully', fakeAsync(() => {
			userProfileServiceSpy.hasPermission.and.returnValue(of(false));

			expect(() => {
				component.ngOnInit();
				tick();
			}).not.toThrow();

			expect(component.displayedColumns.length).toBeGreaterThan(0);
		}));

		it('should call hasPermission with correct parameters', fakeAsync(() => {
			userProfileServiceSpy.hasPermission.and.returnValue(of(true));

			component.ngOnInit();
			tick();

			expect(userProfileServiceSpy.hasPermission).toHaveBeenCalledWith(component.sharePermission, component.mawbModule);
		}));

		it('should add share column each time ngOnInit is called with permission', fakeAsync(() => {
			userProfileServiceSpy.hasPermission.and.returnValue(of(true));

			component.ngOnInit();
			tick();
			const columnsAfterFirst = [...component.displayedColumns];

			component.ngOnInit();
			tick();

			// Component will add share column again (no duplication prevention in current implementation)
			expect(component.displayedColumns.filter((col) => col === 'share').length).toBe(2);
			expect(component.displayedColumns.length).toBe(columnsAfterFirst.length + 1);
		}));
	});

	describe('ngOnChanges Edge Cases', () => {
		it('should handle changes when records is null', () => {
			const changes: SimpleChanges = {
				records: {
					currentValue: null,
					previousValue: mockRecords,
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			component.records = null as any;
			component.ngOnChanges(changes);

			// MatTableDataSource converts null to empty array
			expect(component.dataSource.data).toEqual([]);
		});

		it('should handle changes when records is undefined', () => {
			const changes: SimpleChanges = {
				records: {
					currentValue: undefined,
					previousValue: mockRecords,
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			component.records = undefined as any;
			component.ngOnChanges(changes);

			// MatTableDataSource converts undefined to empty array
			expect(component.dataSource.data).toEqual([]);
		});

		it('should not update dataSource when other properties change', () => {
			const changes: SimpleChanges = {
				totalRecords: {
					currentValue: 100,
					previousValue: 50,
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			const originalData = component.dataSource.data;
			component.ngOnChanges(changes);

			expect(component.dataSource.data).toBe(originalData);
		});

		it('should handle first change correctly', () => {
			const newRecords = [mockRecords[0]];
			const changes: SimpleChanges = {
				records: {
					currentValue: newRecords,
					previousValue: undefined,
					firstChange: true,
					isFirstChange: () => true,
				},
			};

			component.records = newRecords;
			component.ngOnChanges(changes);

			expect(component.dataSource.data).toEqual(newRecords);
		});

		it('should handle empty records array', () => {
			const changes: SimpleChanges = {
				records: {
					currentValue: [],
					previousValue: mockRecords,
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			component.records = [];
			component.ngOnChanges(changes);

			expect(component.dataSource.data).toEqual([]);
		});

		it('should handle changes with no records property', () => {
			const changes: SimpleChanges = {
				pageParams: {
					currentValue: { pageNum: 2, pageSize: 20 },
					previousValue: mockPageParams,
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			const originalData = component.dataSource.data;
			component.ngOnChanges(changes);

			// Should not affect dataSource
			expect(component.dataSource.data).toBe(originalData);
		});
	});

	describe('Sort and Pagination Integration', () => {
		it('should emit pagination with sort when onSortChange is called', () => {
			spyOn(component.pagination, 'emit');
			spyOn(component.sortChange, 'emit');
			const sortEvent: Sort = { active: 'createDate', direction: 'desc' };

			component.onSortChange(sortEvent);

			expect(component.currentSort).toEqual(sortEvent);
			expect(component.sortChange.emit).toHaveBeenCalledWith(sortEvent);
			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: mockPageParams.pageNum - 1,
				pageSize: mockPageParams.pageSize,
				length: component.totalRecords,
				sortField: 'createDate',
				sortDirection: 'desc',
			});
		});

		it('should handle emitPaginationWithSort without event parameter', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: 'mawbNumber', direction: 'asc' };

			// @ts-expect-error private method
			component.emitPaginationWithSort();

			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: mockPageParams.pageNum - 1,
				pageSize: mockPageParams.pageSize,
				length: component.totalRecords,
				sortField: 'mawbNumber',
				sortDirection: 'asc',
			});
		});

		it('should handle emitPaginationWithSort with custom event', () => {
			spyOn(component.pagination, 'emit');
			const customPageEvent: PageEvent = {
				pageIndex: 3,
				pageSize: 25,
				length: 200,
			};
			component.currentSort = { active: 'airlineCode', direction: 'desc' };

			// @ts-expect-error private method
			component.emitPaginationWithSort(customPageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith({
				...customPageEvent,
				sortField: 'airlineCode',
				sortDirection: 'desc',
			});
		});

		it('should handle sort change with empty direction', () => {
			spyOn(component.pagination, 'emit');
			spyOn(component.sortChange, 'emit');
			const sortEvent: Sort = { active: 'goodsDescription', direction: '' };

			component.onSortChange(sortEvent);

			expect(component.currentSort).toEqual(sortEvent);
			expect(component.sortChange.emit).toHaveBeenCalledWith(sortEvent);
			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: mockPageParams.pageNum - 1,
				pageSize: mockPageParams.pageSize,
				length: component.totalRecords,
				sortField: 'goodsDescription',
				sortDirection: '',
			});
		});

		it('should handle sort change with no current sort', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: '', direction: '' };

			// @ts-expect-error private method
			component.emitPaginationWithSort();

			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: mockPageParams.pageNum - 1,
				pageSize: mockPageParams.pageSize,
				length: component.totalRecords,
				sortField: '',
				sortDirection: '',
			});
		});

		it('should handle pagination with zero total records', () => {
			spyOn(component.pagination, 'emit');
			component.totalRecords = 0;
			component.currentSort = { active: 'origin', direction: 'asc' };

			// @ts-expect-error private method
			component.emitPaginationWithSort();

			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: mockPageParams.pageNum - 1,
				pageSize: mockPageParams.pageSize,
				length: 0,
				sortField: 'origin',
				sortDirection: 'asc',
			});
		});
	});

	describe('TrackBy Function', () => {
		it('should return unique identifier for different records', () => {
			const record1 = mockRecords[0];
			const record2 = mockRecords[1];

			const id1 = component.trackByMawbId(0, record1);
			const id2 = component.trackByMawbId(1, record2);

			expect(id1).toBe('12023-01-01');
			expect(id2).toBe('22023-01-02');
			expect(id1).not.toBe(id2);
		});

		it('should handle records with empty createDate', () => {
			const recordWithEmptyDate: MawbListObject = {
				...mockRecords[0],
				createDate: '',
			};

			const result = component.trackByMawbId(0, recordWithEmptyDate);

			expect(result).toBe('1');
		});

		it('should handle records with special characters in mawbId', () => {
			const recordWithSpecialChars: MawbListObject = {
				...mockRecords[0],
				mawbId: 'mawb-123@#$',
				createDate: '2023-01-01',
			};

			const result = component.trackByMawbId(0, recordWithSpecialChars);

			expect(result).toBe('mawb-123@#$2023-01-01');
		});

		it('should return consistent identifier for same record', () => {
			const record = mockRecords[0];
			const id1 = component.trackByMawbId(0, record);
			const id2 = component.trackByMawbId(5, record); // Different index, same record

			expect(id1).toBe(id2);
			expect(id1).toBe('12023-01-01');
		});

		it('should handle null or undefined values in record', () => {
			const recordWithNullValues: MawbListObject = {
				...mockRecords[0],
				mawbId: null as any,
				createDate: undefined as any,
			};

			const result = component.trackByMawbId(0, recordWithNullValues);

			// JavaScript addition of null + undefined results in NaN (number)
			expect(result).toBeNaN();
		});

		it('should handle numeric mawbId', () => {
			const recordWithNumericId: MawbListObject = {
				...mockRecords[0],
				mawbId: '12345',
				createDate: '2023-12-31',
			};

			const result = component.trackByMawbId(0, recordWithNumericId);

			expect(result).toBe('123452023-12-31');
		});
	});

	describe('Component Properties', () => {
		it('should have correct base displayedColumns', () => {
			const baseColumns = [
				'mawbNumber',
				'airlineCode',
				'goodsDescription',
				'origin',
				'destination',
				'latestStatus',
				'eventDate',
				'createDate',
			];

			// Check that all base columns are present
			baseColumns.forEach((column) => {
				expect(component.displayedColumns).toContain(column);
			});
		});

		it('should have correct tablePageSizes', () => {
			expect(component.tablePageSizes).toEqual([10, 50, 100]);
		});

		it('should initialize with empty currentSort', () => {
			expect(component.currentSort).toEqual({ active: '', direction: '' });
		});

		it('should have correct module and permission constants', () => {
			expect(component.mawbModule).toBeDefined();
			expect(component.createPermission).toBeDefined();
			expect(component.sharePermission).toBeDefined();
		});

		it('should have readonly properties that cannot be modified', () => {
			const originalPageSizes = component.tablePageSizes;
			const originalModule = component.mawbModule;
			const originalCreatePermission = component.createPermission;
			const originalSharePermission = component.sharePermission;

			// These should be readonly and not changeable
			expect(component.tablePageSizes).toBe(originalPageSizes);
			expect(component.mawbModule).toBe(originalModule);
			expect(component.createPermission).toBe(originalCreatePermission);
			expect(component.sharePermission).toBe(originalSharePermission);
		});
	});

	describe('Event Emission', () => {
		it('should emit shareMawb event correctly', () => {
			spyOn(component.shareMawb, 'emit');
			const testRecord = mockRecords[0];

			component.shareMawb.emit(testRecord);

			expect(component.shareMawb.emit).toHaveBeenCalledWith(testRecord);
		});

		it('should emit sortChange event with correct data', () => {
			spyOn(component.sortChange, 'emit');
			const sortEvent: Sort = { active: 'destination', direction: 'desc' };

			component.onSortChange(sortEvent);

			expect(component.sortChange.emit).toHaveBeenCalledWith(sortEvent);
			expect(component.sortChange.emit).toHaveBeenCalledTimes(1);
		});

		it('should emit pagination event with sort information', () => {
			spyOn(component.pagination, 'emit');
			const sortEvent: Sort = { active: 'latestStatus', direction: 'asc' };

			component.onSortChange(sortEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: mockPageParams.pageNum - 1,
				pageSize: mockPageParams.pageSize,
				length: component.totalRecords,
				sortField: 'latestStatus',
				sortDirection: 'asc',
			});
		});
	});

	describe('Error Handling and Edge Cases', () => {
		it('should handle component destruction gracefully', () => {
			expect(() => {
				fixture.destroy();
			}).not.toThrow();
		});

		it('should handle invalid pageParams gracefully', () => {
			component.pageParams = null as any;
			spyOn(component.pagination, 'emit');

			expect(() => {
				// @ts-expect-error private method
				component.emitPaginationWithSort();
			}).toThrow();
		});

		it('should handle missing router service', () => {
			(component as any).router = null;

			expect(() => {
				component.createMawbFromHawb();
			}).toThrow();
		});

		it('should handle missing translate service', () => {
			(component as any).translateService = null;

			expect(() => {
				component.openEventTracking(mockRecords[0]);
			}).toThrow();
		});
	});
});
