import { Component, Input, SimpleChanges, OnChanges, OnInit, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { MatTabsModule } from '@angular/material/tabs';
import { MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSelectModule, MatSelect, MatSelectChange } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ServerManagementService } from '../../services/server-management.service';
import { Country } from 'src/app/modules/sli-mgmt/models/country.model';
import { SliCreateRequestService } from 'src/app/modules/sli-mgmt/services/sli-create-request.service';
import { Province } from 'src/app/modules/sli-mgmt/models/province.model';
import { CodeName } from '@shared/models/code-name.model';
import { Person } from '@shared/models/person.model';
import { OrgInfo, PostOrgInfo, ServerType } from '../../models/mgmt-server.model';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { EnumCodeTypeModel } from '@shared/components/enum-code-form-item/enum-code-type.model';
import { UserRole } from '@shared/models/user-role.model';
import { CommonService } from '@shared/services/common.service';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';

@Component({
	selector: 'orll-server-detail',
	imports: [
		MatCheckboxModule,
		MatTabsModule,
		MatAccordion,
		TranslateModule,
		FormsModule,
		ReactiveFormsModule,
		MatInputModule,
		MatExpansionPanel,
		MatExpansionPanelHeader,
		MatExpansionModule,
		MatAutocompleteModule,
		MatSelectModule,
		MatSelect,
		MatIconModule,
		MatButtonModule,
		SpinnerComponent,
	],
	templateUrl: './orll-server-detail.component.html',
	styleUrl: './orll-server-detail.component.scss',
})
export class OrllServerDetailComponent extends DestroyRefComponent implements OnChanges, OnInit {
	@Input() role!: any;
	@Input() type: ServerType = ServerType.RESIDENT;

	@Output() refresh = new EventEmitter<ServerType>();

	countries: Country[] = [];
	provinces: Province[] = [];
	cities: CodeName[] = [];
	filteredCountries: Country[] = [];
	filteredProvinces: Province[] = [];
	filteredCities: CodeName[] = [];
	contactRoles: CodeName[] = [];
	contactDetailTypes: CodeName[] = [];
	filteredResidentsType: CodeName[] = [
		{ code: 'SHP', name: 'SHIPPER-SHP' },
		{ code: 'CNE', name: 'CONSIGNEE-CNE' },
		{ code: 'FFW', name: 'FORWARDER-FFW' },
		{ code: 'AIR', name: 'CARRIER-AIR' },
		{ code: 'GHA', name: 'GHA-GHA' },
		{ code: 'CTM', name: 'CUSTOM-CTM' },
		{ code: 'APT', name: 'AIRPORT-APT' },
	];
	id = '';
	dataLoading = false;
	serverType = ServerType;
	isCarrier = false;
	isForwarder = false;
	form = new FormGroup({
		uri: new FormControl<string>({ value: '', disabled: true }),
		orgnizationUri: new FormControl<string>({ value: '', disabled: true }),
		endpoint: new FormControl<string>({ value: '', disabled: true }),
		apiVersion: new FormControl<string>({ value: '', disabled: true }),
		contentType: new FormControl<string>({ value: '', disabled: true }),
		encoding: new FormControl<string>({ value: '', disabled: true }),
		language: new FormControl<string>({ value: '', disabled: true }),
		ontology: new FormControl<string>({ value: '', disabled: true }),
		ontologyVersion: new FormControl<string>({ value: '', disabled: true }),
		organization: new FormControl<string>(''),
		residentsType: new FormControl<string>('', [Validators.required]),
		airlineCode: new FormControl<string>(''),
		airlinePrefix: new FormControl<string>(''),
		country: new FormControl<string>('', [Validators.required]),
		province: new FormControl<string>('', [Validators.required]),
		city: new FormControl<string>('', [Validators.required]),
		address: new FormControl<string>('', [Validators.required]),
		postCode: new FormControl<string>(''),
		companyName: new FormControl<string>('', [Validators.required]),
		partyRole: new FormControl<string>(''),
		regionCode: new FormControl<string>(''),
		iataCargoAgentCode: new FormControl<string>(''),
		graphDbUrl: new FormControl<string>('', [Validators.required]),
		neOneUrl: new FormControl<string>('', [Validators.required]),
		keycloakUrl: new FormControl<string>('', [Validators.required]),
		grantType: new FormControl<string>('', [Validators.required]),
		clientId: new FormControl<string>('', [Validators.required]),
		clientSecret: new FormControl<string>('', [Validators.required]),
		logisticsAgentUri: new FormControl<string>('', [Validators.required]),
	});

	contactForm = this.fb.group({
		contactList: this.fb.array([
			this.fb.group({
				contactUri: [''],
				contactRole: ['', [Validators.required]],
				jobTitle: [''],
				contactName: ['', [Validators.required]],
				contactDetailType: [{ value: 'PHONE_NUMBER', disabled: true }],
				textualValue: ['', [Validators.required]],
				contactDetailType1: [{ value: 'EMAIL_ADDRESS', disabled: true }],
				textualValue1: [''],
			}),
		]),
	});

	get contactListArray(): FormArray {
		return this.contactForm.get('contactList') as FormArray;
	}

	constructor(
		private readonly fb: FormBuilder,
		private readonly serverManagementService: ServerManagementService,
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly mgmtRequestService: OrgMgmtRequestService,
		private readonly commonService: CommonService,
		private readonly cdr: ChangeDetectorRef,

	) {
		super();
		this.form.get('residentsType')?.valueChanges.pipe(
			takeUntilDestroyed(this.destroyRef)
		).subscribe(value => {
			this.isCarrier = value === UserRole.CARRIER.valueOf();
			this.isForwarder = value === UserRole.FORWARDER.valueOf();
			if (this.isForwarder) {
				this.form.get('iataCargoAgentCode')?.setValidators(Validators.required);
			}
			this.toggleValidatorForCarriers(this.isCarrier);
		});
	}

	ngOnInit(): void {
		this.initRefData();
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes['role']) {
			this.loadRoleData();
		}
	}

	private initRefData(): void {
		this.sliCreateRequestService.getCountries().subscribe((countries: Country[]) => {
			this.countries = countries;
			this.filteredCountries = countries;
		});

		this.mgmtRequestService.getEnumCode(EnumCodeTypeModel.CONTACT_ROLE).subscribe((res: CodeName[]) => {
			this.contactRoles = res;
		});
		this.mgmtRequestService.getEnumCode(EnumCodeTypeModel.CONTACT_DETAIL_TYPE).subscribe((res: CodeName[]) => {
			this.contactDetailTypes = res;
		});
	}

	private async loadRoleData() {
		if (this.role && this.role.id !== 'new-server') {
			this.serverManagementService.getOrgInfo(this.role?.id).subscribe((orgInfo) => {
				this.id = orgInfo.id;
				this.patchFormValue(orgInfo);
				this.patchContactValue(orgInfo.persons);
				this.toggleValidatorForCarriers(this.isCarrier);
				if (this.type === this.serverType.EXTERNAL) {
					this.form.disable();
					this.contactForm.disable();
				} else {
					this.form.controls.uri.disable();
				}
			});
		} else {
			this.role = undefined;
			this.form.reset();
			this.contactForm.controls.contactList.reset();
			this.isCarrier = this.form.get('residentsType')?.value === UserRole.CARRIER.valueOf();
			this.isForwarder = this.form.get('residentsType')?.value === UserRole.FORWARDER.valueOf();
			if (this.type === ServerType.EXTERNAL) {
				this.form.disable();
				this.contactForm.disable();
				this.form.controls.uri.enable();
				this.form.controls.orgnizationUri.enable();
				this.contactForm.controls.contactList.at(0).controls.contactUri.enable();
			} else {
				await this._resetContactList();
				if (this.form.get('residentsType')?.value === UserRole.CONSIGNEE.valueOf()) {
					this.newContact();
				} else {
					this.newContact();
					this.newContact();
				}
			}
		}
	}
	private async _resetContactList(): Promise<void> {
		return new Promise(resolve => {
			this.contactForm.controls.contactList.clear();
			setTimeout(resolve, 0);
		});
	}

	private toggleValidatorForCarriers(isCarrier: boolean) {
		const result = isCarrier;
		if (result) {
			this.form.get('airlineCode')?.setValidators(Validators.required);
			this.form.get('airlinePrefix')?.setValidators(Validators.required);
		} else {
			this.form.get('airlineCode')?.removeValidators(Validators.required);
			this.form.get('airlinePrefix')?.removeValidators(Validators.required);
		}
	}

	private patchContactValue(persons: Person[]) {
		this.contactForm = this.fb.group({
			contactList: this.fb.array(
				persons.map((person) =>
					this.fb.group({
						contactUri: [''],
						contactRole: [person.contactRole || ''],
						jobTitle: [person.jobTitle || ''],
						contactName: [person.contactName || ''],
						contactDetailType: [person.contactDetailType || 'PHONE_NUMBER'],
						textualValue: [person.phoneNumber || ''],
						contactDetailType1: [person.contactDetailType1 || 'EMAIL_ADDRESS'],
						textualValue1: [person.emailAddress || ''],
					})
				)
			),
		});
	}

	private patchFormValue(orgInfo: OrgInfo) {
		this.form.patchValue({
			uri: orgInfo.server?.hasDataHolder,
			endpoint: orgInfo.server?.hasServerEndpoint,
			apiVersion: '',
			contentType: orgInfo.server?.hasSupportedContentType.toString(),
			encoding: '',
			language: orgInfo.server?.hasSupportedLanguage,
			ontology: '',
			ontologyVersion: '',
			organization: '',
			residentsType: orgInfo.partyRole,
			airlineCode: orgInfo.airlineCode,
			airlinePrefix: orgInfo.airlinePrefix,
			country: orgInfo.countryCode,
			companyName: orgInfo.companyName,
			partyRole: orgInfo.partyRole,
			regionCode: orgInfo.regionCode,
			city: orgInfo.cityCode,
			iataCargoAgentCode: orgInfo.iataCargoAgentCode,
			graphDbUrl: orgInfo.orgProperties?.graphDbUrl,
			neOneUrl: orgInfo.orgProperties?.neOneUrl,
			keycloakUrl: orgInfo.orgProperties?.keycloakUrl,
			grantType: orgInfo.orgProperties?.grantType,
			clientId: orgInfo.orgProperties?.clientId,
			clientSecret: orgInfo.orgProperties?.clientSecret,
			logisticsAgentUri: orgInfo.orgProperties?.logisticsAgentUri,
		});
		this.setupCountryValueChange(orgInfo.countryCode);

		this.form.patchValue({
			province: orgInfo.regionCode,
		});

		this.setupRegionValueChange(orgInfo.regionCode);
		this.form.patchValue({
			city: orgInfo.cityCode,
			postCode: orgInfo.textualPostCode,
		});
	}

	private setupCountryValueChange(value: string): void {
		const selectedCountry: Country[] = this.countries.filter((country: Country) => country.code === value);
		this.sliCreateRequestService.getProvinces(selectedCountry[0]).subscribe((provinces: Province[]) => {
			this.provinces = provinces;
			this.filteredProvinces = provinces;
		});
	}

	private setupRegionValueChange(value: string): void {
		const selectedProvince: Province[] = this.provinces.filter((province: Province) => province.code === value);
		this.sliCreateRequestService.getCities(selectedProvince[0]).subscribe((cities: CodeName[]) => {
			this.cities = cities;
			this.filteredCities = cities;
		});
	}

	countryValueChange(event?: MatSelectChange): void {
		this.form.get('province')?.setValue('');
		this.form.get('city')?.setValue('');

		const value = event?.value ?? '';
		this.setupCountryValueChange(value);
	}

	async residentsTypeValueChange(event?: MatSelectChange): Promise<void> {
		await this._resetContactList();
		const value = event?.value ?? '';
		if (!this.role && value === UserRole.CONSIGNEE.valueOf()) {
			this.newContact();
		} else {
			this.newContact();
			this.newContact();
		}
	}

	regionValueChange(event?: MatSelectChange): void {
		const value = event?.value ?? '';
		this.setupRegionValueChange(value);
	}

	onSave(): void {
		this.form.markAllAsTouched();
		this.contactForm.markAllAsTouched();
		if (this.form.invalid || this.contactForm.invalid) {
			this.commonService.showFormInvalid();
			return;
		}

		const param: PostOrgInfo = this.getFormData();
		this.getContactFormData(param);

		if (this.id && this.role) {
			param.idStr = this.id;
		}
		param.orgServerType = this.type;
		param.orgType = this.form.get('residentsType')?.value ?? '';
		this.dataLoading = true;
		this.serverManagementService.saveOrg(param).subscribe({
			next: () => {
				this.dataLoading = false;
				this.refresh.emit(this.type);
			},
			error: () => (this.dataLoading = false),
		});
	}

	private getFormData() {
		return {
			orgStatus: this.form.get('companyName')?.value ?? '',
			orgServerType: this.type === ServerType.EXTERNAL ? 'External' : 'Internal',
			organization: {
				name: this.form.get('companyName')?.value ?? '',
				basedAtLocation: {
					streetAddressLines: this.form.get('address')?.value ?? '',
					cityCode: this.form.get('city')?.value ?? '',
					regionCode: this.form.get('province')?.value ?? '',
					country: this.form.get('country')?.value ?? '',
				},
			},
			orgProperties: {
				graphDbUrl: this.form.get('graphDbUrl')?.value ?? '',
				userName: '',
				password: '',
				neOneUrl: this.form.get('neOneUrl')?.value ?? '',
				keycloakUrl: this.form.get('keycloakUrl')?.value ?? '',
				grantType: this.form.get('grantType')?.value ?? '',
				clientId: this.form.get('clientId')?.value ?? '',
				clientSecret: this.form.get('clientSecret')?.value ?? '',
				logisticsAgentUri: this.form.get('logisticsAgentUri')?.value ?? '',
			},
			party: {
				partyRole: this.form.get('partyRole')?.value ?? '',
			},
			persons: [],
			orgType: this.form.get('partyRole')?.value ?? '',
			name: this.form.get('companyName')?.value ?? '',
		};
	}

	private getContactFormData(param: PostOrgInfo) {
		this.contactForm.controls.contactList.controls.forEach((item) => {
			param.persons.push({
				lastName: item.get('contactName')?.value ?? '',
				contactRole: item.get('contactRole')?.value ?? '',
				contactDetails: [
					{
						contactDetailType: item.get('contactDetailType')?.value ?? '',
						textualValue: item.get('textualValue')?.value ?? '',
					},
					{
						contactDetailType: item.get('contactDetailType1')?.value ?? '',
						textualValue: item.get('textualValue1')?.value ?? '',
					},
				],
			});
		});
	}

	getServerInfo(): void {
		this.serverManagementService.retrieveServerInformation('').subscribe((res) => {
			this.form.get('endpoint')?.patchValue(res.hasServerEndpoint);
			this.form.get('language')?.patchValue(res.hasSupportedLanguage);
			this.form.get('contentType')?.patchValue(res.hasSupportedContentType.toString());

		});
	}

	deleteServer() {
		this.dataLoading = true;
		this.serverManagementService.deleteServer(this.id).subscribe({
			next: () => {
				this.refresh.emit(this.type);
				this.dataLoading = false;
			},
			error: () => (this.dataLoading = false),
		});
	}

	newContact() {
		const contactGroup = this.fb.group({
			contactUri: [''],
			contactRole: ['', [Validators.required]],
			jobTitle: [''],
			contactName: ['', [Validators.required]],
			contactDetailType: [{ value: 'PHONE_NUMBER', disabled: true }],
			textualValue: ['', [Validators.required]],
			contactDetailType1: [{ value: 'EMAIL_ADDRESS', disabled: true }],
			textualValue1: [''],
		});
		if (this.type === this.serverType.EXTERNAL) {
			contactGroup.disable();
			contactGroup.controls.contactUri.enable();
		}
		this.contactForm.controls.contactList.push(contactGroup);
	}

	deleteContact(index: number) {
		this.contactForm.controls.contactList.controls.splice(index, 1);
	}

	retrieveOrg() {
		//implete it later
	}

	retrieveContact() {
		//implete it later
	}
}
