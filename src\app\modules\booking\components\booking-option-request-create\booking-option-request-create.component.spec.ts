import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { BookingOptionRequestDetailObj, BookingOptionRequestListObj } from '../../models/booking.model';
import { TranslateModule } from '@ngx-translate/core';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { UserProfileService } from '@shared/services/user-profile.service';
import { of, throwError } from 'rxjs';
import { BookingOptionRequestCreateComponent } from './booking-option-request-create.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { FormControl, FormGroup } from '@angular/forms';

const mocRes: BookingOptionRequestDetailObj = {
	bookingShipmentDetails: {
		id: '',
		pieceGroups: {
			id: '',
			pieceGroupCount: 2,
		},
		totalGrossWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		chargeableWeight: {
			currencyUnit: '',
			numericalValue: 0,
		},
		dimensions: {
			length: 0,
			width: 0,
			height: 0,
		},
		expectedCommodity: '',
		specialHandlingCodes: [],
		textualHandlingInstructions: '',
	},
	transportLegs: [],
	bookingPreference: [
		{
			maxSegments: 0,
			preferredTransportId: '',
		},
	],
	timePreferences: {
		earliestAcceptanceTime: '',
		latestAcceptanceTime: '',
		latestArrivalTime: '',
		timeOfAvailability: '',
	},
	unitsPreference: {
		currency: {
			currencyUnit: '',
		},
	},
	involvedParties: [],
};

const mockListObj: BookingOptionRequestListObj = {
	id: '222',
	bookingOptionRequestId: '',
	expectedCommodity: '',
	departureLocation: '',
	arrivalLocation: '',
	latestStatus: '',
};

const mockCurrencies = ['CNY', 'USD', 'EUR'];
const mockAirports = [
	{ code: 'PEK', name: 'Beijing' },
	{ code: 'SHA', name: 'Shanghai' },
];

describe('BookingQuoteDetailComponent', () => {
	let component: BookingOptionRequestCreateComponent;
	let fixture: ComponentFixture<BookingOptionRequestCreateComponent>;
	let mockDialog: jasmine.SpyObj<MatDialog>;
	let mockService: jasmine.SpyObj<any>;
	let dialogRef: jasmine.SpyObj<MatDialogRef<BookingOptionRequestCreateComponent>>;
	let mockProfilemockService: jasmine.SpyObj<UserProfileService>;

	beforeEach(async () => {
		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		mockService = jasmine.createSpyObj('BookingOptionRequestmockService', ['getBookingOptionDetail', 'getAirports']);
		mockService.getBookingOptionDetail.and.returnValue(of(mocRes));
		mockService.getAirports.and.returnValue(of(mockAirports));

		dialogRef = jasmine.createSpyObj<MatDialogRef<BookingOptionRequestCreateComponent>>('MatDialogRef', ['close']);
		mockProfilemockService = jasmine.createSpyObj('UserProfilemockService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		mockProfilemockService.hasPermission.and.returnValue(of(true));
		mockProfilemockService.hasSomeRole.and.returnValue(of(true));

		await TestBed.configureTestingModule({
			imports: [BookingOptionRequestCreateComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: BookingOptionRequestService, useValue: mockService },
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: MAT_DIALOG_DATA, useValue: mockListObj },
				{ provide: MatDialogRef, useValue: dialogRef },
				{ provide: UserProfileService, useValue: mockProfilemockService },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		})
			.overrideComponent(BookingOptionRequestCreateComponent, {
				set: {
					template: `
					<div class="booking-option-request-create">
						<div class="booking-option-request-create__content">
							<!-- Mock child component removed -->
						</div>
					</div>
				`,
				},
			})
			.compileComponents();

		fixture = TestBed.createComponent(BookingOptionRequestCreateComponent);
		component = fixture.componentInstance;

		// Set up the child component mock BEFORE detectChanges to prevent ngAfterViewInit errors
		component.bookingOptionRequestFormComponent = {
			bookingOptionReqForm: new FormGroup({
				departureLocation: new FormControl(),
				arrivalLocation: new FormControl(),
				specialHandlingCodes: new FormControl(),
				textualHandlingInstructions: new FormControl(),
				chargeableWeight: new FormControl(),
				totalGrossWeight: new FormControl(),
				dimensions: new FormControl(),
				expectedCommodity: new FormControl(),
				pieceGroupCount: new FormControl(),
			}),
			locationList: [
				{ code: 'PEK', name: 'Beijing' },
				{ code: 'LAX', name: 'Los Angeles' },
			],
			commodityCodeList: [{ code: 'GEN', name: 'General Cargo' }],
		} as any;

		// Don't call detectChanges here to avoid ngAfterViewInit issues
	});

	it('should create', () => {
		// Set bookingOptionRequestId to prevent ngAfterViewInit from calling patchValueFromMawb
		component.data.bookingOptionRequestId = 'test-id';
		fixture.detectChanges();
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should load booking option detail when bookingOptionRequestId exists', fakeAsync(() => {
			component.data.bookingOptionRequestId = 'test-id';
			mockService.getBookingOptionDetail.and.returnValue(of(mocRes));

			// Set up child component to prevent errors
			component.bookingOptionRequestFormComponent = {
				commodityCodeList: [],
				locationList: [],
				bookingOptionReqForm: new FormGroup({
					departureLocation: new FormControl(),
					arrivalLocation: new FormControl(),
					specialHandlingCodes: new FormControl(),
					expectedCommodity: new FormControl(),
				}),
				getFormData: jasmine.createSpy('getFormData').and.returnValue({}),
			} as any;

			component.ngOnInit();
			tick(); // Complete async operations
			fixture.detectChanges();

			expect(mockService.getBookingOptionDetail).toHaveBeenCalledWith('test-id');
			expect(component.dataLoading).toBeFalse();
		}));

		it('should not call patchValueFromMawb in ngOnInit (it is called in ngAfterViewInit)', () => {
			component.data.bookingOptionRequestId = '';
			spyOn(component as any, 'patchValueFromMawb');

			component.ngOnInit();

			expect((component as any).patchValueFromMawb).not.toHaveBeenCalled();
		});

		it('should handle error when loading booking option detail', fakeAsync(() => {
			component.data.bookingOptionRequestId = 'test-id';
			mockService.getBookingOptionDetail.and.returnValue(throwError(() => new Error('Service error')));

			// Set up child component to prevent errors
			component.bookingOptionRequestFormComponent = {
				commodityCodeList: [],
				locationList: [],
				bookingOptionReqForm: new FormGroup({
					departureLocation: new FormControl(),
					arrivalLocation: new FormControl(),
					specialHandlingCodes: new FormControl(),
					expectedCommodity: new FormControl(),
				}),
				getFormData: jasmine.createSpy('getFormData').and.returnValue({}),
			} as any;

			// Expect the error to be thrown since there's no error handling in the component
			expect(() => {
				component.ngOnInit();
				tick(); // Complete async operations
			}).toThrow();
		}));
	});

	describe('sendOptionRequest', () => {
		beforeEach(() => {
			// Set bookingOptionRequestId to prevent ngAfterViewInit from calling patchValueFromMawb
			component.data.bookingOptionRequestId = 'test-id';
			fixture.detectChanges();
		});

		it('should mark form as touched and open dialog if form is invalid', () => {
			// Ensure child component is set up
			if (!component.bookingOptionRequestFormComponent) {
				component.bookingOptionRequestFormComponent = {
					bookingOptionReqForm: new FormGroup({
						departureLocation: new FormControl(),
						arrivalLocation: new FormControl(),
					}),
					locationList: [],
					commodityCodeList: [],
				} as any;
			}

			component.bookingOptionRequestFormComponent.bookingOptionReqForm.setErrors({ invalid: true });

			const dialogSpy = spyOn(component['dialog'], 'open');

			component.sendOptionRequest();

			expect(component.bookingOptionRequestFormComponent.bookingOptionReqForm.touched).toBe(true);
			expect(dialogSpy).toHaveBeenCalledWith(ConfirmDialogComponent, {
				width: '300px',
				data: { content: 'common.dialog.form.validate' },
			});
		});

		it('should call createBookingOptionRequest and close dialog on success when form is valid', () => {
			// Ensure child component is set up
			if (!component.bookingOptionRequestFormComponent) {
				component.bookingOptionRequestFormComponent = {
					bookingOptionReqForm: new FormGroup({
						totalGrossWeight: new FormControl(),
						chargeableWeight: new FormControl(),
						dimLength: new FormControl(),
						dimWidth: new FormControl(),
						dimHeight: new FormControl(),
						pieceGroupCount: new FormControl(),
						expectedCommodity: new FormControl(),
						departureLocation: new FormControl(),
						arrivalLocation: new FormControl(),
						currency: new FormControl(),
					}),
					locationList: [],
					commodityCodeList: [],
					getFormData: jasmine.createSpy('getFormData').and.returnValue({}),
				} as any;
			}

			// make form valid
			component.bookingOptionRequestFormComponent.bookingOptionReqForm.patchValue({
				totalGrossWeight: '10.0',
				chargeableWeight: '9.5',
				dimLength: '10.0',
				dimWidth: '5.0',
				dimHeight: '2.0',
				pieceGroupCount: '1',
				expectedCommodity: 'C1',
				departureLocation: mockAirports[0],
				arrivalLocation: mockAirports[1],
				currency: mockCurrencies[0],
			});

			mockService.createBookingOptionRequest = jasmine.createSpy('createBookingOptionRequest').and.returnValue(of({}));
			(component as any).bookingService = mockService;

			component.sendOptionRequest();

			expect(mockService.createBookingOptionRequest).toHaveBeenCalled();
			expect(dialogRef.close).toHaveBeenCalledWith(true);
			expect(component.dataLoading).toBeFalse();
		});

		it('should handle error from createBookingOptionRequest and close dialog without args', () => {
			// Ensure child component is set up
			if (!component.bookingOptionRequestFormComponent) {
				component.bookingOptionRequestFormComponent = {
					bookingOptionReqForm: new FormGroup({
						totalGrossWeight: new FormControl(),
						chargeableWeight: new FormControl(),
						dimLength: new FormControl(),
						dimWidth: new FormControl(),
						dimHeight: new FormControl(),
						pieceGroupCount: new FormControl(),
						expectedCommodity: new FormControl(),
						departureLocation: new FormControl(),
						arrivalLocation: new FormControl(),
						currency: new FormControl(),
					}),
					locationList: [],
					commodityCodeList: [],
					getFormData: jasmine.createSpy('getFormData').and.returnValue({}),
				} as any;
			}

			// make form valid
			component.bookingOptionRequestFormComponent.bookingOptionReqForm.patchValue({
				totalGrossWeight: '10.0',
				chargeableWeight: '9.5',
				dimLength: '10.0',
				dimWidth: '5.0',
				dimHeight: '2.0',
				pieceGroupCount: '1',
				expectedCommodity: 'C1',
				departureLocation: mockAirports[0],
				arrivalLocation: mockAirports[1],
				currency: mockCurrencies[0],
			});

			mockService.createBookingOptionRequest = jasmine.createSpy('createBookingOptionRequest');
			(mockService.createBookingOptionRequest as jasmine.Spy).and.returnValue(throwError(() => new Error('create error')));
			(component as any).bookingService = mockService;

			component.sendOptionRequest();

			expect(mockService.createBookingOptionRequest).toHaveBeenCalled();
			expect(dialogRef.close).toHaveBeenCalled();
			expect(component.dataLoading).toBeFalse();
		});
	});

	describe('patchValueFromMawb', () => {
		beforeEach(() => {
			component.bookingOptionRequestFormComponent = {
				bookingOptionReqForm: new FormGroup({
					departureLocation: new FormControl(),
					arrivalLocation: new FormControl(),
				}),
				locationList: [],
			} as any;
		});

		it('should patch form values from data and call getAirports', fakeAsync(() => {
			component.data = {
				...mockListObj,
				departureLocation: 'PEK',
				arrivalLocation: 'SHA',
			};

			(component as any).patchValueFromMawb();
			tick(); // Complete async operations

			expect(mockService.getAirports).toHaveBeenCalled();
			expect(component.bookingOptionRequestFormComponent.locationList).toEqual(mockAirports);
			expect(component.bookingOptionRequestFormComponent.bookingOptionReqForm.get('departureLocation')?.value).toEqual(
				mockAirports[0]
			);
			expect(component.bookingOptionRequestFormComponent.bookingOptionReqForm.get('arrivalLocation')?.value).toEqual(mockAirports[1]);
		}));

		it('should handle missing location codes gracefully', fakeAsync(() => {
			component.data = {
				...mockListObj,
				departureLocation: 'UNKNOWN',
				arrivalLocation: 'UNKNOWN',
			};

			expect(() => {
				(component as any).patchValueFromMawb();
				tick(); // Complete async operations
			}).not.toThrow();
		}));
	});

	describe('ngAfterViewInit', () => {
		it('should call patchValueFromMawb when no bookingOptionRequestId', () => {
			component.data.bookingOptionRequestId = '';
			spyOn(component as any, 'patchValueFromMawb');

			component.ngAfterViewInit();

			expect((component as any).patchValueFromMawb).toHaveBeenCalled();
		});

		it('should not call patchValueFromMawb when bookingOptionRequestId exists', () => {
			component.data.bookingOptionRequestId = 'test-id';
			spyOn(component as any, 'patchValueFromMawb');

			component.ngAfterViewInit();

			expect((component as any).patchValueFromMawb).not.toHaveBeenCalled();
		});
	});

	describe('patchDetailValue', () => {
		beforeEach(() => {
			component.bookingOptionRequestFormComponent = {
				bookingOptionReqForm: new FormGroup({
					departureLocation: new FormControl(),
					arrivalLocation: new FormControl(),
					specialHandlingCodes: new FormControl(),
					expectedCommodity: new FormControl(),
				}),
				locationList: mockAirports,
				commodityCodeList: [],
			} as any;
		});

		it('should patch form values from booking option detail', () => {
			const mockDetail = {
				...mocRes,
				bookingShipmentDetails: {
					...mocRes.bookingShipmentDetails,
					expectedCommodity: 'TEST_COMMODITY',
				},
				transportLegs: [
					{
						departureLocation: { locationCodes: [{ code: 'PEK' }] },
						arrivalLocation: { locationCodes: [{ code: 'SHA' }] },
					},
				],
			};

			(component as any).patchDetailValue(mockDetail);

			expect(component.bookingOptionRequestFormComponent.bookingOptionReqForm.get('expectedCommodity')?.value).toBe('TEST_COMMODITY');
		});
	});
});
