import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
// eslint-disable-next-line @typescript-eslint/naming-convention
import OrllDialogComponent from '@shared/components/dialog-template/dialog-template.component';
import { SelectionModel } from '@angular/cdk/collections';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { EcsdService } from '../../service/ecsd.service';
import { CommonService } from '@shared/services/common.service';
import {
	EcsdDialogObj,
	EcsdObj,
	EXEMPTED_SCREEN,
	GROUND_EXEMPTION,
	HawbListObj,
	PieceListObj,
	SCREEM_METHOD,
} from '../../models/ecsd.model';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CodeType } from '@shared/models/search-type.model';
import { CodeName } from '@shared/models/code-name.model';
import { MatDividerModule } from '@angular/material/divider';
import { MatRadioModule } from '@angular/material/radio';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { forkJoin } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { Modules } from '@shared/models/user-role.model';
import { DatePipe } from '@angular/common';
import { provideLuxonDateAdapter } from '@angular/material-luxon-adapter';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { DateTime } from 'luxon';
import { DATE_TIME_FORMAT } from '@shared/models/constant';

@Component({
	selector: 'orll-ecsd-create',
	imports: [
		MatIconModule,
		MatButtonModule,
		TranslateModule,
		MatDialogModule,
		OrllDialogComponent,
		SpinnerComponent,
		MatFormFieldModule,
		ReactiveFormsModule,
		MatInputModule,
		MatTableModule,
		MatDividerModule,
		MatRadioModule,
		MatDatepickerModule,
		MatSelectModule,
		MatCheckboxModule,
	],
	providers: [
		provideLuxonDateAdapter({
			parse: {
				dateInput: 'MM/dd/yyyy',
			},
			display: {
				dateInput: 'MM/dd/yyyy',
				monthYearLabel: 'yyyy',
				dateA11yLabel: 'LL',
				monthYearA11yLabel: 'MM yyyy',
			},
		}),
		DatePipe,
	],
	templateUrl: './ecsd-create.component.html',
	styleUrl: './ecsd-create.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EcsdCreateComponent extends RolesAwareComponent implements OnInit {
	dataLoading = false;
	ecsdForm = new FormGroup({
		securityStatus: new FormControl('', [Validators.required]),
		receivedFrom: new FormControl(''),
		whetherExemptedForScreening: new FormControl('no', [Validators.required]),
		screeningMethod: new FormControl(''),
		groundsForExemption: new FormControl(''),
		issuedBy: new FormControl('', [Validators.required]),
		employeeId: new FormControl(''),
		issuedOn: new FormControl<DateTime | null>(null, [Validators.required]),
		additionalSecurityInfo: new FormControl(''),
		regulatedEntityCategory: new FormControl('', [Validators.required]),
		regulatedEntityIdentifier: new FormControl('', [Validators.required]),
		regulatedEntityCategory1: new FormControl('', [Validators.required]),
		regulatedEntityIdentifier1: new FormControl('', [Validators.required]),
	});

	dataSource = new MatTableDataSource<PieceListObj | HawbListObj>([]);

	pieceDisplayedColumns: string[] = ['select', 'productDescription', 'packageType', 'grossWeight', 'pieceQuantity', 'dimensions'];
	hawbDisplayedColumns: string[] = [
		'select',
		'waybillNumber',
		'shipper',
		'consignee',
		'goodsDescription',
		'origin',
		'destination',
		'weight',
		'slac',
	];
	selection = new SelectionModel<PieceListObj | HawbListObj>(
		true,
		[],
		false,
		(a: PieceListObj | HawbListObj, b: PieceListObj | HawbListObj) => a.id === b.id
	);
	screenExemptionList: CodeName[] = [];
	screenMethodList: CodeName[] = [];
	securityStatusList: CodeName[] = [];
	categoryList: CodeName[] = [];
	packageTypes: CodeName[] = [];

	dateLoading = false;
	moduleEnum = Modules;

	constructor(
		private readonly ecsdService: EcsdService,
		private readonly commonService: CommonService,
		@Inject(MAT_DIALOG_DATA) public data: EcsdDialogObj,
		private readonly dialogRef: MatDialogRef<EcsdCreateComponent>,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnInit() {
		[
			CodeType.SCREEN_EXEMPTION,
			CodeType.SCREEN_METHOD,
			CodeType.SECURITY_STATUS,
			CodeType.ENTITY_CATEGORY,
			CodeType.PACKAGE_TYPE,
		].forEach((item) => {
			this.ecsdService.getCodeByType(item).subscribe((res) => {
				switch (item) {
					case CodeType.SCREEN_EXEMPTION:
						this.screenExemptionList = res;
						break;
					case CodeType.SECURITY_STATUS:
						this.securityStatusList = res;
						break;
					case CodeType.ENTITY_CATEGORY:
						this.categoryList = res;
						break;
					case CodeType.SCREEN_METHOD:
						this.screenMethodList = res;
						break;
					case CodeType.PACKAGE_TYPE:
						this.packageTypes = res;
						break;
					default:
					//ignore
				}
				this.cdr.markForCheck();
			});
		});

		if (this.data.ecsdObj) {
			this.dataLoading = true;
			forkJoin({
				detail: this.ecsdService.getEcsd(this.data.ecsdObj),
				subObj: this.ecsdService.getPiecesAndHawb({ loId: this.data.loId, loType: this.data.loType }),
			})
				.pipe(takeUntilDestroyed(this.destroyRef))
				.subscribe({
					next: ({ detail, subObj }) => {
						this.patchFormValue(detail);
						const isPieceList = subObj.pieceList && subObj.pieceList.length > 0;
						if (isPieceList) {
							subObj.pieceList.forEach(
								(item) => (item.packageType = this.packageTypes.find((type) => type.code === item.packageType)?.name ?? '')
							);
						}
						this.dataSource.data = isPieceList ? subObj.pieceList : subObj.hawbList;
						let idList = detail.pieceList?.map((item) => item.id) ?? [];
						if (this.data.loType === this.moduleEnum.MAWB) {
							idList = detail.hawbList?.map((item) => item.id) ?? [];
						}
						this.selection = new SelectionModel(
							true,
							this.dataSource.data.filter((item) => idList?.includes(item.id))
						);

						this.dataLoading = false;
						this.cdr.markForCheck();
					},
					error: () => {
						this.dataLoading = false;
						this.cdr.markForCheck();
					},
				});
		} else {
			this.dataLoading = true;
			this.ecsdService.getPiecesAndHawb({ loId: this.data.loId, loType: this.data.loType }).subscribe({
				next: (res) => {
					res.pieceList?.forEach(
						(item) => (item.packageType = this.packageTypes.find((type) => type.code === item.packageType)?.name ?? '')
					);
					this.dataSource.data = res.pieceList && res.pieceList.length > 0 ? res.pieceList : res.hawbList;
					this.selection = new SelectionModel(true, this.dataSource.data);
					this.dataLoading = false;
					this.cdr.markForCheck();
				},
				error: () => {
					this.dataLoading = false;
					this.cdr.markForCheck();
				},
			});
		}
		this.watchWhetherExemptedForScreening();
	}

	saveEcsd() {
		this.ecsdForm.markAllAsTouched();
		if (this.ecsdForm.invalid) {
			this.commonService.showFormInvalid();
			return;
		}
		const ecsdObj: EcsdObj = this.getFormData();
		if (this.data.loType === Modules.MAWB) {
			ecsdObj.hawbList = this.selection.selected as HawbListObj[];
		} else {
			ecsdObj.pieceList = this.selection.selected as PieceListObj[];
		}
		this.ecsdService.saveEcsd(ecsdObj).subscribe({
			next: () => {
				this.dataLoading = false;
				this.dialogRef.close(true);
			},
			error: () => {
				this.dataLoading = false;
				this.dialogRef.close(false);
			},
		});
	}

	trackByPieceId(record: PieceListObj): string {
		return record.pieceId;
	}

	trackByHawbId(record: HawbListObj): string {
		return record.hawbId;
	}

	isAllSelected() {
		const numSelected = this.selection.selected.length;
		const numRows = this.dataSource.data.length;
		return numRows > 0 ? numSelected === numRows : false;
	}

	toggleAllRows() {
		if (this.isAllSelected()) {
			this.selection.clear();
			return;
		}
		this.selection.select(...this.dataSource.data);
	}

	getFormData(): EcsdObj {
		const controls = this.ecsdForm.controls;
		const formData: EcsdObj = {
			securityStatus: controls.securityStatus.value ?? '',
			receivedFrom: controls.receivedFrom.value ?? '',
			screeningMethod: controls.screeningMethod.value ?? '',
			groundsForExemption: controls.groundsForExemption.value ?? '',
			issuedBy: controls.issuedBy.value ?? '',
			employeeId: controls.employeeId.value ?? '',
			issuedOn: controls.issuedOn.value ? controls.issuedOn.value.toFormat(DATE_TIME_FORMAT) : '',
			additionalSecurityInfo: controls.additionalSecurityInfo.value ?? '',
			loId: this.data.loId,
			loType: this.data.loType,
			regulatedEntityCategory1: controls.regulatedEntityCategory1.value ?? '',
			regulatedEntityIdentifier1: controls.regulatedEntityIdentifier1.value ?? '',
			regulatedEntityCategory: controls.regulatedEntityCategory.value ?? '',
			regulatedEntityIdentifier: controls.regulatedEntityIdentifier.value ?? '',
		};
		if (this.data.ecsdObj) {
			formData.id = this.data.ecsdObj.id;
			formData.createdBy = this.data.ecsdObj.createdBy;
		}
		return formData;
	}

	private patchFormValue(detail: EcsdObj) {
		this.ecsdForm.patchValue({
			securityStatus: detail.securityStatus,
			receivedFrom: detail.receivedFrom,
			whetherExemptedForScreening: detail.screeningMethod ? 'no' : 'yes',
			screeningMethod: detail.screeningMethod,
			groundsForExemption: detail.groundsForExemption,
			issuedBy: detail.issuedBy,
			employeeId: detail.employeeId,
			issuedOn: DateTime.fromFormat(detail.issuedOn, DATE_TIME_FORMAT),
			additionalSecurityInfo: detail.additionalSecurityInfo,
			regulatedEntityCategory: detail.regulatedEntityCategory,
			regulatedEntityIdentifier: detail.regulatedEntityIdentifier,
			regulatedEntityCategory1: detail.regulatedEntityCategory1,
			regulatedEntityIdentifier1: detail.regulatedEntityIdentifier1,
		});
	}

	private watchWhetherExemptedForScreening() {
		this.ecsdForm
			.get(EXEMPTED_SCREEN)
			?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((value) => {
				if (value) {
					this.updateValidator(value);
				}
			});

		this.updateValidator('no');
	}

	private updateValidator(isExempted: string) {
		const screenControl = this.ecsdForm.get(SCREEM_METHOD);
		const groundControl = this.ecsdForm.get(GROUND_EXEMPTION);

		if (isExempted === 'yes') {
			groundControl?.setValidators([Validators.required]);
			groundControl?.updateValueAndValidity();
			groundControl?.enable();
			screenControl?.clearValidators();
			screenControl?.updateValueAndValidity();
			screenControl?.disable();
			screenControl?.patchValue('');
		} else {
			screenControl?.setValidators([Validators.required]);
			screenControl?.updateValueAndValidity();
			screenControl?.enable();
			groundControl?.clearValidators();
			groundControl?.updateValueAndValidity();
			groundControl?.disable();
			groundControl?.patchValue('');
		}
	}
}
