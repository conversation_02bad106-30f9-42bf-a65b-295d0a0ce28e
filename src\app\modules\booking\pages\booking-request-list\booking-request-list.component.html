<div class="orll-booking-request-list iata-box">
	<div class="orll-booking-request-list__search_bar">
		<div class="row">
			<form [formGroup]="bookingRequestSearchForm" class="width-70">
				<div class="orll-booking-request-list__search-form col-12">
					<div class="col-3">
						<mat-form-field appearance="outline" class="width-100" floatLabel="always">
							<mat-label>{{ 'booking.mgmt.requestedProduct' | translate }}</mat-label>
							<input matInput formControlName="requestedProduct" />
						</mat-form-field>
					</div>

					<div class="col-3">
						<iata-autocomplete
							#departureAutocomplete
							[id]="'airport'"
							[api]="sliSearchRequestService"
							[label]="'sli.mgmt.departureLocation' | translate"
							[multiple]="true"
							(selected)="this.selectedDepartureLocations = $event">
						</iata-autocomplete>
					</div>

					<div class="col-3">
						<iata-autocomplete
							#arrivalAutocomplete
							[id]="'airport'"
							[api]="sliSearchRequestService"
							[label]="'sli.mgmt.arrivalLocation' | translate"
							[multiple]="true"
							(selected)="this.selectedArrivalLocations = $event">
						</iata-autocomplete>
					</div>

					<div class="col-3">
						<mat-form-field appearance="outline" class="width-100" floatLabel="always">
							<mat-label>{{ 'booking.mgmt.requestedStatus' | translate }}</mat-label>
							<mat-select formControlName="requestedStatus">
								@for (status of bookingRequestStatus; track status) {
									<mat-option [value]="status">{{ status }}</mat-option>
								}
							</mat-select>
						</mat-form-field>
					</div>
				</div>
			</form>
			<div class="buttons width-30">
				<button mat-stroked-button color="primary" (click)="onReset($event)" class="reset-button">
					<mat-icon>refresh</mat-icon>
					{{ 'sli.mgmt.reset' | translate }}
				</button>
				<button mat-flat-button color="primary" class="search-button" (click)="onSearch()">
					<mat-icon>search</mat-icon>
					{{ 'sli.mgmt.search' | translate }}
				</button>
				@if (hasSomeRole(forwarder) | async) {
					<button mat-flat-button color="primary" class="create-button" (click)="onCreate()">
						<mat-icon>add</mat-icon>
						{{ 'booking.mgmt.create' | translate }}
					</button>
				}
			</div>
		</div>
	</div>
	<orll-table [param]="bookingRequestSearchParam" [service]="bookingRequestService" [columns]="columns"></orll-table>
</div>
