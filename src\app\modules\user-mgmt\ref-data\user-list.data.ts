import { CodeName } from '@shared/models/code-name.model';
import { UserListObject } from '../models/user-list-object.model';

export const USER_LIST: UserListObject[] = [
	{
		userId: '2b7f9542-8e28-4d1f-b30b-ac0eac97db2d',
		firstName: 'John',
		lastName: 'Doe',
		email: '<EMAIL>',
		orgId: 'org123',
		orgName: 'org123',
		primaryOrgId: 'org123',
		primaryOrgName: 'org123',
		userType: 'user',
		secondaryOrgIds: [
			{
				orgId: 'org456',
				orgName: 'Org 456',
				userType: 'user',
			},
		],
	},
	{
		userId: '2b7f9542-8e28-4d1f-b30b-ac0eac97db1d',
		firstName: 'John',
		lastName: 'Will',
		email: '<EMAIL>',
		orgId: 'org123',
		orgName: 'org123',
		primaryOrgId: 'org123',
		primaryOrgName: 'org123',
		userType: 'admin',
		secondaryOrgIds: [
			{
				orgId: 'org456',
				orgName: 'Org 456',
				userType: 'user',
			},
		],
	},
];

export const USER_TYPES: CodeName[] = [
	{
		code: '1',
		name: 'Normal User',
	},
	{
		code: '3',
		name: 'Super User',
	},
];
