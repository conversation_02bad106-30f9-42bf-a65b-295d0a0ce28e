import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { MawbSearchRequestService } from './mawb-search-request.service';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { SearchType } from '@shared/models/search-type.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { MawbListObject } from '../models/mawb-list-object.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { MawbSearchPayload } from '../models/mawb-search-payload.model';
import { environment } from '@environments/environment';

const baseUrl = environment.baseApi;

describe('MawbSearchRequestService', () => {
	let service: MawbSearchRequestService;
	let httpTestingController: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		});
		service = TestBed.inject(MawbSearchRequestService);
		httpTestingController = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpTestingController.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should filter airline codes case-insensitively', fakeAsync(() => {
		const mockResponse = ['aa', 'DL', 'UA'];
		const keyword = 'a';

		service.getOptions(keyword, SearchType.AIRLINE_CODE).subscribe((result) => {
			expect(result).toEqual([
				{ code: 'aa', name: 'aa' },
				{ code: 'DL', name: 'DL' },
				{ code: 'UA', name: 'UA' },
			]);
		});

		const req = httpTestingController.expectOne((req) => req.url.includes('/mawb-management/get-keyword'));
		req.flush({ code: 200, data: mockResponse });
		tick();
	}));

	it('should return MAWB numbers for MAWB type', fakeAsync(() => {
		const mockResponse = ['12345678', '87654321'];
		const keyword = '123';

		service.getOptions(keyword, SearchType.MAWB).subscribe((result) => {
			expect(result).toEqual([
				{ code: '12345678', name: '12345678' },
				{ code: '87654321', name: '87654321' },
			]);
		});

		const req = httpTestingController.expectOne((req) => req.url.includes('/mawb-management/get-keyword'));
		req.flush({ code: 200, data: mockResponse });
		tick();
	}));

	it('should return statuses for LATEST_STATUS type', fakeAsync(() => {
		const mockResponse = ['DELIVERED', 'IN TRANSIT'];
		const keyword = 'del';

		service.getOptions(keyword, SearchType.LATEST_STATUS).subscribe((result) => {
			expect(result).toEqual([
				{ code: 'DELIVERED', name: 'DELIVERED' },
				{
					code: 'IN TRANSIT',
					name: 'IN TRANSIT',
				},
			]);
		});

		const req = httpTestingController.expectOne((req) => req.url.includes('/mawb-management/get-keyword'));
		req.flush({ code: 200, data: mockResponse });
		tick();
	}));

	it('should return empty array for unknown search type', fakeAsync(() => {
		service.getOptions('test', 'UNKNOWN_TYPE' as any).subscribe((result) => {
			expect(result).toEqual([]);
		});
		tick();
	}));

	it('should handle empty keyword by returning all items', fakeAsync(() => {
		const mockResponse = ['AA', 'DL'];

		service.getOptions('', SearchType.AIRLINE_CODE).subscribe((result) => {
			expect(result).toEqual([
				{ code: 'AA', name: 'AA' },
				{ code: 'DL', name: 'DL' },
			]);
		});

		const req = httpTestingController.expectOne((req) => req.url.includes('/mawb-management/get-keyword'));
		req.flush({ code: 200, data: mockResponse });
		tick();
	}));

	it('should combine pagination and search params for MAWB list', fakeAsync(() => {
		const mockResponse: PaginationResponse<MawbListObject> = {
			rows: [
				{
					mawbNumber: '12345678',
					airlineCode: '',
					goodsDescription: '',
					origin: '',
					destination: '',
					pieceQuantity: '',
					latestStatus: '',
					createDate: '',
					orgId: '',
					mawbId: '',
				} as MawbListObject,
			],
			total: 1,
			pageNum: 1,
		};

		const pagination: PaginationRequest = { pageNum: 1, pageSize: 20, orderByColumn: 'id,desc' };
		const payload: MawbSearchPayload = {
			airlineCodeList: ['AA'],
			mawbNumberList: ['12345678'],
			departureLocationList: ['JFK'],
			arrivalLocationList: ['LAX'],
		};

		service.getMawbList(pagination, payload).subscribe((response) => {
			expect(response).toEqual(mockResponse);
		});

		const req = httpTestingController.expectOne(
			(req) =>
				req.url === `${baseUrl}/mawb-management` &&
				req.params.get('pageNum') === '1' &&
				req.params.get('pageSize') === '20' &&
				req.params.get('orderByColumn') === 'id,desc' &&
				req.params.get('airlineCodeList') === 'AA' &&
				req.params.get('mawbNumberList') === '12345678' &&
				req.params.get('departureLocationList') === 'JFK' &&
				req.params.get('arrivalLocationList') === 'LAX'
		);

		expect(req.request.method).toBe('GET');
		req.flush({ code: 200, data: mockResponse });
		tick();
	}));

	it('should handle empty search payload for MAWB list', fakeAsync(() => {
		const mockResponse: PaginationResponse<MawbListObject> = {
			rows: [],
			total: 0,
			pageNum: 0,
		};

		const pagination: PaginationRequest = { pageNum: 1, pageSize: 10 };
		const payload = {} as MawbSearchPayload;

		service.getMawbList(pagination, payload).subscribe((response) => {
			expect(response).toEqual(mockResponse);
		});

		const req = httpTestingController.expectOne(
			(req) =>
				req.url === `${baseUrl}/mawb-management` &&
				req.params.keys().length === 2 && // Only page and size
				req.params.get('pageNum') === '1' &&
				req.params.get('pageSize') === '10'
		);

		req.flush({
			code: 200,
			data: mockResponse,
		});
		tick();
	}));

	it('should perform a keyword query', fakeAsync(() => {
		const keywordType = SearchType.ORIGIN;
		const keyword = 'LAX';
		const mockResponse: string[] = ['LAX'];

		service.keywordQuery(keyword, keywordType).subscribe((response) => {
			expect(response[0].code).toEqual(mockResponse[0]);
		});

		const req = httpTestingController.expectOne(
			(req) =>
				req.url === `${baseUrl}/mawb-management/get-keyword` &&
				req.params.keys().length === 2 &&
				req.params.get('keywordType') === keywordType &&
				req.params.get('keyword') === keyword
		);

		req.flush({
			code: 200,
			data: mockResponse,
		});
		tick();
	}));

	it('should getTotalPieceQuantity when MAWB id is provided', fakeAsync(() => {
		const mawbId = '12345678';
		const mockResponse: { totalQuantity: number; totalSlac: number } = { totalQuantity: 10, totalSlac: 20 };

		service.getTotalPieceQuantity(mawbId).subscribe((response) => {
			expect(response).toEqual(mockResponse);
		});

		const req = httpTestingController.expectOne(
			(req) =>
				req.url === `${baseUrl}/mawb-management/piece-summary` &&
				req.params.keys().length === 1 &&
				req.params.get('mawbId') === mawbId
		);

		req.flush({
			code: 200,
			data: mockResponse,
		});
		tick();
	}));
});
