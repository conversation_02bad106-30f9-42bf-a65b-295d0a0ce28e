export interface HawbSearchPayload {
	goodsDescription?: string;
	createDateStart?: string | null;
	createDateEnd?: string | null;
	shipperNameList?: string[];
	consigneeNameList?: string[];
	departureLocationList?: string[];
	arrivalLocationList?: string[];
	hawbNumberList?: string[];
	mawbNumberList?: string[];
	hawbIdList?: string[];
	mawbIdList?: string[];
	existMawb?: boolean;
	neone?: boolean;
}
