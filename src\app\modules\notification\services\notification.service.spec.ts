import { TestBed } from '@angular/core/testing';
import { NotificationService } from './notification.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { NotificationEventType, NotificationListObj } from '../models/notification.model';
import { LogisticObjType } from '@shared/models/share-type.model';
import { environment } from '@environments/environment';

const baseUrl = environment.baseApi;

describe('NotificationService', () => {
	let service: NotificationService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [NotificationService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(NotificationService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should getNotifiationPerPage and return NotificationListObj[]', () => {
		const param: PaginationRequest = { pageSize: 10, pageNum: 1 };
		const mockPagedResponse: PaginationResponse<NotificationListObj> = {
			total: 15,
			rows: [
				{
					id: '111',
					eventType: NotificationEventType.LOGISTICS_EVENT_RECEIVED,
					dataType: LogisticObjType.SLI,
					companyName: 'Company A',
					logisticsObject: 'http://10.10.11.10:8090/logistics-objects/47e85903-8f7d-457e-a498-d8b2418ef45b',
					hasRead: false,
					createTime: '2025-01-01 11:30',
					waybillNumber: '1111',
				},
				{
					id: '222',
					eventType: NotificationEventType.LOGISTICS_OBJECT_UPDATED,
					dataType: LogisticObjType.SLI,
					companyName: 'Company A',
					logisticsObject: 'http://10.10.11.10:8090/logistics-objects/47e85903-8f7d-457e-a498-d8b2418ef45b',
					hasRead: false,
					createTime: '2025-01-01 11:30',
					waybillNumber: '2222',
				},
			],
		};

		service.getNotificationPerPage(param, false).subscribe((data) => {
			expect(data).toEqual(mockPagedResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/notification?pageSize=10&pageNum=1&hasRead=true`);
		expect(req.request.method).toBe('GET');

		req.flush(mockPagedResponse);
	});
});
