import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiService } from '@shared/services/api.service';
import { Observable } from 'rxjs';
import { OrgInfo, PermissionPostObj, PermissionResponse, PostOrgInfo, Server } from '../models/mgmt-server.model';
import { Organization } from '@shared/models/organization.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';

@Injectable({
	providedIn: 'root',
})
export class ServerManagementService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getOrgInfo(orgId: string): Observable<OrgInfo> {
		return super.getData<OrgInfo>('org/org', {
			orgId,
		});
	}

	retrieveServerInformation(uri: string): Observable<Server> {
		return this.postData('org/org/retrieveServerInformation', uri);
	}

	saveOrg(param: PostOrgInfo): Observable<string> {
		if (param.id) {
			return this.postData('org/org', param);
		}
		return this.postData('org/org', param);
	}

	getOrgList(serverType: string): Observable<Organization[]> {
		return super.getData<Organization[]>('org/org/list', {
			serverType,
		});
	}

	deleteServer(id: string): Observable<boolean> {
		return super.deleteData('org/org/deleteServer', { id });
	}

	getPermissionList(serverType = 'menu'): Observable<PaginationResponse<PermissionResponse>> {
		return super.getData<PaginationResponse<PermissionResponse>>('permissions/list', {
			serverType,
		});
	}

	bindPermission(param: PermissionPostObj): Observable<string> {
		return super.postData('permissions/relation/batch-bind', {
			param,
		});
	}

	getPermissionByOrgId(pageParams: PaginationRequest, permissionId:string,orgId: string, userType = '1'): Observable<any> {
		return super.postData<any>('permissions/relation/list-org-permission', {
			...pageParams,
			permissionId,
			orgId,
			userType,
		});
	}
}
