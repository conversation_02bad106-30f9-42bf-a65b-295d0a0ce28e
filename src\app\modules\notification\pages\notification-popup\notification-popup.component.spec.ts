import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NotificationPopupComponent } from './notification-popup.component';
import { Router } from '@angular/router';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';
import { TranslateModule } from '@ngx-translate/core';
import { NotificationService } from '../../services/notification.service';
import { of } from 'rxjs';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { LogisticObjType } from '@shared/models/share-type.model';
import { NotificationListObj, NotificationEventType } from '../../models/notification.model';
import { FormsModule } from '@angular/forms';

const mockPagedResponse: PaginationResponse<NotificationListObj> = {
	total: 15,
	rows: [
		{
			id: '111',
			eventType: NotificationEventType.LOGISTICS_EVENT_RECEIVED,
			dataType: LogisticObjType.SLI,
			companyName: 'Company A',
			logisticsObject: 'http://10.10.11.10:8090/logistics-objects/47e85903-8f7d-457e-a498-d8b2418ef45b',
			hasRead: false,
			createTime: '2025-01-01 11:30',
			waybillNumber: '1111',
		},
		{
			id: '222',
			eventType: NotificationEventType.LOGISTICS_OBJECT_UPDATED,
			dataType: LogisticObjType.SLI,
			companyName: 'Company A',
			logisticsObject: 'http://10.10.11.10:8090/logistics-objects/47e85903-8f7d-457e-a498-d8b2418ef45b',
			hasRead: false,
			createTime: '2025-01-01 11:30',
			waybillNumber: '2222',
		},
	],
};

describe('NotificationPopupComponent', () => {
	let component: NotificationPopupComponent;
	let fixture: ComponentFixture<NotificationPopupComponent>;
	let routerSpy: jasmine.SpyObj<Router>;
	let mockNotificationService: jasmine.SpyObj<NotificationService>;

	beforeEach(async () => {
		routerSpy = jasmine.createSpyObj('Router', ['navigate']);
		mockNotificationService = jasmine.createSpyObj('NotificationService', ['getNotificationPerPage']);
		mockNotificationService.getNotificationPerPage.and.returnValue(of(mockPagedResponse));

		await TestBed.configureTestingModule({
			imports: [NotificationPopupComponent, MatSlideToggleModule, BrowserAnimationsModule, TranslateModule.forRoot(), FormsModule],
			providers: [
				{
					provide: Router,
					useValue: routerSpy,
				},
				{ provide: NotificationService, useValue: mockNotificationService },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(NotificationPopupComponent);
		component = fixture.componentInstance;

		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should have refresh input with default value true', () => {
		expect(component.refreshNotification).toBe(true);
	});

	it('should allow refresh input to be set to false', () => {
		component.refreshNotification = false;
		fixture.detectChanges();
		expect(component.refreshNotification).toBe(false);
	});

	it('should emit closePannel event when expected (simulated)', () => {
		spyOn(component.showOpen, 'emit');
	});

	it('should emit false on showOpen when gotToDetail is called', () => {
		spyOn(component.showOpen, 'emit');
		component.goToNotification();
		expect(component.showOpen.emit).toHaveBeenCalledWith(false);
	});

	it('should call router.navigate with /notification when gotToDetail is called', () => {
		component.goToNotification();
		expect(routerSpy.navigate).toHaveBeenCalledWith(['/notification']);
	});

	it('should update showUnread property when onToggleChange is called', () => {
		const event = { checked: true } as MatSlideToggleChange;
		component.onToggleChange(event);
		expect(component.showUnread).toBe(true);

		const event2 = { checked: false } as MatSlideToggleChange;
		component.onToggleChange(event2);
		expect(component.showUnread).toBe(false);
	});

	it('should bind slide toggle state to showUnread property', () => {
		const slideToggleDebugEl = fixture.debugElement.query(By.css('mat-slide-toggle'));

		slideToggleDebugEl.triggerEventHandler('change', { checked: true });
		fixture.detectChanges();

		expect(component.showUnread).toBe(true);

		slideToggleDebugEl.triggerEventHandler('change', { checked: false });
		fixture.detectChanges();

		expect(component.showUnread).toBe(false);
	});

	it('should call gotToDetail when detail link/button is clicked', () => {
		spyOn(component, 'goToNotification');

		const detailElement = fixture.debugElement.query(By.css('.iata-notification-tip__container__more'));
		if (detailElement) {
			detailElement.triggerEventHandler('click', null);
			expect(component.goToNotification).toHaveBeenCalled();
		} else {
			fail('Detail trigger element not found in template');
		}
	});
});
