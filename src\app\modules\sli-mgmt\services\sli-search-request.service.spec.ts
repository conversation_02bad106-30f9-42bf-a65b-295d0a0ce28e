import { TestBed } from '@angular/core/testing';
import { SliSearchRequestService } from './sli-search-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { AIRPORTS } from '../ref-data/airports.data';
import { SLI_LIST } from '../ref-data/sli-list.data';
import { SliListObject } from '../models/sli-list-object.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { SliSearchPayload } from '../models/sli-search-payload.model';
import { HttpClient } from '@angular/common/http';
import { of, throwError } from 'rxjs';
import { environment } from '@environments/environment';

const baseUrl = environment.baseApi;

describe('SliSearchRequestService', () => {
	let service: SliSearchRequestService;
	let httpClientSpy: jasmine.SpyObj<HttpClient>;

	beforeEach(() => {
		httpClientSpy = jasmine.createSpyObj('HttpClient', ['get']);

		TestBed.configureTestingModule({
			providers: [SliSearchRequestService, { provide: HttpClient, useValue: httpClientSpy }],
		});

		service = TestBed.inject(SliSearchRequestService);
	});

	describe('getOptions', () => {
		it('should retrieve all airports when id is "airport"', (done: DoneFn) => {
			// Arrange
			const keyword = '';
			const id = 'airport';

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(AIRPORTS.length);
					expect(response[0].code).toBe(AIRPORTS[0].code);
					expect(response[0].name).toBe(AIRPORTS[0].name);
					done();
				},
				error: done.fail,
			});
		});

		it('should filter airports by keyword', (done: DoneFn) => {
			// Arrange
			const keyword = 'LAX';
			const id = 'airport';

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('LAX');
					expect(response[0].name).toBe('Los Angeles International Airport');
					done();
				},
				error: done.fail,
			});
		});

		it('should retrieve shipper organizations when id is "shipper"', (done: DoneFn) => {
			// Arrange
			const keyword = '';
			const id = 'shipper';

			const mockOrganizations = [
				{ name: 'Delta Air Lines', id: '1', orgType: 'SHP' },
				{ name: 'Lufthansa', id: '2', orgType: 'SHP' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('Delta Air Lines');
					expect(response[0].name).toBe('Delta Air Lines');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called correctly
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli/listSliOrgName`, {
				params: jasmine.any(Object),
			});
		});
	});

	describe('getSliList', () => {
		it('should retrieve all SLI list items', (done: DoneFn) => {
			// Arrange
			const sliSearchPayload: SliSearchPayload = {
				existHawb: false,
			};
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<SliListObject> = {
				rows: SLI_LIST,
				total: SLI_LIST.length,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getSliList(paginationRequest, sliSearchPayload).subscribe({
				next: (response: PaginationResponse<SliListObject>) => {
					// Assert
					expect(response.rows.length).toBe(SLI_LIST.length);
					expect(response.total).toBe(SLI_LIST.length);
					expect(response.rows[0].waybillNumber).toBe(SLI_LIST[0].waybillNumber);
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli`, { params: jasmine.any(Object) });
		});

		it('should apply search filters when provided', (done: DoneFn) => {
			// Arrange
			const sliSearchPayload: SliSearchPayload = {
				sliCodeList: ['S000920'],
				departureLocationList: ['LAX'],
				existHawb: false,
			};
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<SliListObject> = {
				rows: [SLI_LIST[0]],
				total: 1,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getSliList(paginationRequest, sliSearchPayload).subscribe({
				next: (response: PaginationResponse<SliListObject>) => {
					// Assert
					expect(response.rows.length).toBe(1);
					expect(response.rows[0].waybillNumber).toBe('S000920');
					expect(response.rows[0].departureLocation).toBe('LAX');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli`, { params: jasmine.any(Object) });
		});

		it('should handle error response for getSliList', (done: DoneFn) => {
			const sliSearchPayload: SliSearchPayload = { existHawb: false };
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const errorResponse = { status: 500, message: 'Server error' };

			httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

			service.getSliList(paginationRequest, sliSearchPayload).subscribe({
				next: () => done.fail('Expected error but got success response'),
				error: (error) => {
					expect(error).toBe(errorResponse);
					done();
				},
			});
		});
	});

	describe('getSharedSliList', () => {
		it('should retrieve shared SLI list successfully', (done: DoneFn) => {
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const sliSearchPayload: SliSearchPayload = { existHawb: false };
			const mockResponse: PaginationResponse<SliListObject> = {
				rows: SLI_LIST.slice(0, 2),
				total: 2,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getSharedSliList(paginationRequest, sliSearchPayload).subscribe({
				next: (response: PaginationResponse<SliListObject>) => {
					expect(response.rows.length).toBe(2);
					expect(response.total).toBe(2);
					expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli/share/list`, { params: jasmine.any(Object) });
					done();
				},
				error: done.fail,
			});
		});

		it('should handle error response for getSharedSliList', (done: DoneFn) => {
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const sliSearchPayload: SliSearchPayload = { existHawb: false };
			const errorResponse = { status: 403, message: 'Forbidden' };

			httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

			service.getSharedSliList(paginationRequest, sliSearchPayload).subscribe({
				next: () => done.fail('Expected error but got success response'),
				error: (error) => {
					expect(error).toBe(errorResponse);
					done();
				},
			});
		});
	});

	describe('Additional getOptions scenarios', () => {
		it('should handle error response for getOptions with shipper', (done: DoneFn) => {
			const keyword = '';
			const id = 'shipper';
			const errorResponse = { status: 400, message: 'Bad request' };

			httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

			service.getOptions(keyword, id).subscribe({
				next: () => done.fail('Expected error but got success response'),
				error: (error) => {
					expect(error).toBe(errorResponse);
					done();
				},
			});
		});

		it('should handle consignee options', (done: DoneFn) => {
			const keyword = '';
			const id = 'consignee';
			const mockOrganizations = [
				{ name: 'Consignee A', id: '1', orgType: 'CNE' },
				{ name: 'Consignee B', id: '2', orgType: 'CNE' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('Consignee A');
					expect(response[0].name).toBe('Consignee A');
					expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli/listSliOrgName`, {
						params: jasmine.any(Object),
					});
					done();
				},
				error: done.fail,
			});
		});

		it('should handle unsupported id types by returning empty array', (done: DoneFn) => {
			const keyword = 'test';
			const id = 'unsupported';

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					expect(response).toEqual([]);
					done();
				},
				error: done.fail,
			});
		});

		it('should handle sliCode options', (done: DoneFn) => {
			const keyword = '';
			const id = 'sliCode';
			const mockResponse = { sliCodeList: ['SLI001', 'SLI002'] };

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('SLI001');
					expect(response[0].name).toBe('SLI001');
					expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli/listQueryParam`, {
						params: jasmine.any(Object),
					});
					done();
				},
				error: done.fail,
			});
		});

		it('should handle hawbNumber options', (done: DoneFn) => {
			const keyword = '';
			const id = 'hawbNumber';
			const mockResponse = { hawbNumber: ['HAWB001', 'HAWB002'] };

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('HAWB001');
					expect(response[0].name).toBe('HAWB001');
					expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli/listQueryParam`, {
						params: jasmine.any(Object),
					});
					done();
				},
				error: done.fail,
			});
		});

		it('should handle empty response for organization options', (done: DoneFn) => {
			const keyword = '';
			const id = 'shipper';

			httpClientSpy.get.and.returnValue(of([]));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					done();
				},
				error: done.fail,
			});
		});

		it('should filter shipper organizations by keyword', (done: DoneFn) => {
			const keyword = 'Delta';
			const id = 'shipper';
			const mockOrganizations = [
				{ name: 'Delta Air Lines', id: '1', orgType: 'SHP' },
				{ name: 'Lufthansa', id: '2', orgType: 'SHP' },
				{ name: 'Delta Cargo', id: '3', orgType: 'SHP' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('Delta Air Lines');
					expect(response[1].code).toBe('Delta Cargo');
					done();
				},
				error: done.fail,
			});
		});

		it('should filter consignee organizations by keyword', (done: DoneFn) => {
			const keyword = 'cargo';
			const id = 'consignee';
			const mockOrganizations = [
				{ name: 'ABC Cargo', id: '1', orgType: 'CNE' },
				{ name: 'XYZ Logistics', id: '2', orgType: 'CNE' },
				{ name: 'Global Cargo', id: '3', orgType: 'CNE' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('ABC Cargo');
					expect(response[1].code).toBe('Global Cargo');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle orglist options', (done: DoneFn) => {
			const keyword = '';
			const id = 'orglist';
			const mockOrganizations = [
				{ name: 'Organization A', id: 'org-1', orgType: '' },
				{ name: 'Organization B', id: 'org-2', orgType: '' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('org-1');
					expect(response[0].name).toBe('Organization A');
					expect(response[1].code).toBe('org-2');
					expect(response[1].name).toBe('Organization B');
					expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/org/org/list`, {
						params: jasmine.any(Object),
					});
					done();
				},
				error: done.fail,
			});
		});

		it('should filter orglist by keyword', (done: DoneFn) => {
			const keyword = 'logistics';
			const id = 'orglist';
			const mockOrganizations = [
				{ name: 'ABC Logistics', id: 'org-1', orgType: '' },
				{ name: 'XYZ Cargo', id: 'org-2', orgType: '' },
				{ name: 'Global Logistics', id: 'org-3', orgType: '' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('org-1');
					expect(response[0].name).toBe('ABC Logistics');
					expect(response[1].code).toBe('org-3');
					expect(response[1].name).toBe('Global Logistics');
					done();
				},
				error: done.fail,
			});
		});

		it('should filter sliCode by keyword', (done: DoneFn) => {
			const keyword = 'SLI001';
			const id = 'sliCode';
			const mockResponse = { sliCodeList: ['SLI001', 'SLI002', 'SLI001-A'] };

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('SLI001');
					expect(response[1].code).toBe('SLI001-A');
					done();
				},
				error: done.fail,
			});
		});

		it('should filter hawbNumber by keyword', (done: DoneFn) => {
			const keyword = 'HAWB001';
			const id = 'hawbNumber';
			const mockResponse = { hawbNumber: ['HAWB001', 'HAWB002', 'HAWB001-B'] };

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('HAWB001');
					expect(response[1].code).toBe('HAWB001-B');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle null/undefined response for shipper', (done: DoneFn) => {
			const keyword = '';
			const id = 'shipper';

			httpClientSpy.get.and.returnValue(of(null));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					done();
				},
				error: done.fail,
			});
		});

		it('should handle null/undefined response for consignee', (done: DoneFn) => {
			const keyword = '';
			const id = 'consignee';

			httpClientSpy.get.and.returnValue(of(undefined));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					done();
				},
				error: done.fail,
			});
		});

		it('should handle null/undefined response for orglist', (done: DoneFn) => {
			const keyword = '';
			const id = 'orglist';

			httpClientSpy.get.and.returnValue(of(null));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					done();
				},
				error: done.fail,
			});
		});

		it('should handle empty sliCodeList in response', (done: DoneFn) => {
			const keyword = '';
			const id = 'sliCode';
			const mockResponse = { sliCodeList: [] };

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					done();
				},
				error: done.fail,
			});
		});

		it('should handle missing sliCodeList in response', (done: DoneFn) => {
			const keyword = '';
			const id = 'sliCode';
			const mockResponse = {};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					done();
				},
				error: done.fail,
			});
		});

		it('should handle empty hawbNumber in response', (done: DoneFn) => {
			const keyword = '';
			const id = 'hawbNumber';
			const mockResponse = { hawbNumber: [] };

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					done();
				},
				error: done.fail,
			});
		});

		it('should handle missing hawbNumber in response', (done: DoneFn) => {
			const keyword = '';
			const id = 'hawbNumber';
			const mockResponse = {};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					done();
				},
				error: done.fail,
			});
		});

		it('should handle case-insensitive filtering for airports', (done: DoneFn) => {
			const keyword = 'lax';
			const id = 'airport';

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('LAX');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle empty airports array', (done: DoneFn) => {
			const keyword = 'NONEXISTENT';
			const id = 'airport';

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					done();
				},
				error: done.fail,
			});
		});

		it('should handle error response for consignee', (done: DoneFn) => {
			const keyword = '';
			const id = 'consignee';
			const errorResponse = { status: 500, message: 'Server error' };

			httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

			service.getOptions(keyword, id).subscribe({
				next: () => done.fail('Expected error but got success response'),
				error: (error) => {
					expect(error).toBe(errorResponse);
					done();
				},
			});
		});

		it('should handle error response for orglist', (done: DoneFn) => {
			const keyword = '';
			const id = 'orglist';
			const errorResponse = { status: 404, message: 'Not found' };

			httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

			service.getOptions(keyword, id).subscribe({
				next: () => done.fail('Expected error but got success response'),
				error: (error) => {
					expect(error).toBe(errorResponse);
					done();
				},
			});
		});

		it('should handle error response for sliCode', (done: DoneFn) => {
			const keyword = '';
			const id = 'sliCode';
			const errorResponse = { status: 403, message: 'Forbidden' };

			httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

			service.getOptions(keyword, id).subscribe({
				next: () => done.fail('Expected error but got success response'),
				error: (error) => {
					expect(error).toBe(errorResponse);
					done();
				},
			});
		});

		it('should handle error response for hawbNumber', (done: DoneFn) => {
			const keyword = '';
			const id = 'hawbNumber';
			const errorResponse = { status: 401, message: 'Unauthorized' };

			httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

			service.getOptions(keyword, id).subscribe({
				next: () => done.fail('Expected error but got success response'),
				error: (error) => {
					expect(error).toBe(errorResponse);
					done();
				},
			});
		});

		it('should handle whitespace in keywords for shipper filtering', (done: DoneFn) => {
			const keyword = '  Delta  ';
			const id = 'shipper';
			const mockOrganizations = [
				{ name: 'Delta Air Lines', id: '1', orgType: 'SHP' },
				{ name: 'Lufthansa', id: '2', orgType: 'SHP' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('Delta Air Lines');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle whitespace in keywords for consignee filtering', (done: DoneFn) => {
			const keyword = '  cargo  ';
			const id = 'consignee';
			const mockOrganizations = [
				{ name: 'ABC Cargo', id: '1', orgType: 'CNE' },
				{ name: 'XYZ Logistics', id: '2', orgType: 'CNE' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('ABC Cargo');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle whitespace in keywords for orglist filtering', (done: DoneFn) => {
			const keyword = '  logistics  ';
			const id = 'orglist';
			const mockOrganizations = [
				{ name: 'ABC Logistics', id: 'org-1', orgType: '' },
				{ name: 'XYZ Cargo', id: 'org-2', orgType: '' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('org-1');
					expect(response[0].name).toBe('ABC Logistics');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle whitespace in keywords for airport filtering', (done: DoneFn) => {
			const keyword = '  lax  ';
			const id = 'airport';

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('LAX');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle whitespace in keywords for sliCode filtering', (done: DoneFn) => {
			const keyword = '  SLI001  ';
			const id = 'sliCode';
			const mockResponse = { sliCodeList: ['SLI001', 'SLI002'] };

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('SLI001');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle whitespace in keywords for hawbNumber filtering', (done: DoneFn) => {
			const keyword = '  HAWB001  ';
			const id = 'hawbNumber';
			const mockResponse = { hawbNumber: ['HAWB001', 'HAWB002'] };

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('HAWB001');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle mixed case keywords for shipper filtering', (done: DoneFn) => {
			const keyword = 'DeLtA';
			const id = 'shipper';
			const mockOrganizations = [
				{ name: 'Delta Air Lines', id: '1', orgType: 'SHP' },
				{ name: 'Lufthansa', id: '2', orgType: 'SHP' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('Delta Air Lines');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle mixed case keywords for airport filtering', (done: DoneFn) => {
			const keyword = 'LaX';
			const id = 'airport';

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('LAX');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle empty keyword for shipper options', (done: DoneFn) => {
			const keyword = '';
			const id = 'shipper';
			const mockData = [{ name: 'Test Shipper', id: '1', orgType: 'SHP' }];

			httpClientSpy.get.and.returnValue(of(mockData));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('Test Shipper');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle empty keyword for consignee options', (done: DoneFn) => {
			const keyword = '';
			const id = 'consignee';
			const mockData = [{ name: 'Test Consignee', id: '1', orgType: 'CNE' }];

			httpClientSpy.get.and.returnValue(of(mockData));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('Test Consignee');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle empty keyword for orglist options', (done: DoneFn) => {
			const keyword = '';
			const id = 'orglist';
			const mockData = [{ name: 'Test Org', id: 'org-1', orgType: '' }];

			httpClientSpy.get.and.returnValue(of(mockData));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('org-1');
					expect(response[0].name).toBe('Test Org');
					done();
				},
				error: done.fail,
			});
		});

		it('should handle empty keyword for airport options', (done: DoneFn) => {
			const keyword = '';
			const id = 'airport';

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBeGreaterThan(0);
					done();
				},
				error: done.fail,
			});
		});

		it('should handle getOptions with no id parameter', (done: DoneFn) => {
			const keyword = 'test';

			service.getOptions(keyword).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					expect(response).toEqual([]);
					done();
				},
				error: done.fail,
			});
		});
	});
});
