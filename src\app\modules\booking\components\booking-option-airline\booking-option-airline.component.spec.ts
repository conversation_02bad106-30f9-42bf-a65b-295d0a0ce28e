import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BookingOptionAirlineComponent } from './booking-option-airline.component';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { of, throwError } from 'rxjs';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Organization } from '@shared/models/organization.model';
import { UserProfileService } from '@shared/services/user-profile.service';
import { BookingOptionRequestListObj } from '../../models/booking.model';
import { TranslateModule } from '@ngx-translate/core';

const orgs: Organization[] = [
	{
		id: '111',
		name: '111',
		orgType: '11',
	},
	{
		id: '222',
		name: '222',
		orgType: '222',
	},
];

const mockListObj: BookingOptionRequestListObj = {
	id: '222',
	bookingOptionRequestId: '',
	expectedCommodity: '',
	departureLocation: '',
	arrivalLocation: '',
	latestStatus: '',
};

describe('BookingOptionAirlineComponent', () => {
	let component: BookingOptionAirlineComponent;
	let fixture: ComponentFixture<BookingOptionAirlineComponent>;
	let mockService: jasmine.SpyObj<BookingOptionRequestService>;
	let mockOrgService: jasmine.SpyObj<OrgMgmtRequestService>;
	let dialogRef: jasmine.SpyObj<MatDialogRef<BookingOptionAirlineComponent>>;
	let dialog: jasmine.SpyObj<MatDialog>;
	let mockProfileService: jasmine.SpyObj<UserProfileService>;

	beforeEach(async () => {
		mockService = jasmine.createSpyObj('BookingOptionRequestService', ['shareOption']);
		mockService.shareOption.and.returnValue(of(true));
		mockOrgService = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList']);
		mockOrgService.getOrgList.and.returnValue(of(orgs));
		dialogRef = jasmine.createSpyObj<MatDialogRef<BookingOptionAirlineComponent>>('MatDialogRef', ['close']);

		dialog = jasmine.createSpyObj('MatDialog', ['open']);

		mockProfileService = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		mockProfileService.hasPermission.and.returnValue(of(true));
		mockProfileService.hasSomeRole.and.returnValue(of(true));

		await TestBed.configureTestingModule({
			imports: [BookingOptionAirlineComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: OrgMgmtRequestService, useValue: mockOrgService },
				{ provide: BookingOptionRequestService, useValue: mockService },
				{ provide: MatDialog, useValue: dialog },
				{ provide: MatDialogRef, useValue: dialogRef },
				{ provide: UserProfileService, useValue: mockProfileService },
				{ provide: MAT_DIALOG_DATA, useValue: mockListObj },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(BookingOptionAirlineComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('ngOnInit should load airlines from orgService', () => {
		// ngOnInit ran on fixture.detectChanges(); airlines should be populated
		expect(component.airlines).toEqual(orgs);
	});

	it('selectAirline should set involvedParties', () => {
		component.selectAirline({ value: 'some-party' } as any);
		expect(component.involvedParties).toBe('some-party');
	});

	describe('sendRequest', () => {
		it('should set dataLoading to true and call shareOption', () => {
			const mockData: { id: string; fromBooking: boolean } = {
				id: '',
				fromBooking: false,
			};
			mockService.shareOption.and.returnValue(of(true));
			component.data = mockData;
			component.involvedParties = 'test';
			component.sendRequest();

			expect(component.dataLoading).toBe(false);
			expect(mockService.shareOption).toHaveBeenCalledWith(mockData.id, 'test', mockData.fromBooking);
		});

		it('should set dataLoading to false and close dialog on success', () => {
			mockService.shareOption.and.returnValue(of(true));

			component.sendRequest();

			expect(component.dataLoading).toBe(false);
			expect(dialogRef.close).toHaveBeenCalled();
		});

		it('should set dataLoading to false and close dialog on error', () => {
			mockService.shareOption.and.returnValue(throwError(() => new Error('fail')));

			component.sendRequest();

			expect(component.dataLoading).toBe(false);
			expect(dialogRef.close).toHaveBeenCalled();
		});
	});
});
