import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ShareDialogComponent } from './share-dialog.component';
import { MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSortModule } from '@angular/material/sort';
import { AutocompleteComponent } from '../autocomplete/autocomplete.component';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { By } from '@angular/platform-browser';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { Organization } from '@shared/models/organization.model';
import { UserProfileService } from '@shared/services/user-profile.service';
import { noop, of, throwError } from 'rxjs';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { DebugElement } from '@angular/core';
import { ShareType } from '@shared/models/share-type.model';
import { ShareDataService } from '@shared/services/share/share-data.service';

describe('ShareDialogComponent', () => {
	let component: ShareDialogComponent;
	let fixture: ComponentFixture<ShareDialogComponent>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;
	const mockDialogRef = jasmine.createSpyObj(['close']);
	const mockDialogData = {
		title: 'Share SLI',
		shareType: ShareType.SLI,
		param: '1',
	};
	let inputEl: DebugElement;

	// Mock data
	const mockRecords: Organization[] = [
		{
			id: '1',
			name: 'AAA',
			orgType: 'APT',
		},
		{
			id: '2',
			name: 'BBB',
			orgType: 'AIR',
		},
		{
			id: '3',
			name: 'CCC',
			orgType: 'AIR',
		},
	];
	const mockFilterData: Organization[] = [
		{
			id: '2',
			name: 'BBB',
			orgType: 'AIR',
		},
		{
			id: '3',
			name: 'CCC',
			orgType: 'AIR',
		},
	];
	const mockOrgTypes: string[] = ['APT', 'AIR'];
	const mockOrgMgtService = {
		getOrgList: jasmine.createSpy('getOrgList').and.returnValue(of(mockRecords)),
		getOptions: jasmine.createSpy('getOptions').and.returnValue(of(mockOrgTypes)),
	};

	mockOrgMgtService.getOrgList.and.callFake((arg?: string) => {
		if (arg) {
			if (arg === 'error') {
				return throwError(() => new Error('backend error'));
			}
			return of(mockFilterData);
		} else {
			return of(mockRecords);
		}
	});

	const mockShareDataService = {
		shareData: jasmine.createSpy('shareData').and.returnValue(of('')),
	};

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [
				ShareDialogComponent,
				AutocompleteComponent,
				MatIconModule,
				ReactiveFormsModule,
				MatTableModule,
				MatSortModule,
				MatDialogModule,
				MatCheckboxModule,
				MatButtonModule,
				TranslateModule.forRoot(),
			],
			providers: [
				{
					provide: UserProfileService,
					useValue: userProfileServiceSpy,
				},
				{
					provide: OrgMgmtRequestService,
					useValue: mockOrgMgtService,
				},
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: mockDialogData },
				{ provide: ShareDataService, useValue: mockShareDataService },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();
	});

	beforeEach(() => {
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		// Reset all spies before each test
		mockOrgMgtService.getOrgList.calls.reset();
		mockShareDataService.shareData.calls.reset();
		mockDialogRef.close.calls.reset();

		// Reset mock to return original data
		mockOrgMgtService.getOrgList.and.callFake((arg?: string) => {
			if (arg) {
				if (arg === 'error') {
					return throwError(() => new Error('backend error'));
				}
				return of(mockFilterData);
			} else {
				return of(mockRecords);
			}
		});

		fixture = TestBed.createComponent(ShareDialogComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
		inputEl = fixture.debugElement.query(By.css('.mat-mdc-input-element'));
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize the sharedOrgSearchForm', () => {
		expect(component.sharedOrgSearchForm).toBeDefined();
	});

	it('onReset() should reset all the form fields and selected items', () => {
		component.selectedOrgType = 'AIR';
		component.onReset({ preventDefault: noop, stopPropagation: noop } as any);
		expect(component.selectedOrgType).toBeNull();
	});

	it('should render table with data', () => {
		const compiled = fixture.nativeElement as HTMLElement;
		const rows = compiled.querySelectorAll('tbody tr');
		expect(rows.length).toBe(3);
	});

	it('should show 2 options when input is focused and panel opened', fakeAsync(() => {
		inputEl.nativeElement.click();
		fixture.detectChanges();
		tick(200);
		const options = document.querySelectorAll('.mat-mdc-option');
		expect(options.length).toBe(2);
	}));

	it('should allow sorting by orgType column', () => {
		const headerCells = fixture.debugElement.queryAll(By.css('.mat-sort-header'));
		const typeHeader = headerCells.find((h) => h.nativeElement.textContent.includes('orgType'));

		typeHeader?.triggerEventHandler('click', null);
		fixture.detectChanges();

		const firstRow = fixture.nativeElement.querySelector('tbody tr td:nth-child(3)');
		expect(firstRow.textContent).toContain('AIR');
	});

	it('should select a row when checkbox is clicked', () => {
		const checkboxes = fixture.debugElement.queryAll(By.css('mat-checkbox'));
		checkboxes[1].triggerEventHandler('change', { checked: true });
		fixture.detectChanges();

		expect(component.selection.selected.length).toBe(1);
		expect(component.selection.isSelected('1')).toBeTrue();
	});

	it('should toggle all rows when master checkbox is clicked', () => {
		const masterCheckbox = fixture.debugElement.query(By.css('.mat-mdc-header-row .mat-mdc-checkbox'));
		masterCheckbox.triggerEventHandler('change', { checked: true });
		fixture.detectChanges();

		expect(component.selection.selected.length).toBe(3);

		masterCheckbox.triggerEventHandler('change', { checked: false });
		fixture.detectChanges();

		expect(component.selection.selected.length).toBe(0);
	});

	it('onSearch() should filter data when search is called', () => {
		component.selectedOrgType = 'AIR';
		component.onSearch();
		fixture.detectChanges();

		expect(component.dataSource.data).toEqual(mockFilterData);
		expect(component.dataSource.data.length).toBe(2);
		expect(component.dataSource.data[0].orgType).toBe('AIR');
	});

	it('onSortChange() should sort data', () => {
		component.onSortChange({ active: 'orgType', direction: 'asc' });
		fixture.detectChanges();

		// Check that the data is sorted correctly
		expect(component.dataSource.data[0].orgType).toBe('AIR');
		expect(component.currentSort).toEqual({ active: 'orgType', direction: 'asc' });
	});

	it('should stop loading when getData fails', () => {
		component.selectedOrgType = 'error';

		component.ngOnInit();
		fixture.detectChanges();
		expect(component.dataLoading).toBe(false);
	});

	it('trackById', () => {
		const org: Organization = {
			id: 'org',
			name: 'Test Org',
			orgType: '',
		};
		const result = component.trackByOrgId(0, org);
		expect(result).toBe('org');
	});

	describe('#isAllSelected', () => {
		it('should return true when there is no data (0 selected == 0 rows)', () => {
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return false when there are data but nothing is selected', () => {
			component.dataSource.data = mockRecords;
			component.selection.deselect('1', '2', '3');
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return true when all rows are selected', () => {
			component.dataSource.data = mockRecords;
			component.selection.select('1', '2', '3');
			expect(component.isAllSelected()).toBe(true);
		});

		it('should return false when only some rows are selected', () => {
			component.dataSource.data = mockRecords;
			component.selection.select('1');
			expect(component.isAllSelected()).toBe(false);
		});
	});

	describe('selectedItems', () => {
		it('should set selectedOrgType when called with valid string', () => {
			const testOrgType = 'AIR';

			component.selectedItems(testOrgType);

			expect(component.selectedOrgType).toBe(testOrgType);
		});

		it('should set selectedOrgType when called with empty string', () => {
			component.selectedOrgType = 'AIR'; // Set initial value

			component.selectedItems('');

			expect(component.selectedOrgType).toBe('');
		});

		it('should handle null value', () => {
			component.selectedItems(null as any);

			expect(component.selectedOrgType).toBeNull();
		});

		it('should handle undefined value', () => {
			component.selectedItems(undefined as any);

			expect(component.selectedOrgType).toBeUndefined();
		});

		it('should overwrite previous selectedOrgType value', () => {
			component.selectedOrgType = 'APT';

			component.selectedItems('AIR');

			expect(component.selectedOrgType).toBe('AIR');
		});

		it('should handle special characters in org type', () => {
			const specialOrgType = 'ORG@#$%';

			component.selectedItems(specialOrgType);

			expect(component.selectedOrgType).toBe(specialOrgType);
		});
	});

	describe('onShare', () => {
		beforeEach(() => {
			// Reset spies before each test
			mockShareDataService.shareData.calls.reset();
			mockDialogRef.close.calls.reset();

			component.dataSource.data = mockRecords;
			component.selection.clear();
			component.selection.select('1', '2');
		});

		it('should call shareData service with correct parameters', () => {
			component.onShare();

			expect(mockShareDataService.shareData).toHaveBeenCalledWith(ShareType.SLI, '1', ['1', '2']);
		});

		it('should set dataLoading to true when sharing starts', () => {
			component.dataLoading = false;

			// Check dataLoading is set to true during the call
			let dataLoadingDuringCall = false;
			mockShareDataService.shareData.and.callFake(() => {
				dataLoadingDuringCall = component.dataLoading;
				return of('');
			});

			component.onShare();

			expect(dataLoadingDuringCall).toBe(true);
		});

		it('should close dialog on successful share', () => {
			component.onShare();

			expect(mockDialogRef.close).toHaveBeenCalled();
		});

		it('should handle share with no selected items', () => {
			component.selection.clear();

			component.onShare();

			expect(mockShareDataService.shareData).toHaveBeenCalledWith(ShareType.SLI, '1', []);
		});

		it('should handle share with all items selected', () => {
			component.selection.clear();
			component.selection.select('1', '2', '3');

			component.onShare();

			expect(mockShareDataService.shareData).toHaveBeenCalledWith(ShareType.SLI, '1', ['1', '2', '3']);
		});

		it('should handle share service error', () => {
			mockShareDataService.shareData.and.returnValue(throwError(() => new Error('Share failed')));

			component.onShare();

			expect(component.dataLoading).toBe(false);
			expect(mockDialogRef.close).toHaveBeenCalled();
		});

		it('should handle different share types', () => {
			// Reset to original mock data
			component.data = {
				title: 'Share MAWB',
				shareType: ShareType.MAWB,
				param: 'mawb-123',
			};

			component.onShare();

			expect(mockShareDataService.shareData).toHaveBeenCalledWith(ShareType.MAWB, 'mawb-123', ['1', '2']);
		});
	});

	describe('toggleAllRows', () => {
		beforeEach(() => {
			component.dataSource.data = mockRecords;
		});

		it('should select all rows when none are selected', () => {
			component.selection.clear();

			component.toggleAllRows();

			expect(component.selection.selected).toEqual(['1', '2', '3']);
		});

		it('should clear selection when all rows are selected', () => {
			component.selection.select('1', '2', '3');

			component.toggleAllRows();

			expect(component.selection.selected).toEqual([]);
		});

		it('should select all rows when some are selected', () => {
			component.selection.select('1');

			component.toggleAllRows();

			expect(component.selection.selected).toEqual(['1', '2', '3']);
		});

		it('should handle empty data source', () => {
			component.dataSource.data = [];
			component.selection.clear();

			component.toggleAllRows();

			expect(component.selection.selected).toEqual([]);
		});
	});

	describe('onReset Edge Cases', () => {
		it('should handle onReset with autocompleteList undefined', () => {
			component.autocompleteList = undefined as any;
			component.selectedOrgType = 'AIR';
			const mockEvent = { preventDefault: jasmine.createSpy(), stopPropagation: jasmine.createSpy() };

			expect(() => component.onReset(mockEvent)).toThrow();
			expect(mockEvent.preventDefault).toHaveBeenCalled();
			expect(mockEvent.stopPropagation).toHaveBeenCalled();
		});

		it('should reset form and selectedOrgType correctly', () => {
			component.sharedOrgSearchForm.patchValue({ orgType: 'AIR' });
			component.selectedOrgType = 'AIR';
			const mockEvent = { preventDefault: jasmine.createSpy(), stopPropagation: jasmine.createSpy() };

			component.onReset(mockEvent);

			expect(component.sharedOrgSearchForm.get('orgType')?.value).toBeNull();
			expect(component.selectedOrgType).toBeNull();
		});
	});

	describe('onSearch Edge Cases', () => {
		it('should handle search with empty selectedOrgType', () => {
			component.selectedOrgType = '';
			component.dataLoading = true;

			component.onSearch();

			expect(mockOrgMgtService.getOrgList).toHaveBeenCalledWith('');
			expect(component.dataLoading).toBe(false);
		});

		it('should handle search error and set dataLoading to false', () => {
			component.selectedOrgType = 'error';
			component.dataLoading = true;

			component.onSearch();

			expect(component.dataLoading).toBe(false);
		});

		it('should update dataSource on successful search', () => {
			component.selectedOrgType = 'AIR';
			const initialData = component.dataSource.data;

			component.onSearch();

			expect(component.dataSource.data).toEqual(mockFilterData);
			expect(component.dataSource.data).not.toBe(initialData);
		});

		it('should call markForCheck after successful search', () => {
			spyOn(component['cdr'], 'markForCheck');
			component.selectedOrgType = 'AIR';

			component.onSearch();

			expect(component['cdr'].markForCheck).toHaveBeenCalled();
		});
	});

	describe('initData Edge Cases', () => {
		it('should set dataLoading to true initially', () => {
			component.dataLoading = false;

			// Check dataLoading is set to true during the call
			let dataLoadingDuringCall = false;
			mockOrgMgtService.getOrgList.and.callFake(() => {
				dataLoadingDuringCall = component.dataLoading;
				return of(mockRecords);
			});

			component['initData']();

			expect(dataLoadingDuringCall).toBe(true);
		});

		it('should handle initData error and set dataLoading to false', () => {
			mockOrgMgtService.getOrgList.and.returnValue(throwError(() => new Error('Init error')));
			component.dataLoading = true;

			component['initData']();

			expect(component.dataLoading).toBe(false);
		});

		it('should call markForCheck after successful init', () => {
			spyOn(component['cdr'], 'markForCheck');
			mockOrgMgtService.getOrgList.and.returnValue(of(mockRecords));

			component['initData']();

			expect(component['cdr'].markForCheck).toHaveBeenCalled();
		});
	});

	describe('onSortChange Edge Cases', () => {
		beforeEach(() => {
			component.dataSource.data = mockRecords;
		});

		it('should handle sort by orgType in descending order', () => {
			component.onSortChange({ active: 'orgType', direction: 'desc' });

			expect(component.currentSort).toEqual({ active: 'orgType', direction: 'desc' });
			// First item should be APT (comes after AIR in desc order)
			expect(component.dataSource.data[0].orgType).toBe('APT');
		});

		it('should handle sort by unsupported column', () => {
			const originalData = [...component.dataSource.data];

			component.onSortChange({ active: 'unsupported', direction: 'asc' });

			expect(component.currentSort).toEqual({ active: 'unsupported', direction: 'asc' });
			// Data should remain unchanged for unsupported columns
			expect(component.dataSource.data).toEqual(originalData);
		});

		it('should handle empty direction', () => {
			component.onSortChange({ active: 'orgType', direction: '' });

			expect(component.currentSort).toEqual({ active: 'orgType', direction: '' });
		});

		it('should handle sort with empty data', () => {
			component.dataSource.data = [];

			component.onSortChange({ active: 'orgType', direction: 'asc' });

			expect(component.dataSource.data).toEqual([]);
		});
	});

	describe('compare method', () => {
		it('should return negative value when a < b and isAsc is true', () => {
			const result = component['compare']('AIR', 'APT', true);
			expect(result).toBeLessThan(0);
		});

		it('should return positive value when a > b and isAsc is true', () => {
			const result = component['compare']('APT', 'AIR', true);
			expect(result).toBeGreaterThan(0);
		});

		it('should return positive value when a < b and isAsc is false', () => {
			const result = component['compare']('AIR', 'APT', false);
			expect(result).toBeGreaterThan(0);
		});

		it('should return negative value when a > b and isAsc is false', () => {
			const result = component['compare']('APT', 'AIR', false);
			expect(result).toBeLessThan(0);
		});

		it('should handle equal values', () => {
			const result = component['compare']('AIR', 'AIR', true);
			expect(result).toBe(1);
		});

		it('should handle null and undefined values correctly', () => {
			const result1 = component['compare'](null, 'AIR', true);
			const result2 = component['compare']('AIR', null, true);
			const result3 = component['compare'](undefined, 'AIR', true);

			expect(result1).toBe(1);
			expect(result2).toBe(1);
			expect(result3).toBe(1);
		});
	});

	describe('Component Integration', () => {
		beforeEach(() => {
			// Reset all spies before integration tests
			mockShareDataService.shareData.calls.reset();
			mockDialogRef.close.calls.reset();
			mockOrgMgtService.getOrgList.calls.reset();

			// Reset mock to return original data
			mockOrgMgtService.getOrgList.and.callFake((arg?: string) => {
				if (arg) {
					if (arg === 'error') {
						return throwError(() => new Error('backend error'));
					}
					return of(mockFilterData);
				} else {
					return of(mockRecords);
				}
			});
		});

		it('should handle complete workflow from init to share', () => {
			// Initialize component
			component.ngOnInit();
			expect(component.dataSource.data).toEqual(mockRecords);

			// Select org type
			component.selectedItems('AIR');
			expect(component.selectedOrgType).toBe('AIR');

			// Search with filter
			component.onSearch();
			expect(component.dataSource.data).toEqual(mockFilterData);

			// Select items
			component.selection.clear();
			component.selection.select('2', '3');
			expect(component.selection.selected).toEqual(['2', '3']);

			// Share data
			component.onShare();
			expect(mockShareDataService.shareData).toHaveBeenCalledWith(ShareType.SLI, '1', ['2', '3']);
			expect(mockDialogRef.close).toHaveBeenCalled();
		});

		it('should handle error recovery workflow', () => {
			// Start with error in init
			mockOrgMgtService.getOrgList.and.returnValue(throwError(() => new Error('Init failed')));
			component.ngOnInit();
			expect(component.dataLoading).toBe(false);

			// Recover with successful search
			mockOrgMgtService.getOrgList.and.returnValue(of(mockRecords));
			component.selectedItems('AIR');
			component.onSearch();
			expect(component.dataSource.data).toEqual(mockRecords);
		});
	});
});
