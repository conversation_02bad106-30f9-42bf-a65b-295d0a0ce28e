import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { ShareType } from '@shared/models/share-type.model';
import { RequestStatus } from '@shared/models/biz-logic/version-history.model';
import { MatDialog } from '@angular/material/dialog';
import { ChangeDetectorRef } from '@angular/core';
import { VersionHistoryService } from '@shared/services/biz-logic/verion-history/version-history.service';
import { TranslateService } from '@ngx-translate/core';
import { MatPaginatorIntl } from '@angular/material/paginator';
// eslint-disable-next-line @typescript-eslint/naming-convention
import ChangeRequestListComponent from './change-request-list.component';

describe('ChangeRequestListComponent', () => {
	let component: ChangeRequestListComponent;
	let fixture: ComponentFixture<ChangeRequestListComponent>;

	beforeEach(async () => {
		const mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		const mockTranslate = jasmine.createSpyObj('TranslateService', ['instant', 'get']);
		const mockCdr = jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']);

		mockTranslate.instant.and.returnValue('Change Request');
		mockTranslate.get.and.returnValue(of('Change Request'));
		mockTranslate.stream = () => of('Change Request');
		// provide onLangChange observable so services that subscribe won't error
		(mockTranslate as any).onLangChange = of(null);
		mockDialog.open.and.returnValue({ afterClosed: () => of(true) } as any);

		// avoid rendering the component template (and nested pipes/components) in tests
		TestBed.overrideComponent(ChangeRequestListComponent, { set: { template: '' } });

		await TestBed.configureTestingModule({
			imports: [ChangeRequestListComponent],
			providers: [
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: TranslateService, useValue: mockTranslate },
				{ provide: ChangeDetectorRef, useValue: mockCdr },
				{ provide: VersionHistoryService, useValue: jasmine.createSpyObj('VersionHistoryService', ['getDataPerPage']) },
				// stub MatPaginatorIntl to avoid app-level CustomPaginatorIntl construction
				{ provide: MatPaginatorIntl, useValue: { changes: of(), getRangeLabel: () => '' } },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(ChangeRequestListComponent);
		component = fixture.componentInstance;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('ngOnChanges should set param when loId or type changes', () => {
		component.loId = 'LO-1';
		component.type = 'TYPE-A';
		component.ngOnChanges({
			loId: { currentValue: 'LO-1', firstChange: true, previousValue: undefined, isFirstChange: () => true } as any,
		});
		expect(component.param).toEqual({ loId: 'LO-1', type: 'TYPE-A', shareType: ShareType.CHANGE_REQUEST });
	});

	it('transformStatus should map value using RequestStatus enum', () => {
		const key = Object.keys(RequestStatus)[0] as keyof typeof RequestStatus;
		const result = component.transformStatus(key);
		expect(result).toBe(RequestStatus[key]);
	});

	it('refreshList should set param based on inputs', () => {
		component.loId = 'LO-2';
		component.type = 'TYPE-B';
		component.param = null;
		component.refreshList();
		expect(component.param).toEqual(jasmine.objectContaining({ loId: 'LO-2', type: 'TYPE-B' }));
	});

	it('openVersionHistory should open dialog and refresh param on afterClosed true', () => {
		const mockRow: any = { actionRequestUri: '/v1', id: 'ver-1' };
		const dialogSpy: any = TestBed.inject(MatDialog);

		component.param = { loId: 'L', type: 'T', shareType: ShareType.CHANGE_REQUEST };
		component.openVersionHistory(mockRow);

		expect(dialogSpy.open).toHaveBeenCalledWith(
			jasmine.any(Function),
			jasmine.objectContaining({
				width: '80vw',
				autoFocus: false,
				data: jasmine.objectContaining({ id: 'ver-1', actionRequestUri: '/v1' }),
			})
		);
		expect(component.param).toEqual({ loId: 'L', type: 'T', shareType: ShareType.CHANGE_REQUEST });
	});
});
