import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiService } from '@shared/services/api.service';
import { Observable } from 'rxjs';

@Injectable({
	providedIn: 'root',
})
export class RetrieveService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getObjDetail(param: { loId: string; type: string }): Observable<boolean> {
		return this.postData('mawb-management/retrieve-lo', param);
	}
}
