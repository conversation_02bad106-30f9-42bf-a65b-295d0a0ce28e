import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { ShipmentParty } from '../../../../sli-mgmt/models/shipment-party.model';
import { OrllCopyDirective } from '@shared/directive/orll-copy.directive';

@Component({
	selector: 'orll-shipper-or-consignee-info',
	imports: [CommonModule, MatIconModule, MatButtonModule, TranslateModule, MatExpansionModule, OrllCopyDirective],
	templateUrl: './shipper-or-consignee-info.component.html',
	styleUrl: './shipper-or-consignee-info.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ShipperOrConsigneeInfoComponent {
	@Input() title = '';

	@Input() shipmentParty: ShipmentParty = {} as ShipmentParty;
}
