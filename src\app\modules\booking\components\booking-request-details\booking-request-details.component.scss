.orll-booking-request-details {
	.iata-box {
		padding: 30px !important;
		font-size: 14px;
		color: var(--iata-grey-400);
	}
	.row {
		padding: 10px 0px;

		.sub-title {
			display: inline-flex;
			gap: 10px;
			margin-bottom: 20px;
			font-size: 20px;
			font-weight: 600;
			color: var(--iata-grey-600);
		}
	}

	&__clear_dialog {
		float: right;
		margin: 20px 20px 0px 0px;
		cursor: pointer;
	}

	.mat-dialog-actions {
		display: flex;
		justify-content: flex-end;
		padding: 16px;
		background-color: var(--iata-white);

		.left-button {
			margin-right: auto;
		}
	}

	&__reverse-icon {
		display: inline-flex;
		flex-direction: row-reverse;
		align-items: center;
		gap: 10px;
		margin-left: 20px;
	}
}

::ng-deep .mat-mdc-dialog-container .mat-mdc-dialog-content {
	background-color: var(--iata-grey-100);
}
