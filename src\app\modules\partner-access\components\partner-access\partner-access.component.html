<div class="orll-partner-show iata-box">
	<div class="orll-partner-show__introduction">
		<div class="title">{{ 'partner.mgmt.title' | translate }}</div>
		<div class="title">{{ 'partner.mgmt.title1' | translate }}</div>
		<div class="title">{{ 'partner.mgmt.title2' | translate }}</div>
	</div>

	<div class="controls">
		<button mat-button color="primary" (click)="toggleEdit()">
			<mat-icon>edit</mat-icon>
			{{ isEditMode ? ('partner.mgmt.save' | translate) : ('partner.mgmt.edit' | translate) }}
		</button>
		@if (newRow === null) {
			<button mat-flat-button color="primary" (click)="addOrSaveRow()">
				<mat-icon>add</mat-icon>
				{{ 'partner.mgmt.addPartner' | translate }}
			</button>
		} @else {
			<button mat-flat-button color="primary" (click)="addOrSaveRow()">
				{{ 'partner.mgmt.save' | translate }}
			</button>
		}
	</div>

	<div class="orll-partner-show__table">
		<mat-table [dataSource]="dataSource">
			<ng-container matColumnDef="businessData">
				<mat-header-cell *matHeaderCellDef class="orll-partner-show__mat-head">
					{{ 'partner.table.column.businessData' | translate }}
				</mat-header-cell>
				<mat-cell *matCellDef="let row" class="orll-partner-show__mat-cell">
					@if (shouldShowEditUI(row)) {
						<div class="select-container">
							<mat-select [(value)]="row.businessData" class="custom-select">
								@for (opt of bizTypeList; track opt) {
									<mat-option [value]="opt.name">{{ opt.name }}</mat-option>
								}
							</mat-select>
						</div>
					} @else {
						<span>{{ row.businessData }}</span>
					}
				</mat-cell>
			</ng-container>

			<ng-container matColumnDef="partner">
				<mat-header-cell *matHeaderCellDef class="orll-partner-show__mat-head">
					{{ 'partner.table.column.partner' | translate }}
				</mat-header-cell>
				<mat-cell *matCellDef="let row" class="orll-partner-show__mat-cell">
					@if (shouldShowEditUI(row)) {
						<div class="select-container">
							<mat-select [(value)]="row.orgId" (selectionChange)="setOrgInfo($event, row)" class="custom-select">
								@for (opt of orgList; track opt) {
									<mat-option [value]="opt.id">{{ opt.name }}</mat-option>
								}
							</mat-select>
						</div>
					} @else {
						<span>{{ row.orgName }}</span>
					}
				</mat-cell>
			</ng-container>
			@for (col of listObjeNames; track col) {
				<ng-container [matColumnDef]="col.code">
					<mat-header-cell *matHeaderCellDef class="orll-partner-show__mat-head">
						{{ 'partner.table.column.' + col.name | translate }}
					</mat-header-cell>
					<mat-cell *matCellDef="let row">
						@if (shouldShowEditUI(row)) {
							<mat-checkbox
								[checked]="row[col.code] === '1'"
								(change)="onCheckboxChange($event, row, col.code)"></mat-checkbox>
						} @else {
							<span>
								@if (row[col.code] === '1') {
									<mat-icon [style.color]="'green'">check_circle</mat-icon>
								}
							</span>
						}
					</mat-cell>
				</ng-container>
			}
			<mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
			<mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
		</mat-table>
	</div>
	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>
