import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { ChangeDetectorRef } from '@angular/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { noop, Observable, of } from 'rxjs';
import { AutocompleteComponent } from './autocomplete.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { AbstractAutocompleteService } from '@shared/models/autocomplete.model';

class MockAutocompleteService {
	// eslint-disable-next-line
	getOptions(query: string | object, id?: string): Observable<any[]> {
		return of([
			{ code: 1, name: 'Option 1' },
			{ code: 2, name: 'Option 2' },
		]);
	}
}

describe('AutocompleteComponent', () => {
	let component: AutocompleteComponent<any>;
	let fixture: ComponentFixture<AutocompleteComponent<any>>;
	let mockService: MockAutocompleteService;

	beforeEach(() => {
		mockService = new MockAutocompleteService();

		TestBed.configureTestingModule({
			imports: [ReactiveFormsModule, MatAutocompleteModule, AutocompleteComponent, NoopAnimationsModule],
			providers: [ChangeDetectorRef],
		}).compileComponents();

		fixture = TestBed.createComponent(AutocompleteComponent);
		component = fixture.componentInstance;
		component.api = mockService as AbstractAutocompleteService<any>;
		fixture.detectChanges();
	});

	it('should create the component', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with default values', () => {
		expect(component.minLength).toBe(3);
		expect(component.label).toBe('');
		expect(component.isReadonly).toBe(false);
		expect(component.selection).toBeDefined();
	});

	it('#writeValue should call writeValue and set value', () => {
		component.writeValue('test');
		expect(component.selection.value).toBe('test');
	});

	it('#onChange should register onChange function', () => {
		const fn = jasmine.createSpy('onChange');
		component.registerOnChange(fn);
		component.onChange('test');
		expect(fn).toHaveBeenCalledWith('test');
	});

	it('#onTouched should register onTouched function', () => {
		const fn = jasmine.createSpy('onTouched');
		component.registerOnTouched(fn);
		component.onTouched();
		expect(fn).toHaveBeenCalled();
	});

	it('#setDisabledState should set disabled state', () => {
		component.setDisabledState(true);
		expect(component.disableControl).toBeTrue();
		component.setDisabledState(false);
		expect(component.disableControl).toBeFalse();
	});

	it('#setSelectionValue should filter and debounce value changes', fakeAsync(() => {
		const spy = spyOn(mockService, 'getOptions').and.callThrough();
		component.setDisabledState(true);
		component.ngOnInit();
		component.setDisabledState(false);

		component.selection.setValue('test');
		tick(500);
		fixture.detectChanges();
		expect(spy).toHaveBeenCalledWith('test');
	}));

	it('#markAsTouched should mark as touched', () => {
		const spy = spyOn(component, 'onTouched').and.callThrough();
		component['markAsTouched']();
		expect(component.touched).toBeTrue();
		expect(spy).toHaveBeenCalled();
	});

	it('#eraseValue should erase string value', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.eraseValue({ preventDefault: noop, stopPropagation: noop } as any);
		expect(spySelected).toHaveBeenCalledWith(null);
	});

	it('#eraseValue should erase array data list when support multiple selection is supported', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.multiple = true;
		component.selectedItems = [{ code: 'test', name: 'Test' }];
		component.eraseValue({ preventDefault: noop, stopPropagation: noop } as any);

		expect(component.selectedItems).toEqual([]);
		expect(spySelected).toHaveBeenCalledWith([]);
	});

	it('#onSelect should emit selected value', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.onSelect({ option: { value: '1001' } } as any);
		expect(spySelected).toHaveBeenCalledWith('1001');
	});

	it('#onSelect should emit selected value when support multiple selection is supported', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.multiple = true;
		component.selectedItems = [];
		component.onSelect({ option: { value: { code: 'item-01' } } } as any);
		expect(spySelected).toHaveBeenCalledWith([{ code: 'item-01' }]);
	});

	it('#onSelect should emit appended selected values when support multiple selection is supported', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.multiple = true;
		component.selectedItems = [{ code: 'item-01' }];
		component.onSelect({ option: { value: { code: 'item-02' } } } as any);
		expect(spySelected).toHaveBeenCalledWith([{ code: 'item-01' }, { code: 'item-02' }]);
	});

	it('#onSelect should not emit new selected values when some of the items already have been selected', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.multiple = true;
		component.selectedItems = [{ code: 'item-01' }];
		component.onSelect({ option: { value: { code: 'item-01' } } } as any);
		expect(spySelected).toHaveBeenCalledWith([{ code: 'item-01' }]);
	});

	it('#remove should remove item from selected items', () => {
		const spyOnChange = spyOn(component, 'onChange').and.callThrough();
		component.selectedItems = [{ code: 'item-01' }, { code: 'item-02' }];
		component.remove({ code: 'item-01' });
		expect(spyOnChange).toHaveBeenCalledWith([{ code: 'item-02' }]);
	});

	describe('autocompleteDisplayValue', () => {
		it('should return string value when item is string', () => {
			const result = component.autocompleteDisplayValue('test string', 'general');
			expect(result).toBe('test string');
		});

		it('should return formatted airport display when id is airport', () => {
			const airportItem = { code: 'JFK', name: 'John F. Kennedy International Airport' };
			const result = component.autocompleteDisplayValue(airportItem, 'airport');
			expect(result).toBe('JFK - John F. Kennedy International Airport');
		});

		it('should return name for non-airport items', () => {
			const item = { code: 'TEST', name: 'Test Item' };
			const result = component.autocompleteDisplayValue(item, 'general');
			expect(result).toBe('Test Item');
		});

		it('should return empty string when item is null', () => {
			const result = component.autocompleteDisplayValue(null as any, 'general');
			expect(result).toBe('');
		});

		it('should return empty string when item is undefined', () => {
			const result = component.autocompleteDisplayValue(undefined as any, 'general');
			expect(result).toBe('');
		});

		it('should handle item without name property', () => {
			const item = { code: 'TEST' } as any;
			const result = component.autocompleteDisplayValue(item, 'general');
			expect(result).toBeUndefined();
		});
	});

	describe('autocompleteDisplaySelectedValue', () => {
		it('should return string value when item is string', () => {
			const result = component.autocompleteDisplaySelectedValue('test string', 'general');
			expect(result).toBe('test string');
		});

		it('should return code for airport items', () => {
			const airportItem = { code: 'JFK', name: 'John F. Kennedy International Airport' };
			const result = component.autocompleteDisplaySelectedValue(airportItem, 'airport');
			expect(result).toBe('JFK');
		});

		it('should return name for non-airport items', () => {
			const item = { code: 'TEST', name: 'Test Item' };
			const result = component.autocompleteDisplaySelectedValue(item, 'general');
			expect(result).toBe('Test Item');
		});

		it('should throw error when item is null', () => {
			expect(() => component.autocompleteDisplaySelectedValue(null as any, 'general')).toThrow();
		});

		it('should throw error when item is undefined', () => {
			expect(() => component.autocompleteDisplaySelectedValue(undefined as any, 'general')).toThrow();
		});
	});

	describe('inputIconTooltip', () => {
		it('should return clear all values tooltip for multiple selection', () => {
			component.multiple = true;
			expect(component.inputIconTooltip).toBe('Clear all values');
		});

		it('should return clear value tooltip when single selection has value', () => {
			component.multiple = false;
			component.selection.setValue('test');
			expect(component.inputIconTooltip).toBe('Clear value');
		});

		it('should return select value tooltip when single selection has no value', () => {
			component.multiple = false;
			component.selection.setValue('');
			expect(component.inputIconTooltip).toBe('Select value');
		});

		it('should return select value tooltip when single selection value is null', () => {
			component.multiple = false;
			component.selection.setValue(null);
			expect(component.inputIconTooltip).toBe('Select value');
		});
	});

	describe('writeValue Edge Cases', () => {
		it('should handle array input for multiple selection', () => {
			component.multiple = true;
			const testArray = [
				{ code: 'item1', name: 'Item 1' },
				{ code: 'item2', name: 'Item 2' },
			];

			component.writeValue(testArray);

			expect(component.selectedItems).toEqual(testArray);
			expect(component.selection.value).toBe('');
		});

		it('should handle non-array input for multiple selection', () => {
			component.multiple = true;
			const testValue = 'single value';

			component.writeValue(testValue);

			expect(component.selectedItems).toEqual([]);
			expect(component.selection.value).toBe('');
		});

		it('should handle null input for multiple selection', () => {
			component.multiple = true;

			component.writeValue(null);

			expect(component.selectedItems).toEqual([]);
			expect(component.selection.value).toBe('');
		});

		it('should handle object input for single selection', () => {
			component.multiple = false;
			const testObject = { code: 'test', name: 'Test Item' };

			component.writeValue(testObject);

			expect(component.selection.value).toEqual(testObject);
		});
	});

	describe('eraseValue Edge Cases', () => {
		it('should not erase value when single mode is enabled', () => {
			component.single = true;
			component.multiple = false;
			component.selection.setValue('test value');
			const mockEvent = { preventDefault: jasmine.createSpy(), stopPropagation: jasmine.createSpy() } as any;

			component.eraseValue(mockEvent);

			expect(component.selection.value).toBe('test value');
			expect(mockEvent.preventDefault).not.toHaveBeenCalled();
			expect(mockEvent.stopPropagation).not.toHaveBeenCalled();
		});

		it('should handle eraseValue when selected emitter is undefined', () => {
			component.selected = undefined as any;
			component.multiple = false;

			expect(() => component.eraseValue({ preventDefault: noop, stopPropagation: noop } as any)).not.toThrow();
		});

		it('should emit null for single selection erase', () => {
			const spySelected = spyOn(component.selected, 'emit');
			component.multiple = false;
			component.single = false;

			component.eraseValue({ preventDefault: noop, stopPropagation: noop } as any);

			expect(spySelected).toHaveBeenCalledWith(null);
		});

		it('should emit empty array for multiple selection erase', () => {
			const spySelected = spyOn(component.selected, 'emit');
			component.multiple = true;
			component.single = false;
			component.selectedItems = [{ code: 'test', name: 'Test' }];

			component.eraseValue({ preventDefault: noop, stopPropagation: noop } as any);

			expect(spySelected).toHaveBeenCalledWith([]);
			expect(component.selectedItems).toEqual([]);
		});
	});

	describe('onSelect Edge Cases', () => {
		it('should handle onSelect without event parameter', () => {
			const spySelected = spyOn(component.selected, 'emit');
			component.multiple = false;

			component.onSelect();

			expect(spySelected).toHaveBeenCalledWith(undefined);
		});

		it('should handle onSelect with undefined option value', () => {
			const spySelected = spyOn(component.selected, 'emit');
			component.multiple = false;

			component.onSelect({ option: { value: undefined } } as any);

			expect(spySelected).toHaveBeenCalledWith(undefined);
		});

		it('should handle single mode with multiple flag enabled', () => {
			const spySelected = spyOn(component.selected, 'emit');
			component.multiple = true;
			component.single = true;
			component.selectedItems = [{ code: 'existing', name: 'Existing' }];

			component.onSelect({ option: { value: { code: 'new', name: 'New Item' } } } as any);

			expect(component.selectedItems).toEqual([{ code: 'new', name: 'New Item' }]);
			expect(spySelected).toHaveBeenCalledWith([{ code: 'new', name: 'New Item' }]);
		});

		it('should handle onSelect when selected emitter is undefined', () => {
			component.selected = undefined as any;
			component.multiple = false;

			expect(() => component.onSelect({ option: { value: 'test' } } as any)).not.toThrow();
		});

		it('should clear selection input for multiple selection', () => {
			component.multiple = true;
			component.selection.setValue('search text');

			component.onSelect({ option: { value: { code: 'test', name: 'Test' } } } as any);

			expect(component.selection.value).toBe('');
		});

		it('should not add duplicate items in multiple selection', () => {
			component.multiple = true;
			component.selectedItems = [{ code: 'item-01', name: 'Item 1' }];

			component.onSelect({ option: { value: { code: 'item-01', name: 'Item 1' } } } as any);

			expect(component.selectedItems.length).toBe(1);
			expect(component.selectedItems[0].code).toBe('item-01');
		});
	});

	describe('remove Edge Cases', () => {
		it('should handle remove when item is not in selectedItems', () => {
			const spyOnChange = spyOn(component, 'onChange');
			component.selectedItems = [{ code: 'item-01', name: 'Item 1' }];

			component.remove({ code: 'item-99', name: 'Non-existent' });

			expect(spyOnChange).not.toHaveBeenCalled();
			expect(component.selectedItems.length).toBe(1);
		});

		it('should handle remove with empty selectedItems array', () => {
			const spyOnChange = spyOn(component, 'onChange');
			component.selectedItems = [];

			component.remove({ code: 'item-01', name: 'Item 1' });

			expect(spyOnChange).not.toHaveBeenCalled();
		});

		it('should remove correct item when multiple items have same name', () => {
			const spyOnChange = spyOn(component, 'onChange');
			component.selectedItems = [
				{ code: 'item-01', name: 'Same Name' },
				{ code: 'item-02', name: 'Same Name' },
			];

			component.remove({ code: 'item-01', name: 'Same Name' });

			expect(component.selectedItems).toEqual([{ code: 'item-02', name: 'Same Name' }]);
			expect(spyOnChange).toHaveBeenCalledWith([{ code: 'item-02', name: 'Same Name' }]);
		});

		it('should handle remove with null item', () => {
			component.selectedItems = [{ code: 'item-01', name: 'Item 1' }];

			expect(() => component.remove(null as any)).toThrow();
			expect(component.selectedItems.length).toBe(1);
		});
	});

	describe('ngOnInit Edge Cases', () => {
		it('should initialize options$ with id parameter', () => {
			const spy = spyOn(mockService, 'getOptions').and.callThrough();
			component.id = 'test-id';

			component.ngOnInit();

			expect(spy).toHaveBeenCalledWith('', 'test-id');
		});

		it('should initialize options$ without id parameter', () => {
			const spy = spyOn(mockService, 'getOptions').and.callThrough();
			component.id = undefined;

			component.ngOnInit();

			expect(spy).toHaveBeenCalledWith('');
		});

		it('should disable selection when disableControl is true', () => {
			component.disableControl = true;

			component.ngOnInit();

			expect(component.selection.disabled).toBe(true);
		});

		it('should handle value changes with object search params', fakeAsync(() => {
			const spy = spyOn(mockService, 'getOptions').and.callThrough();
			component.ngOnInit();

			component.selection.setValue({ name: 'test search' });
			tick(500);

			expect(spy).toHaveBeenCalledWith('test search');
		}));

		it('should handle value changes with null search params', fakeAsync(() => {
			const spy = spyOn(mockService, 'getOptions').and.callThrough();
			component.ngOnInit();

			component.selection.setValue(null);
			tick(500);

			expect(spy).toHaveBeenCalledWith('');
		}));

		it('should call markForCheck on value changes', fakeAsync(() => {
			const spy = spyOn(component['cdr'], 'markForCheck');
			component.ngOnInit();

			component.selection.setValue('test');
			tick(500);

			expect(spy).toHaveBeenCalled();
		}));
	});

	describe('setValidators', () => {
		it('should set validators when validatorFn is provided', () => {
			const mockValidator = jasmine.createSpy('validator').and.returnValue(null);
			component.validatorFn = mockValidator;

			component['setValidators']();

			expect(component.selection.hasError).toBeDefined();
		});

		it('should add required validator when selection has required error', () => {
			component.validatorFn = jasmine.createSpy('validator').and.returnValue({ required: true });

			component['setValidators']();

			expect(component.selection.hasError('required')).toBe(true);
		});

		it('should not set validators when validatorFn is null', () => {
			component.validatorFn = null;
			const initialValidators = component.selection.validator;

			component['setValidators']();

			expect(component.selection.validator).toBe(initialValidators);
		});
	});

	describe('Component Integration', () => {
		it('should handle complete workflow for single selection', fakeAsync(() => {
			const spySelected = spyOn(component.selected, 'emit');
			component.multiple = false;

			// Initialize
			component.ngOnInit();
			tick(500);

			// Search
			component.selection.setValue('test search');
			tick(500);

			// Select
			component.onSelect({ option: { value: { code: 'test', name: 'Test Item' } } } as any);

			expect(spySelected).toHaveBeenCalledWith({ code: 'test', name: 'Test Item' });
		}));

		it('should handle complete workflow for multiple selection', fakeAsync(() => {
			const spySelected = spyOn(component.selected, 'emit');
			component.multiple = true;

			// Initialize
			component.ngOnInit();
			tick(500);

			// Add first item
			component.onSelect({ option: { value: { code: 'item1', name: 'Item 1' } } } as any);
			expect(spySelected).toHaveBeenCalledWith([{ code: 'item1', name: 'Item 1' }]);

			// Add second item
			component.onSelect({ option: { value: { code: 'item2', name: 'Item 2' } } } as any);
			expect(spySelected).toHaveBeenCalledWith([
				{ code: 'item1', name: 'Item 1' },
				{ code: 'item2', name: 'Item 2' },
			]);

			// Remove first item
			component.remove({ code: 'item1', name: 'Item 1' });
			expect(component.selectedItems).toEqual([{ code: 'item2', name: 'Item 2' }]);

			// Clear all
			component.eraseValue({ preventDefault: noop, stopPropagation: noop } as any);
			expect(spySelected).toHaveBeenCalledWith([]);
		}));
	});
});
