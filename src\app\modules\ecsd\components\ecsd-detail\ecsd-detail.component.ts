import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { EcsdService } from '../../service/ecsd.service';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { EcsdDialogObj, EcsdObj, HawbListObj, PieceListObj } from '../../models/ecsd.model';
import { TranslateModule } from '@ngx-translate/core';
// eslint-disable-next-line @typescript-eslint/naming-convention
import OrllDialogComponent from '@shared/components/dialog-template/dialog-template.component';
import { MatDividerModule } from '@angular/material/divider';
import { Modules } from '@shared/models/user-role.model';
import { MatIconModule } from '@angular/material/icon';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';

@Component({
	selector: 'orll-ecsd-detail',
	imports: [
		TranslateModule,
		OrllDialogComponent,
		MatDividerModule,
		MatDialogModule,
		MatIconModule,
		MatButtonModule,
		SpinnerComponent,
		MatTableModule,
	],
	templateUrl: './ecsd-detail.component.html',
	styleUrl: './ecsd-detail.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EcsdDetailComponent implements OnInit {
	dataLoading = false;
	ecsdDetail: EcsdObj | null = null;
	moduleEnum = Modules;
	dataSource = new MatTableDataSource<PieceListObj | HawbListObj>([]);

	pieceDisplayedColumns: string[] = ['productDescription', 'packageType', 'grossWeight', 'pieceQuantity', 'dimensions'];
	hawbDisplayedColumns: string[] = [
		'waybillNumber',
		'shipper',
		'consignee',
		'goodsDescription',
		'origin',
		'destination',
		'weight',
		'slac',
	];

	constructor(
		private readonly ecsdService: EcsdService,
		@Inject(MAT_DIALOG_DATA) public data: EcsdDialogObj,
		private readonly cdr: ChangeDetectorRef
	) {}

	ngOnInit(): void {
		if (this.data.ecsdObj) {
			this.dataLoading = true;
			this.ecsdService.getEcsd(this.data.ecsdObj).subscribe({
				next: (res) => {
					this.ecsdDetail = res;
					this.ecsdDetail.whetherExemptedForScreening = this.ecsdDetail.screeningMethod ? 'No' : 'Yes';
					this.dataSource.data = (res.pieceList || res.hawbList) ?? [];
					this.dataLoading = false;
					this.cdr.markForCheck();
				},
				error: () => {
					this.dataLoading = false;
					this.cdr.markForCheck();
				},
			});
		}
	}
}
