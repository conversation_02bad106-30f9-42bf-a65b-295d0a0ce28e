import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { TranslateModule } from '@ngx-translate/core';
import { UserProfileService } from '@shared/services/user-profile.service';
import { of } from 'rxjs';
import { BookingOptionRequestFormComponent } from './booking-option-request-form.component';
import { FormControl } from '@angular/forms';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';

const mockCurrencies = ['CNY', 'USD', 'EUR'];
const mockAirports = [
	{ code: 'PEK', name: 'Beijing' },
	{ code: 'SHA', name: 'Shanghai' },
];
const mockCommodityCodes = [
	{ code: 'C1', name: 'C1' },
	{ name: 'C2', code: 'C2' },
];
const mockHandlingCodes = [
	{ code: 'C1', name: 'C1' },
	{ name: 'C2', code: 'C2' },
];

describe('BookingOptionRequestFormComponent', () => {
	let component: BookingOptionRequestFormComponent;
	let fixture: ComponentFixture<BookingOptionRequestFormComponent>;
	let mockBookingService: jasmine.SpyObj<BookingOptionRequestService>;
	let mockOrgService: jasmine.SpyObj<OrgMgmtRequestService>;
	let mockProfileService: jasmine.SpyObj<UserProfileService>;

	beforeEach(async () => {
		mockBookingService = jasmine.createSpyObj('BookingOptionRequestService', ['getCurrencies', 'getCodeByType']);
		mockBookingService.getCurrencies.and.returnValue(of(mockCurrencies));
		mockBookingService.getCodeByType.and.callFake((codeType: string) => {
			if (codeType === 'commodityCode') {
				return of(mockCommodityCodes);
			} else if (codeType === 'specialHandlingCode') {
				return of(mockHandlingCodes);
			}
			return of([]);
		});

		mockOrgService = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList']);
		mockOrgService.getOrgList.and.returnValue(of([]));

		mockProfileService = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		mockProfileService.hasPermission.and.returnValue(of(true));
		mockProfileService.hasSomeRole.and.returnValue(of(true));

		await TestBed.configureTestingModule({
			imports: [BookingOptionRequestFormComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: BookingOptionRequestService, useValue: mockBookingService },
				{ provide: OrgMgmtRequestService, useValue: mockOrgService },
				{ provide: UserProfileService, useValue: mockProfileService },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(BookingOptionRequestFormComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should load initial data via forkJoin', fakeAsync(() => {
			component.ngOnInit();
			tick(); // Complete async operations
			fixture.detectChanges();

			expect(mockBookingService.getCurrencies).toHaveBeenCalled();
			expect(mockBookingService.getCodeByType).toHaveBeenCalled();
			expect(mockOrgService.getOrgList).toHaveBeenCalled();

			expect(component.currencyList).toEqual(mockCurrencies);
			expect(component.commodityCodeList).toEqual(mockCommodityCodes);
		}));
	});

	describe('mawbNumberValidator', () => {
		it('should return null for empty value', () => {
			const control = new FormControl('');
			const result = component.mawbNumberValidator(control);
			expect(result).toBeNull();
		});

		it('should return null for null value', () => {
			const control = new FormControl(null);
			const result = component.mawbNumberValidator(control);
			expect(result).toBeNull();
		});

		it('should return error for invalid format (not 8 digits)', () => {
			const control = new FormControl('12345');
			const result = component.mawbNumberValidator(control);
			expect(result).toEqual({
				mawbNumberFormat: { message: jasmine.any(String) },
			});
		});

		it('should return error for invalid format (contains letters)', () => {
			const control = new FormControl('1234567a');
			const result = component.mawbNumberValidator(control);
			expect(result).toEqual({
				mawbNumberFormat: { message: jasmine.any(String) },
			});
		});

		it('should return error for invalid check digit', () => {
			const control = new FormControl('12345679'); // check digit should be 1 (1234567 % 7 = 1)
			const result = component.mawbNumberValidator(control);
			expect(result).toEqual({
				mawbNumberCheckDigit: { message: jasmine.any(String) },
			});
		});

		it('should return null for valid MAWB number', () => {
			const control = new FormControl('12345675'); // check digit is 5 (1234567 % 7 = 5)
			const result = component.mawbNumberValidator(control);
			expect(result).toBeNull();
		});

		it('should validate check digit correctly for different numbers', () => {
			// Test case: 1000000 % 7 = 1, so valid number is 10000001
			const control = new FormControl('10000001');
			const result = component.mawbNumberValidator(control);
			expect(result).toBeNull();
		});
	});

	describe('getFormData', () => {
		beforeEach(() => {
			component.carriers = [
				{ id: 'carrier1', prefix: 'CA', name: 'Carrier 1' },
				{ id: 'carrier2', prefix: 'CB', name: 'Carrier 2' },
			] as any;
		});

		it('should return BookingInfo when fromBooking is true', () => {
			component.fromBooking = true;
			component.bookingOptionReqForm.patchValue({
				pieceGroupCount: '5',
				totalGrossWeight: '100.5',
				chargeableWeight: '95.0',
				dimLength: '10.0',
				dimWidth: '5.0',
				dimHeight: '2.0',
				expectedCommodity: 'TEST',
				currency: 'USD',
				mawbPrefix: 'CA',
				mawbNumber: '12345671',
			});

			const result = component.getFormData();

			expect(result).toEqual(
				jasmine.objectContaining({
					waybillPrefix: 'CA',
					waybillNumber: '12345671',
					bookingShipment: jasmine.objectContaining({
						pieceGroups: jasmine.objectContaining({
							pieceGroupCount: '5',
						}),
						totalGrossWeight: jasmine.objectContaining({
							numericalValue: '100.5',
						}),
					}),
				})
			);
		});

		it('should return BookingOptionRequestDetailObj when fromBooking is false', () => {
			component.fromBooking = false;
			component.bookingOptionReqForm.patchValue({
				pieceGroupCount: '3',
				totalGrossWeight: '50.0',
				expectedCommodity: 'COMMODITY',
				departureLocation: mockAirports[0],
				arrivalLocation: mockAirports[1],
				currency: 'EUR',
			});

			const result = component.getFormData();

			expect(result).toEqual(
				jasmine.objectContaining({
					bookingShipmentDetails: jasmine.objectContaining({
						expectedCommodity: 'COMMODITY',
					}),
					transportLegs: jasmine.arrayContaining([
						jasmine.objectContaining({
							departureLocation: jasmine.objectContaining({
								locationName: 'Beijing',
							}),
							arrivalLocation: jasmine.objectContaining({
								locationName: 'Shanghai',
							}),
						}),
					]),
				})
			);
		});

		it('should include id when bookingOptionRequestId is set', () => {
			component.fromBooking = false;
			component.bookingOptionRequestId = 'test-id-123';
			component.bookingOptionReqForm.patchValue({
				expectedCommodity: 'TEST',
				currency: 'USD',
			});

			const result = component.getFormData();

			expect((result as any).id).toBe('test-id-123');
		});
	});
});
