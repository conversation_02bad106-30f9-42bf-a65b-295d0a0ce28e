import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import VersionHistoryListComponent from './version-history-list.component';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { provideTranslateService, TranslateModule } from '@ngx-translate/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { VersionHistoryService } from '@shared/services/biz-logic/verion-history/version-history.service';
import { RequestStatus, VersionHistoryObj } from '@shared/models/biz-logic/version-history.model';
import { VersionHistoryDetailComponent } from '../version-history-detail/version-history-detail.component';
import { of } from 'rxjs';
import { SimpleChange } from '@angular/core';

// Mock Service
class MockVersionHistoryObjectService {
	getDataPerPage = jasmine.createSpy('getDataPerPage').and.returnValue(of({ rows: [], total: 0 }));
	loadAllData = jasmine.createSpy('loadAllData').and.returnValue(of([]));
}

describe('HistoryListComponent', () => {
	let component: VersionHistoryListComponent;
	let fixture: ComponentFixture<VersionHistoryListComponent>;
	let dialogSpy: jasmine.SpyObj<MatDialog>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<VersionHistoryDetailComponent>>;

	beforeEach(async () => {
		mockDialogRef = jasmine.createSpyObj<MatDialogRef<VersionHistoryDetailComponent>>('MatDialogRef', ['close', 'afterClosed']);

		mockDialogRef.afterClosed.and.returnValue(of(true));

		dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);

		dialogSpy.open.and.returnValue(mockDialogRef);

		await TestBed.configureTestingModule({
			imports: [VersionHistoryListComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			providers: [
				{ provide: VersionHistoryService, useClass: MockVersionHistoryObjectService },
				{ provide: MatDialog, useValue: dialogSpy },
				{ provide: MatDialogRef, useValue: mockDialogRef },
				provideTranslateService(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(VersionHistoryListComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnChanges', () => {
		it('should update param when id or type changes', () => {
			// Create a changes object with the records property
			const changes = {
				loId: new SimpleChange(null, 1111, true),
			};

			// Set the records input property
			component.loId = '123';
			component.type = 'document';

			// Call ngOnChanges with the changes object
			component.ngOnChanges(changes);

			expect(component.param).toEqual({ loId: '123', type: 'document' });
		});

		it('should not update param if no relevant changes', () => {
			const oldParam = component.param;
			component.ngOnChanges({});
			expect(component.param).toBe(oldParam);
		});
	});

	describe('columns', () => {
		it('should initialize columns with correct keys and headers', () => {
			const expectedKeys = ['loType', 'actionRequestUri', 'version', 'actionRequestDate', 'updateOrgName', 'requestStatus'];
			component.columns.forEach((col, index) => {
				expect(col.key).toBe(expectedKeys[index]);
			});
		});

		it('should have a clickCell handler in requestStatus column', () => {
			const statusCol = component.columns.find((c) => c.key === 'requestStatus');
			expect(statusCol).toBeDefined();
			expect(statusCol?.clickCell).toEqual(jasmine.any(Function));
		});
	});

	describe('openHistoryDialog', () => {
		let row: VersionHistoryObj;

		beforeEach(() => {
			row = {
				loId: '111',
				loType: 'pdf',
				actionRequestUri: 'https://example.com/doc.pdf',
				version: '1.0',
				actionRequestDate: '2025-01-01',
				updateOrgName: 'Admin',
				requestStatus: RequestStatus.REQUEST_ACCEPTED,
			} as VersionHistoryObj;

			dialogSpy.open.and.returnValue(mockDialogRef as any);
		});

		it('should open dialog with correct data', () => {
			component.openVersionHistory(row);

			expect(dialogSpy.open).toHaveBeenCalledWith(VersionHistoryDetailComponent, {
				width: '80vw',
				autoFocus: false,
				data: {
					title: 'common.change.request.dialog.title',
					actionRequestUri: 'https://example.com/doc.pdf',
				},
			});
		});
	});
});
