import { Dimensions } from './dimensions.model';

export interface PieceList {
	type: string;
	pieceId: string;
	productDescription: string;
	packagingType: string;
	grossWeight: number;
	dimensions: Dimensions;
	pieceQuantity: number;
	slac: number;
	latestStatus?: string;
	containedPieces?: PieceList[];
	level?: number;
	expandable?: boolean;
	expanded?: boolean;
	originalQuantity?: number;
	textualHandlingInstructions?: string;
}

export interface ConsolidatePieceDialogData {
	pieces: PieceList[];
	sliNumber: string;
	pieceId: string;
}
