<div class="orll-status-update-dialog__container">
	<h2 mat-dialog-title>{{ 'mawb.event.update.title' | translate }}</h2>
	<mat-dialog-content class="orll-status-update-dialog__content">
		<div class="orll-status-update-dialog__event_container">
			@for (option of allEvents; track option) {
				<div class="row">
					<div class="col-5">
						@if (events.includes(option.code)) {
							<mat-radio-button
								[checked]="option === selectedEvent"
								[value]="option"
								class="switch-item"
								(change)="onEventChange(option.code)">
							</mat-radio-button>
						}
					</div>
					<div class="col-3">{{ option.code }}</div>
				</div>
			}
		</div>
		<hr class="orll-status-update-dialog__devider" />
		<div class="row justify-content-between">
			<div class="orll-status-update-dialog__milestone col-6">
				<mat-checkbox
					class="orll-status-update-dialog__checkbox"
					[checked]="eventTimeType === 'PLANNED'"
					(change)="onEventTypeChange($event)">
					{{ 'mawb.event.planned.milestone' | translate }}</mat-checkbox
				>
				<mat-checkbox class="orll-status-update-dialog__checkbox" [(ngModel)]="partialEventIndicator">
					{{ 'mawb.event.partial.milestone' | translate }}</mat-checkbox
				>
			</div>

			<div class="col-6">
				<mat-form-field class="orll-status-update-dialog__date_input">
					<mat-label>{{ 'mawb.event.milestone.date' | translate }}</mat-label>
					<input matInput [matDatepicker]="picker" [max]="maxDate" [(ngModel)]="milestoneTime" />
					<mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
					<mat-datepicker #picker></mat-datepicker>
				</mat-form-field>
			</div>
		</div>
	</mat-dialog-content>
	<mat-dialog-actions class="orll-status-update-dialog__btn">
		<button mat-button (click)="dialogRef.close()">{{ 'common.dialog.cancel' | translate }}</button>
		<button mat-flat-button color="primary" (click)="updateEventStatus()" [disabled]="!selectedEvent">
			{{ 'mawb.event.update.btn' | translate }} <mat-icon>update</mat-icon>
		</button>
	</mat-dialog-actions>
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
