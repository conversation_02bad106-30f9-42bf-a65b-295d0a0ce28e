import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { DestroyRef, ChangeDetectorRef } from '@angular/core';
import { of, throwError } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';
import { HawbPieceListComponent } from './hawb-piece-list.component';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { PieceList } from 'src/app/modules/sli-mgmt/models/piece/piece-list.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';

describe('HawbPieceListComponent', () => {
	let component: HawbPieceListComponent;
	let fixture: ComponentFixture<HawbPieceListComponent>;
	let mockHawbSearchRequestService: jasmine.SpyObj<HawbSearchRequestService>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;
	let mockDestroyRef: jasmine.SpyObj<DestroyRef>;

	// Mock test data
	const mockPieceListResponse: PaginationResponse<PieceList> = {
		rows: [
			{
				type: 'Piece',
				pieceId: 'PIECE001',
				productDescription: 'GPU Components',
				packagingType: 'Box, plastic',
				grossWeight: 15.5,
				dimensions: {
					length: 120,
					width: 60,
					height: 40,
				},
				pieceQuantity: 2,
				slac: 0,
				latestStatus: 'In Transit',
			},
			{
				type: 'Piece',
				pieceId: 'PIECE002',
				productDescription: 'Computer Accessories',
				packagingType: 'Box, fibreboard',
				grossWeight: 25.0,
				dimensions: {
					length: 200,
					width: 100,
					height: 80,
				},
				pieceQuantity: 1,
				slac: 0,
				latestStatus: 'Delivered',
			},
		],
		total: 2,
	};

	const mockEmptyPieceListResponse: PaginationResponse<PieceList> = {
		rows: [],
		total: 0,
	};

	beforeEach(async () => {
		// Create spies for dependencies
		mockHawbSearchRequestService = jasmine.createSpyObj<HawbSearchRequestService>('HawbSearchRequestService', [
			'getPieceList',
			'getTotalPieceQuantity',
		]);
		mockChangeDetectorRef = jasmine.createSpyObj<ChangeDetectorRef>('ChangeDetectorRef', ['markForCheck']);
		mockDestroyRef = jasmine.createSpyObj<DestroyRef>('DestroyRef', ['onDestroy']);

		// Configure default mock return values
		mockHawbSearchRequestService.getPieceList.and.returnValue(of(mockPieceListResponse));
		mockHawbSearchRequestService.getTotalPieceQuantity.and.returnValue(
			of({ totalQuantity: 3, totalSlac: 0, eventStatus: 'In Transit' })
		);

		await TestBed.configureTestingModule({
			imports: [HawbPieceListComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			providers: [
				provideHttpClient(),
				provideHttpClientTesting(),
				{ provide: HawbSearchRequestService, useValue: mockHawbSearchRequestService },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
				{ provide: DestroyRef, useValue: mockDestroyRef },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(HawbPieceListComponent);
		component = fixture.componentInstance;
	});

	describe('Component Initialization', () => {
		it('should create the component', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with default values', () => {
			expect(component.pieceList).toEqual([]);
			expect(component.pageParams).toEqual({
				pageNum: 1,
				pageSize: 10,
			});
			expect(component.totalRecords).toBe(0);
			expect(component.dataLoading).toBe(false);
			expect(component.hasMoreData).toBe(true);
			expect(component.hawbId).toBe('');
			expect(component.hawbNumber).toBe('');
			expect(component.sliNumber).toBe('');
		});

		it('should call refreshData on ngOnChanges', () => {
			spyOn(component, 'refreshData');

			component.ngOnChanges({
				selectedTabIndex: {
					currentValue: 1,
					previousValue: 0,
					firstChange: true,
					isFirstChange: () => true,
				},
			});

			expect(component.refreshData).toHaveBeenCalled();
		});
	});

	describe('refreshData Method', () => {
		it('should fetch piece list when hawbId is provided', () => {
			component.hawbId = 'HAWB123';
			component.selectedTabIndex = 1;
			spyOn(component as any, 'getPieceListPage');

			component.refreshData();

			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should not fetch piece list when hawbId is empty', () => {
			component.hawbId = '';
			spyOn(component as any, 'getPieceListPage');

			component.refreshData();

			expect(component['getPieceListPage']).not.toHaveBeenCalled();
		});

		it('should not fetch piece list when hawbId is null', () => {
			component.hawbId = null as any;
			spyOn(component as any, 'getPieceListPage');

			component.refreshData();

			expect(component['getPieceListPage']).not.toHaveBeenCalled();
		});

		it('should not fetch piece list when hawbId is undefined', () => {
			component.hawbId = undefined as any;
			spyOn(component as any, 'getPieceListPage');

			component.refreshData();

			expect(component['getPieceListPage']).not.toHaveBeenCalled();
		});
	});

	describe('onSortChange Method', () => {
		it('should clear sort parameters when direction is empty', () => {
			const sort: Sort = { active: 'productDescription', direction: '' };
			component.pageParams.orderByColumn = 'previousColumn';
			component.pageParams.isAsc = 'asc';

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});

		it('should set sort parameters when direction is asc', () => {
			const sort: Sort = { active: 'productDescription', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should set sort parameters when direction is desc', () => {
			const sort: Sort = { active: 'grossWeight', direction: 'desc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('grossWeight');
			expect(component.pageParams.isAsc).toBe('desc');
		});

		it('should handle different column names', () => {
			const sort: Sort = { active: 'pieceQuantity', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('pieceQuantity');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should reset pageNum and pageSize when sorting', () => {
			const sort: Sort = { active: 'productDescription', direction: 'asc' };
			component.pageParams.pageNum = 5;
			component.pageParams.pageSize = 25;

			component.onSortChange(sort);

			expect(component.pageParams.pageNum).toBe(1);
			expect(component.pageParams.pageSize).toBe(10);
			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should not reset pageNum and pageSize when direction is empty', () => {
			const sort: Sort = { active: 'productDescription', direction: '' };
			component.pageParams.pageNum = 5;
			component.pageParams.pageSize = 25;

			component.onSortChange(sort);

			expect(component.pageParams.pageNum).toBe(5);
			expect(component.pageParams.pageSize).toBe(25);
			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});
	});

	describe('loadMoreData Method', () => {
		it('should increment pageNum and call getPieceListPage with appendData true when hasMoreData is true and not loading', () => {
			component.hasMoreData = true;
			component.dataLoading = false;
			component.pageParams.pageNum = 2;
			spyOn(component as any, 'getPieceListPage');

			component.loadMoreData();

			expect(component.pageParams.pageNum).toBe(3);
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams, true);
		});

		it('should not load more data when hasMoreData is false', () => {
			component.hasMoreData = false;
			component.dataLoading = false;
			component.pageParams.pageNum = 2;
			spyOn(component as any, 'getPieceListPage');

			component.loadMoreData();

			expect(component.pageParams.pageNum).toBe(2);
			expect(component['getPieceListPage']).not.toHaveBeenCalled();
		});

		it('should not load more data when dataLoading is true', () => {
			component.hasMoreData = true;
			component.dataLoading = true;
			component.pageParams.pageNum = 2;
			spyOn(component as any, 'getPieceListPage');

			component.loadMoreData();

			expect(component.pageParams.pageNum).toBe(2);
			expect(component['getPieceListPage']).not.toHaveBeenCalled();
		});

		it('should not load more data when both hasMoreData is false and dataLoading is true', () => {
			component.hasMoreData = false;
			component.dataLoading = true;
			component.pageParams.pageNum = 2;
			spyOn(component as any, 'getPieceListPage');

			component.loadMoreData();

			expect(component.pageParams.pageNum).toBe(2);
			expect(component['getPieceListPage']).not.toHaveBeenCalled();
		});

		it('should handle multiple consecutive calls correctly', () => {
			component.hasMoreData = true;
			component.dataLoading = false;
			component.pageParams.pageNum = 1;
			spyOn(component as any, 'getPieceListPage');

			component.loadMoreData();
			component.loadMoreData();

			expect(component.pageParams.pageNum).toBe(3);
			expect(component['getPieceListPage']).toHaveBeenCalledTimes(2);
		});
	});

	describe('onPageChange Method', () => {
		it('should update pageParams and call getPieceListPage', () => {
			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 20,
				length: 100,
			};
			spyOn(component as any, 'getPieceListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(3); // pageIndex + 1
			expect(component.pageParams.pageSize).toBe(20);
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should update sort parameters when provided in pageEvent', () => {
			const pageEvent: PageEvent & { sortField?: string; sortDirection?: string } = {
				pageIndex: 1,
				pageSize: 15,
				length: 50,
				sortField: 'grossWeight',
				sortDirection: 'desc',
			};
			spyOn(component as any, 'getPieceListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(2);
			expect(component.pageParams.pageSize).toBe(15);
			expect(component.pageParams.orderByColumn).toBe('grossWeight');
			expect(component.pageParams.isAsc).toBe('desc');
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should not update sort parameters when not provided in pageEvent', () => {
			const pageEvent: PageEvent = {
				pageIndex: 0,
				pageSize: 10,
				length: 20,
			};
			component.pageParams.orderByColumn = 'existingColumn';
			component.pageParams.isAsc = 'asc';
			spyOn(component as any, 'getPieceListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(1);
			expect(component.pageParams.pageSize).toBe(10);
			expect(component.pageParams.orderByColumn).toBe('existingColumn');
			expect(component.pageParams.isAsc).toBe('asc');
		});
	});

	describe('getPieceListPage Method', () => {
		it('should fetch piece list data successfully with appendData false (default)', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			const pageParams: PaginationRequest = { pageNum: 1, pageSize: 10 };

			component['getPieceListPage'](pageParams);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(component.pieceList).toEqual(mockPieceListResponse.rows);
			expect(component.totalRecords).toBe(mockPieceListResponse.total);
			expect(mockHawbSearchRequestService.getPieceList).toHaveBeenCalledWith(pageParams, 'HAWB123');
		}));

		it('should append data when appendData is true', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			component.pieceList = [mockPieceListResponse.rows[0]]; // Existing data
			component.hasMoreData = true;
			const pageParams: PaginationRequest = { pageNum: 2, pageSize: 10 };

			component['getPieceListPage'](pageParams, true);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(component.pieceList).toEqual([
				mockPieceListResponse.rows[0], // Existing data
				...mockPieceListResponse.rows, // New data appended
			]);
			expect(component.totalRecords).toBe(mockPieceListResponse.total);
			expect(component.hasMoreData).toBe(false); // Should be false since pieceList.length (3) >= total (2)
		}));

		it('should replace data when appendData is false', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			component.pieceList = [mockPieceListResponse.rows[0]]; // Existing data
			const pageParams: PaginationRequest = { pageNum: 1, pageSize: 10 };

			component['getPieceListPage'](pageParams, false);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(component.pieceList).toEqual(mockPieceListResponse.rows); // Data replaced, not appended
			expect(component.totalRecords).toBe(mockPieceListResponse.total);
		}));

		it('should set hasMoreData correctly when appending data', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			component.pieceList = []; // Start with empty list
			const largeResponse = {
				rows: [mockPieceListResponse.rows[0]],
				total: 10, // More total than current data
			};
			mockHawbSearchRequestService.getPieceList.and.returnValue(of(largeResponse));

			component['getPieceListPage'](component.pageParams, true);
			tick();

			expect(component.hasMoreData).toBe(true); // Should be true since pieceList.length (1) < total (10)
			expect(component.pieceList.length).toBe(1);
			expect(component.totalRecords).toBe(10);
		}));

		it('should set dataLoading to true and clear pieceList at start, then complete successfully', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			component.dataLoading = false;
			component.pieceList = [mockPieceListResponse.rows[0]];

			// Call the method and complete immediately
			component['getPieceListPage'](component.pageParams);
			tick();

			// Since the observable completes synchronously, check final state
			expect(component.dataLoading).toBe(false);
			expect(component.pieceList).toEqual(mockPieceListResponse.rows);
			expect(component.totalRecords).toBe(mockPieceListResponse.total);
		}));

		it('should handle empty response', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			mockHawbSearchRequestService.getPieceList.and.returnValue(of(mockEmptyPieceListResponse));

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(component.pieceList).toEqual([]);
			expect(component.totalRecords).toBe(0);
			expect(component.dataLoading).toBe(false);
		}));

		it('should handle empty response when appending data', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			component.pieceList = [mockPieceListResponse.rows[0]]; // Existing data
			mockHawbSearchRequestService.getPieceList.and.returnValue(of(mockEmptyPieceListResponse));

			component['getPieceListPage'](component.pageParams, true);
			tick();

			expect(component.pieceList).toEqual([mockPieceListResponse.rows[0]]); // Existing data preserved
			expect(component.totalRecords).toBe(0);
			expect(component.dataLoading).toBe(false);
			expect(component.hasMoreData).toBe(false);
		}));

		it('should handle service error', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			component.dataLoading = false;
			mockHawbSearchRequestService.getPieceList.and.returnValue(throwError(() => new Error('Service error')));

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(component.dataLoading).toBe(false);
		}));

		it('should call service with correct parameters', () => {
			component.hawbId = 'HAWB456';
			const customPageParams: PaginationRequest = {
				pageNum: 3,
				pageSize: 25,
				orderByColumn: 'productDescription',
				isAsc: 'desc',
			};

			component['getPieceListPage'](customPageParams);

			expect(mockHawbSearchRequestService.getPieceList).toHaveBeenCalledWith(customPageParams, 'HAWB456');
		});
	});

	describe('Input Properties', () => {
		it('should accept hawbId input', () => {
			const testHawbId = 'TEST-HAWB-001';
			component.hawbId = testHawbId;

			expect(component.hawbId).toBe(testHawbId);
		});

		it('should accept hawbNumber input', () => {
			const testHawbNumber = 'H000123';
			component.hawbNumber = testHawbNumber;

			expect(component.hawbNumber).toBe(testHawbNumber);
		});

		it('should accept sliNumber input', () => {
			const testSliNumber = 'S000456';
			component.sliNumber = testSliNumber;

			expect(component.sliNumber).toBe(testSliNumber);
		});
	});

	describe('Component Integration', () => {
		it('should initialize and fetch data when hawbId is set', () => {
			component.hawbId = 'HAWB789';
			component.selectedTabIndex = 1;
			spyOn(component as any, 'getPieceListPage');

			component.ngOnChanges({
				selectedTabIndex: {
					currentValue: 1,
					previousValue: 0,
					firstChange: true,
					isFirstChange: () => true,
				},
			});

			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should handle complete workflow: sort, paginate, and fetch', fakeAsync(() => {
			component.hawbId = 'HAWB999';

			// Test sorting
			const sort: Sort = { active: 'productDescription', direction: 'asc' };
			component.onSortChange(sort);

			// Test pagination
			const pageEvent: PageEvent = { pageIndex: 1, pageSize: 20, length: 100 };
			component.onPageChange(pageEvent);
			tick();

			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBe('asc');
			expect(component.pageParams.pageNum).toBe(2);
			expect(component.pageParams.pageSize).toBe(20);
			expect(mockHawbSearchRequestService.getPieceList).toHaveBeenCalledWith(component.pageParams, 'HAWB999');
		}));

		it('should handle complete workflow: load more data', fakeAsync(() => {
			component.hawbId = 'HAWB888';
			component.hasMoreData = true;
			component.dataLoading = false;
			component.pageParams.pageNum = 1;

			component.loadMoreData();
			tick();

			expect(component.pageParams.pageNum).toBe(2);
			expect(mockHawbSearchRequestService.getPieceList).toHaveBeenCalledWith(component.pageParams, 'HAWB888');
		}));

		it('should handle workflow: refresh data after load more', () => {
			component.hawbId = 'HAWB777';
			component.selectedTabIndex = 1;
			component.pageParams.pageNum = 3; // Simulate after loading more data
			spyOn(component as any, 'getPieceListPage');

			component.refreshData();

			// refreshData should reset to initial page params and call with appendData false
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should maintain state consistency during multiple operations', () => {
			// Initial state
			expect(component.dataLoading).toBe(false);
			expect(component.pieceList).toEqual([]);
			expect(component.totalRecords).toBe(0);
			expect(component.hasMoreData).toBe(true);

			// After setting inputs
			component.hawbId = 'HAWB-MULTI';
			component.hawbNumber = 'H-MULTI';
			component.sliNumber = 'S-MULTI';

			expect(component.hawbId).toBe('HAWB-MULTI');
			expect(component.hawbNumber).toBe('H-MULTI');
			expect(component.sliNumber).toBe('S-MULTI');
		});

		it('should handle complex scenario: sort, load more, then paginate', fakeAsync(() => {
			component.hawbId = 'HAWB-COMPLEX';
			component.hasMoreData = true;
			component.dataLoading = false;

			// First sort
			const sort: Sort = { active: 'grossWeight', direction: 'desc' };
			component.onSortChange(sort);

			expect(component.pageParams.pageNum).toBe(1);
			expect(component.pageParams.pageSize).toBe(10);

			// Then load more data
			component.loadMoreData();
			expect(component.pageParams.pageNum).toBe(2);

			// Then paginate
			const pageEvent: PageEvent = { pageIndex: 2, pageSize: 15, length: 100 };
			component.onPageChange(pageEvent);
			tick();

			expect(component.pageParams.pageNum).toBe(3);
			expect(component.pageParams.pageSize).toBe(15);
			expect(component.pageParams.orderByColumn).toBe('grossWeight');
			expect(component.pageParams.isAsc).toBe('desc');
		}));
	});

	describe('Edge Cases', () => {
		it('should handle undefined sort direction', () => {
			const sort: Sort = { active: 'productDescription', direction: undefined as any };
			// Set initial values
			component.pageParams.orderByColumn = 'initialColumn';
			component.pageParams.isAsc = 'asc';

			component.onSortChange(sort);

			// Undefined direction is not the same as empty string, so values should be set
			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBeUndefined();
		});

		it('should handle zero pageIndex in pagination', () => {
			const pageEvent: PageEvent = { pageIndex: 0, pageSize: 10, length: 50 };
			spyOn(component as any, 'getPieceListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(1);
		});

		it('should handle large page sizes', () => {
			const pageEvent: PageEvent = { pageIndex: 0, pageSize: 1000, length: 5000 };
			spyOn(component as any, 'getPieceListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageSize).toBe(1000);
		});

		it('should handle service returning null response', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			mockHawbSearchRequestService.getPieceList.and.returnValue(of(null as any));

			// The component will throw an error when trying to access res.rows on null
			expect(() => {
				component['getPieceListPage'](component.pageParams);
				tick();
			}).toThrow();

			// Verify loading state was set initially
			expect(component.dataLoading).toBe(true);
		}));

		it('should handle loadMoreData when pageNum is at maximum value', () => {
			component.hasMoreData = true;
			component.dataLoading = false;
			component.pageParams.pageNum = Number.MAX_SAFE_INTEGER;
			spyOn(component as any, 'getPieceListPage');

			component.loadMoreData();

			// Should still increment even if it's a very large number
			expect(component.pageParams.pageNum).toBe(Number.MAX_SAFE_INTEGER + 1);
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams, true);
		});

		it('should handle hasMoreData calculation when pieceList length equals total', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			component.pieceList = [mockPieceListResponse.rows[0]]; // 1 item
			const exactResponse = {
				rows: [mockPieceListResponse.rows[1]], // 1 more item
				total: 2, // Total is exactly 2
			};
			mockHawbSearchRequestService.getPieceList.and.returnValue(of(exactResponse));

			component['getPieceListPage'](component.pageParams, true);
			tick();

			expect(component.pieceList.length).toBe(2); // 1 existing + 1 new
			expect(component.totalRecords).toBe(2);
			expect(component.hasMoreData).toBe(false); // Should be false since length equals total
		}));

		it('should handle empty hawbId during getPieceListPage', () => {
			component.hawbId = '';
			spyOn(component as any, 'getPieceListPage').and.callThrough();

			// This should still call the service even with empty hawbId
			component['getPieceListPage'](component.pageParams);

			expect(mockHawbSearchRequestService.getPieceList).toHaveBeenCalledWith(component.pageParams, '');
		});

		it('should handle service error when appending data', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			component.pieceList = [mockPieceListResponse.rows[0]]; // Existing data
			component.dataLoading = false;
			mockHawbSearchRequestService.getPieceList.and.returnValue(throwError(() => new Error('Service error')));

			component['getPieceListPage'](component.pageParams, true);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(component.pieceList).toEqual([mockPieceListResponse.rows[0]]); // Existing data should remain
		}));
	});
});
