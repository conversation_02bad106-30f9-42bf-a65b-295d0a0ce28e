<div class="orll-mawb-table__container">
	@if (!bookingId) {
		<div class="orll-mawb-table__create">
			<button mat-stroked-button color="primary" (click)="retrieveObj()">
				<mat-icon>search</mat-icon>
				{{ 'common.enter.uri.btn' | translate }}
			</button>
			@if (hasPermission(createPermission, mawbModule) | async) {
				<button mat-flat-button color="primary" (click)="createMawbFromHawb()">
					<mat-icon>add</mat-icon>
					{{ 'mawb.mgmt.create' | translate }}
				</button>
			}
		</div>
	}

	<table
		mat-table
		[dataSource]="dataSource"
		[trackBy]="trackByMawbId"
		matSort
		(matSortChange)="onSortChange($event)"
		aria-label="MAWB table"
		class="orll-mawb-table__mat">
		<ng-container matColumnDef="mawbNumber">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="mawb-number-width">
				{{ 'mawb.table.column.mawbNumber' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">
				<a class="mawb-number__link" (click)="editMawb(record.mawbId)">
					{{ record.mawbNumber }}
				</a>
			</td>
		</ng-container>

		<ng-container matColumnDef="airlineCode">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="mawb-airline-code-width">
				{{ 'mawb.table.column.airlineCode' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.airlineCode }}</td>
		</ng-container>

		<ng-container matColumnDef="goodsDescription">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="mawb-goods-description-width">
				{{ 'mawb.table.column.goodsDescription' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.goodsDescription }}</td>
		</ng-container>

		<ng-container matColumnDef="origin">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="mawb-airport-width">
				{{ 'mawb.table.column.origin' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.origin }}</td>
		</ng-container>

		<ng-container matColumnDef="destination">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="mawb-airport-width">
				{{ 'mawb.table.column.destination' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.destination }}</td>
		</ng-container>

		<ng-container matColumnDef="latestStatus">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="mawb-date-width">
				{{ 'mawb.table.column.latestStatus' | translate }}
			</th>
			<td mat-cell *matCellDef="let record" (keydown.enter)="$event.stopPropagation()" (click)="openEventTracking(record)">
				<span class="orll-mawb-table__event_col">
					@if (record?.latestStatus?.trim()) {
						{{ record?.latestStatus?.trim() }}
					} @else {
						{{ 'N/A' }}
					}
				</span>
			</td>
		</ng-container>

		<ng-container matColumnDef="eventDate">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="mawb-date-width">
				{{ 'mawb.table.column.eventDate' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.eventDate ?? '' | iataDateFormat }}</td>
		</ng-container>

		<ng-container matColumnDef="createDate">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="mawb-date-width">
				{{ 'mawb.table.column.createDate' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.createDate | iataDateFormat }}</td>
		</ng-container>

		<ng-container matColumnDef="share">
			<th scope="col" mat-header-cell *matHeaderCellDef class="mawb-share-width">{{ 'mawb.table.column.share' | translate }}</th>
			<td mat-cell *matCellDef="let record">
				<div class="d-flex">
					<button mat-icon-button aria-label="Share a MAWB record" class="share-button" (click)="shareMawb.emit(record)">
						<mat-icon>share</mat-icon>
					</button>
					<button mat-icon-button [orllCopy]="record.mawbId" color="primary">
						<mat-icon>content_copy</mat-icon>
					</button>
				</div>
			</td>
		</ng-container>
		<ng-container matColumnDef="copy">
			<th scope="col" mat-header-cell *matHeaderCellDef class="mawb-share-width">{{ 'mawb.table.column.copy' | translate }}</th>
			<td mat-cell *matCellDef="let record">
				<div class="d-flex">
					<button mat-icon-button [orllCopy]="record.mawbId" color="primary">
						<mat-icon>content_copy</mat-icon>
					</button>
				</div>
			</td>
		</ng-container>

		<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
		<tr mat-row *matRowDef="let record; columns: displayedColumns" class="orll-mawb-table__row"></tr>
	</table>
</div>

<mat-paginator [pageSizeOptions]="tablePageSizes" [length]="totalRecords" (page)="pagination.emit($event)"></mat-paginator>
