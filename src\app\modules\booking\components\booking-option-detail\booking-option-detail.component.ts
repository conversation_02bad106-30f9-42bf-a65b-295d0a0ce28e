import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
// eslint-disable-next-line @typescript-eslint/naming-convention
import OrllDialogComponent from '@shared/components/dialog-template/dialog-template.component';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import {
	BookingOptionRequestDetailObj,
	BookingOptionRequestListObj,
	BookingRequestObj,
	OptionRequestDetail,
	QUOTE_PROVIDED_STATUS,
} from '../../models/booking.model';
import { BookingAirlineDetailObj, Transport } from '@shared/models/booking-option.model';
import { MatButtonModule } from '@angular/material/button';
import { buildOptionRequestDetail } from '../../util/booking.util';
import { BookingOptionRequestDetailComponent } from '../booking-option-request-detail/booking-option-request-detail.component';
import { BookingTransportComponent } from '../booking-transport/booking-transport.component';
import { UserRole } from '@shared/models/user-role.model';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { AsyncPipe } from '@angular/common';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { BookingRequestService } from '../../services/booking-request.service';
import { CodeType } from '@shared/models/search-type.model';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { AbstractControl, FormControl, FormGroup, ReactiveFormsModule, ValidationErrors } from '@angular/forms';
import { Organization } from '@shared/models/organization.model';
import { OrgType } from '@shared/models/org-type.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';

@Component({
	selector: 'orll-booking-option-detail',
	imports: [
		OrllDialogComponent,
		TranslateModule,
		MatIconModule,
		MatButtonModule,
		MatFormFieldModule,
		MatInputModule,
		MatSelectModule,
		ReactiveFormsModule,
		BookingOptionRequestDetailComponent,
		BookingTransportComponent,
		AsyncPipe,
		MatDialogModule,
		SpinnerComponent,
	],
	templateUrl: './booking-option-detail.component.html',
	styleUrl: './booking-option-detail.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [IataDateFormatPipe],
})
export class BookingOptionDetailComponent extends RolesAwareComponent implements OnInit {
	bookingOptionDetail: BookingOptionRequestDetailObj | null = null;
	bookingRequestDetail: OptionRequestDetail | null = null;
	dataLoading = false;
	bookingAirlineDetails: BookingAirlineDetailObj[] = [];
	selectedOption: BookingAirlineDetailObj | null = null;
	carriers: Organization[] = [];

	readonly forwarder: string[] = [UserRole.FORWARDER];
	readonly carrier: string[] = [UserRole.CARRIER];
	readonly quoteProvidedStatus = QUOTE_PROVIDED_STATUS;

	mawbForm: FormGroup = new FormGroup({
		mawbPrefix: new FormControl<string>(''),
		mawbNumber: new FormControl<string>('', [this.mawbNumberValidator.bind(this)]),
	});

	constructor(
		@Inject(MAT_DIALOG_DATA) public data: BookingOptionRequestListObj,
		private readonly dialogRef: MatDialogRef<BookingOptionDetailComponent>,
		private readonly bookingService: BookingOptionRequestService,
		private readonly bookingRequestService: BookingRequestService,
		private readonly cdr: ChangeDetectorRef,
		private readonly iataDateFormatPipe: IataDateFormatPipe,
		private readonly translateService: TranslateService,
		private readonly orgMgmtRequestService: OrgMgmtRequestService
	) {
		super();
	}

	mawbNumberValidator(control: AbstractControl): ValidationErrors | null {
		const value = control.value;

		if (!value) {
			return null;
		}

		const numberPattern = /^\d{8}$/;
		if (!numberPattern.test(value)) {
			return { mawbNumberFormat: { message: this.translateService.instant('mawb.formItem.mawbNumber.checkLength') } };
		}

		const first7Digits = value.substring(0, 7);
		const checkDigit = Number.parseInt(value.charAt(7), 10);

		const calculatedCheckDigit = Number.parseInt(first7Digits, 10) % 7;

		if (checkDigit !== calculatedCheckDigit) {
			return {
				mawbNumberCheckDigit: {
					message: this.translateService.instant('mawb.formItem.mawbNumber.checkDigit') + calculatedCheckDigit,
				},
			};
		}

		return null;
	}

	handleDataLoading(): void {
		this.dataLoading = false;
		this.cdr.markForCheck();
	}

	createBooking() {
		if (this.mawbForm.invalid) {
			this.mawbForm.markAllAsTouched();
			return;
		}

		this.dataLoading = true;
		const transportList = this.selectedOption?.transportVoList ?? [];
		const mawbPrefix = this.mawbForm.get('mawbPrefix')?.value ?? '';
		const bookingRequestPayload: BookingRequestObj = {
			waybillPrefix: this.carriers.find((item) => item.id === mawbPrefix)?.prefix ?? '',
			waybillNumber: this.mawbForm.get('mawbNumber')?.value ?? '',
			bookingOptionId: this.selectedOption?.id ?? '',
			bookingShipmentId: this.bookingOptionDetail?.bookingShipmentDetails?.id ?? '',
			airlineName: this.selectedOption?.airlinesName ?? '',
			requestedProduct: this.bookingRequestDetail?.expectedCommodity ?? '',
			requestedFlight: transportList[0]?.carrier ?? '',
			departureLocation: transportList[0]?.departureLocation ?? '',
			arrivalLocation: transportList.at(-1)?.arrivalLocation ?? '',
			flightDate: this.iataDateFormatPipe.transform(transportList[0]?.departureDate ?? ''),
		};

		this.bookingRequestService.createBookingRequest(bookingRequestPayload).subscribe({
			next: () => {
				this.dialogRef.close(true);
				this.handleDataLoading();
			},
			error: () => {
				this.dialogRef.close(false);
				this.handleDataLoading();
			},
		});
	}

	ngOnInit(): void {
		if (this.data.bookingOptionRequestId) {
			this.dataLoading = true;

			this.orgMgmtRequestService.getOrgList(OrgType.CARRIER).subscribe((res) => {
				this.carriers = res;
			});

			this.bookingService.getCodeByType(CodeType.COMMODITY_CODE).subscribe((commodityCodes) => {
				this.bookingService.getBookingOptionDetail(this.data.bookingOptionRequestId).subscribe({
					next: (res) => {
						this.bookingOptionDetail = res;
						this.bookingRequestDetail = buildOptionRequestDetail(res, commodityCodes);
						this.transBookingOpitonInfo(res, this.bookingRequestDetail.currency);
						this.handleDataLoading();
					},
					error: () => {
						this.handleDataLoading();
					},
				});
			});
		}
	}

	private transBookingOpitonInfo(bookingDetail: BookingOptionRequestDetailObj, currenUnit: string) {
		if (bookingDetail.bookingOptionList) {
			for (const item of bookingDetail.bookingOptionList) {
				const transportList: Transport[] = [];

				if (item.transportLegsList) {
					for (const trans of item.transportLegsList) {
						transportList.push({
							departureLocation: trans.departureLocation,
							arrivalLocation: trans.arrivalLocation,
							departureDate: trans.departureDate,
							arrivalDate: trans.arrivalDate,
							carrier: trans.transportIdentifier,
							sequenceNumber: trans.legNumber,
						});
					}
					const ailrLineDetail = {
						id: item.id,
						airlinesName: item.carrier?.companyName,
						totalPrice: {
							numericalValue: item.grandTotal,
							currencyUnit: currenUnit,
						},
						priceList: item.priceList,
						transportVoList: transportList,
					};
					this.bookingAirlineDetails.push(ailrLineDetail);
					if (item.isChoose) {
						this.selectedOption = ailrLineDetail;
						return;
					}
				}
			}
		}
	}

	setBookingSelectedOption(selectedId: string) {
		const newAirlineArray: BookingAirlineDetailObj[] = [];
		for (const item of this.bookingAirlineDetails) {
			if (item.id === selectedId) {
				this.selectedOption = item;
				this.selectedOption.selected = true;
			}
			newAirlineArray.push({ ...item, selected: item.id === selectedId });
		}
		this.bookingAirlineDetails = newAirlineArray;

		for (const item of this.bookingOptionDetail?.bookingOptionList ?? []) {
			item.isChoose = item.id === selectedId;
		}
	}
}
