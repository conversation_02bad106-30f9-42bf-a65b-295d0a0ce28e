import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MatPaginatorModule, MatPaginatorIntl, PageEvent } from '@angular/material/paginator';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { SliListObject } from '../../models/sli-list-object.model';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatSortModule, Sort } from '@angular/material/sort';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { Modules, UserPermission, UserRole } from '@shared/models/user-role.model';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { AsyncPipe } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { forkJoin } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { AddPieceDialogComponent } from '../add-piece-dialog/add-piece-dialog.component';
import { SLI_TAB_INDEX } from '@shared/models/constant';
import { OrllCopyDirective } from '@shared/directive/orll-copy.directive';
import { RetrieveComponent } from '@shared/components/biz-logic/retrieve/retrieve.component';
import { LogisticObjType } from '@shared/models/share-type.model';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';

@Component({
	selector: 'orll-sli-table',
	templateUrl: './sli-table.component.html',
	styleUrls: ['./sli-table.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatTableModule,
		MatSortModule,
		MatButtonModule,
		MatMenuModule,
		MatIconModule,
		MatPaginatorModule,
		TranslateModule,
		RouterModule,
		AsyncPipe,
		OrllCopyDirective,
		IataDateFormatPipe,
	],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class SliTableComponent extends RolesAwareComponent implements OnChanges, OnInit {
	@Input() records: SliListObject[] = [];
	@Input() totalRecords = 0;
	@Input() pageParams!: PaginationRequest;
	@Input() fromCreateHawb = false;

	@Output() shareSli: EventEmitter<SliListObject> = new EventEmitter<SliListObject>();
	@Output() sortChange = new EventEmitter<Sort>();
	@Output() pagination = new EventEmitter<PageEvent & { sortField?: string; sortDirection?: string }>();
	@Output() newPieceRequest = new EventEmitter<{ pieceType: string; pieceId?: string }>();

	currentSort: Sort = { active: '', direction: '' };

	displayedColumns: string[] = ['waybillNumber', 'shipper', 'consignee', 'goodsDescription', 'departureLocation', 'arrivalLocation'];
	readonly tablePageSizes: number[] = [10, 50, 100];

	dataSource = new MatTableDataSource<SliListObject>(this.records || []);

	readonly sliModule = Modules.SLI;
	readonly createPermission = UserPermission.CREATE;
	readonly sharePermission = UserPermission.SHARE;
	readonly shipperRoles: string[] = [UserRole.SHIPPER];
	readonly forwarderRoles: string[] = [UserRole.FORWARDER];

	constructor(
		private readonly router: Router,
		private readonly route: ActivatedRoute,
		private readonly dialog: MatDialog
	) {
		super();
	}

	ngOnInit(): void {
		const roleChecks$ = forkJoin({
			hasShipperRole: this.hasSomeRole(this.shipperRoles),
			hasForwarderRole: this.hasSomeRole(this.forwarderRoles),
			hasSharePermission: this.hasPermission(this.sharePermission, this.sliModule),
		});

		roleChecks$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(({ hasShipperRole, hasForwarderRole, hasSharePermission }) => {
			const newColumns = [...this.displayedColumns];

			if (hasShipperRole) {
				newColumns.push('createDate');
			}
			if (hasForwarderRole) {
				newColumns.push('receivedFrom');
			}

			newColumns.push('hawbNumber');

			if (hasSharePermission && !this.fromCreateHawb) {
				newColumns.push('share');
			}

			this.displayedColumns = newColumns;
		});
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.dataSource.data = this.records;
		}
	}

	onSortChange(sort: Sort): void {
		this.currentSort = sort;
		this.sortChange.emit(sort);
		this.emitPaginationWithSort();
	}

	private emitPaginationWithSort(event?: PageEvent) {
		const pageEvent = event || {
			pageIndex: this.pageParams.pageNum - 1,
			pageSize: this.pageParams.pageSize,
			length: this.totalRecords,
		};

		this.pagination.emit({
			...pageEvent,
			sortField: this.currentSort.active,
			sortDirection: this.currentSort.direction,
		});
	}

	createSli(): void {
		const dialogRef = this.dialog.open(AddPieceDialogComponent, {
			width: '400px',
			data: {},
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (result) {
				this.newPieceRequest.emit({ pieceType: result });
			} else {
				this.router.navigate(['create', SLI_TAB_INDEX], {
					relativeTo: this.route,
				});
			}
		});
	}

	editSli(waybillNumber: string): void {
		this.router.navigate(['edit', waybillNumber, 0], {
			relativeTo: this.route,
		});
	}

	// eslint-disable-next-line
	trackBySliCode(_index: number, record: SliListObject): string {
		return record.waybillNumber + record.createDate;
	}

	shareSliObject(event: SliListObject) {
		this.shareSli.emit(event);
	}

	retrieveObj() {
		this.dialog.open(RetrieveComponent, {
			width: '40vw',
			autoFocus: false,
			data: LogisticObjType.SLI,
		});
	}
}
