import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { CodeName } from '@shared/models/code-name.model';
import { startWith } from 'rxjs';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { Piece } from '../../models/piece/piece.model';
import { PieceType } from '../../models/piece/piece-type.model';

const REGX_NUMBER_1_DECIMAL = /^([1-9]\d*(\.\d)?|0\.[1-9])$/;

@Component({
	selector: 'orll-sli-piece-form',
	templateUrl: './sli-piece-form.component.html',
	styleUrl: './sli-piece-form.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatInputModule,
		MatSelectModule,
		MatIconModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		CommonModule,
		MatAutocompleteModule,
	],
})
export class SliPieceFormComponent extends DestroyRefComponent implements OnInit, OnChanges {
	@Input() piece: Piece | null = null;

	packagingTypes: CodeName[] = [];
	filteredPackagingTypes: CodeName[] = [];
	nvdForCustoms: string[] = [DropDownType.NCV, DropDownType.YES];
	nvdForCarriage: string[] = [DropDownType.NVD, DropDownType.YES];

	sliPieceForm: FormGroup = new FormGroup({
		productDescription: new FormControl<string>('', [Validators.required]),
		hsCommodityDescription: new FormControl<string>(''),
		packagingType: new FormControl<string>('', [Validators.required]),
		packagedIdentifier: new FormControl<string>(''),
		grossWeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimLength: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimWidth: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimHeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		nvdForCustoms: new FormControl<string>(DropDownType.NCV),
		nvdForCarriage: new FormControl<string>(DropDownType.NVD),
		upid: new FormControl<string>(''),
		shippingMarks: new FormControl<string>(''),
		textualHandlingInstructions: new FormControl<string>(''),
	});

	constructor(private readonly sliCreateRequestService: SliCreateRequestService) {
		super();
	}

	ngOnInit(): void {
		this.initRefData();
		this.setupAutocomplete();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['piece']) {
			this.fillPieceForm();
		}
	}

	private initRefData(): void {
		this.sliCreateRequestService.getPackingTypes().subscribe((packagingTypes: CodeName[]) => {
			this.packagingTypes = packagingTypes;
			this.filteredPackagingTypes = packagingTypes;
		});
	}

	private setupAutocomplete(): void {
		this.sliPieceForm
			.get('packagingType')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredPackagingTypes = this.packagingTypes.filter((packagingTypes) =>
					packagingTypes.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
				);
			});
	}

	displayPackagingTypeName = (code: string): string => {
		const type = this.packagingTypes.find((item) => item.code === code);
		return type?.name ?? '';
	};

	getNvdStatus(piece: Piece | null, type: string): string {
		if (!piece) return type;

		if (piece.nvdForCustoms && type === DropDownType.NCV) {
			return DropDownType.NCV;
		} else if (piece.nvdForCarriage && type === DropDownType.NVD) {
			return DropDownType.NVD;
		} else {
			return DropDownType.YES;
		}
	}

	fillPieceForm(): void {
		this.sliPieceForm.patchValue({
			productDescription: this.piece?.product.description ?? '',
			hsCommodityDescription: this.piece?.product.hsCommodityDescription ?? '',
			packagingType: this.piece?.packagingType.typeCode ?? '',
			packagedIdentifier: this.piece?.packagedIdentifier ?? '',
			grossWeight: this.piece?.grossWeight ?? '',
			dimLength: this.piece?.dimensions.length ?? '',
			dimWidth: this.piece?.dimensions.width ?? '',
			dimHeight: this.piece?.dimensions.height ?? '',
			nvdForCustoms: this.getNvdStatus(this.piece, DropDownType.NCV),
			nvdForCarriage: this.getNvdStatus(this.piece, DropDownType.NVD),
			upid: this.piece?.upid ?? '',
			shippingMarks: this.piece?.shippingMarks ?? '',
			textualHandlingInstructions: this.piece?.textualHandlingInstructions ?? '',
		});
	}

	// eslint-disable-next-line
	getFormData(ignore?: boolean): {} | null {
		if (!ignore && this.sliPieceForm.invalid) {
			return null;
		}
		return {
			type: PieceType.GENERAL,
			product: {
				description: this.sliPieceForm.value.productDescription,
				hsCommodityDescription: this.sliPieceForm.value.hsCommodityDescription,
			},
			packagingType: {
				typeCode: this.sliPieceForm.value.packagingType,
				description: this.displayPackagingTypeName(this.sliPieceForm.value.packagingType),
			},
			packagedIdentifier: this.sliPieceForm.value.packagedIdentifier,
			grossWeight: this.sliPieceForm.value.grossWeight ? Number(this.sliPieceForm.value.grossWeight) : '',
			dimensions: {
				length: this.sliPieceForm.value.dimLength ? Number(this.sliPieceForm.value.dimLength) : '',
				width: this.sliPieceForm.value.dimWidth ? Number(this.sliPieceForm.value.dimWidth) : '',
				height: this.sliPieceForm.value.dimHeight ? Number(this.sliPieceForm.value.dimHeight) : '',
			},
			nvdForCustoms: this.sliPieceForm.value.nvdForCustoms === 'NCV',
			nvdForCarriage: this.sliPieceForm.value.nvdForCarriage === 'NVD',
			upid: this.sliPieceForm.value.upid,
			shippingMarks: this.sliPieceForm.value.shippingMarks,
			textualHandlingInstructions: this.sliPieceForm.value.textualHandlingInstructions,
		};
	}
}
