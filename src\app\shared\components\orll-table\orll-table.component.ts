// src/app/components/generic-table/generic-table.component.ts
import {
	Component,
	Input,
	ViewChild,
	AfterViewInit,
	Output,
	EventEmitter,
	OnChanges,
	SimpleChanges,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from '@angular/core';
import { MatPaginatorIntl, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { OrllColumnDef } from '@shared/models/orlll-common-table';
import { GenericTableService } from '@shared/services/table/orll-table.interface';
import { SpinnerComponent } from '../spinner/spinner.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { OrllCopyDirective } from '@shared/directive/orll-copy.directive';

@Component({
	selector: 'orll-table',
	templateUrl: './orll-table.component.html',
	styleUrls: ['./orll-table.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		TranslateModule,
		MatTableModule,
		MatPaginatorModule,
		SpinnerComponent,
		MatSortModule,
		MatIconModule,
		MatButtonModule,
		OrllCopyDirective,
	],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class OrllTableComponent<T> implements AfterViewInit, OnChanges {
	@Input() service!: GenericTableService<T>;
	@Input() columns: OrllColumnDef<T>[] = [];
	@Input() hasPagination = true;
	@Input() hasMoreData = false;
	@Input() enableInfiniteScroll = false;
	@Input() param: any;
	@Output() rowClick = new EventEmitter<T>();

	displayedColumns: string[] = [];
	dataSource = new MatTableDataSource<T>();
	totalRecords = 0;
	pageSize = 10;
	pageIndex = 0;
	readonly tablePageSizes: number[] = [10, 50, 100];
	sortField = '';
	sortDirection: 'asc' | 'desc' = 'desc';

	queryParam: any;
	private isViewInit = false;

	dataLoading = false;

	@ViewChild(MatSort) sort!: MatSort;

	constructor(private readonly cdr: ChangeDetectorRef) {}

	ngAfterViewInit(): void {
		this.displayedColumns = this.columns.map((col) => col.key as string);
		this.dataSource.sort = this.sort;
		this.isViewInit = true;
		this.loadData();
	}

	ngOnChanges(changes: SimpleChanges): void {
		// param obj will be changed.
		if (changes['param']) {
			this.queryParam = { ...this.param };
			if (this.isViewInit) {
				this.loadData();
			}
		}
		if (changes['columns']) {
			this.displayedColumns = this.columns.map((col) => col.key as string);
		}
	}

	loadData(): void {
		this.dataLoading = true;
		if (this.hasPagination) {
			let request = {
				pageNum: this.pageIndex + 1,
				pageSize: this.pageSize,
				orderByColumn: this.sortField,
				isAsc: this.sortDirection,
			};

			if (this.param) {
				request = { ...request, ...this.queryParam };
			}

			this.service.getDataPerPage(request).subscribe({
				next: (response) => {
					if (this.enableInfiniteScroll) {
						this.dataSource.data = [...this.dataSource.data, ...response.rows];
						this.hasMoreData = this.dataSource.data.length < response.total;
					} else {
						this.dataSource.data = response.rows;
					}
					this.totalRecords = response.total;
					this.handleDataLoading();
				},
				error: (err) => {
					console.error('orll table data retriving error', err);
					this.handleDataLoading();
				},
			});
		} else {
			const request = this.param || {};
			this.service.loadAllData(request).subscribe({
				next: (res) => {
					this.dataSource.data = res;
					this.handleDataLoading();
				},
				error: () => {
					this.handleDataLoading();
				},
			});
		}
	}

	handleDataLoading(): void {
		this.dataLoading = false;
		this.cdr.markForCheck();
	}

	onPageChange(event: any): void {
		this.pageIndex = event.pageIndex;
		this.pageSize = event.pageSize;
		this.loadData();
	}

	onSortChange(sortState: Sort): void {
		if (sortState.direction) {
			this.sortField = sortState.active;
			this.sortDirection = sortState.direction;
		} else {
			this.sortField = '';
			this.sortDirection = 'asc';
		}
		this.pageIndex = 0;
		this.loadData();
	}

	onRowClicked(row: T): void {
		this.rowClick.emit(row);
	}

	getCellValue(column: OrllColumnDef<T>, row: T): any {
		let value: any;
		if (column.accessor) {
			value = column.accessor(row);
		} else {
			value = row[column.key as keyof T];
		}
		return column.transform ? column.transform(value) : value;
	}

	onTableScroll(event: Event): void {
		const container = event.target as HTMLDivElement;
		const scrollTop = container.scrollTop;
		const scrollHeight = container.scrollHeight;
		const clientHeight = container.clientHeight;

		const isAtBottom = scrollHeight - (scrollTop + clientHeight) === 0;

		if (this.enableInfiniteScroll && isAtBottom && !this.dataLoading && this.hasMoreData) {
			this.loadData();
		}
	}
}
