.booking-option-request {
	color: var(--iata-grey-600);
	&__sub-title {
		padding: 10px 0px 15px 0px;
	}

	.row {
		gap: 10px;
		margin-left: 0px;
		margin-right: 0px;
	}
	.dimensions {
		width: 50%;
		max-width: 100%;
	}
	.lwh {
		width: 33%;
	}
	.width-100 {
		width: 100%;
	}
	.width-20 {
		width: 20%;
	}
	.width-35 {
		width: 35%;
	}
	.width-55 {
		width: 55%;
	}
	.label {
		color: var(--iata-grey-400);
		font-size: 12px;
		padding-left: 10px;
	}
	.value {
		display: flex;
		gap: 4rem;
		padding: 20px 0px 20px 10px;
	}
	.dimension-value {
		display: flex;
		gap: 2rem;
		padding: 20px 0px 20px 10px;
	}
}
