.orll-partner-show {
	padding: 20px;
	&__introduction {
		color: var(--iata-grey-500);
	}
	.iata-box {
		position: relative;
		min-height: 200px;
		padding: 0;
	}
	&__create {
		display: flex;
		justify-content: flex-end;
	}
	.title {
		flex: 0 0 400px !important;
		margin-bottom: 10px;
	}
	&__mat-head{
		background-color:var(--iata-grey-50) !important;
		color: var(--iata-grey-300)
	}
	&__mat-cell {
		display: flex;
		align-items: center;
		height: 100%;
		color:var(--iata-grey-600)
	}
	&__table {
		width: 100%;
		border: 1px solid var(--iata-grey-200);

	}

	.controls {
		margin-top: 50px;
		border: 1px solid var(--iata-grey-200);
		padding: 10px;
		display: flex;
		justify-content: flex-end;
		background-color:var(--iata-white);
		border-bottom: none;
	}
	button {
		display: flex;
		justify-content: flex-end;
		margin-left: 15px;
		padding: 10px 20px;

	}
	.custom-select {
		border: 1px solid var(--iata-grey-200);
		padding: 4px !important;
		width: 200px;
		font-size: 14px;
		border-radius: 5px;
	}

}

