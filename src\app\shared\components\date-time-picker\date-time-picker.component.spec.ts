import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { DateTimePickerComponent } from './date-time-picker.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FormsModule } from '@angular/forms';
import { DateTime } from 'luxon';
import { ElementRef } from '@angular/core';
import { UserProfileService } from '@shared/services/user-profile.service';
import { of } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';

describe('DateTimePickerComponent', () => {
	let component: DateTimePickerComponent;
	let fixture: ComponentFixture<DateTimePickerComponent>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;

	beforeEach(async () => {
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));
		await TestBed.configureTestingModule({
			imports: [
				DateTimePickerComponent,
				MatDatepickerModule,
				MatFormFieldModule,
				MatInputModule,
				NoopAnimationsModule,
				FormsModule,
				TranslateModule.forRoot(),
			],
			providers: [{ provide: UserProfileService, useValue: userProfileServiceSpy }],
		}).compileComponents();

		fixture = TestBed.createComponent(DateTimePickerComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should initialize with current date and time if no value provided', () => {
			component.ngOnInit();
			expect(component.selectedDate).toBeNull();
			expect(component.selectedTime).toBe('00:00');
		});
	});

	describe('writeValue', () => {
		it('should set selectedDate and selectedTime when DateTime value is provided', () => {
			const testDate = DateTime.fromISO('2023-01-01T12:00:00');
			spyOn(component as any, 'updateDisplayValue');

			component.writeValue(testDate);

			expect(component.selectedDate?.toISODate()).toEqual(testDate.toISODate());
			expect(component.selectedTime).toEqual('12:00');
			expect((component as any).updateDisplayValue).toHaveBeenCalled();
		});

		it('should parse string value in DATE_TIME_FORMAT', () => {
			const dateString = '2023-01-01 14:30:00'; // yyyy-MM-dd HH:mm:ss format
			spyOn(component as any, 'updateDisplayValue');

			component.writeValue(dateString as any);

			expect(component.selectedDate).toBeTruthy();
			expect(component.selectedTime).toEqual('14:30');
			expect((component as any).updateDisplayValue).toHaveBeenCalled();
		});

		it('should parse string value in ISO format when DATE_TIME_FORMAT fails', () => {
			const isoString = '2023-01-01T14:30:00';
			spyOn(component as any, 'updateDisplayValue');

			component.writeValue(isoString as any);

			expect(component.selectedDate).toBeTruthy();
			expect(component.selectedTime).toEqual('14:30');
			expect((component as any).updateDisplayValue).toHaveBeenCalled();
		});

		it('should use default time when value is null', () => {
			spyOn(component as any, 'updateDisplayValue');

			component.writeValue(null);

			expect(component.selectedDate).toBeNull();
			expect(component.selectedTime).toEqual('00:00');
			expect((component as any).updateDisplayValue).toHaveBeenCalled();
		});

		it('should handle empty string value', () => {
			spyOn(component as any, 'updateDisplayValue');

			component.writeValue('' as any);

			expect(component.selectedDate as any).toBe(''); // Empty string is assigned directly
			expect(component.selectedTime).toEqual('00:00'); // Because empty string is falsy
			expect((component as any).updateDisplayValue).toHaveBeenCalled();
		});
	});

	describe('onDateChange', () => {
		it('should update selectedDate when valid date is provided', () => {
			const testDate = new Date('2023-01-01');
			component.onDateChange(testDate);
			expect(component.selectedDate?.toISODate()).toEqual('2023-01-01');
		});

		it('should use current date when null is provided', () => {
			const currentDate = DateTime.now();
			component.onDateChange(null);
			expect(component.selectedDate?.hasSame(currentDate, 'day')).toBeTrue();
		});

		it('should call updateDisplayValue', () => {
			spyOn(component as any, 'updateDisplayValue');
			component.onDateChange(new Date());
			expect((component as any).updateDisplayValue).toHaveBeenCalled();
		});
	});

	describe('onTimeChange', () => {
		it('should update selectedTime and call updateDisplayValue', () => {
			const event = { target: { value: '14:30' } } as unknown as Event;
			spyOn(component as any, 'updateDisplayValue');

			component.onTimeChange(event);

			expect(component.selectedTime).toEqual('14:30');
			expect((component as any).updateDisplayValue).toHaveBeenCalled();
		});

		it('should apply pending selection', () => {
			const event = { target: { value: '14:30' } } as unknown as Event;
			spyOn(component.picker, '_applyPendingSelection' as any);

			component.onTimeChange(event);

			expect(component.picker['_applyPendingSelection']).toHaveBeenCalled();
		});
	});

	describe('updateDisplayValue', () => {
		it('should set displayValue to null when no date is selected', () => {
			component.selectedDate = null;
			spyOn(component as any, 'emitValue');

			(component as any).updateDisplayValue();

			expect(component.displayValue).toBeNull();
			expect((component as any).emitValue).toHaveBeenCalled();
		});

		it('should clear input element when no date is selected', fakeAsync(() => {
			component.selectedDate = null;
			component.pickerInput = {
				nativeElement: { value: 'some value', dispatchEvent: jasmine.createSpy() },
			} as unknown as ElementRef<HTMLInputElement>;

			(component as any).updateDisplayValue();
			tick();

			expect(component.pickerInput.nativeElement.value).toBe('');
			expect(component.pickerInput.nativeElement.dispatchEvent).toHaveBeenCalled();
		}));

		it('should combine date and time correctly', () => {
			component.selectedDate = DateTime.fromISO('2023-01-01');
			component.selectedTime = '14:30';
			spyOn(component as any, 'emitValue');

			(component as any).updateDisplayValue();

			expect(component.displayValue).toContain('01/01/2023 14:30');
			expect((component as any).emitValue).toHaveBeenCalled();
		});

		it('should update input element value when date is selected', fakeAsync(() => {
			component.selectedDate = DateTime.fromISO('2023-01-01');
			component.selectedTime = '14:30';

			// Need to set up the pickerInput in the test
			component.pickerInput = {
				nativeElement: { value: '', dispatchEvent: jasmine.createSpy() },
			} as unknown as ElementRef<HTMLInputElement>;

			(component as any).updateDisplayValue();
			tick();

			expect(component.pickerInput.nativeElement.value).toContain('01/01/2023 14:30');
			expect(component.pickerInput.nativeElement.dispatchEvent).toHaveBeenCalled();
		}));

		it('should handle missing pickerInput gracefully', () => {
			component.selectedDate = DateTime.fromISO('2023-01-01');
			component.selectedTime = '14:30';
			component.pickerInput = undefined as any;
			spyOn(component as any, 'emitValue');

			expect(() => (component as any).updateDisplayValue()).not.toThrow();
			expect((component as any).emitValue).toHaveBeenCalled();
		});

		it('should call emitValue', () => {
			component.selectedDate = DateTime.fromISO('2023-01-01');
			spyOn(component as any, 'emitValue');

			(component as any).updateDisplayValue();

			expect((component as any).emitValue).toHaveBeenCalled();
		});
	});

	describe('emitValue', () => {
		it('should emit combined date and time', () => {
			component.selectedDate = DateTime.fromISO('2023-01-01');
			component.selectedTime = '14:30';
			spyOn(component.valueChange, 'emit');

			(component as any).emitValue();

			const expectedDate = DateTime.fromISO('2023-01-01T14:30:00');
			expect(component.valueChange.emit).toHaveBeenCalledWith(expectedDate);
		});

		it('should call onChange and onTouched when date is selected', () => {
			component.selectedDate = DateTime.fromISO('2023-01-01');
			(component as any).onChange = jasmine.createSpy();
			(component as any).onTouched = jasmine.createSpy();

			(component as any).emitValue();

			expect((component as any).onChange).toHaveBeenCalled();
			expect((component as any).onTouched).toHaveBeenCalled();
		});

		it('should emit null when no date is selected', () => {
			component.selectedDate = null;
			spyOn(component.valueChange, 'emit');
			(component as any).onChange = jasmine.createSpy();
			(component as any).onTouched = jasmine.createSpy();

			(component as any).emitValue();

			expect(component.valueChange.emit).toHaveBeenCalledWith(null);
			expect((component as any).onChange).toHaveBeenCalledWith(null);
			expect((component as any).onTouched).toHaveBeenCalled();
		});
	});

	describe('eraseValue', () => {
		it('should prevent default and stop propagation', () => {
			const mockEvent = jasmine.createSpyObj('Event', ['preventDefault', 'stopPropagation']);

			component.eraseValue(mockEvent);

			expect(mockEvent.preventDefault).toHaveBeenCalled();
			expect(mockEvent.stopPropagation).toHaveBeenCalled();
		});

		it('should clear selectedDate and reset selectedTime', () => {
			component.selectedDate = DateTime.fromISO('2023-01-01');
			component.selectedTime = '14:30';
			const mockEvent = jasmine.createSpyObj('Event', ['preventDefault', 'stopPropagation']);

			component.eraseValue(mockEvent);

			expect(component.selectedDate).toBeNull();
			expect(component.selectedTime).toBe('00:00');
		});

		it('should call updateDisplayValue', () => {
			const mockEvent = jasmine.createSpyObj('Event', ['preventDefault', 'stopPropagation']);
			spyOn(component as any, 'updateDisplayValue');

			component.eraseValue(mockEvent);

			expect((component as any).updateDisplayValue).toHaveBeenCalled();
		});
	});

	describe('ngAfterViewInit', () => {
		it('should call updateDisplayValue', () => {
			spyOn(component as any, 'updateDisplayValue');

			component.ngAfterViewInit();

			expect((component as any).updateDisplayValue).toHaveBeenCalled();
		});

		it('should subscribe to picker closedStream', () => {
			spyOn(component.picker.closedStream, 'pipe').and.returnValue({
				subscribe: jasmine.createSpy('subscribe'),
			} as any);

			component.ngAfterViewInit();

			expect(component.picker.closedStream.pipe).toHaveBeenCalled();
		});
	});

	describe('ControlValueAccessor methods', () => {
		describe('registerOnChange', () => {
			it('should register onChange callback', () => {
				const mockCallback = jasmine.createSpy('onChange');

				component.registerOnChange(mockCallback);

				expect((component as any).onChange).toBe(mockCallback);
			});
		});

		describe('registerOnTouched', () => {
			it('should register onTouched callback', () => {
				const mockCallback = jasmine.createSpy('onTouched');

				component.registerOnTouched(mockCallback);

				expect((component as any).onTouched).toBe(mockCallback);
			});
		});

		describe('setDisabledState', () => {
			it('should set disable property to true', () => {
				component.setDisabledState(true);

				expect(component.selection.disabled).toBeTrue();
			});

			it('should set disable property to false', () => {
				component.setDisabledState(false);

				expect(component.selection.disabled).toBeFalse();
			});
		});
	});

	describe('onBlur', () => {
		it('should call onTouched callback', () => {
			(component as any).onTouched = jasmine.createSpy('onTouched');

			component.onBlur();

			expect((component as any).onTouched).toHaveBeenCalled();
		});
	});

	describe('closedStream subscription', () => {
		it('should reopen picker when closed without selection', fakeAsync(() => {
			spyOn(component.picker, 'open');

			// Simulate closing without selection
			component.selectedDate = null;
			(component.picker.closedStream as any).next();
			tick();

			expect(component.picker.open).toHaveBeenCalled();
		}));

		it('should not reopen picker when closed with selection', fakeAsync(() => {
			spyOn(component.picker, 'open');

			// Simulate closing with selection
			component.selectedDate = DateTime.now();
			(component.picker.closedStream as any).next();
			tick();

			expect(component.picker.open).not.toHaveBeenCalled();
		}));
	});
});
