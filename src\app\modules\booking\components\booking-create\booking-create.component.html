<div class="booking-create">
	<div class="booking-create__detail iata-box">
		<orll-booking-option-request-form
			[fromBooking]="true"
			(chargeableWeightChange)="updateGrandTotal(optionForm.controls.options.controls[0])"></orll-booking-option-request-form>
	</div>
	<div class="booking-create__option">
		<form [formGroup]="optionForm">
			<div formArrayName="options">
				@for (bookingOption of optionForm.controls.options.controls; track $index) {
					<div class="iata-box">
						<div class="row sub-title">
							<div class="col-1">{{ 'booking.dialog.title.option' | translate }}</div>
							@if (!bookingOption.controls.showOption.value) {
								<div class="col-8">
									{{ 'booking.option.price.title' | translate }}
								</div>
								<div class="col-2 sub-total">
									{{ 'booking.option.grand.total' | translate }}{{ bookingOption.controls.grandTotal.value ?? '' }}
								</div>
							}
							<mat-icon
								(click)="bookingOption.controls.showOption.value = !bookingOption.controls.showOption.value"
								(keydown.enter)="$event.stopPropagation()"
								color="primary"
								class="option-toggle-btn">
								@if (bookingOption.controls.showOption.value) {
									expand_less
								} @else {
									expand_more
								}
							</mat-icon>
						</div>
						@if (bookingOption.controls.showOption.value) {
							<div [formGroupName]="$index" class="booking-create__option-item">
								<div class="row price-summary">
									<div class="col-12">
										{{ 'booking.option.price.title' | translate }}
									</div>
								</div>
								<div formArrayName="priceList">
									@for (price of getPriceList($index).controls; track $index) {
										<div [formGroupName]="$index">
											<div class="booking-create__option-price">
												<div class="row">
													<mat-form-field class="col-2">
														<mat-label>{{ 'booking.option.form.charge.type' | translate }}</mat-label>
														<mat-select formControlName="chargeType">
															@for (code of chargeTypes; track code) {
																<mat-option [value]="code.code">{{ code.name }}</mat-option>
															}
														</mat-select>
														@if (price.get('chargeType').hasError('required')) {
															<mat-error>
																{{
																	'validators.required'
																		| translate
																			: { field: 'booking.option.form.charge.type' | translate }
																}}
															</mat-error>
														}
													</mat-form-field>
													<mat-form-field class="col-2">
														<mat-label>{{ 'booking.option.form.charge.rate.class' | translate }}</mat-label>
														<mat-select formControlName="rateClassCode">
															@for (code of rateClassCodes; track code) {
																<mat-option [value]="code.code">{{ code.name }}</mat-option>
															}
														</mat-select>
														@if (price.get('rateClassCode').hasError('required')) {
															<mat-error>
																{{
																	'validators.required'
																		| translate
																			: { field: 'booking.option.form.charge.rate.class' | translate }
																}}
															</mat-error>
														}
													</mat-form-field>
													<mat-form-field class="col-2">
														<mat-label>{{ 'booking.option.form.rate' | translate }}</mat-label>
														<input matInput formControlName="subTotal" />
														@if (price.get('subTotal').hasError('required')) {
															<mat-error>
																{{
																	'validators.required'
																		| translate: { field: 'booking.option.form.rate' | translate }
																}}
															</mat-error>
														}
														@if (price.get('subTotal').hasError('pattern')) {
															<mat-error>
																{{ 'validators.maxDecimal2' | translate }}
															</mat-error>
														}
													</mat-form-field>

													<mat-form-field class="col-2">
														<mat-label>{{ 'booking.option.form.payment.type' | translate }}</mat-label>
														<mat-select formControlName="chargePaymentType">
															@for (code of chargePaymentTypes; track code) {
																<mat-option [value]="code.code">{{ code.name }}</mat-option>
															}
														</mat-select>
													</mat-form-field>

													<div class="col-4">
														{{ 'booking.option.grand.total' | translate
														}}{{ bookingOption.controls.grandTotal.value ?? '' }}
													</div>
												</div>
											</div>
										</div>
									}
								</div>
								<mat-divider></mat-divider>
								<div formArrayName="transportLegsList" class="booking-create__option-transport">
									<div class="row sub-title">
										{{ 'booking.option.subTitle.itinerary' | translate }}
									</div>
									@for (tran of getTransportLegsList($index).controls; track $index) {
										<div [formGroupName]="$index">
											<div class="row">
												<mat-form-field appearance="outline" class="col-2" floatLabel="always">
													<mat-label>{{ 'booking.option.form.departure' | translate }}</mat-label>
													<mat-select formControlName="departureLocation">
														@for (code of locationList; track code) {
															<mat-option [value]="code.code">{{ code.name }}</mat-option>
														}
													</mat-select>
													@if (tran.get('departureLocation').hasError('required')) {
														<mat-error>
															{{
																'validators.required'
																	| translate: { field: 'booking.option.form.departure' | translate }
															}}
														</mat-error>
													}
												</mat-form-field>
												<mat-form-field appearance="outline" class="col-2" floatLabel="always">
													<mat-label>{{ 'booking.option.form.arrival' | translate }}</mat-label>
													<mat-select formControlName="arrivalLocation">
														@for (code of locationList; track code) {
															<mat-option [value]="code.code">{{ code.name }}</mat-option>
														}
													</mat-select>
													@if (tran.get('arrivalLocation').hasError('required')) {
														<mat-error>
															{{
																'validators.required'
																	| translate: { field: 'booking.option.form.arrival' | translate }
															}}
														</mat-error>
													}
												</mat-form-field>
												<mat-form-field class="col-2">
													<mat-label>{{ 'booking.option.form.airline.code' | translate }}</mat-label>
													<input matInput formControlName="airlineCode" />
												</mat-form-field>
												<mat-form-field class="col-2">
													<mat-label>{{ 'booking.option.form.flight.number' | translate }}</mat-label>
													<input matInput formControlName="transportIdentifier" />
													@if (tran.get('transportIdentifier').hasError('required')) {
														<mat-error>
															{{
																'validators.required'
																	| translate: { field: 'booking.option.form.flight.number' | translate }
															}}
														</mat-error>
													}
												</mat-form-field>
											</div>
											<div class="row">
												<div class="col-2">
													<orll-date-time-picker
														[label]="'booking.option.form.departure.time'"
														formControlName="departureDate"
														[validatorFn]="validators.required"></orll-date-time-picker>
												</div>
												<div class="col-2">
													<orll-date-time-picker
														[label]="'booking.option.form.arrival.time'"
														formControlName="arrivalDate"></orll-date-time-picker>
												</div>
											</div>
										</div>
									}
								</div>
							</div>
						}
					</div>
				}
			</div>
		</form>
	</div>
	<div class="row booking-create__btn-panel">
		<button mat-stroked-button color="primary" (click)="onCancel()" class="booking-create__cancel-button">
			{{ 'sli.mgmt.cancel' | translate }}
		</button>
		<button mat-flat-button color="primary" (click)="saveBooking()" class="booking-create__save-button">
			<mat-icon>save</mat-icon>
			{{ 'booking.mgmt.save' | translate }}
		</button>
	</div>
</div>

@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
