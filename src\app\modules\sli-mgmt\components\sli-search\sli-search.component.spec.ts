import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SliSearchComponent } from './sli-search.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { DatePipe } from '@angular/common';
import { SliSearchRequestService } from '../../services/sli-search-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { TranslateModule } from '@ngx-translate/core';
import { noop } from 'rxjs';

describe('SliSearchComponent', () => {
	let component: SliSearchComponent;
	let fixture: ComponentFixture<SliSearchComponent>;
	let mockSearchService: Partial<SliSearchRequestService>;

	beforeEach(async () => {
		mockSearchService = {
			getOptions: jasmine.createSpy('getOptions'),
		};

		await TestBed.configureTestingModule({
			imports: [ReactiveFormsModule, MatDatepickerModule, MatNativeDateModule, TranslateModule.forRoot()],
			providers: [DatePipe, { provide: SliSearchRequestService, useValue: mockSearchService }],
		}).compileComponents();

		fixture = TestBed.createComponent(SliSearchComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('selectedItems', () => {
		const mockItems: CodeName[] = [
			{ code: 'TEST1', name: 'Test Item 1' },
			{ code: 'TEST2', name: 'Test Item 2' },
		];

		it('should set selectedShippers when keyword is shipper', () => {
			component.selectedItems(mockItems, 'shipper');
			expect(component.selectedShippers).toEqual(mockItems);
		});

		it('should set selectedConsignees when keyword is consignee', () => {
			component.selectedItems(mockItems, 'consignee');
			expect(component.selectedConsignees).toEqual(mockItems);
		});

		it('should set selectedDepartureLocations when keyword is departureLocation', () => {
			component.selectedItems(mockItems, 'departureLocation');
			expect(component.selectedDepartureLocations).toEqual(mockItems);
		});

		it('should set selectedArrivalLocations when keyword is arrivalLocation', () => {
			component.selectedItems(mockItems, 'arrivalLocation');
			expect(component.selectedArrivalLocations).toEqual(mockItems);
		});

		it('should set selectedSliCodes when keyword is sliCode', () => {
			component.selectedItems(mockItems, 'sliCode');
			expect(component.selectedSliCodes).toEqual(mockItems);
		});

		it('should set selectedHawbNumbers when keyword is hawbNumber', () => {
			component.selectedItems(mockItems, 'hawbNumber');
			expect(component.selectedHawbNumbers).toEqual(mockItems);
		});

		it('should not set any selected items for unknown keyword', () => {
			const initialShippers = [...component.selectedShippers];
			const initialConsignees = [...component.selectedConsignees];

			component.selectedItems(mockItems, 'unknownKeyword');

			expect(component.selectedShippers).toEqual(initialShippers);
			expect(component.selectedConsignees).toEqual(initialConsignees);
		});
	});

	it('should initialize the sliSearchForm', () => {
		expect(component.sliSearchForm).toBeDefined();
		expect(component.sliSearchForm.controls['goodsDescription']).toBeDefined();
		expect(component.sliSearchForm.controls['startDate']).toBeDefined();
		expect(component.sliSearchForm.controls['endDate']).toBeDefined();
	});

	it('onSearch() the searchSli emit should be allowed when search button is clicked', () => {
		spyOn(component.searchSli, 'emit');

		component.onSearch();

		expect(component.searchSli.emit).toHaveBeenCalled();
	});

	it('onReset() should reset all the form fields and selected items', () => {
		component.sliSearchForm.patchValue({ goodsDescription: 'test', startDate: new Date(), endDate: new Date() });
		component.selectedShippers = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedConsignees = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedSliCodes = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedHawbNumbers = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedDepartureLocations = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedArrivalLocations = [{ code: '1', name: 'Test' }] as CodeName[];

		component.onReset({ preventDefault: noop, stopPropagation: noop } as any);

		expect(component.sliSearchForm.value).toEqual({
			goodsDescription: null,
			startDate: null,
			endDate: null,
		});
		expect(component.selectedShippers).toEqual([]);
		expect(component.selectedConsignees).toEqual([]);
		expect(component.selectedSliCodes).toEqual([]);
		expect(component.selectedHawbNumbers).toEqual([]);
		expect(component.selectedDepartureLocations).toEqual([]);
		expect(component.selectedArrivalLocations).toEqual([]);
	});

	it('selectedItems() should update the selected items', () => {
		const testItems = [{ code: '1', name: 'Test' }] as CodeName[];

		component.selectedItems(testItems, 'shipper');
		expect(component.selectedShippers).toEqual(testItems);

		component.selectedItems(testItems, 'consignee');
		expect(component.selectedConsignees).toEqual(testItems);

		component.selectedItems(testItems, 'sliCode');
		expect(component.selectedSliCodes).toEqual(testItems);

		component.selectedItems(testItems, 'hawbNumber');
		expect(component.selectedHawbNumbers).toEqual(testItems);

		component.selectedItems(testItems, 'departureLocation');
		expect(component.selectedDepartureLocations).toEqual(testItems);

		component.selectedItems(testItems, 'arrivalLocation');
		expect(component.selectedArrivalLocations).toEqual(testItems);
	});

	describe('onSearch - Enhanced Tests', () => {
		it('should emit searchSli with correct payload structure', () => {
			spyOn(component.searchSli, 'emit');

			// Set up comprehensive test data
			component.selectedShippers = [
				{ code: 'SH1', name: 'Shipper 1' },
				{ code: 'SH2', name: 'Shipper 2' },
			];
			component.selectedConsignees = [{ code: 'CN1', name: 'Consignee 1' }];
			component.selectedDepartureLocations = [{ code: 'JFK', name: 'JFK Airport' }];
			component.selectedArrivalLocations = [{ code: 'LHR', name: 'Heathrow Airport' }];
			component.selectedSliCodes = [{ code: 'SLI001', name: 'SLI Code 1' }];
			component.selectedHawbNumbers = [{ code: 'HAWB001', name: 'HAWB Number 1' }];

			const startDate = new Date('2024-01-01');
			const endDate = new Date('2024-01-31');
			component.sliSearchForm.patchValue({
				goodsDescription: 'Electronics',
				startDate: startDate,
				endDate: endDate,
			});

			component.onSearch();

			expect(component.searchSli.emit).toHaveBeenCalledWith({
				goodsDescription: 'Electronics',
				createDateStart: '2024-01-01',
				createDateEnd: '2024-01-31',
				shipperNameList: ['Shipper 1', 'Shipper 2'],
				consigneeNameList: ['Consignee 1'],
				departureLocationList: ['JFK'],
				arrivalLocationList: ['LHR'],
				sliCodeList: ['SLI001'],
				hawbNumberList: ['HAWB001'],
				existHawb: true,
			});
		});

		it('should handle date formatting correctly', () => {
			spyOn(component.searchSli, 'emit');

			const startDate = new Date('2024-12-25');
			const endDate = new Date('2024-12-31');
			component.sliSearchForm.patchValue({
				startDate: startDate,
				endDate: endDate,
			});

			component.onSearch();

			const emittedPayload = (component.searchSli.emit as jasmine.Spy).calls.mostRecent().args[0];
			expect(emittedPayload.createDateStart).toBe('2024-12-25');
			expect(emittedPayload.createDateEnd).toBe('2024-12-31');
		});
	});

	describe('onReset - Enhanced Tests', () => {
		it('should call preventDefault and stopPropagation on event', () => {
			const mockEvent = {
				preventDefault: jasmine.createSpy('preventDefault'),
				stopPropagation: jasmine.createSpy('stopPropagation'),
			} as any;

			component.onReset(mockEvent);

			expect(mockEvent.preventDefault).toHaveBeenCalled();
			expect(mockEvent.stopPropagation).toHaveBeenCalled();
		});

		it('should call eraseValue on all autocomplete components', () => {
			const mockAutocomplete1 = { eraseValue: jasmine.createSpy('eraseValue') };
			const mockAutocomplete2 = { eraseValue: jasmine.createSpy('eraseValue') };
			component.autocompleteList = [mockAutocomplete1, mockAutocomplete2] as any;

			const mockEvent = { preventDefault: noop, stopPropagation: noop } as any;
			component.onReset(mockEvent);

			expect(mockAutocomplete1.eraseValue).toHaveBeenCalledWith(mockEvent);
			expect(mockAutocomplete2.eraseValue).toHaveBeenCalledWith(mockEvent);
		});

		it('should handle empty autocompleteList gracefully', () => {
			// Mock empty QueryList
			const emptyQueryList = {
				forEach: jasmine.createSpy('forEach'),
			} as any;
			component.autocompleteList = emptyQueryList;
			const mockEvent = { preventDefault: noop, stopPropagation: noop } as any;

			expect(() => component.onReset(mockEvent)).not.toThrow();
			expect(emptyQueryList.forEach).toHaveBeenCalled();
		});
	});

	describe('selectedItems - Enhanced Tests', () => {
		it('should handle empty arrays correctly', () => {
			const emptyItems: CodeName[] = [];

			component.selectedItems(emptyItems, 'shipper');
			expect(component.selectedShippers).toEqual([]);

			component.selectedItems(emptyItems, 'consignee');
			expect(component.selectedConsignees).toEqual([]);
		});

		it('should not modify other selected items when updating one type', () => {
			const initialShippers = [{ code: 'SH1', name: 'Shipper 1' }];
			const newConsignees = [{ code: 'CN1', name: 'Consignee 1' }];

			component.selectedShippers = initialShippers;
			component.selectedItems(newConsignees, 'consignee');

			expect(component.selectedShippers).toEqual(initialShippers);
			expect(component.selectedConsignees).toEqual(newConsignees);
		});

		it('should handle case-sensitive keywords correctly', () => {
			const testItems = [{ code: 'TEST', name: 'Test Item' }];

			// Test that case matters
			component.selectedItems(testItems, 'Shipper'); // Capital S
			expect(component.selectedShippers).toEqual([]); // Should not match

			component.selectedItems(testItems, 'shipper'); // lowercase s
			expect(component.selectedShippers).toEqual(testItems); // Should match
		});
	});
});
