<div>
	<div>
		<div class="">
			<div class="mawb-row">
				<div class="col-mawb-fixed">
					<div class="mawb-header-item">
						<div class="d-inline-flex align-items-center item">
							<div>
								<mat-checkbox [checked]="true"></mat-checkbox>
							</div>
							<div>
								MAWB
							</div>
							<div>
								# 3454700322331
							</div>
						</div>
					</div>
				</div>

				<div class="col-lastest-status">
					<div class="d-inline-flex h-full align-items-center">
						<div>Latest Status:</div>
						<a href="PUP">PUP</a>
					</div>
				</div>

				<div class="col-update-date">
					<div class="d-inline-flex h-full align-items-center">
						<div>Update Date:</div>
						<div>DD MM YYYY TT:TT</div>
					</div>
				</div>

				<div class="col-update-by">
					<div class="d-inline-flex h-full align-items-center">
						<div>Update By:</div>
						<div>Forwarder User-1</div>
					</div>
				</div>
			</div>
		</div>

		@for (hawbItem of hawbList; track hawbItem.id) {
			<div class="mt-8 ml-6">
				<div class="bordered rounded">
					<div class="hawb-row relative">
						<div class="col-hawb-fixed">
							<div class="d-inline-flex align-items-center item">
								<div>
									<mat-checkbox [checked]="true"></mat-checkbox>
								</div>
								<div>
									Hawb
								</div>
								<div>
									# 3454700322331
								</div>
							</div>
						</div>

						<div class="col-lastest-status">
							<div class="d-inline-flex h-full align-items-center">
								<div>Latest Status:</div>
								<a href="PUP">PUP</a>
							</div>
						</div>

						<div class="col-update-date">
							<div class="d-inline-flex h-full align-items-center">
								<div>Update Date:</div>
								<div>DD MM YYYY TT:TT</div>
							</div>
						</div>

						<div class="col-update-by">
							<div class="d-inline-flex h-full align-items-center">
								<div>Update By:</div>
								<div>Forwarder User-1</div>
							</div>
						</div>

						<div class="absolute icon-panel-toggle">
							<mat-icon class="autocomplete-arrow"
								(keydown.enter)="toggleExpanded(hawbItem)"
								(click)="toggleExpanded(hawbItem)">
								@if (hawbItem.expanded) {
									keyboard_arrow_down
								} @else {
									keyboard_arrow_up
								}
							</mat-icon>
						</div>
					</div>

					@if (hawbItem.children && hawbItem.children.length > 0 && hawbItem.expanded) {
						@for (piece of hawbItem.children; track piece.id) {
							<div class="piece-level1-wrapper relative" [ngClass]="{'mb-40': $last}">
								<div class="absolute icon-panel-toggle">
									<mat-icon class="autocomplete-arrow"
										(keydown.enter)="toggleExpanded(piece)"
										(click)="toggleExpanded(piece)">
										@if (piece.expanded) {
											keyboard_arrow_down
										} @else {
											keyboard_arrow_up
										}
									</mat-icon>
								</div>
								<div class="piece-level1-row">
									<div class="col-piece-l1-fixed">
										<div class="d-inline-flex align-items-center item">
											<div>
												<mat-checkbox [checked]="true"></mat-checkbox>
											</div>
											<div>
												Piece Descritpion
											</div>
											<div class="ml-2">
												CPU GPU
											</div>
										</div>
									</div>

									@if (!piece.expanded) {
										<div class="col-lastest-status">
											<div class="d-inline-flex h-full align-items-center">
												<div>Latest Status:</div>
												<a href="PUP">PUP</a>
											</div>
										</div>

										<div class="col-update-date">
											<div class="d-inline-flex h-full align-items-center">
												<div>Update Date:</div>
												<div>DD MM YYYY TT:TT</div>
											</div>
										</div>

										<div class="col-update-by">
											<div class="d-inline-flex h-full align-items-center">
												<div>Update By:</div>
												<div>Forwarder User-1</div>
											</div>
										</div>
									}
								</div>

								@if (piece.children && piece.children.length > 0 && piece.expanded) {
									@for (pieceL2Item of piece.children; track pieceL2Item.id) {
										<div class="piece-level2-row">
											<div class="col-piece-l2-fixed">
												<div class="d-inline-flex align-items-center item">
													<div>
														<mat-checkbox [checked]="true"></mat-checkbox>
													</div>
													<div>
														Piece Descritpion
													</div>
													<div class="ml-2">
														CPU GPU
													</div>
												</div>
											</div>

											<div class="col-lastest-status">
												<div class="d-inline-flex h-full align-items-center">
													<div>Latest Status:</div>
													<a href="PUP">PUP</a>
												</div>
											</div>

											<div class="col-update-date">
												<div class="d-inline-flex h-full align-items-center">
													<div>Update Date:</div>
													<div>DD MM YYYY TT:TT</div>
												</div>
											</div>

											<div class="col-update-by">
												<div class="d-inline-flex h-full align-items-center">
													<div>Update By:</div>
													<div>Forwarder User-1</div>
												</div>
											</div>
										</div>
									}
								}
							</div>
						}
					}
				</div>

			</div>
		}
	</div>
</div>
