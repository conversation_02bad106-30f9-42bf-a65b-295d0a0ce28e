import { PaginationRequest } from '@shared/models/pagination-request.model';

export interface MAWBEventObj {
	mawbId: string;
	choseAllHAWB: boolean;
	hawbIdList: HAWBEventObj[];
}

export interface HAWBEventObj {
	hawbId: string;
	choseAllPiece: boolean;
	pieceIdList: string[];
}

export interface BaseEventListObj {
	description?: string;
	latestStatus: string;
	updateTime: string;
	orgName: string;
	userName: string;
	updateBy: string;
	checked: boolean;
	opened: boolean;
}

export interface MAWBEventListObj extends BaseEventListObj {
	mawbId: string;
	code: string;
	eventDate: string;
}

export interface HAWBEventListObj extends BaseEventListObj {
	hawbId: string;
	hawbNumber: string;
	pageNum: number;
	noMorePieces: boolean;
	pieceStatusList: PieceEventListObj[];
}

export interface PieceEventListObj extends BaseEventListObj {
	pieceId: string;
}

export interface MawbStatusPageRequest extends PaginationRequest {
	mawbId: string;
}

export interface HawbStatusPageRequest extends PaginationRequest {
	hawbId: string;
}

export interface MAWBUpdateEventObj extends MAWBEventObj {
	status: string;
	updateTime: string | null;
	eventTimeType: string;
}

export enum EventTimeType {
	ACTUAL,
	PLANNED,
}

export interface StatusHistory {
	latestStatus: string;
	eventDate: string;
	orgName: string;
	userName: string;
	eventTimeType: string;
	partialEventIndicator: boolean;
	eventLoId: string;
}

export interface HistoryDialogData {
	loId: string;
	type: string;
}

export interface StatusUpdateLog {
	bizId: string;
	batchId: string;
	type: string;
	operationStatus: string;
	newValue: string;
	errorMsg: string;
	createDate: string;
	userName: string;
	orgName: string;
}

export enum OperationStatus {
	PENDING = '1',
	SUCCESS = '2',
	FAILED = '3',
}
