<orll-dialog class="orll-ecsd-detail">
	<ng-container dialog-title>
		<span>{{ 'ecsd.view.btn' | translate }}</span>
	</ng-container>

	<div dialog-content class="orll-ecsd-create__content">
		<div class="row info">{{ 'ecsd.info.regulated' | translate }}</div>
		<div class="row">
			<div class="col-6">
				<div class="label">{{ 'ecsd.form.category' | translate }}</div>

				<div class="value">{{ ecsdDetail?.regulatedEntityCategory ?? '' }}</div>
			</div>
			<div class="col-6">
				<div class="label">{{ 'ecsd.form.identifier' | translate }}</div>

				<div class="value">{{ ecsdDetail?.regulatedEntityIdentifier ?? '' }}</div>
			</div>
		</div>
		<div class="row">
			<div class="col-6">
				<div class="label">{{ 'ecsd.form.status' | translate }}</div>
				<div class="value">{{ ecsdDetail?.securityStatus ?? '' }}</div>
			</div>
		</div>
		<mat-divider class="divider"></mat-divider>
		<div class="row info">
			{{ 'ecsd.info.reason' | translate }}
		</div>
		<div class="row">
			<div class="col-4">
				<div class="label">{{ 'ecsd.form.screen' | translate }}</div>
				<div class="value">{{ ecsdDetail?.whetherExemptedForScreening ?? '' }}</div>
			</div>
			<div class="col-4">
				<div class="label">{{ 'ecsd.form.method' | translate }}</div>
				<div class="value">{{ ecsdDetail?.screeningMethod ?? '' }}</div>
			</div>
			<div class="col-4">
				<div class="label">{{ 'ecsd.form.ground' | translate }}</div>
				<div class="value">{{ ecsdDetail?.groundsForExemption ?? '' }}</div>
			</div>
		</div>
		<div class="row">
			<div class="col-4">
				<div class="label">{{ 'ecsd.form.from' | translate }}</div>
				<div class="value">{{ ecsdDetail?.receivedFrom ?? '' }}</div>
			</div>
			<div class="col-4">
				<div class="label">{{ 'ecsd.form.issue.by' | translate }}</div>
				<div>{{ ecsdDetail?.issuedBy ?? '' }}</div>
			</div>
			<div class="col-4">
				<div class="label">{{ 'ecsd.form.issue.on' | translate }}</div>
				<div class="value">{{ ecsdDetail?.issuedOn ?? '' }}</div>
			</div>
		</div>
		<div class="row info">
			{{ 'ecsd.info.accepted' | translate }}
		</div>
		<div class="row">
			<div class="col-6">
				<div class="label">{{ 'ecsd.form.category' | translate }}</div>
				<div class="value">{{ ecsdDetail?.regulatedEntityCategory1 ?? '' }}</div>
			</div>
			<div class="col-6">
				<div class="label">{{ 'ecsd.form.identifier' | translate }}</div>
				<div class="value">{{ ecsdDetail?.regulatedEntityIdentifier1 ?? '' }}</div>
			</div>
		</div>
		<div class="row">
			<div class="col-12">
				<div class="label">{{ 'ecsd.form.information' | translate }}</div>
				<div class="value">{{ ecsdDetail?.additionalSecurityInfo ?? '' }}</div>
			</div>
		</div>

		<div class="orll-ecsd-create__table">
			@if (data.loType === moduleEnum.SLI || data.loType === moduleEnum.HAWB) {
				<table
					mat-table
					[dataSource]="dataSource"
					[trackBy]="trackByPieceId"
					aria-label="Piece table"
					class="orll-ecsd-create__table">
					<ng-container matColumnDef="productDescription">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'sli.piece.table.column.productDescription' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">
							{{ record.productDescription }}
						</td>
					</ng-container>

					<ng-container matColumnDef="packageType">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'sli.piece.table.column.packagingType' | translate }}
						</th>
						<td mat-cell *matCellDef="let record" class="orll-sli-piece-table__cell">{{ record.packageType }}</td>
					</ng-container>

					<ng-container matColumnDef="grossWeight">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'sli.piece.table.column.grossWeight' | translate }}
						</th>
						<td mat-cell *matCellDef="let record" class="orll-sli-piece-table__cell">{{ record.grossWeight }}</td>
					</ng-container>

					<ng-container matColumnDef="dimensions">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'sli.piece.table.column.dimensions' | translate }}
						</th>
						<td mat-cell *matCellDef="let record" class="orll-sli-piece-table__cell">
							{{ record.dimensions?.length }}CMx{{ record.dimensions?.width }}CMx{{ record.dimensions?.height }}CM
						</td>
					</ng-container>

					<ng-container matColumnDef="pieceQuantity">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'sli.piece.table.column.pieceQuantity' | translate }}
						</th>
						<td mat-cell *matCellDef="let record" class="orll-sli-piece-table__cell">{{ record.pieceQuantity }}</td>
					</ng-container>

					<tr mat-header-row *matHeaderRowDef="pieceDisplayedColumns; sticky: true"></tr>
					<tr mat-row *matRowDef="let record; columns: pieceDisplayedColumns" class="orll-sli-piece-table__row"></tr>
				</table>
			}
			@if (data.loType === moduleEnum.MAWB) {
				<table mat-table [dataSource]="dataSource" [trackBy]="trackByHawbId" aria-label=" Hawb table" class="common-scroll-table">
					<ng-container matColumnDef="waybillNumber">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.number' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">
							{{ record.waybillNumber }}
						</td>
					</ng-container>

					<ng-container matColumnDef="shipper">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.shipper' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.shipper }}</td>
					</ng-container>

					<ng-container matColumnDef="consignee">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.consignee' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.consignee }}</td>
					</ng-container>

					<ng-container matColumnDef="goodsDescription">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.description' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">
							{{ record.goodsDescription }}
						</td>
					</ng-container>
					<ng-container matColumnDef="origin">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.origin' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.origin }}</td>
					</ng-container>
					<ng-container matColumnDef="destination">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.destination' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.destination }}</td>
					</ng-container>

					<ng-container matColumnDef="weight">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.weight' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.weight }}</td>
					</ng-container>
					<ng-container matColumnDef="slac">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="table-header">
							{{ 'ecsd.hawb.table.slac' | translate }}
						</th>
						<td mat-cell *matCellDef="let record">{{ record.slac }}</td>
					</ng-container>

					<tr mat-header-row *matHeaderRowDef="hawbDisplayedColumns; sticky: true"></tr>
					<tr mat-row *matRowDef="let record; columns: hawbDisplayedColumns"></tr>
				</table>
			}
		</div>
		@if (dataLoading) {
			<iata-spinner></iata-spinner>
		}
	</div>
	<ng-container dialog-actions>
		<button mat-stroked-button [mat-dialog-close]="'cancel'" color="primary">
			{{ 'common.dialog.cancel' | translate }}
		</button>
		<button mat-flat-button color="primary" [mat-dialog-close]="'cancel'">
			<mat-icon>check</mat-icon>
			{{ 'common.dialog.ok' | translate }}
		</button>
	</ng-container>
</orll-dialog>
