.orll-delegation-request-table {
	&__container {
		min-height: 200px;

		.request-status__link {
			color: var(--iata-blue-primary);
			text-decoration: underline;
			cursor: pointer;
		}
	}

	&__create {
		display: flex;
		justify-content: flex-end;
	}

	&__table-container {
		position: relative;
		height: 600px;
		min-height: 200px;
		max-height: 100vh;
		overflow-y: auto;
		border: 1px solid var(--iata-grey-200);
		border-radius: 4px;
		margin-top: 20px;
	}

	&__table {
		width: 100%;

		.mat-mdc-header-row {
			position: sticky;
			top: 0;
			z-index: 10;
			background-color: var(--iata-grey-50);
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		}

		.mat-mdc-row {
			&:hover {
				background-color: var(--iata-grey-50);
			}

			&:nth-child(even) {
				background-color: var(--iata-grey-50);
			}
		}

		.mat-mdc-cell,
		.mat-mdc-header-cell {
			border-bottom: 1px solid var(--iata-grey-200);
			padding: 12px 16px;
		}
		.orll-sli-piece-table__cell {
			border-bottom: none;
			padding: 0px !important;
		}
	}
	&__tree_col {
		min-height: 56px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0px !important;
	}
	&__tree_col_icon {
		margin-top: 5px;
		font-size: 16px;
	}
	&__tree_col_head {
		width: 10rem;
	}
	&__description {
		padding: 0px;
	}

	&__table_info {
		margin: 10px 0 0 25px;
		margin-right: auto;
		display: flex;
		gap: 1rem;
		align-items: center;

		.label {
			color: var(--iata-grey-300);
		}
	}

	&__delete-button {
		margin: 0 20px !important;
		color: var(--iata-blue-primary) !important;
		border: 1px solid var(--iata-blue-primary);

		&.mat-mdc-button-disabled {
			background-color: var(--iata-blue-grey-50);
			color: var(--iata-grey-300) !important;
			border: 1px solid var(--iata-grey-300);
		}
	}

	&__hawb-tab {
		display: flex;
		margin-bottom: 20px;
		.bold {
			font-size: 18px;
			font-weight: 600;
		}
	}
}
