<div class="orll-piece-consolidate-dialog">
	<h2 mat-dialog-title>{{ 'sli.piece.consolidate.title' | translate }}</h2>
	<mat-dialog-content class="orll-piece-consolidate-dialog_content">
		<form [formGroup]="pieceInfoForm">
			<div class="row">
				<mat-form-field appearance="outline" class="col-12" floatLabel="always">
					<mat-label>{{ 'sli.piece.table.column.productDescription' | translate }}</mat-label>
					<textarea rows="4" matInput formControlName="productDescription"></textarea>
				</mat-form-field>
			</div>
			<div class="row">
				<div class="col-4">
					<mat-form-field appearance="outline" floatLabel="always" class="orll-piece-consolidate-dialog__weight">
						<mat-label>{{ 'sli.piece.table.column.grossWeight' | translate }}</mat-label>
						<div matInput class="d-flex align-items-center width-100">
							<input matInput formControlName="grossWeight" required />
							<div matSuffix class="unit">KG</div>
						</div>
					</mat-form-field>
				</div>
				<div class="col-8 d-inline-flex align-items-center">
					<div class="lwh">
						<mat-form-field appearance="outline" class="width-100" floatLabel="always">
							<mat-label>{{ 'sli.piece.dimensions' | translate }}</mat-label>
							<input
								matInput
								formControlName="dimLength"
								placeholder="{{ 'sli.mgmt.pieceList.dimLength' | translate }}"
								required />
							<span matSuffix class="unit">CM</span>
							@if (pieceInfoForm.get('dimLength')?.hasError('required')) {
								<mat-error>{{ 'sli.mgmt.pieceList.dimLength.required' | translate }}</mat-error>
							}
							@if (pieceInfoForm.get('dimLength')?.hasError('pattern')) {
								<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber1' | translate }}</mat-error>
							}
						</mat-form-field>
					</div>
					<div class="lwh">
						<mat-form-field appearance="outline" class="width-100" floatLabel="always">
							<input
								matInput
								formControlName="dimWidth"
								placeholder="{{ 'sli.mgmt.pieceList.dimWidth' | translate }}"
								required />
							<span matSuffix class="unit">CM</span>
							@if (pieceInfoForm.get('dimWidth')?.hasError('required')) {
								<mat-error>{{ 'sli.mgmt.pieceList.dimWidth.required' | translate }}</mat-error>
							}
							@if (pieceInfoForm.get('dimWidth')?.hasError('pattern')) {
								<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber1' | translate }}</mat-error>
							}
						</mat-form-field>
					</div>
					<div class="lwh">
						<mat-form-field appearance="outline" class="width-100" floatLabel="always">
							<input
								matInput
								formControlName="dimHeight"
								placeholder="{{ 'sli.mgmt.pieceList.dimHeight' | translate }}"
								required />
							<span matSuffix class="unit">CM</span>
							@if (pieceInfoForm.get('dimHeight')?.hasError('required')) {
								<mat-error>{{ 'sli.mgmt.pieceList.dimHeight.required' | translate }}</mat-error>
							}
							@if (pieceInfoForm.get('dimHeight')?.hasError('pattern')) {
								<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber1' | translate }}</mat-error>
							}
						</mat-form-field>
					</div>
				</div>
			</div>

			<div class="row">
				<mat-form-field appearance="outline" class="col-3" floatLabel="always">
					<mat-label> {{ 'sli.piece.table.column.packagingType' | translate }}</mat-label>
					<mat-select formControlName="packagingType">
						@for (opt of packagingTypes; track opt.code) {
							<mat-option [value]="opt.code">{{ opt.name }}</mat-option>
						}
					</mat-select>
				</mat-form-field>
				<mat-form-field appearance="outline" floatLabel="always" class="input-box col-3">
					<mat-label>{{ 'sli.dgPiece.formItem.upid' | translate }}</mat-label>
					<div matInput class="unit-row">
						<input matInput formControlName="upid" />
					</div>
				</mat-form-field>

				<mat-form-field appearance="outline" class="col-3" floatLabel="always">
					<mat-label>{{ 'sli.liveAnimalPiece.formItem.packagedIdentifier' | translate }}</mat-label>
					<input matInput formControlName="packagedIdentifier" />
				</mat-form-field>

				<mat-form-field appearance="outline" floatLabel="always" class="input-box col-3">
					<mat-label>{{ 'sli.piece.hsCommodityDescription' | translate }}</mat-label>
					<div matInput class="unit-row">
						<input matInput formControlName="hsCommodityDescription" />
					</div>
				</mat-form-field>
			</div>

			<div class="row">
				<mat-form-field appearance="outline" class="col-4" floatLabel="always">
					<mat-label>{{ 'sli.dgPiece.formItem.whetherHaveDeclaredValueForCustoms' | translate }}</mat-label>
					<mat-select formControlName="nvdForCustoms">
						<mat-option [value]="true">NCV</mat-option>
						<mat-option [value]="false">Yes</mat-option>
					</mat-select>
				</mat-form-field>

				<mat-form-field appearance="outline" class="col-4" floatLabel="always">
					<mat-label>{{ 'sli.dgPiece.formItem.whetherHaveDeclaredValueForCarriage' | translate }}</mat-label>
					<mat-select formControlName="nvdForCarriage">
						<mat-option [value]="true">NVD</mat-option>
						<mat-option [value]="false">Yes</mat-option>
					</mat-select>
				</mat-form-field>

				<mat-form-field appearance="outline" floatLabel="always" class="input-box col-4">
					<mat-label>{{ 'sli.dgPiece.formItem.shippingMarks' | translate }}</mat-label>
					<div matInput class="unit-row">
						<input matInput formControlName="shippingMarks" />
					</div>
				</mat-form-field>
			</div>

			<div class="row">
				<mat-form-field appearance="outline" class="col-12" floatLabel="always">
					<mat-label>{{ 'sli.dgPiece.formItem.textualHandlingInstructions' | translate }}</mat-label>
					<textarea rows="4" matInput formControlName="textualHandlingInstructions"></textarea>
				</mat-form-field>
			</div>
		</form>
		<div class="row">
			<table mat-table [dataSource]="dataSource" matSort aria-label="Company table">
				<ng-container matColumnDef="pieceDescription">
					<th scope="col" mat-header-cell *matHeaderCellDef>{{ 'sli.piece.table.column.productDescription' | translate }}</th>
					<td mat-cell *matCellDef="let row">
						{{ row.productDescription }}
					</td>
				</ng-container>

				<ng-container matColumnDef="packagingType">
					<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
						{{ 'sli.piece.table.column.packagingType' | translate }}
					</th>
					<td mat-cell *matCellDef="let row">{{ row.packagingType }}</td>
				</ng-container>

				<ng-container matColumnDef="grossWeight">
					<th scope="col" mat-header-cell *matHeaderCellDef>{{ 'sli.piece.table.column.grossWeight' | translate }}</th>
					<td mat-cell *matCellDef="let row">
						{{ row.grossWeight }}
					</td>
				</ng-container>

				<ng-container matColumnDef="dimensions">
					<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
						{{ 'sli.piece.table.column.dimensions' | translate }}
					</th>
					<td mat-cell *matCellDef="let row">
						{{
							`${row.dimensions?.length}${row.dimensions?.unit ?? 'CM'} * ${row.dimensions?.width}${row.dimensions?.unit ?? 'CM'}
								* ${row.dimensions?.height}${row.dimensions?.unit ?? 'CM'} `
						}}
					</td>
				</ng-container>

				<ng-container matColumnDef="pieceQuantity">
					<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
						{{ 'sli.piece.table.column.pieceQuantity' | translate }}
					</th>
					<td mat-cell *matCellDef="let row">
						<input
							type="number"
							[(ngModel)]="row.pieceQuantity"
							(change)="calculateGrossWeight($event, row)"
							min="1"
							oninput="this.value = this.value.replace(/[^0-9]/g, '')" />
					</td>
				</ng-container>

				<ng-container matColumnDef="delete">
					<th scope="col" mat-header-cell *matHeaderCellDef></th>
					<td mat-cell *matCellDef="let row">
						<button mat-icon-button color="primary" aria-label="Delete record" (click)="deletePiece($event, row)">
							<mat-icon>delete</mat-icon>
						</button>
					</td>
				</ng-container>
				<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
				<tr mat-row *matRowDef="let row; columns: displayedColumns" class="orll-mawb-table__row"></tr>
			</table>
		</div>
		<div class="orll-piece-consolidate-dialog__btn">
			<button mat-stroked-button color="primary" confirmDialog (confirm)="cancelConsolidate()">
				{{ 'common.dialog.cancel' | translate }}
			</button>
			<button mat-flat-button color="primary" (click)="consolidatePiece()" [disabled]="dataSource.data.length === 0">
				<mat-icon>view_in_ar</mat-icon>
				{{ 'sli.piece.consolidate.btn' | translate }}
			</button>
		</div>
	</mat-dialog-content>
	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>
