import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SubscriptionConfigurationListComponent } from './subscription-configuration-list.component';
import { SubscriptionConfigurationService } from '../../services/subscription-configuration.service';
import { SubscriptionConfigurationListObj } from '../../models/subscription.model';
import { MatDialog } from '@angular/material/dialog';
import { of, throwError } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { UserProfileService } from '@shared/services/user-profile.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ShareDialogComponent } from '@shared/components/share-dialog/share-dialog.component';
import { SubscriptionConfigurationDetailComponent } from '../subscription-configuration-detail/subscription-configuration-detail.component';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { InviteSubscribeComponent } from '../invite-subscribe/invite-subscribe.component';
import { ShareType } from '@shared/models/share-type.model';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('SubscriptionConfigurationListComponent', () => {
	let component: SubscriptionConfigurationListComponent;
	let fixture: ComponentFixture<SubscriptionConfigurationListComponent>;
	let mockSubscriptionService: jasmine.SpyObj<SubscriptionConfigurationService>;
	let formBuilder: FormBuilder;
	let mockDialog: jasmine.SpyObj<MatDialog>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;

	const mockRow: SubscriptionConfigurationListObj = {
		id: '',
		topic: '',
		topicType: '',
		subscriptionType: '',
		subscriptionEventType: '',
		description: '',
		expiresAt: '',
		userId: '',
		orgId: '',
		subscriberId: '',
		createAt: '',
		subscriptionRequestUri: '',
	};

	beforeEach(async () => {
		mockSubscriptionService = jasmine.createSpyObj<SubscriptionConfigurationService>('SubscriptionConfigurationService', [
			'getDataPerPage',
			'loadAllData',
			'deleteConfiguration',
		]);

		const mockConfigRes: SubscriptionConfigurationListObj[] = [
			{
				id: '333',
				topic: '333',
				topicType: '333',
				subscriptionType: '',
				subscriptionEventType: '',
				description: '',
				expiresAt: '',
				userId: '',
				orgId: '',
				subscriberId: '',
				createAt: '',
				subscriptionRequestUri: '',
			},
		];
		mockSubscriptionService.getDataPerPage.and.returnValue(of({ total: 55, rows: mockConfigRes }));
		mockSubscriptionService.deleteConfiguration.and.returnValue(of(true));

		mockDialog = jasmine.createSpyObj('MatDialog', ['open'], {
			openDialogs: [],
			afterOpened: { next: jasmine.createSpy('next') },
			afterAllClosed: { next: jasmine.createSpy('next') },
			getAfterAllClosed: jasmine.createSpy('getAfterAllClosed').and.returnValue({ next: jasmine.createSpy('next') }),
		});
		const mockDialogRef = {
			afterClosed: jasmine.createSpy('afterClosed').and.returnValue(of(null)),
		};
		mockDialog.open.and.returnValue(mockDialogRef as any);

		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		await TestBed.configureTestingModule({
			imports: [SubscriptionConfigurationListComponent, ReactiveFormsModule, TranslateModule.forRoot(), NoopAnimationsModule],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
			providers: [
				FormBuilder,
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: SubscriptionConfigurationService, useValue: mockSubscriptionService },
				{ provide: UserProfileService, useValue: userProfileServiceSpy },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SubscriptionConfigurationListComponent);
		component = fixture.componentInstance;
		formBuilder = TestBed.inject(FormBuilder);
		component.configrationSearchForm = formBuilder.group({
			description: [''],
		});

		component.configurationParam = { description: '' };
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('onSearch', () => {
		it('should update param with description from configrationSearchForm', () => {
			component.configrationSearchForm.patchValue({ description: 'test search' });
			component.onSearch();
			expect(component.configurationParam).toEqual({
				...component.configurationParam,
				description: 'test search',
			});
		});

		it('should handle empty description', () => {
			component.configrationSearchForm.patchValue({ description: null });
			component.onSearch();
			expect(component.configurationParam.description).toBe('');
		});

		it('should handle undefined description', () => {
			component.configrationSearchForm.patchValue({ description: undefined });
			component.onSearch();
			expect(component.configurationParam.description).toBe('');
		});
	});

	describe('shareConfiguration', () => {
		it('should open ShareDialogComponent with correct configuration', () => {
			const testRow: SubscriptionConfigurationListObj = {
				...mockRow,
				id: 'test-id-123',
			};

			spyOn(component['dialog'], 'open').and.returnValue({
				afterClosed: () => of(null),
			} as any);

			component.shareConfiguration(testRow);

			expect(component['dialog'].open).toHaveBeenCalledWith(ShareDialogComponent, {
				width: '60vw',
				autoFocus: false,
				data: {
					shareType: ShareType.SUBSCRIPTION_CONFIG,
					param: 'test-id-123',
				},
			});
		});

		it('should handle row with empty id', () => {
			const testRow: SubscriptionConfigurationListObj = {
				...mockRow,
				id: '',
			};

			spyOn(component['dialog'], 'open').and.returnValue({
				afterClosed: () => of(null),
			} as any);

			component.shareConfiguration(testRow);

			expect(component['dialog'].open).toHaveBeenCalledWith(ShareDialogComponent, {
				width: '60vw',
				autoFocus: false,
				data: {
					shareType: ShareType.SUBSCRIPTION_CONFIG,
					param: '',
				},
			});
		});
	});

	describe('createOrUpdateConfiguration', () => {
		it('should open SubscriptionConfigurationDetailComponent with row data', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(false));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.createOrUpdateConfiguration(mockRow);

			expect(component['dialog'].open).toHaveBeenCalledWith(SubscriptionConfigurationDetailComponent, {
				width: '60vw',
				autoFocus: false,
				data: mockRow,
			});
		});

		it('should reset param when dialog closes with true', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(true));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			const originalParam = { ...component.configurationParam };
			component.createOrUpdateConfiguration(mockRow);

			expect(dialogRefSpy.afterClosed).toHaveBeenCalled();
			expect(component.configurationParam).toEqual({ ...originalParam });
		});

		it('should not reset param when dialog closes with false', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(false));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			const originalParam = { ...component.configurationParam };
			component.createOrUpdateConfiguration(mockRow);

			expect(component.configurationParam).toEqual(originalParam);
		});

		it('should handle undefined row data', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(false));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.createOrUpdateConfiguration(undefined);

			expect(component['dialog'].open).toHaveBeenCalledWith(SubscriptionConfigurationDetailComponent, {
				width: '60vw',
				autoFocus: false,
				data: undefined,
			});
		});
	});

	describe('deleteConfiguration', () => {
		it('should open ConfirmDialogComponent with correct content', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(false));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.deleteConfiguration(mockRow);

			expect(component['dialog'].open).toHaveBeenCalledWith(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: jasmine.any(String),
				},
			});
		});

		it('should call deleteConfiguration service when dialog closes with true', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(true));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.deleteConfiguration(mockRow);

			expect(mockSubscriptionService.deleteConfiguration).toHaveBeenCalledWith(mockRow.id);
		});

		it('should not call deleteConfiguration service when dialog closes with false', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(false));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.deleteConfiguration(mockRow);

			expect(mockSubscriptionService.deleteConfiguration).not.toHaveBeenCalled();
		});

		it('should set dataLoading to true when deleting and false on success', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(true));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			expect(component.dataLoading).toBe(false);

			component.deleteConfiguration(mockRow);

			// The dataLoading should be set to true during the deletion process
			// and then set to false after completion (handled by the service observable)
			expect(mockSubscriptionService.deleteConfiguration).toHaveBeenCalledWith(mockRow.id);
		});

		it('should set dataLoading to false on error', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(true));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);
			mockSubscriptionService.deleteConfiguration.and.returnValue(throwError(() => new Error('Delete failed')));

			component.deleteConfiguration(mockRow);

			expect(component.dataLoading).toBe(false);
		});
	});

	describe('inviteToSubscribe', () => {
		it('should open InviteSubscribeComponent with correct configuration', () => {
			spyOn(component['dialog'], 'open').and.returnValue({
				afterClosed: () => of(null),
			} as any);

			component.inviteToSubscribe();

			expect(component['dialog'].open).toHaveBeenCalledWith(InviteSubscribeComponent, {
				width: '60vw',
				autoFocus: false,
			});
		});
	});

	describe('component initialization', () => {
		it('should initialize with correct default values', () => {
			expect(component.configrationSearchForm).toBeDefined();
			expect(component.configrationSearchForm.get('description')?.value).toBe('');
			expect(component.configurationParam).toEqual({ description: '' });
			expect(component.dataLoading).toBe(false);
		});

		it('should have correct column definitions', () => {
			expect(component.columns).toBeDefined();
			expect(component.columns.length).toBe(6);

			const actionColumn = component.columns.find((col) => col.key === 'actions');
			expect(actionColumn).toBeDefined();
			expect(actionColumn?.actions).toBeDefined();
			expect(actionColumn?.actions?.length).toBe(3);
		});

		it('should have correct action icons in columns', () => {
			const actionColumn = component.columns.find((col) => col.key === 'actions');
			const actions = actionColumn?.actions;

			expect(actions?.[0].iconKey).toBe('share');
			expect(actions?.[1].iconKey).toBe('edit');
			expect(actions?.[2].iconKey).toBe('delete');
		});

		it('should call correct methods when action icons are clicked', () => {
			spyOn(component, 'shareConfiguration');
			spyOn(component, 'createOrUpdateConfiguration');
			spyOn(component, 'deleteConfiguration');

			const actionColumn = component.columns.find((col) => col.key === 'actions');
			const actions = actionColumn?.actions;

			// Test share action
			actions?.[0].iconClickAction?.(mockRow);
			expect(component.shareConfiguration).toHaveBeenCalledWith(mockRow);

			// Test edit action
			actions?.[1].iconClickAction?.(mockRow);
			expect(component.createOrUpdateConfiguration).toHaveBeenCalledWith(mockRow);

			// Test delete action
			actions?.[2].iconClickAction?.(mockRow);
			expect(component.deleteConfiguration).toHaveBeenCalledWith(mockRow);
		});
	});

	describe('edge cases and error handling', () => {
		it('should handle null dialog result in createOrUpdateConfiguration', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(null));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			const originalParam = { ...component.configurationParam };
			component.createOrUpdateConfiguration(mockRow);

			expect(component.configurationParam).toEqual(originalParam);
		});

		it('should handle null dialog result in deleteConfiguration', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(null));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.deleteConfiguration(mockRow);

			expect(mockSubscriptionService.deleteConfiguration).not.toHaveBeenCalled();
		});
	});
});
