<orll-dialog class="retrive-obj">
	<ng-container dialog-title>
		<span>{{ 'common.enter.uri.title' | translate }}{{ data ?? '' }}</span>
	</ng-container>

	<div dialog-content>
		<form [formGroup]="retrieveForm">
			<mat-form-field class="width-100">
				<input matInput formControlName="uri" />
			</mat-form-field>
		</form>
		@if (dataLoading) {
			<iata-spinner></iata-spinner>
		}
	</div>
	<ng-container dialog-actions>
		<button mat-stroked-button [mat-dialog-close]="'cancel'" color="primary">
			{{ 'common.dialog.cancel' | translate }}
		</button>
		<button mat-flat-button color="primary" (click)="retrieveObj()" [disabled]="">
			<mat-icon>check</mat-icon>
			{{ 'common.retrieve.btn' | translate }}
		</button>
	</ng-container>
</orll-dialog>
