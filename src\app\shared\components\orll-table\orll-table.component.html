<div class="orll-table">
	<div class="common-scroll-table-container" (scroll)="onTableScroll($event)">
		<table mat-table [dataSource]="dataSource" matSort (matSortChange)="onSortChange($event)" class="common-scroll-table">
			@for (column of columns; track column) {
				<ng-container [matColumnDef]="column.key">
					<th mat-header-cell *matHeaderCellDef mat-sort-header class="mat-mdc-header-row">
						{{ column.header | translate }}
					</th>
					<td mat-cell *matCellDef="let row" [class.orll-table__action-col]="column.actions">
						@if (column.clickCell) {
							<span
								[class]="!column.noshowLink || !column.noshowLink(row) ? 'orll-table_clickable-col' : ''"
								(click)="column.clickCell ? column.clickCell(row) : onRowClicked(row)"
								(keydown.enter)="$event.stopPropagation()">
								{{ getCellValue(column, row) }}</span
							>
						} @else if (column.actions) {
							<div class="orll-table_clickable__actions">
								@for (action of column.actions; track action.iconKey) {
									@if (
										!action.showCopy && (!action.showCondition || (action.showCondition && action.showCondition(row)))
									) {
										<mat-icon
											color="primary"
											(click)="action.iconClickAction ? action.iconClickAction(row) : onRowClicked(row)"
											(keydown.enter)="action.iconClickAction ? action.iconClickAction(row) : onRowClicked(row)"
											>{{ action.iconKey }}
										</mat-icon>
									}
									@if (action.showCopy && action.showCopy(row)) {
										<button mat-icon-button color="primary" [orllCopy]="row.copyId" class="orll-table__copy-button">
											<mat-icon>{{ action.iconKey }}</mat-icon>
										</button>
									}
								}
							</div>
						} @else {
							{{ getCellValue(column, row) }}
						}
					</td>
				</ng-container>
			}

			<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
			<tr
				mat-row
				*matRowDef="let row; columns: displayedColumns"
				(click)="onRowClicked(row)"
				(keydown.enter)="$event.stopPropagation()"></tr>
		</table>
	</div>
	@if (hasPagination && !enableInfiniteScroll) {
		<mat-paginator [length]="totalRecords" [pageSizeOptions]="tablePageSizes" (page)="onPageChange($event)"> </mat-paginator>
	}
	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>
