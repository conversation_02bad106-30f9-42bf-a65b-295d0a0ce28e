import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { DelegationRequestDialogComponent } from './delegation-request-dialog.component';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormField, MatInputModule } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { AutocompleteComponent } from '../autocomplete/autocomplete.component';
import { DelegationPermission } from '@shared/models/delegation-permission';
import { DelegationRequestService } from '@shared/services/delegation/delegation-request.service';
import { of } from 'rxjs';

describe('DelegationRequestDialogComponent', () => {
	let component: DelegationRequestDialogComponent;
	let fixture: ComponentFixture<DelegationRequestDialogComponent>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<DelegationRequestDialogComponent>>;
	let delegationRequestServiceSpy: jasmine.SpyObj<DelegationRequestService>;

	const mockResponse = {
		code: 200,
		msg: 'success',
		data: '123',
	};

	beforeEach(async () => {
		mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
		delegationRequestServiceSpy = jasmine.createSpyObj('DelegationRequestService', [
			'requestDelegation',
			'revokeDelegation',
			'rejectDelegation',
			'approveDelegation',
		]);

		await TestBed.configureTestingModule({
			imports: [
				MatDialogModule,
				MatButtonModule,
				MatIconModule,
				TranslateModule,
				MatCheckboxModule,
				MatButtonModule,
				MatInputModule,
				MatFormField,
				ReactiveFormsModule,
				MatAutocompleteModule,
				AutocompleteComponent,
				TranslateModule.forRoot(),
				NoopAnimationsModule,
			],
			providers: [
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: {} },
				{ provide: DelegationRequestService, useValue: delegationRequestServiceSpy },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(DelegationRequestDialogComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('Dialog actions', () => {
		it('should close dialog with 0 when cancel button is clicked', () => {
			component.onCancel();
			expect(mockDialogRef.close).toHaveBeenCalledWith(false);
		});

		it('should close dialog with true when OK button is clicked', fakeAsync(() => {
			component.selectedOrgs = [{ code: '1', name: 'Demo Shipper' }];
			component.delegationRequestForm.patchValue({
				hasDescription: 'aaa',
				hasLogisticsObject: '123',
				hasPermission: {
					[DelegationPermission.GET_LOGISTICS_EVENT]: true,
					[DelegationPermission.GET_LOGISTICS_OBJECT]: true,
					[DelegationPermission.POST_LOGISTICS_EVENT]: true,
					[DelegationPermission.PATCH_LOGISTICS_OBJECT]: true,
				},
			});

			const payload = {
				isRequestedFor: ['1'],
				hasDescription: 'aaa',
				hasLogisticsObject: ['123'],
				hasPermission: jasmine.arrayContaining([
					DelegationPermission.GET_LOGISTICS_EVENT,
					DelegationPermission.GET_LOGISTICS_OBJECT,
					DelegationPermission.POST_LOGISTICS_EVENT,
					DelegationPermission.PATCH_LOGISTICS_OBJECT,
				]),
			};

			delegationRequestServiceSpy.requestDelegation.and.returnValue(of(mockResponse));

			component.onOk();

			expect(delegationRequestServiceSpy.requestDelegation).toHaveBeenCalledWith(jasmine.objectContaining(payload));

			fixture.detectChanges();

			expect(mockDialogRef.close).toHaveBeenCalledWith(true);
		}));

		it('should call onCancel when cancel button is clicked', () => {
			spyOn(component, 'onCancel');
			const cancelButton = fixture.debugElement.query(By.css('.orll-delegation-request-dialog__cancel-button'));
			cancelButton.nativeElement.click();
			expect(component.onCancel).toHaveBeenCalled();
		});

		it('should call onOk when OK button is clicked', () => {
			spyOn(component, 'onOk');
			const okButton = fixture.debugElement.query(By.css('.orll-delegation-request-dialog__ok-button'));
			okButton.nativeElement.click();
			expect(component.onOk).toHaveBeenCalled();
		});

		it('should close dialog with true when reject button is clicked', fakeAsync(() => {
			component.requestId = '123';

			delegationRequestServiceSpy.rejectDelegation.and.returnValue(of(mockResponse));

			component.onReject();

			expect(delegationRequestServiceSpy.rejectDelegation).toHaveBeenCalledWith('123');

			fixture.detectChanges();

			expect(mockDialogRef.close).toHaveBeenCalledWith(true);
		}));

		it('should close dialog with true when approve button is clicked', fakeAsync(() => {
			component.requestId = '123';

			delegationRequestServiceSpy.approveDelegation.and.returnValue(of(mockResponse));

			component.onApprove();

			expect(delegationRequestServiceSpy.approveDelegation).toHaveBeenCalledWith('123');

			fixture.detectChanges();

			expect(mockDialogRef.close).toHaveBeenCalledWith(true);
		}));

		it('should close dialog with true when revoke button is clicked', fakeAsync(() => {
			component.requestId = '123';

			delegationRequestServiceSpy.revokeDelegation.and.returnValue(of(mockResponse));

			component.onRevoke();

			expect(delegationRequestServiceSpy.revokeDelegation).toHaveBeenCalledWith('123');

			fixture.detectChanges();

			expect(mockDialogRef.close).toHaveBeenCalledWith(true);
		}));
	});

	describe('Template rendering', () => {
		it('should render dialog content with translation', () => {
			const contentElement = fixture.debugElement.query(By.css('mat-dialog-content div'));
			expect(contentElement).toBeTruthy();
			// Note: Actual translation text would be handled by the TranslateService in a real environment
		});

		it('should render cancel and OK buttons', () => {
			const cancelButton = fixture.debugElement.query(By.css('.orll-delegation-request-dialog__cancel-button'));
			expect(cancelButton).toBeTruthy();

			const okButton = fixture.debugElement.query(By.css('.orll-delegation-request-dialog__ok-button'));
			expect(okButton).toBeTruthy();
		});

		it('should render reject and approve buttons', () => {
			component.ngOnInit();

			component.isApprovedBy = 'aaa';
			component.delegationDetail = {
				isRequestedFor: ['123'],
				isRequestedBy: 'abc',
				isRequestedAt: '2023-01-01',
				requestStatus: 'REQUEST_PENDING',
				hasDescription: '123456',
				hasPermission: ['234'],
				hasLogisticsObject: ['345'],
				isApprovedBy: '',
			};
			component.requestStatus = 'REQUEST_PENDING';

			fixture.detectChanges();

			const rejectButton = fixture.debugElement.query(By.css('.orll-delegation-request-dialog__reject-button'));
			expect(rejectButton).toBeTruthy();

			const approveButton = fixture.debugElement.query(By.css('.orll-delegation-request-dialog__approve-button'));
			expect(approveButton).toBeTruthy();
		});

		it('should render revoke button', () => {
			component.ngOnInit();

			component.delegationDetail = {
				isRequestedFor: ['123'],
				isRequestedBy: 'abc',
				isRequestedAt: '2023-01-01',
				requestStatus: 'REQUEST_PENDING',
				hasDescription: '123456',
				hasPermission: ['234'],
				hasLogisticsObject: ['345'],
				isApprovedBy: '',
			};
			component.requestStatus = 'REQUEST_PENDING';

			fixture.detectChanges();

			const revokeButton = fixture.debugElement.query(By.css('.orll-delegation-request-dialog__revoke-button'));
			expect(revokeButton).toBeTruthy();
		});
	});
});
