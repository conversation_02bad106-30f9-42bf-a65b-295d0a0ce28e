.orll-booking-transport {
	margin: 30px;
	display: flex;
	flex-direction: row;
	.row {
		margin-right: -25px;
	}

	.transport-detail {
		flex: 1;
	}
	.transport-select {
		flex: 0, 0, 100px;
		background-color: var(--iata-grey-200);
		border-right: 1px solid var(--iata-grey-100);
		border-radius: 0 8px 8px 0;
		display: flex;
		align-items: center;
		color: var(--iata-blue-600);
		cursor: pointer;
	}
	.transport-selected {
		background-color: var(--iata-blue-600);
		color: var(--iata-white);
	}

	.airline {
		margin-left: 20px;
		font-size: 20px;
		font-weight: 600;
		color: var(--iata-grey-600);
	}

	.border-right {
		border-right: 1px solid var(--iata-grey-100);
		margin-top: -44px;
		margin-bottom: -20px;
	}

	.border-bottom {
		border-bottom: 1px solid var(--iata-grey-100);
		margin: 20px 0;
	}

	.ml-1 {
		margin-left: -1px;
	}

	.mt-10 {
		margin-top: 10px;
	}

	.flight-info {
		display: flex;
		flex-direction: row;
		padding: 20px;

		.departure,
		.arrival,
		.flight-icon {
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 0 0 30%;
			flex-direction: column;

			&.flex-40 {
				flex: 0 0 40%;

				.svg {
					width: 100px;
				}
			}
		}
	}

	.total-price {
		display: flex;
		justify-content: center;
		align-items: end;
		height: 100%;

		&.pb-30 {
			padding-bottom: 30px;
		}
	}

	.time,
	.price,
	.location,
	.carrier {
		margin-right: 10px;
		font-size: 24px;
		font-weight: 600;
		color: var(--iata-blue-primary);

		&.small {
			font-size: 20px;
		}
	}

	.center {
		display: flex;
		align-items: center;
	}

	.detail {
		margin-top: -40px;
		background-color: var(--iata-grey-50);
		border-top: 1px solid var(--iata-grey-100);
	}

	.transport-list {
		padding: 20px;

		.flight-icon {
			margin-right: 10px;

			.svg {
				height: 50px;
			}
		}

		.flight-time {
			font-size: 20px;
		}
	}

	.transport-list > .border-bottom:last-child {
		border-bottom: none;
	}

	.price-col {
		position: relative;
		flex: 1;
		margin-top: -44px;
	}
	.price-btn {
		cursor: pointer;
	}
	.pirce-expand {
		border: 2px solid var(--iata-blue-600);
		border-bottom: 0;
		z-index: 200;
	}

	&__piece-detail-table {
		z-index: 100;
		width: 70vw;
		position: absolute;
		right: 0px;
		background-color: var(--iata-white);

		color: var(--iata-grey-600);
		.table-title {
			color: var(--iata-grey-300) !important;
			background-color: var(--iata-grey-50);
		}
		.row {
			padding: 10px 0px;
			margin: 0px !important;
			border-top: 2px solid var(--iata-grey-200);
		}

		border: 2px solid var(--iata-blue-600);
		border-top: 0;

		&::after {
			content: '';
			position: absolute;
			width: 1012px;
			height: 1px;
			top: 0;
			left: 0;
			border-top: 2px solid var(--iata-blue-600);
		}
	}
}
