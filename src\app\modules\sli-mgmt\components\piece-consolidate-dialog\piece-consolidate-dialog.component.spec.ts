import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialog } from '@angular/material/dialog';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { PieceConsolidateDialogComponent } from './piece-consolidate-dialog.component';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { ConsolidatePieceDialogData, PieceList } from '../../models/piece/piece-list.model';
import { REGX_NUMBER_1_DECIMAL } from '@shared/models/constant';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ChangeDetectorRef } from '@angular/core';
import { Piece } from '../../models/piece/piece.model';
import { CodeName } from '@shared/models/code-name.model';
import { PieceType } from '../../models/piece/piece-type.model';

describe('PieceConsolidateDialogComponent', () => {
	let component: PieceConsolidateDialogComponent;
	let fixture: ComponentFixture<PieceConsolidateDialogComponent>;
	let sliCreateRequestService: jasmine.SpyObj<SliCreateRequestService>;
	let dialogRef: jasmine.SpyObj<MatDialogRef<any>>;

	const mockDialogData: ConsolidatePieceDialogData = {
		pieces: [],
		sliNumber: '1111',
		pieceId: '',
	};

	beforeEach(() => {
		const sliCreateRequestServiceSpy = jasmine.createSpyObj('SliCreateRequestService', [
			'consolidatePiece',
			'getPackingTypes',
			'getPieceDetail',
		]);
		const matDialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);
		const matDialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
		matDialogSpy.open.and.returnValue({
			afterClosed: () => of(true),
			close: jasmine.createSpy('close'),
		} as any);
		const translateServiceSpy = jasmine.createSpyObj('TranslateService', ['instant']);
		const cdrSpy = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

		TestBed.configureTestingModule({
			imports: [PieceConsolidateDialogComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceSpy },
				{ provide: MatDialogRef, useValue: matDialogRefSpy },
				{ provide: MatDialog, useValue: matDialogSpy },
				{ provide: TranslateService, useValue: translateServiceSpy },
				{ provide: ChangeDetectorRef, useValue: cdrSpy },
				FormBuilder,

				{ provide: MAT_DIALOG_DATA, useValue: mockDialogData },

				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		});

		fixture = TestBed.createComponent(PieceConsolidateDialogComponent);
		component = fixture.componentInstance;
		sliCreateRequestService = TestBed.inject(SliCreateRequestService) as jasmine.SpyObj<SliCreateRequestService>;
		dialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<any>>;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('Constructor and Initialization', () => {
		it('should initialize with default values', () => {
			expect(component.displayedColumns).toEqual([
				'pieceDescription',
				'packagingType',
				'grossWeight',
				'dimensions',
				'pieceQuantity',
				'delete',
			]);
			expect(component.packagingTypes).toEqual([]);
			expect(component.dataLoading).toBe(false);
			expect(component.dataSource).toBeDefined();
			expect(component.pieceInfoForm).toBeDefined();
		});

		it('should initialize form with correct validators', () => {
			const form = component.pieceInfoForm;

			expect(form.get('productDescription')?.hasError('required')).toBe(true);
			expect(form.get('packagingType')?.hasError('required')).toBe(true);
			expect(form.get('grossWeight')?.hasError('required')).toBe(true);
			expect(form.get('dimLength')?.hasError('required')).toBe(true);
			expect(form.get('dimWidth')?.hasError('required')).toBe(true);
			expect(form.get('dimHeight')?.hasError('required')).toBe(true);
		});

		it('should initialize form with default dropdown values', () => {
			const form = component.pieceInfoForm;

			expect(form.get('nvdForCustoms')?.value).toBe(DropDownType.NCV);
			expect(form.get('nvdForCarriage')?.value).toBe(DropDownType.NVD);
		});
	});

	it('should delete a piece when deletePiece is called', () => {
		const event = new Event('click') as any;
		event.preventDefault = jasmine.createSpy('preventDefault');

		const pieceIdToDelete = '123';

		const pieces: PieceList[] = [
			{
				pieceId: pieceIdToDelete,
				type: '123',
				productDescription: '',
				packagingType: '',
				grossWeight: 0,
				dimensions: { length: 0, width: 0, height: 0 },
				pieceQuantity: 0,
				slac: 0,
			},
			{
				pieceId: '456',
				type: '',
				productDescription: '',
				packagingType: '',
				grossWeight: 0,
				dimensions: { length: 0, width: 0, height: 0 },
				pieceQuantity: 0,
				slac: 0,
			},
		];

		component.dataSource.data = pieces;

		component.deletePiece(event, { pieceId: pieceIdToDelete } as any);

		expect(event.preventDefault).toHaveBeenCalled();
		expect(component.dataSource.data.length).toBe(1);
		expect(component.dataSource.data[0].pieceId).not.toBe(pieceIdToDelete);
	});

	it('should call consolidatePiece and close dialog on success', () => {
		const formData = {
			piece: {
				sliNumber: 'pieceId1',
				type: 'Piece',
				product: { description: '44', hsCommodityDescription: '4' },
				packagingType: { typeCode: '1A', description: 'Drum, steel' },
				packagedIdentifier: '4',
				grossWeight: 4,
				dimensions: { length: 4, width: 4, height: 4 },
				nvdForCustoms: false,
				nvdForCarriage: false,
				upid: '4',
				shippingMarks: '44',
				textualHandlingInstructions: '444',
				pieceQuantity: 0,
				containedItems: [],
			},
			originalPieces: [
				{
					pieceId: 'pieceId1',
					productDescription: '666',
					packagingType: 'Drum, aluminium',
					grossWeight: 6,
					dimensions: { id: null, length: 6, width: 6, height: 6 },
					pieceQuantity: 1,
					slac: 0,
					type: 'Piece',
				},
				{
					pieceId: 'subPiece1',
					productDescription: '888',
					packagingType: 'Drum, plywood',
					grossWeight: 8,
					dimensions: { id: null, length: 8, width: 8, height: 8 },
					pieceQuantity: 1,
					slac: 0,
					type: 'Piece',
				},
			],
		};
		component.dataLoading = false;
		component.pieceInfoForm = new FormGroup({
			productDescription: new FormControl<string>('', [Validators.required]),
			hsCommodityDescription: new FormControl<string>(''),
			packagingType: new FormControl<string>('', [Validators.required]),
			packagedIdentifier: new FormControl<string>(''),
			grossWeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
			dimLength: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
			dimWidth: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
			dimHeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
			nvdForCustoms: new FormControl<string>(DropDownType.NCV),
			nvdForCarriage: new FormControl<string>(DropDownType.NVD),
			upid: new FormControl<string>(''),
			shippingMarks: new FormControl<string>(''),
			textualHandlingInstructions: new FormControl<string>(''),
		});
		component.getFormData = jasmine.createSpy('getFormData').and.returnValue(formData);

		sliCreateRequestService.consolidatePiece.and.returnValue(of(true));

		component.consolidatePiece();

		expect(component.dataLoading).toBe(false);
		expect(component.getFormData).toHaveBeenCalled();
		expect(sliCreateRequestService.consolidatePiece).toHaveBeenCalledWith(formData);
		expect(component.dataLoading).toBe(false);
		expect(dialogRef.close).toHaveBeenCalled();
	});

	it('should handle error in consolidatePiece', () => {
		component.dataLoading = false;
		component.pieceInfoForm = new FormGroup({});
		component.getFormData = jasmine.createSpy('getFormData').and.returnValue(null);

		component.consolidatePiece();

		expect(component.dataLoading).toBe(false);
	});

	describe('consolidatePiece - Enhanced Tests', () => {
		beforeEach(() => {
			component.pieceInfoForm = new FormGroup({
				productDescription: new FormControl('Test Product', [Validators.required]),
				packagingType: new FormControl('BOX', [Validators.required]),
				grossWeight: new FormControl('10', [Validators.required]),
				dimLength: new FormControl('5', [Validators.required]),
				dimWidth: new FormControl('5', [Validators.required]),
				dimHeight: new FormControl('5', [Validators.required]),
			});
		});

		it('should mark form as touched before processing', () => {
			spyOn(component.pieceInfoForm, 'markAllAsTouched');
			spyOn(component, 'getFormData').and.returnValue({
				piece: {
					sliNumber: 'test',
					type: PieceType.GENERAL,
					slac: 1,
					product: { description: 'Test', hsCommodityDescription: 'Test' },
					packagingType: { typeCode: 'BOX', description: 'Box' },
					packagedIdentifier: 'PI001',
					grossWeight: 10,
					dimensions: { length: 5, width: 5, height: 5 },
					nvdForCustoms: DropDownType.NCV,
					nvdForCarriage: DropDownType.NVD,
					upid: 'UPID001',
					shippingMarks: 'Test',
					textualHandlingInstructions: 'Test',
					pieceQuantity: 1,
					containedItems: [],
				},
				originalPieces: [],
			});
			sliCreateRequestService.consolidatePiece.and.returnValue(of(true));

			component.consolidatePiece();

			expect(component.pieceInfoForm.markAllAsTouched).toHaveBeenCalled();
		});

		it('should handle service error and close dialog', () => {
			spyOn(component, 'getFormData').and.returnValue({
				piece: {
					sliNumber: 'test',
					type: PieceType.GENERAL,
					slac: 1,
					product: { description: 'Test', hsCommodityDescription: 'Test' },
					packagingType: { typeCode: 'BOX', description: 'Box' },
					packagedIdentifier: 'PI001',
					grossWeight: 10,
					dimensions: { length: 5, width: 5, height: 5 },
					nvdForCustoms: DropDownType.NCV,
					nvdForCarriage: DropDownType.NVD,
					upid: 'UPID001',
					shippingMarks: 'Test',
					textualHandlingInstructions: 'Test',
					pieceQuantity: 1,
					containedItems: [],
				},
				originalPieces: [],
			});
			sliCreateRequestService.consolidatePiece.and.returnValue(throwError(() => new Error('Service error')));

			component.consolidatePiece();

			expect(component.dataLoading).toBe(false);
			expect(dialogRef.close).toHaveBeenCalledWith();
		});

		it('should set loading state during consolidation', () => {
			spyOn(component, 'getFormData').and.returnValue({
				piece: {
					sliNumber: 'test',
					type: PieceType.GENERAL,
					slac: 1,
					product: { description: 'Test', hsCommodityDescription: 'Test' },
					packagingType: { typeCode: 'BOX', description: 'Box' },
					packagedIdentifier: 'PI001',
					grossWeight: 10,
					dimensions: { length: 5, width: 5, height: 5 },
					nvdForCustoms: DropDownType.NCV,
					nvdForCarriage: DropDownType.NVD,
					upid: 'UPID001',
					shippingMarks: 'Test',
					textualHandlingInstructions: 'Test',
					pieceQuantity: 1,
					containedItems: [],
				},
				originalPieces: [],
			});
			sliCreateRequestService.consolidatePiece.and.returnValue(of(true));

			expect(component.dataLoading).toBe(false);
			component.consolidatePiece();
			expect(component.dataLoading).toBe(false); // After completion
		});

		it('should not proceed when getFormData returns null', () => {
			spyOn(component, 'getFormData').and.returnValue(null);

			component.consolidatePiece();

			expect(sliCreateRequestService.consolidatePiece).not.toHaveBeenCalled();
			expect(component.dataLoading).toBe(false);
		});
	});

	describe('ngOnInit', () => {
		const mockPackagingTypes: CodeName[] = [
			{ code: 'BOX', name: 'Box' },
			{ code: 'DRUM', name: 'Drum' },
		];

		beforeEach(() => {
			sliCreateRequestService.getPackingTypes.and.returnValue(of(mockPackagingTypes));
			// Mock getPieceDetail to return an observable for all tests in this describe block
			sliCreateRequestService.getPieceDetail.and.returnValue(
				of({
					id: 'piece1',
					product: { description: 'Test Product' },
					packagingType: { typeCode: 'BOX', description: 'Box' },
					grossWeight: 10.5,
					dimensions: { length: 10, width: 20, height: 30 },
					containedPieces: [],
					pieceQuantity: 1,
					containedItems: [],
				} as Piece)
			);
		});

		it('should load packaging types on init', () => {
			// Ensure no pieceId to avoid the getPieceDetail branch
			component.data.pieceId = '';

			component.ngOnInit();

			expect(sliCreateRequestService.getPackingTypes).toHaveBeenCalled();
			expect(component.packagingTypes).toEqual(mockPackagingTypes);
		});

		it('should call getPieceDetail when pieceId is provided', () => {
			component.data.pieceId = 'piece1';

			// Mock the service to return a proper observable
			sliCreateRequestService.getPieceDetail.and.returnValue(
				of({
					id: 'piece1',
					product: { description: 'Test Product' },
					packagingType: { typeCode: 'BOX', description: 'Box' },
					grossWeight: 10.5,
					dimensions: { length: 10, width: 20, height: 30 },
					containedPieces: [],
					pieceQuantity: 1,
					containedItems: [],
				} as Piece)
			);

			// Test that the method can be called without throwing
			expect(() => component.ngOnInit()).not.toThrow();
		});

		it('should handle pieces consolidation when no pieceId is provided', () => {
			const mockPieces: PieceList[] = [
				{
					pieceId: 'piece1',
					type: 'General',
					productDescription: 'Product 1',
					packagingType: 'Box',
					grossWeight: 10,
					dimensions: { length: 10, width: 10, height: 10 },
					pieceQuantity: 1,
					slac: 0,
				},
			];

			component.data.pieceId = '';
			component.data.pieces = mockPieces;
			spyOn(component, 'fillPieceFormForConsolidate');

			component.ngOnInit();

			expect(component.fillPieceFormForConsolidate).toHaveBeenCalledWith(mockPieces);
			expect(component.dataSource.data).toEqual(mockPieces);
			expect(mockPieces[0].originalQuantity).toBe(1);
		});

		it('should handle empty contained pieces', () => {
			const mockPiece: Piece = {
				id: 'piece1',
				product: { description: 'Test Product' },
				packagingType: { typeCode: 'BOX', description: 'Box' },
				grossWeight: 10.5,
				dimensions: { length: 10, width: 20, height: 30 },
				containedPieces: undefined,
				pieceQuantity: 1,
				containedItems: [],
			} as Piece;

			component.data.pieceId = 'piece1';
			sliCreateRequestService.getPieceDetail.and.returnValue(of(mockPiece));

			component.ngOnInit();

			expect(component.dataSource.data).toEqual([]);
		});
	});

	describe('fillPieceForm', () => {
		it('should fill form with piece data', () => {
			const mockPiece: Piece = {
				product: {
					description: 'Test Product',
					hsCommodityDescription: 'HS Description',
				},
				packagingType: { typeCode: 'BOX', description: 'Box' },
				packagedIdentifier: 'PI001',
				grossWeight: 15.5,
				dimensions: { length: 10, width: 20, height: 30 },
				nvdForCustoms: true,
				nvdForCarriage: false,
				upid: 'UPID001',
				shippingMarks: 'Fragile',
				textualHandlingInstructions: 'Handle with care',
			} as Piece;

			component.fillPieceForm(mockPiece);

			expect(component.pieceInfoForm.get('productDescription')?.value).toBe('Test Product');
			expect(component.pieceInfoForm.get('hsCommodityDescription')?.value).toBe('HS Description');
			expect(component.pieceInfoForm.get('packagingType')?.value).toBe('BOX');
			expect(component.pieceInfoForm.get('packagedIdentifier')?.value).toBe('PI001');
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(15.5);
			expect(component.pieceInfoForm.get('dimLength')?.value).toBe(10);
			expect(component.pieceInfoForm.get('dimWidth')?.value).toBe(20);
			expect(component.pieceInfoForm.get('dimHeight')?.value).toBe(30);
			expect(component.pieceInfoForm.get('nvdForCustoms')?.value).toBe(true);
			expect(component.pieceInfoForm.get('nvdForCarriage')?.value).toBe(false);
			expect(component.pieceInfoForm.get('upid')?.value).toBe('UPID001');
			expect(component.pieceInfoForm.get('shippingMarks')?.value).toBe('Fragile');
			expect(component.pieceInfoForm.get('textualHandlingInstructions')?.value).toBe('Handle with care');
		});

		it('should handle null/undefined piece values', () => {
			const mockPiece: Piece = {
				product: { description: null },
				packagingType: { typeCode: null },
				packagedIdentifier: null,
				grossWeight: null,
				dimensions: { length: null, width: null, height: null },
				nvdForCustoms: null,
				nvdForCarriage: null,
				upid: null,
				shippingMarks: null,
				textualHandlingInstructions: null,
			} as any;

			component.fillPieceForm(mockPiece);

			expect(component.pieceInfoForm.get('productDescription')?.value).toBe('');
			expect(component.pieceInfoForm.get('packagingType')?.value).toBe('');
			expect(component.pieceInfoForm.get('packagedIdentifier')?.value).toBe('');
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe('');
			expect(component.pieceInfoForm.get('dimLength')?.value).toBe('');
			expect(component.pieceInfoForm.get('dimWidth')?.value).toBe('');
			expect(component.pieceInfoForm.get('dimHeight')?.value).toBe('');
			expect(component.pieceInfoForm.get('upid')?.value).toBe('');
			expect(component.pieceInfoForm.get('shippingMarks')?.value).toBe('');
			expect(component.pieceInfoForm.get('textualHandlingInstructions')?.value).toBe('');
		});
	});

	describe('fillPieceFormForConsolidate', () => {
		it('should consolidate piece descriptions and calculate total weight', () => {
			const mockPieces: PieceList[] = [
				{
					pieceId: 'piece1',
					type: 'General',
					productDescription: 'Product 1',
					packagingType: 'Box',
					grossWeight: 10,
					dimensions: { length: 10, width: 10, height: 10 },
					pieceQuantity: 2,
					slac: 0,
					textualHandlingInstructions: 'Handle carefully',
				},
				{
					pieceId: 'piece2',
					type: 'General',
					productDescription: 'Product 2',
					packagingType: 'Drum',
					grossWeight: 15,
					dimensions: { length: 15, width: 15, height: 15 },
					pieceQuantity: 3,
					slac: 0,
					textualHandlingInstructions: 'Keep upright',
				},
			];

			component.fillPieceFormForConsolidate(mockPieces);

			expect(component.pieceInfoForm.get('productDescription')?.value).toBe('Product 1;Product 2');
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(65); // (10*2) + (15*3) = 65
			expect(component.pieceInfoForm.get('textualHandlingInstructions')?.value).toBe('Handle carefully;Keep upright');
			expect(component.pieceInfoForm.get('nvdForCustoms')?.value).toBe(true);
			expect(component.pieceInfoForm.get('nvdForCarriage')?.value).toBe(true);
		});

		it('should handle pieces with null/undefined values', () => {
			const mockPieces: PieceList[] = [
				{
					pieceId: 'piece1',
					type: 'General',
					productDescription: '',
					packagingType: 'Box',
					grossWeight: 0,
					dimensions: { length: 10, width: 10, height: 10 },
					pieceQuantity: 0,
					slac: 0,
					textualHandlingInstructions: undefined,
				},
			];

			component.fillPieceFormForConsolidate(mockPieces);

			expect(component.pieceInfoForm.get('productDescription')?.value).toBe('');
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(0);
			expect(component.pieceInfoForm.get('textualHandlingInstructions')?.value).toBe('');
		});

		it('should handle empty pieces array', () => {
			const mockPieces: PieceList[] = [];

			component.fillPieceFormForConsolidate(mockPieces);

			expect(component.pieceInfoForm.get('productDescription')?.value).toBe('');
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(0);
			expect(component.pieceInfoForm.get('textualHandlingInstructions')?.value).toBe('');
		});
	});

	describe('calculateGrossWeight', () => {
		beforeEach(() => {
			const mockPieces: PieceList[] = [
				{
					pieceId: 'piece1',
					type: 'General',
					productDescription: 'Product 1',
					packagingType: 'Box',
					grossWeight: 10,
					dimensions: { length: 10, width: 10, height: 10 },
					pieceQuantity: 2,
					originalQuantity: 2,
					slac: 0,
				},
				{
					pieceId: 'piece2',
					type: 'General',
					productDescription: 'Product 2',
					packagingType: 'Drum',
					grossWeight: 15,
					dimensions: { length: 15, width: 15, height: 15 },
					pieceQuantity: 1,
					originalQuantity: 1,
					slac: 0,
				},
			];
			component.data.pieces = mockPieces;
		});

		it('should calculate gross weight correctly for valid quantity', () => {
			const mockEvent = {
				target: { value: '3' },
			} as any;
			const mockRow: PieceList = {
				pieceId: 'piece1',
				type: 'General',
				productDescription: 'Product 1',
				packagingType: 'Box',
				grossWeight: 10,
				dimensions: { length: 10, width: 10, height: 10 },
				pieceQuantity: 2,
				originalQuantity: 5,
				slac: 0,
			};

			component.calculateGrossWeight(mockEvent, mockRow);

			// The method calculates weight based on input quantity (3) and other pieces weight (15)
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(45); // (3*10) + 15 = 45
		});

		it('should reset to original quantity when input is empty', () => {
			const mockEvent = {
				target: { value: '   ' },
			} as any;
			const mockRow: PieceList = {
				pieceId: 'piece1',
				type: 'General',
				productDescription: 'Product 1',
				packagingType: 'Box',
				grossWeight: 10,
				dimensions: { length: 10, width: 10, height: 10 },
				pieceQuantity: 3,
				originalQuantity: 2,
				slac: 0,
			};

			component.calculateGrossWeight(mockEvent, mockRow);

			expect(mockRow.pieceQuantity).toBe(2);
		});

		it('should limit quantity to original quantity when exceeded', () => {
			const mockEvent = {
				target: { value: '10' },
			} as any;
			const mockRow: PieceList = {
				pieceId: 'piece1',
				type: 'General',
				productDescription: 'Product 1',
				packagingType: 'Box',
				grossWeight: 10,
				dimensions: { length: 10, width: 10, height: 10 },
				pieceQuantity: 2,
				originalQuantity: 5,
				slac: 0,
			};

			component.calculateGrossWeight(mockEvent, mockRow);

			expect(mockRow.pieceQuantity).toBe(5); // Limited to originalQuantity
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(65); // (5*10) + 15 = 65
		});

		it('should handle invalid number input', () => {
			const mockEvent = {
				target: { value: 'invalid' },
			} as any;
			const mockRow: PieceList = {
				pieceId: 'piece1',
				type: 'General',
				productDescription: 'Product 1',
				packagingType: 'Box',
				grossWeight: 10,
				dimensions: { length: 10, width: 10, height: 10 },
				pieceQuantity: 2,
				originalQuantity: 5,
				slac: 0,
			};

			// Set initial form value to test that it doesn't change
			component.pieceInfoForm.patchValue({ grossWeight: 100 });

			component.calculateGrossWeight(mockEvent, mockRow);

			// Should not update form when input is invalid
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(100);
		});

		it('should handle missing originalQuantity', () => {
			const mockEvent = {
				target: { value: '10' },
			} as any;
			const mockRow: PieceList = {
				pieceId: 'piece1',
				type: 'General',
				productDescription: 'Product 1',
				packagingType: 'Box',
				grossWeight: 10,
				dimensions: { length: 10, width: 10, height: 10 },
				pieceQuantity: 2,
				originalQuantity: undefined,
				slac: 0,
			};

			component.calculateGrossWeight(mockEvent, mockRow);

			// Should allow any quantity when no originalQuantity
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(115); // (10*10) + 15 = 115
		});
	});

	describe('cancelConsolidate', () => {
		it('should close dialog without data', () => {
			component.cancelConsolidate();

			expect(dialogRef.close).toHaveBeenCalledWith();
		});
	});

	describe('getFormData - Enhanced Tests', () => {
		beforeEach(() => {
			component.packagingTypes = [
				{ code: 'BOX', name: 'Box Package' },
				{ code: 'DRUM', name: 'Drum Package' },
			];
			component.dataSource.data = [
				{
					pieceId: 'piece1',
					type: 'General',
					productDescription: 'Product 1',
					packagingType: 'Box',
					grossWeight: 10,
					dimensions: { length: 10, width: 10, height: 10 },
					pieceQuantity: 2,
					slac: 0,
				},
			];
		});

		it('should return correct form data when form is valid', () => {
			component.pieceInfoForm.patchValue({
				productDescription: 'Test Product',
				hsCommodityDescription: 'HS Description',
				packagingType: 'BOX',
				packagedIdentifier: 'PI001',
				grossWeight: '25.5',
				dimLength: '10.5',
				dimWidth: '20.5',
				dimHeight: '30.5',
				nvdForCustoms: DropDownType.NCV,
				nvdForCarriage: DropDownType.NVD,
				upid: 'UPID001',
				shippingMarks: 'Fragile',
				textualHandlingInstructions: 'Handle with care',
			});

			const result = component.getFormData();

			expect(result).toBeDefined();
			expect(result?.piece.sliNumber).toBe('1111');
			expect(result?.piece.type).toBe(PieceType.GENERAL);
			expect(result?.piece.slac).toBe(2); // dataSource.data.length
			expect(result?.piece.product.description).toBe('Test Product');
			expect(result?.piece.product.hsCommodityDescription).toBe('HS Description');
			expect(result?.piece.packagingType.typeCode).toBe('BOX');
			expect(result?.piece.packagingType.description).toBe('Box Package');
			expect(result?.piece.packagedIdentifier).toBe('PI001');
			expect(result?.piece.grossWeight).toBe(25.5);
			expect(result?.piece.dimensions.length).toBe(10.5);
			expect(result?.piece.dimensions.width).toBe(20.5);
			expect(result?.piece.dimensions.height).toBe(30.5);
			expect(result?.piece.nvdForCustoms).toBe(DropDownType.NCV);
			expect(result?.piece.nvdForCarriage).toBe(DropDownType.NVD);
			expect(result?.piece.upid).toBe('UPID001');
			expect(result?.piece.shippingMarks).toBe('Fragile');
			expect(result?.piece.textualHandlingInstructions).toBe('Handle with care');
			expect(result?.piece.pieceQuantity).toBe(2); // Sum of piece quantities
			expect(result?.originalPieces).toEqual(component.dataSource.data);
		});

		it('should handle numeric string conversion correctly', () => {
			component.pieceInfoForm.patchValue({
				productDescription: 'Test Product',
				packagingType: 'BOX',
				grossWeight: '25.5',
				dimLength: '10.5',
				dimWidth: '20.5',
				dimHeight: '30.5',
			});

			const result = component.getFormData();

			expect(result?.piece.grossWeight).toBe(25.5);
			expect(result?.piece.dimensions.length).toBe(10.5);
			expect(result?.piece.dimensions.width).toBe(20.5);
			expect(result?.piece.dimensions.height).toBe(30.5);
		});

		it('should calculate piece quantity from dataSource', () => {
			component.dataSource.data = [
				{
					pieceId: 'piece1',
					type: 'General',
					productDescription: 'Product 1',
					packagingType: 'Box',
					grossWeight: 10,
					dimensions: { length: 10, width: 10, height: 10 },
					pieceQuantity: 3,
					slac: 0,
				},
				{
					pieceId: 'piece2',
					type: 'General',
					productDescription: 'Product 2',
					packagingType: 'Drum',
					grossWeight: 15,
					dimensions: { length: 15, width: 15, height: 15 },
					pieceQuantity: 2,
					slac: 0,
				},
			];

			component.pieceInfoForm.patchValue({
				productDescription: 'Test Product',
				packagingType: 'BOX',
				grossWeight: '25.5',
				dimLength: '10.5',
				dimWidth: '20.5',
				dimHeight: '30.5',
			});

			const result = component.getFormData();

			expect(result?.piece.pieceQuantity).toBe(5); // 3 + 2 = 5
			expect(result?.piece.slac).toBe(5); // dataSource.data.length
		});

		it('should handle missing packaging type description', () => {
			component.packagingTypes = []; // Empty packaging types
			component.pieceInfoForm.patchValue({
				productDescription: 'Test Product',
				packagingType: 'UNKNOWN',
				grossWeight: '10',
				dimLength: '5',
				dimWidth: '5',
				dimHeight: '5',
			});

			const result = component.getFormData();

			expect(result?.piece.packagingType.typeCode).toBe('UNKNOWN');
			expect(result?.piece.packagingType.description).toBe(''); // Should handle missing description
		});
	});

	describe('Form Validation Edge Cases', () => {
		it('should handle form with pattern validation errors', () => {
			component.pieceInfoForm.patchValue({
				productDescription: 'Test Product',
				packagingType: 'BOX',
				grossWeight: 'invalid-weight', // Invalid pattern
				dimLength: 'invalid-length', // Invalid pattern
				dimWidth: '5',
				dimHeight: '5',
			});

			expect(component.pieceInfoForm.get('grossWeight')?.hasError('pattern')).toBe(true);
			expect(component.pieceInfoForm.get('dimLength')?.hasError('pattern')).toBe(true);
			expect(component.pieceInfoForm.invalid).toBe(true);
		});

		it('should handle empty required fields', () => {
			component.pieceInfoForm.patchValue({
				productDescription: '', // Required but empty
				packagingType: '', // Required but empty
				grossWeight: '',
				dimLength: '',
				dimWidth: '',
				dimHeight: '',
			});

			expect(component.pieceInfoForm.get('productDescription')?.hasError('required')).toBe(true);
			expect(component.pieceInfoForm.get('packagingType')?.hasError('required')).toBe(true);
			expect(component.pieceInfoForm.get('grossWeight')?.hasError('required')).toBe(true);
			expect(component.pieceInfoForm.invalid).toBe(true);
		});
	});

	describe('Event Handling Edge Cases', () => {
		it('should handle deletePiece with different event types', () => {
			const mockEvent = {
				preventDefault: jasmine.createSpy('preventDefault'),
				type: 'click',
			} as any;

			const initialData = [
				{
					pieceId: 'piece1',
					type: 'General',
					productDescription: 'Product 1',
					packagingType: 'Box',
					grossWeight: 10,
					dimensions: { length: 10, width: 10, height: 10 },
					pieceQuantity: 1,
					slac: 0,
				},
			];

			component.dataSource.data = [...initialData];

			component.deletePiece(mockEvent, initialData[0]);

			expect(mockEvent.preventDefault).toHaveBeenCalled();
			expect(component.dataSource.data.length).toBe(0);
		});

		it('should handle calculateGrossWeight with edge case values', () => {
			const mockEvent = {
				target: { value: '0' },
			} as any;
			const mockRow: PieceList = {
				pieceId: 'piece1',
				type: 'General',
				productDescription: 'Product 1',
				packagingType: 'Box',
				grossWeight: 10,
				dimensions: { length: 10, width: 10, height: 10 },
				pieceQuantity: 2,
				originalQuantity: 5,
				slac: 0,
			};

			component.data.pieces = [mockRow];
			component.pieceInfoForm.patchValue({ grossWeight: 100 });

			component.calculateGrossWeight(mockEvent, mockRow);

			// Should handle zero quantity
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(0); // 0 * 10 + 0 = 0
		});

		it('should handle calculateGrossWeight with negative values', () => {
			const mockEvent = {
				target: { value: '-5' },
			} as any;
			const mockRow: PieceList = {
				pieceId: 'piece1',
				type: 'General',
				productDescription: 'Product 1',
				packagingType: 'Box',
				grossWeight: 10,
				dimensions: { length: 10, width: 10, height: 10 },
				pieceQuantity: 2,
				originalQuantity: 5,
				slac: 0,
			};

			component.data.pieces = [mockRow];
			component.pieceInfoForm.patchValue({ grossWeight: 100 });

			component.calculateGrossWeight(mockEvent, mockRow);

			// The method actually calculates with the negative value (-5 * 10 = -50)
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(-50);
		});
	});

	describe('Utility Function Coverage', () => {
		it('should use displayPackagingTypeName utility in getFormData', () => {
			component.packagingTypes = [
				{ code: 'BOX', name: 'Box Package' },
				{ code: 'DRUM', name: 'Drum Package' },
			];

			component.pieceInfoForm.patchValue({
				productDescription: 'Test Product',
				packagingType: 'BOX',
				grossWeight: '10',
				dimLength: '5',
				dimWidth: '5',
				dimHeight: '5',
			});

			const result = component.getFormData();

			// This tests the displayPackagingTypeName utility function usage
			expect(result?.piece.packagingType.description).toBe('Box Package');
		});

		it('should handle displayPackagingTypeName with non-existent packaging type', () => {
			component.packagingTypes = [{ code: 'BOX', name: 'Box Package' }];

			component.pieceInfoForm.patchValue({
				productDescription: 'Test Product',
				packagingType: 'NONEXISTENT',
				grossWeight: '10',
				dimLength: '5',
				dimWidth: '5',
				dimHeight: '5',
			});

			const result = component.getFormData();

			// This tests the displayPackagingTypeName utility function with non-existent type
			expect(result?.piece.packagingType.description).toBe('');
		});
	});

	describe('Data Transformation Coverage', () => {
		it('should handle fillPieceFormForConsolidate correctly', () => {
			const mockPieces: PieceList[] = [
				{
					pieceId: 'piece1',
					type: 'General',
					productDescription: 'Product 1',
					packagingType: 'Box',
					grossWeight: 10,
					dimensions: { length: 10, width: 10, height: 10 },
					pieceQuantity: 2,
					slac: 0,
					textualHandlingInstructions: 'Handle carefully',
				},
			];

			component.fillPieceFormForConsolidate(mockPieces);

			// Verify form is populated with consolidated data
			expect(component.pieceInfoForm.get('productDescription')?.value).toBe('Product 1');
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(20); // 2 * 10 = 20
			expect(component.pieceInfoForm.get('textualHandlingInstructions')?.value).toBe('Handle carefully');
			expect(component.pieceInfoForm.get('nvdForCustoms')?.value).toBe(true);
			expect(component.pieceInfoForm.get('nvdForCarriage')?.value).toBe(true);
		});

		it('should handle form control value extraction edge cases', () => {
			// Test with undefined/null form values but make form valid
			component.pieceInfoForm.patchValue({
				productDescription: 'Valid Product', // Required field must be valid
				hsCommodityDescription: undefined,
				packagingType: 'BOX', // Required field must be valid
				packagedIdentifier: null,
				grossWeight: '10', // Required field must be valid
				dimLength: '5', // Required field must be valid
				dimWidth: '5', // Required field must be valid
				dimHeight: '5', // Required field must be valid
				upid: null,
				shippingMarks: undefined,
				textualHandlingInstructions: null,
			});

			const result = component.getFormData();

			expect(result?.piece.product.description).toBe('Valid Product');
			expect(result?.piece.product.hsCommodityDescription).toBeUndefined(); // Raw form value
			expect(result?.piece.packagedIdentifier).toBeNull(); // Raw form value
			expect(result?.piece.grossWeight).toBe(10);
			expect(result?.piece.dimensions.length).toBe(5);
			expect(result?.piece.dimensions.width).toBe(5);
			expect(result?.piece.dimensions.height).toBe(5);
			expect(result?.piece.upid).toBeNull(); // Raw form value
			expect(result?.piece.shippingMarks).toBeUndefined(); // Raw form value
			expect(result?.piece.textualHandlingInstructions).toBeNull(); // Raw form value
		});
	});

	describe('Component State Management', () => {
		it('should maintain dataSource state during operations', () => {
			const initialData = [
				{
					pieceId: 'piece1',
					type: 'General',
					productDescription: 'Product 1',
					packagingType: 'Box',
					grossWeight: 10,
					dimensions: { length: 10, width: 10, height: 10 },
					pieceQuantity: 1,
					slac: 0,
				},
			];

			component.dataSource.data = [...initialData];
			expect(component.dataSource.data.length).toBe(1);

			// Test that dataSource maintains state
			const currentData = component.dataSource.data;
			expect(currentData).toEqual(initialData);
		});

		it('should handle displayedColumns property', () => {
			expect(component.displayedColumns).toBeDefined();
			expect(component.displayedColumns.length).toBeGreaterThan(0);
			expect(component.displayedColumns).toContain('pieceDescription');
			expect(component.displayedColumns).toContain('packagingType');
			expect(component.displayedColumns).toContain('grossWeight');
			expect(component.displayedColumns).toContain('dimensions');
			expect(component.displayedColumns).toContain('pieceQuantity');
			expect(component.displayedColumns).toContain('delete');
		});
	});

	describe('Additional Function Coverage', () => {
		it('should handle ngOnInit with pieces consolidation path', () => {
			const mockPieces: PieceList[] = [
				{
					pieceId: 'piece1',
					type: 'General',
					productDescription: 'Product 1',
					packagingType: 'Box',
					grossWeight: 10,
					dimensions: { length: 10, width: 10, height: 10 },
					pieceQuantity: 2,
					slac: 0,
				},
			];

			// Set up for pieces consolidation path
			component.data.pieceId = '';
			component.data.pieces = mockPieces;
			spyOn(component, 'fillPieceFormForConsolidate');

			// Ensure getPackingTypes returns an observable
			sliCreateRequestService.getPackingTypes.and.returnValue(of([]));

			component.ngOnInit();

			expect(component.fillPieceFormForConsolidate).toHaveBeenCalledWith(mockPieces);
			expect(component.dataSource.data).toEqual(mockPieces);
			expect(mockPieces[0].originalQuantity).toBe(2); // Should be set by ngOnInit
		});

		it('should handle calculateGrossWeight with whitespace input', () => {
			const mockEvent = {
				target: { value: '   ' },
			} as any;
			const mockRow: PieceList = {
				pieceId: 'piece1',
				type: 'General',
				productDescription: 'Product 1',
				packagingType: 'Box',
				grossWeight: 10,
				dimensions: { length: 10, width: 10, height: 10 },
				pieceQuantity: 3,
				originalQuantity: 2,
				slac: 0,
			};

			component.data.pieces = [mockRow];
			component.pieceInfoForm.patchValue({ grossWeight: 100 });

			component.calculateGrossWeight(mockEvent, mockRow);

			// Should reset to originalQuantity when input is whitespace
			expect(mockRow.pieceQuantity).toBe(2);
		});

		it('should handle calculateGrossWeight when originalQuantity is undefined', () => {
			const mockEvent = {
				target: { value: '   ' },
			} as any;
			const mockRow: PieceList = {
				pieceId: 'piece1',
				type: 'General',
				productDescription: 'Product 1',
				packagingType: 'Box',
				grossWeight: 10,
				dimensions: { length: 10, width: 10, height: 10 },
				pieceQuantity: 3,
				originalQuantity: undefined,
				slac: 0,
			};

			component.data.pieces = [mockRow];
			component.pieceInfoForm.patchValue({ grossWeight: 100 });

			component.calculateGrossWeight(mockEvent, mockRow);

			// Should default to 1 when originalQuantity is undefined
			expect(mockRow.pieceQuantity).toBe(1);
		});

		it('should handle calculateGrossWeight with decimal values', () => {
			const mockEvent = {
				target: { value: '2.5' },
			} as any;
			const mockRow: PieceList = {
				pieceId: 'piece1',
				type: 'General',
				productDescription: 'Product 1',
				packagingType: 'Box',
				grossWeight: 10,
				dimensions: { length: 10, width: 10, height: 10 },
				pieceQuantity: 2,
				originalQuantity: 5,
				slac: 0,
			};

			component.data.pieces = [mockRow];
			component.pieceInfoForm.patchValue({ grossWeight: 100 });

			component.calculateGrossWeight(mockEvent, mockRow);

			// Should handle decimal values (2.5 * 10 = 25)
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(25);
		});

		it('should handle fillPieceForm with missing optional fields', () => {
			const mockPiece: Piece = {
				product: { description: 'Test Product' },
				packagingType: { typeCode: 'BOX', description: 'Box' },
				grossWeight: 15.5,
				dimensions: { length: 10, width: 20, height: 30 },
				// Missing optional fields
			} as Piece;

			component.fillPieceForm(mockPiece);

			expect(component.pieceInfoForm.get('productDescription')?.value).toBe('Test Product');
			expect(component.pieceInfoForm.get('packagingType')?.value).toBe('BOX');
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(15.5);
			expect(component.pieceInfoForm.get('hsCommodityDescription')?.value).toBe('');
			expect(component.pieceInfoForm.get('packagedIdentifier')?.value).toBe('');
			expect(component.pieceInfoForm.get('upid')?.value).toBe('');
			expect(component.pieceInfoForm.get('shippingMarks')?.value).toBe('');
			expect(component.pieceInfoForm.get('textualHandlingInstructions')?.value).toBe('');
		});

		it('should handle fillPieceFormForConsolidate with multiple pieces', () => {
			const mockPieces: PieceList[] = [
				{
					pieceId: 'piece1',
					type: 'General',
					productDescription: 'Product 1',
					packagingType: 'Box',
					grossWeight: 10,
					dimensions: { length: 10, width: 10, height: 10 },
					pieceQuantity: 2,
					slac: 0,
					textualHandlingInstructions: 'Handle carefully',
				},
				{
					pieceId: 'piece2',
					type: 'General',
					productDescription: 'Product 2',
					packagingType: 'Drum',
					grossWeight: 15,
					dimensions: { length: 15, width: 15, height: 15 },
					pieceQuantity: 3,
					slac: 0,
					textualHandlingInstructions: 'Keep upright',
				},
			];

			component.fillPieceFormForConsolidate(mockPieces);

			expect(component.pieceInfoForm.get('productDescription')?.value).toBe('Product 1;Product 2');
			expect(component.pieceInfoForm.get('grossWeight')?.value).toBe(65); // (10*2) + (15*3) = 65
			expect(component.pieceInfoForm.get('textualHandlingInstructions')?.value).toBe('Handle carefully;Keep upright');
		});

		it('should handle getFormData with empty dataSource', () => {
			component.dataSource.data = []; // Empty data source
			component.pieceInfoForm.patchValue({
				productDescription: 'Test Product',
				packagingType: 'BOX',
				grossWeight: '10',
				dimLength: '5',
				dimWidth: '5',
				dimHeight: '5',
			});

			const result = component.getFormData();

			expect(result?.piece.slac).toBe(0); // dataSource.data.length
			expect(result?.piece.pieceQuantity).toBe(0); // No pieces to sum
		});
	});
});
