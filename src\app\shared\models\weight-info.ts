export interface CalculateRequest {
	sliId?: string;
	orgId?: string;
	hawbId?: string;
	hawbIds?: string[];
}

export interface WeightInfoListObj {
	totalSlac: number;
	totalGrossWeight: number;
	noPieceRcp: number;
	chargableWeight: number;
	pieceGrossWeight: number;
	volume: number;
	pieces: Piece[];
}

export interface Piece {
	pieceId: string;
	productDescription: string;
	packagingType: string;
	grossWeight: number;
	dimensions: Dimensions;
	pieceQuantity: number;
	slac: number;
	type: string;
	latestStatus: string;
	textualHandlingInstructions: string;
}

interface Dimensions {
	id: string;
	length: number;
	width: number;
	height: number;
	volume: number;
}
