import { Component, Input, OnInit, OnChanges } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { startWith } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { isBlank } from '@shared/utils/type.utils';

const REGX_NUMBER_2_DECIMAL = /^([1-9]\d*(\.\d{1,2})?|0\.(?!0+$)\d{1,2})$/;

@Component({
	selector: 'orll-currency-input',
	imports: [FormsModule, MatAutocompleteModule, MatFormFieldModule, MatIconModule, MatInputModule, TranslateModule, ReactiveFormsModule],
	templateUrl: './currency-input.component.html',
	styleUrl: './currency-input.component.scss',
})
export class CurrencyInputComponent extends DestroyRefComponent implements OnInit, OnChanges {
	@Input()
	formLabel = '';

	@Input()
	fieldName = '';

	@Input()
	hiddenUnit = false;

	@Input()
	pattern = '2';

	@Input()
	currencyForm = new FormGroup({
		currencyUnit: new FormControl<string>(''),
		numericalValue: new FormControl<number | null>(null),
	});

	@Input()
	currencies: string[] = [];

	filteredCurrency: string[] = [];

	@Input()
	defaultValue = '';

	numericalValue = this.defaultValue;

	currencyUnit = 'Unit';

	ngOnInit(): void {
		this.currencyForm
			.get('currencyUnit')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredCurrency = this.filterCurrency(search!);
			});
	}

	ngOnChanges() {
		if (isBlank(this.currencyForm.get('numericalValue')?.value)) {
			this.numericalValue = this.defaultValue;
		} else {
			this.numericalValue = `${this.currencyForm.get('numericalValue')!.value}`;
		}
	}

	filterCurrency(search: string): string[] {
		return this.currencies.filter((currency) => currency.toLowerCase().includes(search?.toLowerCase().trim() ?? ''));
	}

	/**
	 * update only on blur
	 * @param value
	 */
	onNumericalValueChange(value: string) {
		if (REGX_NUMBER_2_DECIMAL.test(value)) {
			this.currencyForm.patchValue({
				numericalValue: Number(value),
			});
		} else {
			this.currencyForm.patchValue({
				numericalValue: null,
			});
		}
	}

	onNumericalValueBlur() {
		if (this.numericalValue && !REGX_NUMBER_2_DECIMAL.test(this.numericalValue)) {
			this.numericalValue = this.defaultValue;
			this.currencyForm.patchValue({
				numericalValue: null,
			});
		}
	}
}
