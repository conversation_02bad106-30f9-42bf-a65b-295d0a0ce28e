import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
	providedIn: 'root',
})
export class EventService {
	private readonly pieceInstructionChangeSource = new BehaviorSubject<string>('');

	pieceInstructionChange$ = this.pieceInstructionChangeSource.asObservable();

	publicPicesInstructionChange(message: string) {
		this.pieceInstructionChangeSource.next(message);
	}
}
