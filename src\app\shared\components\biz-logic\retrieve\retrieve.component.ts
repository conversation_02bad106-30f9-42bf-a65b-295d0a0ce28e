import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
// eslint-disable-next-line @typescript-eslint/naming-convention
import OrllDialogComponent from '@shared/components/dialog-template/dialog-template.component';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { LogisticObjType } from '@shared/models/share-type.model';
import { MatButtonModule } from '@angular/material/button';
import { CommonService } from '@shared/services/common.service';
import { RetrieveService } from '@shared/services/biz-logic/obj-retrieve/retrieve.service';
import { Router } from '@angular/router';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';

const SLI_TAB_INDEX = 0;

@Component({
	selector: 'orll-retrieve',
	imports: [
		OrllDialogComponent,
		TranslateModule,
		MatDialogModule,
		MatInputModule,
		FormsModule,
		ReactiveFormsModule,
		MatIconModule,
		MatButtonModule,
		SpinnerComponent,
	],
	templateUrl: './retrieve.component.html',
	styleUrl: './retrieve.component.scss',
})
export class RetrieveComponent {
	dataLoading = false;

	constructor(
		private readonly commonService: CommonService,
		private readonly service: RetrieveService,
		private readonly router: Router,
		private readonly dialogRef: MatDialogRef<RetrieveComponent>,
		@Inject(MAT_DIALOG_DATA) public data: LogisticObjType
	) {}

	retrieveForm = new FormGroup({
		uri: new FormControl<string>('', [Validators.required]),
	});

	retrieveObj() {
		this.retrieveForm.markAllAsTouched();
		if (this.retrieveForm.invalid) {
			this.commonService.showFormInvalid();
			return;
		}
		const loId = this.retrieveForm.get('uri')?.value ?? '';
		this.dataLoading = true;
		this.service.getObjDetail({ loId: loId, type: this.data.toLocaleLowerCase() }).subscribe({
			next: () => {
				this.dataLoading = false;
				this.dialogRef.close();
				if (this.data === LogisticObjType.SLI) {
					this.router.navigate(['sli', 'edit', loId, SLI_TAB_INDEX]);
				} else {
					this.router.navigate([this.data.toLocaleLowerCase(), 'edit', loId]);
				}
			},
			error: (err) => {
				this.dataLoading = false;
				this.dialogRef.close();
				if (err.status === 403) {
					this.commonService.showWarning('common.no.access.error');
				}
			},
		});
	}
}
