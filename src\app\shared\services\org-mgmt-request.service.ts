import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { Organization } from '../models/organization.model';
import { OrgInfo } from '../models/org-info.model';
import { CodeName } from '@shared/models/code-name.model';
import { map } from 'rxjs/operators';
import { EnumCodeTypeModel } from '@shared/components/enum-code-form-item/enum-code-type.model';
import { AbstractAutocompleteService } from '@shared/models/autocomplete.model';

@Injectable({ providedIn: 'root' })
export class OrgMgmtRequestService extends ApiService implements AbstractAutocompleteService<string> {
	constructor(http: HttpClient) {
		super(http);
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	getOptions(searchParam: string, id?: string): Observable<string[]> {
		return super.getData<Organization[]>('org/org/list').pipe(map((res) => [...new Set(res.map((org) => org.orgType))]));
	}

	getOrgList(orgType?: string | null): Observable<Organization[]> {
		return super.getData<Organization[]>('org/org/list', { orgType: orgType ?? '' });
	}

	getOrgInfo(orgId: string): Observable<OrgInfo> {
		return super.getData<OrgInfo>('org/org', { orgId });
	}

	getEnumCode(enumCode: EnumCodeTypeModel): Observable<CodeName[]> {
		return super.getData<CodeName[]>(`sys-management/enums/${enumCode}`).pipe(
			map((res) => {
				return res.map((packageType: CodeName) => {
					return { code: packageType.code, name: packageType.name };
				});
			})
		);
	}
}
