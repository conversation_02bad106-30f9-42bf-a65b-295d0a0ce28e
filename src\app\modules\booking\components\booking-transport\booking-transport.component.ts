import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { BookingAirlineDetailObj, BookingOptionPieceObj, BookingRequestDetailObj, Transport } from '@shared/models/booking-option.model';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';

@Component({
	selector: 'orll-booking-transport',
	templateUrl: './booking-transport.component.html',
	styleUrl: './booking-transport.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [TranslateModule, IataDateFormatPipe, MatIconModule],
})
export class BookingTransportComponent implements OnChanges {
	@Input() bookingRequestDetails: BookingRequestDetailObj | null = null;
	@Input() bookingAirlineDetail: BookingAirlineDetailObj | null = null;
	@Input() selectable = false;

	@Output() selectedOption = new EventEmitter<string>();

	firstTransport: Transport | null = null;
	lastTransport: Transport | null = null;
	priceList: BookingOptionPieceObj[] = [];
	showPieceDetail = false;

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['bookingRequestDetails'] || changes['bookingAirlineDetail']) {
			const transportList = this.bookingRequestDetails?.transportVoList ?? this.bookingAirlineDetail?.transportVoList ?? [];
			transportList.forEach((item) => {
				item.departureDate = item.departureDate ? new Date(item.departureDate).toISOString() : '';
				item.arrivalDate = item.arrivalDate ? new Date(item.arrivalDate).toISOString() : '';
			});

			this.firstTransport = transportList[0] ?? null;
			this.lastTransport = transportList[transportList.length - 1] ?? null;
			this.priceList = this.bookingRequestDetails?.priceList ?? this.bookingAirlineDetail?.priceList ?? [];
		}
	}

	selectOption() {
		this.selectedOption.emit(this.bookingAirlineDetail?.id);
	}
}
