<orll-dialog class="booking-option-request">
	<ng-container dialog-title>
		<span>{{ 'booking.option.create.quote.btn' | translate }}</span>
	</ng-container>

	<div dialog-content class="booking-option-request_content">
		<orll-booking-option-request-form></orll-booking-option-request-form>
	</div>

	<ng-container dialog-actions>
		<button mat-stroked-button [mat-dialog-close]="'cancel'" color="primary">
			{{ 'common.dialog.cancel' | translate }}
		</button>

		<button mat-flat-button color="primary" (click)="sendOptionRequest()">
			<mat-icon>save</mat-icon>
			{{ 'sli.mgmt.save' | translate }}
		</button>
	</ng-container>
</orll-dialog>
