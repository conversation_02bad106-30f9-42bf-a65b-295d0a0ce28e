import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MawbStatusListComponent } from './mawb-status-list.component';
import { of, throwError } from 'rxjs';
import { MawbStatusService } from '../../services/mawb-status.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { HTTP_INTERCEPTORS, provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { UserProfile } from '@shared/models/user-profile.model';
import { MatDialog } from '@angular/material/dialog';
import { ChangeDetectorRef } from '@angular/core';
import { HAWBEventListObj, PieceEventListObj, StatusUpdateLog } from '../../models/mawb-event.model';

function createMockUserProfile(overrides: Partial<UserProfile> = {}): UserProfile {
	return {
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'ORG_TYPE',
		userType: '1',
		menuList: [],
		permissionList: [],
		orgList: [],
		...overrides,
	};
}

describe('MawbStatusListComponent', () => {
	let component: MawbStatusListComponent;
	let fixture: ComponentFixture<MawbStatusListComponent>;
	let statusService: jasmine.SpyObj<MawbStatusService>;
	let mockDialog: jasmine.SpyObj<MatDialog>;
	let mockTranslateService: jasmine.SpyObj<TranslateService>;
	let mockCdr: jasmine.SpyObj<ChangeDetectorRef>;

	const mockStatusService = {
		getMawbStatus: jasmine.createSpy('getMawbStatus').and.returnValue(
			of({
				mawbId: 'test-mawb',
				latestStatus: 'DELIVERED',
				updateTime: '2025-01-01T12:00:00Z',
				orgName: 'Test Org',
				userName: 'Test User',
				updateBy: 'test-user',
				checked: false,
				opened: false,
				code: 'DEL',
				eventDate: '2025-01-01',
			})
		),
		listHawbStatusByMawb: jasmine.createSpy('listHawbStatusByMawb').and.returnValue(
			of({
				rows: [
					{
						hawbId: 'hawb-1',
						latestStatus: 'DELIVERED',
						updateTime: '2025-01-01T12:00:00Z',
						orgName: 'Test Org',
						userName: 'Test User',
						updateBy: 'test-user',
						checked: false,
						opened: false,
					},
				],
				total: 1,
			})
		),
		listPieceStatusByHawb: jasmine.createSpy('listPieceStatusByHawb').and.returnValue(
			of({
				rows: [
					{
						pieceId: 'piece-1',
						latestStatus: 'DELIVERED',
						updateTime: '2025-01-01T12:00:00Z',
						orgName: 'Test Org',
						userName: 'Test User',
						updateBy: 'test-user',
						checked: false,
						opened: false,
					},
				],
				total: 1,
			})
		),
	};

	beforeEach(async () => {
		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		mockTranslateService = jasmine.createSpyObj('TranslateService', ['instant', 'get']);
		mockCdr = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

		mockTranslateService.instant.and.returnValue('mawb.event.tracking.title');
		mockTranslateService.get.and.returnValue(of('mawb.event.tracking.title'));
		mockDialog.open.and.returnValue({
			afterClosed: () => of(true),
			componentInstance: {},
		} as any);

		await TestBed.configureTestingModule({
			imports: [MawbStatusListComponent, RolesAwareComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MawbStatusService, useValue: mockStatusService },
				{ provide: ChangeDetectorRef, useValue: mockCdr },
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClientTesting(),
				provideHttpClient(),
				{ provide: takeUntilDestroyed, useValue: () => (source: any) => source },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(MawbStatusListComponent);
		component = fixture.componentInstance;
		const mockUser = createMockUserProfile({ primaryOrgId: 'org123', orgType: 'GHA' });
		spyOn(component, 'getCurrentUser').and.returnValue(of(mockUser));

		// Assign the mock dialog directly to the component
		(component as any).dialog = mockDialog;

		component.mawbId = 'test-mawb';
		component.tabLabel = 'mawb.event.tracking.title';
		component.mawbStatus = {
			mawbId: 'test-mawb',
			latestStatus: 'DELIVERED',
			updateTime: '2025-01-01T12:00:00Z',
			orgName: 'Test Org',
			userName: 'Test User',
			updateBy: 'test-user',
			checked: false,
			opened: false,
			code: 'DEL',
			eventDate: '2025-01-01',
		};

		fixture.detectChanges();
		statusService = TestBed.inject(MawbStatusService) as jasmine.SpyObj<MawbStatusService>;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should not load children if already loaded', () => {
		const mockEvent: any = {
			opened: false,
			pieceStatusList: ['some-data'],
			hawbId: 'parent2',
		};

		component.togglePanel(mockEvent);

		expect(mockEvent.opened).toBe(true);
	});

	it('should load children and update dataLoading and childrenList', () => {
		const mockParent: any = {
			hawbId: 'parent3',
			pageNum: 1,
			pieceStatusList: [],
			opened: false,
			checked: false,
		};
		const mockRes: any = {
			pageNum: 1,
			total: 6,
			rows: [
				{
					pieceId: '1111',
					updateTime: '2025-01-01 12:00:00Z',
					latestStatus: 'UP',
					orgName: '1111',
					userName: 'tom',
					updateBy: '111:tom',
					checked: false,
					opened: false,
				},
				{
					pieceId: '2222',
					updateTime: '2025-01-01 12:00:00Z',
					latestStatus: 'UP',
					orgName: '1111',
					userName: 'tom',
					updateBy: '111:tom',
					checked: false,
					opened: false,
				},
				{
					pieceId: '33333',
					updateTime: '2025-01-01 12:00:00Z',
					latestStatus: 'UP',
					orgName: '1111',
					userName: 'tom',
					updateBy: '111:tom',
					checked: false,
					opened: false,
				},
			],
		};

		statusService.listPieceStatusByHawb.and.returnValue(of(mockRes));

		component.loadPieces(mockParent);

		expect(component.dataLoading).toBe(false);
	});

	describe('toggleParent', () => {
		it('should toggle parent and check all children', () => {
			component.toggleMawb();

			expect(component.mawbStatus?.checked).toBe(true);
			component.hawbStatusList.forEach((child) => {
				expect(child.checked).toBe(true);
			});
		});

		it('should uncheck parent and uncheck all children', () => {
			component.mawbStatus.checked = true;
			component.toggleMawb();

			expect(component.mawbStatus?.checked).toBe(false);
			component.hawbStatusList.forEach((child) => {
				expect(child.checked).toBe(false);
			});
		});

		it('should return early if mawbStatus is null', () => {
			component.mawbStatus = null as any;
			const initialHawbList = [...component.hawbStatusList];

			component.toggleMawb();

			expect(component.hawbStatusList).toEqual(initialHawbList);
		});
	});

	describe('toggleHawb', () => {
		beforeEach(() => {
			component.hawbStatusList = [
				{
					hawbId: 'hawb-1',
					hawbNumber: 'HAWB001',
					latestStatus: 'DELIVERED',
					updateTime: '2025-01-01T12:00:00Z',
					orgName: 'Test Org',
					userName: 'Test User',
					updateBy: 'test-user',
					checked: false,
					opened: false,
					pageNum: 1,
					noMorePieces: false,
					pieceStatusList: [
						{
							pieceId: 'piece-1',
							latestStatus: 'DELIVERED',
							updateTime: '2025-01-01T12:00:00Z',
							orgName: 'Test Org',
							userName: 'Test User',
							updateBy: 'test-user',
							checked: false,
							opened: false,
						},
					],
				},
			];
		});

		it('should toggle hawb and update all pieces', () => {
			const hawb = component.hawbStatusList[0];

			component.toggleHawb(hawb);

			expect(hawb.checked).toBe(true);
			expect(hawb.pieceStatusList[0].checked).toBe(true);
		});

		it('should update mawb status when all hawbs are checked', () => {
			const hawb = component.hawbStatusList[0];
			hawb.checked = false;
			component.mawbStatus.checked = false;

			component.toggleHawb(hawb);

			expect(component.mawbStatus.checked).toBe(true);
		});

		it('should not update mawb status when not all hawbs are checked', () => {
			component.hawbStatusList.push({
				hawbId: 'hawb-2',
				hawbNumber: 'HAWB002',
				latestStatus: 'DELIVERED',
				updateTime: '2025-01-01T12:00:00Z',
				orgName: 'Test Org',
				userName: 'Test User',
				updateBy: 'test-user',
				checked: false,
				opened: false,
				pageNum: 1,
				noMorePieces: false,
				pieceStatusList: [],
			});

			const hawb = component.hawbStatusList[0];
			component.toggleHawb(hawb);

			expect(component.mawbStatus.checked).toBe(false);
		});
	});

	describe('togglePiece', () => {
		let mockHawb: HAWBEventListObj;
		let mockPiece: PieceEventListObj;

		beforeEach(() => {
			mockPiece = {
				pieceId: 'piece-1',
				latestStatus: 'DELIVERED',
				updateTime: '2025-01-01T12:00:00Z',
				orgName: 'Test Org',
				userName: 'Test User',
				updateBy: 'test-user',
				checked: false,
				opened: false,
			};

			mockHawb = {
				hawbId: 'hawb-1',
				hawbNumber: 'HAWB001',
				latestStatus: 'DELIVERED',
				updateTime: '2025-01-01T12:00:00Z',
				orgName: 'Test Org',
				userName: 'Test User',
				updateBy: 'test-user',
				checked: false,
				opened: false,
				pageNum: 1,
				noMorePieces: false,
				pieceStatusList: [mockPiece],
			};

			component.hawbStatusList = [mockHawb];
			component.mawbStatus.checked = false;
		});

		it('should toggle piece and update hawb status', () => {
			component.togglePiece(mockHawb, mockPiece);

			expect(mockPiece.checked).toBe(true);
			expect(mockHawb.checked).toBe(true);
		});

		it('should uncheck other hawbs when piece is checked and mawb is unchecked', () => {
			const otherHawb: HAWBEventListObj = {
				hawbId: 'hawb-2',
				hawbNumber: 'HAWB002',
				latestStatus: 'DELIVERED',
				updateTime: '2025-01-01T12:00:00Z',
				orgName: 'Test Org',
				userName: 'Test User',
				updateBy: 'test-user',
				checked: true,
				opened: false,
				pageNum: 1,
				noMorePieces: false,
				pieceStatusList: [
					{
						pieceId: 'piece-2',
						latestStatus: 'DELIVERED',
						updateTime: '2025-01-01T12:00:00Z',
						orgName: 'Test Org',
						userName: 'Test User',
						updateBy: 'test-user',
						checked: true,
						opened: false,
					},
				],
			};

			component.hawbStatusList.push(otherHawb);

			component.togglePiece(mockHawb, mockPiece);

			expect(otherHawb.checked).toBe(false);
			expect(otherHawb.pieceStatusList[0].checked).toBe(false);
		});

		it('should update mawb status when all hawbs are checked', () => {
			component.togglePiece(mockHawb, mockPiece);

			expect(component.mawbStatus.checked).toBe(true);
		});
	});

	describe('onWindowScroll', () => {
		beforeEach(() => {
			Object.defineProperty(window, 'scrollY', { value: 500, writable: true });
			Object.defineProperty(document.documentElement, 'scrollHeight', { value: 1000, writable: true });
			Object.defineProperty(window, 'innerHeight', { value: 400, writable: true });
			component.dataLoading = false;
			component.noMoreHawb = false;
		});

		it('should return early if mawbId is not set', () => {
			component.mawbId = '';
			spyOn(component as any, 'listHawbByMawbId');

			component.onWindowScroll();

			expect((component as any).listHawbByMawbId).not.toHaveBeenCalled();
		});

		it('should return early if tabLabel does not match', () => {
			component.tabLabel = 'Different Tab';
			spyOn(component as any, 'listHawbByMawbId');

			component.onWindowScroll();

			expect((component as any).listHawbByMawbId).not.toHaveBeenCalled();
		});

		it('should return early if dataLoading is true', () => {
			component.dataLoading = true;
			spyOn(component as any, 'listHawbByMawbId');

			component.onWindowScroll();

			expect((component as any).listHawbByMawbId).not.toHaveBeenCalled();
		});

		it('should return early if noMoreHawb is true', () => {
			component.noMoreHawb = true;
			spyOn(component as any, 'listHawbByMawbId');

			component.onWindowScroll();

			expect((component as any).listHawbByMawbId).not.toHaveBeenCalled();
		});
	});

	describe('onScroll', () => {
		let mockHawb: HAWBEventListObj;
		let mockEvent: Event;

		beforeEach(() => {
			mockHawb = {
				hawbId: 'hawb-1',
				hawbNumber: 'HAWB001',
				latestStatus: 'DELIVERED',
				updateTime: '2025-01-01T12:00:00Z',
				orgName: 'Test Org',
				userName: 'Test User',
				updateBy: 'test-user',
				checked: false,
				opened: false,
				pageNum: 1,
				noMorePieces: false,
				pieceStatusList: [],
			};

			const mockDiv = {
				scrollHeight: 1000,
				scrollTop: 900,
				clientHeight: 100,
			};

			mockEvent = {
				target: mockDiv,
			} as any;

			component.dataLoading = false;
		});

		it('should call loadPieces when at bottom and not loading', () => {
			spyOn(component, 'loadPieces');

			component.onScroll(mockEvent, mockHawb);

			expect(component.loadPieces).toHaveBeenCalledWith(mockHawb);
		});

		it('should not call loadPieces when dataLoading is true', () => {
			component.dataLoading = true;
			spyOn(component, 'loadPieces');

			component.onScroll(mockEvent, mockHawb);

			expect(component.loadPieces).not.toHaveBeenCalled();
		});

		it('should not call loadPieces when noMorePieces is true', () => {
			mockHawb.noMorePieces = true;
			spyOn(component, 'loadPieces');

			component.onScroll(mockEvent, mockHawb);

			expect(component.loadPieces).not.toHaveBeenCalled();
		});
	});

	describe('updateBtnDisabled', () => {
		beforeEach(() => {
			component.mawbStatus = {
				mawbId: 'test-mawb',
				latestStatus: 'DELIVERED',
				updateTime: '2025-01-01T12:00:00Z',
				orgName: 'Test Org',
				userName: 'Test User',
				updateBy: 'test-user',
				checked: false,
				opened: false,
				code: 'DEL',
				eventDate: '2025-01-01',
			};
		});

		it('should return true if mawbStatus is null', () => {
			component.mawbStatus = null as any;

			const result = component.updateBtnDisabled();

			expect(result).toBe(true);
		});

		it('should return false if mawbStatus is checked', () => {
			component.mawbStatus.checked = true;

			const result = component.updateBtnDisabled();

			expect(result).toBe(false);
		});

		it('should return false if any hawb is checked', () => {
			component.hawbStatusList = [
				{
					hawbId: 'hawb-1',
					hawbNumber: 'HAWB001',
					latestStatus: 'DELIVERED',
					updateTime: '2025-01-01T12:00:00Z',
					orgName: 'Test Org',
					userName: 'Test User',
					updateBy: 'test-user',
					checked: true,
					opened: false,
					pageNum: 1,
					noMorePieces: false,
					pieceStatusList: [],
				},
			];

			const result = component.updateBtnDisabled();

			expect(result).toBe(false);
		});

		it('should return false if any piece is checked', () => {
			component.hawbStatusList = [
				{
					hawbId: 'hawb-1',
					hawbNumber: 'HAWB001',
					latestStatus: 'DELIVERED',
					updateTime: '2025-01-01T12:00:00Z',
					orgName: 'Test Org',
					userName: 'Test User',
					updateBy: 'test-user',
					checked: false,
					opened: false,
					pageNum: 1,
					noMorePieces: false,
					pieceStatusList: [
						{
							pieceId: 'piece-1',
							latestStatus: 'DELIVERED',
							updateTime: '2025-01-01T12:00:00Z',
							orgName: 'Test Org',
							userName: 'Test User',
							updateBy: 'test-user',
							checked: true,
							opened: false,
						},
					],
				},
			];

			const result = component.updateBtnDisabled();

			expect(result).toBe(false);
		});

		it('should return true if nothing is checked', () => {
			component.hawbStatusList = [
				{
					hawbId: 'hawb-1',
					hawbNumber: 'HAWB001',
					latestStatus: 'DELIVERED',
					updateTime: '2025-01-01T12:00:00Z',
					orgName: 'Test Org',
					userName: 'Test User',
					updateBy: 'test-user',
					checked: false,
					opened: false,
					pageNum: 1,
					noMorePieces: false,
					pieceStatusList: [
						{
							pieceId: 'piece-1',
							latestStatus: 'DELIVERED',
							updateTime: '2025-01-01T12:00:00Z',
							orgName: 'Test Org',
							userName: 'Test User',
							updateBy: 'test-user',
							checked: false,
							opened: false,
						},
					],
				},
			];

			const result = component.updateBtnDisabled();

			expect(result).toBe(true);
		});
	});

	describe('bulkUpdate', () => {
		beforeEach(() => {
			mockDialog.open.calls.reset();
			component.mawbStatus = {
				mawbId: 'test-mawb',
				latestStatus: 'DELIVERED',
				updateTime: '2025-01-01T12:00:00Z',
				orgName: 'Test Org',
				userName: 'Test User',
				updateBy: 'test-user',
				checked: false,
				opened: false,
				code: 'DEL',
				eventDate: '2025-01-01',
			};
		});

		it('should return early if mawbStatus is null', () => {
			component.mawbStatus = null as any;

			component.bulkUpdate();

			expect(mockDialog.open).not.toHaveBeenCalled();
		});

		it('should open dialog with correct parameters when mawb is checked', () => {
			component.mawbStatus.checked = true;

			component.bulkUpdate();

			expect(mockDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
				width: '50vw',
				autoFocus: false,
				data: {
					param: {
						mawbId: 'test-mawb',
						choseAllHAWB: true,
						hawbIdList: [],
					},
				},
			});
		});

		it('should open dialog with hawb list when specific hawbs are selected', () => {
			component.hawbStatusList = [
				{
					hawbId: 'hawb-1',
					hawbNumber: 'HAWB001',
					latestStatus: 'DELIVERED',
					updateTime: '2025-01-01T12:00:00Z',
					orgName: 'Test Org',
					userName: 'Test User',
					updateBy: 'test-user',
					checked: true,
					opened: false,
					pageNum: 1,
					noMorePieces: false,
					pieceStatusList: [],
				},
			];

			component.bulkUpdate();

			expect(mockDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
				width: '50vw',
				autoFocus: false,
				data: {
					param: {
						mawbId: 'test-mawb',
						choseAllHAWB: false,
						hawbIdList: [
							{
								hawbId: 'hawb-1',
								choseAllPiece: false,
								pieceIdList: [],
							},
						],
					},
				},
			});
		});

		it('should call initData when dialog closes with false', () => {
			const initSpy = spyOn(component as any, 'initData');
			component.hawbStatusList = [
				{
					hawbId: 'hawb-1',
					hawbNumber: 'HAWB001',
					latestStatus: 'DELIVERED',
					updateTime: '2025-01-01T12:00:00Z',
					orgName: 'Test Org',
					userName: 'Test User',
					updateBy: 'test-user',
					checked: true,
					opened: false,
					pageNum: 1,
					noMorePieces: false,
					pieceStatusList: [],
				},
			];

			// dialog returns false
			mockDialog.open.and.returnValue({ afterClosed: () => of(false) } as any);

			component.bulkUpdate();
			expect(initSpy).toHaveBeenCalled();
		});
	});

	describe('uncheckOtherHawb and listHawbByMawbId', () => {
		it('uncheckOtherHawb should clear other hawb and piece selections', () => {
			const hawbA: any = { hawbId: 'A', pieceStatusList: [{ pieceId: 'p1', checked: true }], checked: true };
			const hawbB: any = { hawbId: 'B', pieceStatusList: [{ pieceId: 'p2', checked: true }], checked: true };
			component.hawbStatusList = [hawbA, hawbB];
			(component as any).uncheckOtherHawb(hawbA);
			expect(component.hawbStatusList[1].checked).toBeFalse();
			expect(component.hawbStatusList[1].pieceStatusList[0].checked).toBeFalse();
		});

		it('listHawbByMawbId should append rows and set noMoreHawb when total reached', () => {
			component.mawbId = 'MAWB-10';
			component.currentPageNum = 1;
			// start with empty list to assert exact append
			component.hawbStatusList = [];
			const resp = { total: 2, rows: [{ hawbId: 'h1' }, { hawbId: 'h2' }] } as any;
			statusService.listHawbStatusByMawb.and.returnValue(of(resp));

			(component as any).listHawbByMawbId();
			expect(component.hawbStatusList.length).toBe(2);
			expect(component.noMoreHawb).toBeTrue();
		});
	});

	describe('openHistory', () => {
		it('should open status history dialog with correct parameters', () => {
			mockDialog.open.calls.reset();
			component.openHistory('test-id', 'mawb');

			expect(mockDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
				width: '60vw',
				autoFocus: false,
				data: {
					loId: 'test-id',
					type: 'mawb',
				},
			});
		});
	});

	describe('updateLog', () => {
		it('should open update log dialog with mawbId', () => {
			mockDialog.open.calls.reset();
			const param: StatusUpdateLog = {
				bizId: '',
				batchId: '1111',
				type: '',
				operationStatus: '',
				newValue: '',
				errorMsg: '',
				createDate: '',
				userName: '',
				orgName: '',
			};

			component.updateLog(param);

			expect(mockDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
				width: '90vw',
				autoFocus: false,
				data: param.batchId,
			});
		});
	});

	describe('ngOnInit', () => {
		it('should initialize data and set current user org type', () => {
			component.ngOnInit();

			expect(component.currentUserOrgType).toBe('GHA');
		});
	});

	describe('initData error handling', () => {
		it('should handle 403 error and trigger delegation request', () => {
			const error = {
				status: 403,
				error: {
					data: 'test-mawb',
					code: 403,
					msg: 'Permission denied',
				},
			};
			statusService.getMawbStatus.and.returnValue(throwError(() => error));
			spyOn(component, 'delegationRequest').and.returnValue(of(true));
			spyOn(component.refreshDelegationRequest, 'emit');

			component.mawbId = 'test-mawb';
			(component as any).initData();

			expect(component.delegationRequest).toHaveBeenCalledWith(error.error, 'test-mawb');
			expect(component.refreshDelegationRequest.emit).toHaveBeenCalled();
		});

		it('should handle non-403 errors', () => {
			const error = { status: 500, error: 'Server Error' };
			statusService.getMawbStatus.and.returnValue(throwError(() => error));

			component.mawbId = 'test-mawb';
			(component as any).initData();

			expect(component.dataLoading).toBe(false);
		});
	});

	describe('loadPieces error handling', () => {
		it('should handle error in loadPieces', () => {
			const mockHawb: HAWBEventListObj = {
				hawbId: 'hawb-1',
				hawbNumber: 'HAWB001',
				latestStatus: 'DELIVERED',
				updateTime: '2025-01-01T12:00:00Z',
				orgName: 'Test Org',
				userName: 'Test User',
				updateBy: 'test-user',
				checked: false,
				opened: false,
				pageNum: 1,
				noMorePieces: false,
				pieceStatusList: [],
			};

			statusService.listPieceStatusByHawb.and.returnValue(throwError(() => new Error('Test error')));

			component.loadPieces(mockHawb);

			expect(component.dataLoading).toBe(false);
		});
	});
});
