// @ts-check
const eslint = require('@eslint/js');
const tseslint = require('typescript-eslint');
const angular = require('angular-eslint');
const stylistic = require('@stylistic/eslint-plugin');
const eslintPluginPrettierRecommended = require('eslint-config-prettier');

module.exports = tseslint.config(
	{
		ignores: ['dist/*', 'coverage/*', '.angular/*', 'src/assets/*'],
	},
	eslintPluginPrettierRecommended,
	{
		files: ['**/*.ts'],
		plugins: { stylistic },
		extends: [
			eslint.configs.recommended,
			...tseslint.configs.recommended,
			...tseslint.configs.stylistic,
			...angular.configs.tsRecommended,
		],
		processor: angular.processInlineTemplates,
		rules: {
			'max-len': ['warn', { code: 160 }],
			'no-inferrable-types': 0,
			'no-console': ['error', { allow: ['warn', 'error'] }],
			'no-debugger': 'error',
			'no-empty': 'error',
			'brace-style': 'off',
			'stylistic/brace-style': ['error', '1tbs', { allowSingleLine: true }],
			eqeqeq: 'error',
			'consistent-return': 'error',
			'no-multi-spaces': 'error',
			'no-empty-function': 'off',
			'@typescript-eslint/no-empty-function': ['error'],
			quotes: 'off',
			'@typescript-eslint/no-explicit-any': 'off',
			'@angular-eslint/no-input-rename': 'off',
			'@typescript-eslint/no-unused-expressions': ['error', { allowTernary: true }],
			'stylistic/quotes': [
				'error',
				'single',
				{
					avoidEscape: true,
					allowTemplateLiterals: true,
				},
			],
			'@typescript-eslint/naming-convention': [
				'error',
				{
					selector: 'default',
					format: ['camelCase'],
					filter: {
						// allow the pagination and Authorization names
						regex: '^(pagination\\.[a-zA-Z]+)|Authorization$',
						match: false,
					},
				},
				{
					selector: 'variable',
					format: ['camelCase', 'UPPER_CASE'],
				},
				{
					selector: 'parameter',
					format: ['camelCase'],
					leadingUnderscore: 'forbid',
				},
				{
					selector: 'memberLike',
					modifiers: ['private'],
					format: ['camelCase'],
					leadingUnderscore: 'allow',
				},
				{
					selector: 'typeLike',
					format: ['PascalCase'],
				},
				{
					selector: 'variable',
					modifiers: ['const', 'global', 'exported'],
					format: ['UPPER_CASE'],
				},
				{
					selector: 'enumMember',
					format: ['UPPER_CASE'],
				},
			],
		},
	},
	{
		files: ['**/*.html'],
		extends: [...angular.configs.templateRecommended],
		rules: {
			'max-len': [
				'error',
				{
					code: 180,
					ignorePattern: '^\\s*(<path|<svg|d="|viewBox=")[^>]*>?.*$',
					ignoreUrls: true,
					ignoreStrings: true,
				},
			],
		},
	}
);
