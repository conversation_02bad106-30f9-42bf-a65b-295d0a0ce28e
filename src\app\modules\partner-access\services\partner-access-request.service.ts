import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiService } from '@shared/services/api.service';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { Observable } from 'rxjs';
import { BizData, PartnerListObject } from '../models/partner-access.model';
import { Organization } from '@shared/models/organization.model';

@Injectable({
	providedIn: 'root',
})
export class PartnerAccessRequestService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getPartnerList(pageParams: PaginationRequest, keywords: string): Observable<PaginationResponse<PartnerListObject>> {
		return super.getData<PaginationResponse<PartnerListObject>>('partner-access/list', {
			...pageParams,
			keywords,
		});
	}

	getSystemList(type: string): Observable<BizData[]> {
		return super.getData<BizData[]>('sys-management/enums/businessType', {
			type,
		});
	}

	getOrgList(name: string, type: string): Observable<Organization[]> {
		return super.getData<Organization[]>('org/org/list', {
			name,
			type,
		});
	}

	addPartner(partner: PartnerListObject): Observable<string> {
		return super.postData<string>('partner-access/add', partner);
	}

	updatePartners(partners: PartnerListObject[]): Observable<string> {
		return super.updateData<string>('partner-access/update', partners);
	}

	deletePartners(ids: string[]): Observable<string> {
		return super.deleteData<string>('partner-access/delete', { ids });
	}
}
