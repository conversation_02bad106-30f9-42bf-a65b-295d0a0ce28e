import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import BookingOptionRequestListComponent from './booking-option-request-list.component';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { BookingOptionRequestListObj } from '../../models/booking.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { of } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { UserProfileService } from '@shared/services/user-profile.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

const bookingList: BookingOptionRequestListObj[] = [
	{
		id: '111',
		bookingOptionRequestId: '111',
		expectedCommodity: '11',
		departureLocation: '11',
		arrivalLocation: '11',
		latestStatus: '11',
	},
	{
		id: '2222',
		bookingOptionRequestId: '222',
		expectedCommodity: '222',
		departureLocation: '222',
		arrivalLocation: '22',
		latestStatus: '22',
	},
];

const mocRes: PaginationResponse<BookingOptionRequestListObj> = {
	total: 2,
	rows: bookingList,
};

describe('BookingQuoteListComponent', () => {
	let component: BookingOptionRequestListComponent;
	let fixture: ComponentFixture<BookingOptionRequestListComponent>;
	let mockService: jasmine.SpyObj<BookingOptionRequestService>;
	let mockProfileService: jasmine.SpyObj<UserProfileService>;
	let dialogSpy: jasmine.SpyObj<MatDialog>;
	let routerSpy: jasmine.SpyObj<Router>;

	beforeEach(async () => {
		mockService = jasmine.createSpyObj('BookingOptionRequestService', ['getDataPerPage']);
		mockService.getDataPerPage.and.returnValue(of(mocRes));

		mockProfileService = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		mockProfileService.hasPermission.and.returnValue(of(true));
		mockProfileService.hasSomeRole.and.returnValue(of(true));

		dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
		routerSpy = jasmine.createSpyObj('Router', ['navigate']);

		await TestBed.configureTestingModule({
			imports: [BookingOptionRequestListComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: BookingOptionRequestService, useValue: mockService },
				{ provide: UserProfileService, useValue: mockProfileService },
				{ provide: MatDialog, useValue: dialogSpy },
				{ provide: Router, useValue: routerSpy },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(BookingOptionRequestListComponent);
		component = fixture.componentInstance;
		(component as any).dialog = dialogSpy;
		fixture.detectChanges();
	});

	describe('dialog and navigation helpers', () => {
		it('shareBookingOption should open dialog with data', () => {
			const row = bookingList[0];
			dialogSpy.open.and.returnValue({ afterClosed: () => of(false) } as any);
			component.shareBookingOption(row);
			expect(dialogSpy.open).toHaveBeenCalledWith(jasmine.any(Function), jasmine.any(Object));
		});

		it('createBookingOptions should navigate with state', () => {
			const row = bookingList[0];
			component.createBookingOptions(row);
			expect(routerSpy.navigate).toHaveBeenCalledWith(['/quote/option'], { state: { id: row.bookingOptionRequestId } });
		});

		it('createBookingOptionRequest should open dialog and refresh params on close', () => {
			const dialogRef = { afterClosed: () => of(true) } as any;
			dialogSpy.open.and.returnValue(dialogRef);
			component.param = {};
			component.createBookingOptionRequest();
			expect(dialogSpy.open).toHaveBeenCalled();
			expect(component.param).toEqual({});
		});

		it('viewOptionDetail should open dialog and refresh params on close', () => {
			const dialogRef = { afterClosed: () => of(true) } as any;
			dialogSpy.open.and.returnValue(dialogRef);
			const row = bookingList[0];
			component.viewOptionDetail(row);
			expect(dialogSpy.open).toHaveBeenCalled();
		});
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('onSearch', () => {
		beforeEach(() => {
			component.param = {}; // Reset param before each test
		});

		it('should add keyword to params when description has value', () => {
			// Set up form with description
			component.bookingOptionSearchForm.setValue({ productDescription: 'test search' });

			component.onSearch();

			expect(component.param).toEqual({ keyword: 'test search' });
		});

		it('should not modify params when description is empty', () => {
			// Set up form with empty description
			component.bookingOptionSearchForm.setValue({ productDescription: '' });

			component.onSearch();

			expect(component.param).toEqual({});
		});

		it('should preserve existing params when adding keyword', () => {
			component.param = { page: 1, size: 10 };
			component.bookingOptionSearchForm.setValue({ productDescription: 'test' });

			component.onSearch();

			expect(component.param).toEqual({
				page: 1,
				size: 10,
				keyword: 'test',
			});
		});
	});
});
