import { TestBed } from '@angular/core/testing';
import { RouteGuardService } from './route-guard.service';
import { UserProfileService } from './user-profile.service';
import { Observable, of } from 'rxjs';
import { UserProfile } from '@shared/models/user-profile.model';

describe('RouteGuardService', () => {
	let service: RouteGuardService;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;

	const createMockUserProfile = (overrides: Partial<UserProfile> = {}): UserProfile => ({
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'SHP',
		userType: '1',
		menuList: [],
		permissionList: [
			{
				name: 'SLI Management',
				module: 'sli',
				code: ['create', 'update', 'query'],
			},
		],
		orgList: [],
		...overrides,
	});

	beforeEach(() => {
		const spy = jasmine.createSpyObj('UserProfileService', ['getProfile', 'hasSomeRole']);

		TestBed.configureTestingModule({
			providers: [RouteGuardService, { provide: UserProfileService, useValue: spy }],
		});

		service = TestBed.inject(RouteGuardService);
		userProfileServiceSpy = TestBed.inject(UserProfileService) as jasmine.SpyObj<UserProfileService>;
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	describe('isSuperUser', () => {
		it('should return true when user is super user', (done: DoneFn) => {
			// Arrange
			userProfileServiceSpy.getProfile.and.returnValue(of({ ...createMockUserProfile(), userType: '3' }));

			// Act
			service.isSuperUser().subscribe((result) => {
				// Assert
				expect(result).toBe(true);
				expect(userProfileServiceSpy.getProfile).toHaveBeenCalled();
				done();
			});
		});

		it('should return false when user is not super user', (done: DoneFn) => {
			// Arrange
			userProfileServiceSpy.getProfile.and.returnValue(of(createMockUserProfile()));

			// Act
			service.isSuperUser().subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				expect(userProfileServiceSpy.getProfile).toHaveBeenCalled();
				done();
			});
		});

		it('should return observable of boolean', () => {
			// Arrange
			userProfileServiceSpy.getProfile.and.returnValue(of(createMockUserProfile()));

			// Act
			const result = service.isSuperUser();

			// Assert
			expect(result).toBeInstanceOf(Observable);
		});
	});

	describe('hasSomeRole', () => {
		it('should return true when user has one of the needed roles', (done: DoneFn) => {
			// Arrange
			const neededRoles = ['SHIPPER', 'FORWARDER'];
			userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

			// Act
			service.hasSomeRole(neededRoles).subscribe((result) => {
				// Assert
				expect(result).toBe(true);
				expect(userProfileServiceSpy.hasSomeRole).toHaveBeenCalledWith(neededRoles);
				done();
			});
		});

		it('should return false when user does not have any of the needed roles', (done: DoneFn) => {
			// Arrange
			const neededRoles = ['ADMIN', 'MANAGER'];
			userProfileServiceSpy.hasSomeRole.and.returnValue(of(false));

			// Act
			service.hasSomeRole(neededRoles).subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				expect(userProfileServiceSpy.hasSomeRole).toHaveBeenCalledWith(neededRoles);
				done();
			});
		});

		it('should return false when neededRoles is empty array', (done: DoneFn) => {
			// Arrange
			const neededRoles: string[] = [];

			// Act
			service.hasSomeRole(neededRoles).subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				expect(userProfileServiceSpy.hasSomeRole).not.toHaveBeenCalled();
				done();
			});
		});

		it('should return false when neededRoles is null', (done: DoneFn) => {
			// Arrange
			const neededRoles: string[] = null as any;

			// Act
			service.hasSomeRole(neededRoles).subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				expect(userProfileServiceSpy.hasSomeRole).not.toHaveBeenCalled();
				done();
			});
		});

		it('should return false when neededRoles is undefined', (done: DoneFn) => {
			// Arrange
			const neededRoles: string[] = undefined as any;

			// Act
			service.hasSomeRole(neededRoles).subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				expect(userProfileServiceSpy.hasSomeRole).not.toHaveBeenCalled();
				done();
			});
		});

		it('should return observable of boolean', () => {
			// Arrange
			const neededRoles = ['SHIPPER'];
			userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

			// Act
			const result = service.hasSomeRole(neededRoles);

			// Assert
			expect(result).toBeInstanceOf(Observable);
		});

		it('should handle single role in array', (done: DoneFn) => {
			// Arrange
			const neededRoles = ['SHIPPER'];
			userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

			// Act
			service.hasSomeRole(neededRoles).subscribe((result) => {
				// Assert
				expect(result).toBe(true);
				expect(userProfileServiceSpy.hasSomeRole).toHaveBeenCalledWith(neededRoles);
				done();
			});
		});

		it('should handle multiple roles in array', (done: DoneFn) => {
			// Arrange
			const neededRoles = ['SHIPPER', 'FORWARDER', 'ADMIN'];
			userProfileServiceSpy.hasSomeRole.and.returnValue(of(false));

			// Act
			service.hasSomeRole(neededRoles).subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				expect(userProfileServiceSpy.hasSomeRole).toHaveBeenCalledWith(neededRoles);
				done();
			});
		});

		it('should pass through the result from UserProfileService.hasSomeRole', (done: DoneFn) => {
			// Arrange
			const neededRoles = ['TEST_ROLE'];
			const expectedResult = true;
			userProfileServiceSpy.hasSomeRole.and.returnValue(of(expectedResult));

			// Act
			service.hasSomeRole(neededRoles).subscribe((result) => {
				// Assert
				expect(result).toBe(expectedResult);
				done();
			});
		});
	});

	describe('Integration Tests', () => {
		it('should work correctly when both methods are called in sequence', (done: DoneFn) => {
			// Arrange
			userProfileServiceSpy.getProfile.and.returnValue(of(createMockUserProfile()));
			userProfileServiceSpy.hasSomeRole.and.returnValue(of(false));
			const neededRoles = ['SHIPPER'];

			// Act & Assert
			service.isSuperUser().subscribe((isSuperUserResult) => {
				expect(isSuperUserResult).toBe(false);

				service.hasSomeRole(neededRoles).subscribe((hasSomeRoleResult) => {
					expect(hasSomeRoleResult).toBe(false);
					expect(userProfileServiceSpy.getProfile).toHaveBeenCalled();
					expect(userProfileServiceSpy.hasSomeRole).toHaveBeenCalledWith(neededRoles);
					done();
				});
			});
		});
	});

	describe('Error Handling', () => {
		it('should handle errors from UserProfileService.hasSomeRole', (done: DoneFn) => {
			// Arrange
			const neededRoles = ['SHIPPER'];
			const errorMessage = 'Service error';
			userProfileServiceSpy.hasSomeRole.and.returnValue(
				new Observable((subscriber) => {
					subscriber.error(new Error(errorMessage));
				})
			);

			// Act
			service.hasSomeRole(neededRoles).subscribe({
				next: () => {
					fail('Should have thrown an error');
				},
				error: (error) => {
					// Assert
					expect(error.message).toBe(errorMessage);
					done();
				},
			});
		});
	});
});
