import { TestBed } from '@angular/core/testing';
import { WeightCalculateRequestService } from './weight-calculate-request.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { WeightInfoListObj } from '@shared/models/weight-info';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { environment } from '@environments/environment';

const baseUrl = environment.baseApi;

const pageReq: PaginationRequest = {
	pageNum: 0,
	pageSize: 0,
};

describe('WeightCalculateRequestService', () => {
	let service: WeightCalculateRequestService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [WeightCalculateRequestService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(WeightCalculateRequestService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should getSliData', () => {
		const mockResponse: WeightInfoListObj = {
			totalSlac: 0,
			totalGrossWeight: 0,
			noPieceRcp: 0,
			chargableWeight: 0,
			pieceGrossWeight: 0,
			volume: 0,
			pieces: [],
		};
		const sliId = '111';

		service.getSliData(pageReq, sliId).subscribe((res) => {
			expect(res).toEqual(mockResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/sli/piece/piecesCaculation?pageNum=0&pageSize=0&sliId=111`);
		expect(req.request.method).toBe('GET');
		req.flush(mockResponse);
	});

	it('should   getHawbData', () => {
		const mockResponse: WeightInfoListObj = {
			totalSlac: 0,
			totalGrossWeight: 0,
			noPieceRcp: 0,
			chargableWeight: 0,
			pieceGrossWeight: 0,
			volume: 0,
			pieces: [],
		};
		const hawbIds = ['111'];

		service.getHawbData(pageReq, hawbIds).subscribe((res) => {
			expect(res).toEqual(mockResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/hawb-management/piece/piecesCaculation`);
		expect(req.request.method).toBe('POST');
		req.flush(mockResponse);
	});
});
