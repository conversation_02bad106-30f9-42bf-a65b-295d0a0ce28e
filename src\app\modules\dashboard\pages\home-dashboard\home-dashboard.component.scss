.orll-home-dashboard {
	margin-top: 40px;

	&__title {
		width: 60vw;
		line-height: 40px;
		font-size: 40px;
		margin-bottom: 40px;
		color: var(--iata-blue-primary);
	}

	&__chart {
		display: flex;
		margin-bottom: 40px;
		padding: 20px;

		.chart-container {
			width: 100%;
			height: 400px;

			&__summary {
				display: flex;
				flex-direction: column;
				gap: 10px;
				padding: 20px;
			}

			&__summary-item {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.right-number {
					font-weight: 600;
				}
			}
		}
	}

	&__document-title {
		font-size: 16px;
		margin: 20px 0;
	}

	&__document-content {
		padding: 20px;

		.margin-b-20 {
			margin-bottom: 20px;
		}
	}

	&__document-item-title {
		display: flex;
		align-items: center;
		gap: 10px;
	}

	&__document-item-links {
		display: flex;
		flex-direction: column;
		gap: 10px;
		margin: 20px 35px;

		a {
			color: var(--iata-blue-primary);
			font-weight: 500;
			white-space: nowrap;
		}
	}

	&__activity-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 20px 0;
	}

	&__activity-title {
		font-size: 16px;
	}

	&__activity-header-right {
		display: flex;
		gap: 20px;
	}

	&__activity-scroll-container {
		position: relative;
		height: 300px;
		margin-top: 20px;
		overflow-y: auto;
		overflow-x: hidden;
		border: 1px solid var(--iata-grey-100);
		border-radius: 8px;

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-track {
			background: var(--iata-grey-50);
			border-radius: 3px;
		}

		&::-webkit-scrollbar-thumb {
			background: var(--iata-grey-200);
			border-radius: 3px;

			&:hover {
				background: var(--iata-grey-300);
			}
		}

		scroll-behavior: smooth;
	}

	&__activity-scroll-content {
		position: relative;
		min-height: 100%;
	}

	&__activity-content {
		padding: 20px;
		transition: background-color 0.2s ease;

		&:hover {
			background-color: var(--iata-grey-50);
		}
	}

	&__activity-content-item {
		.date {
			font-size: 12px;
			color: var(--iata-grey-300);
			margin-bottom: 8px;
		}

		.link {
			display: flex;
			justify-content: space-between;
			align-items: center;

			a {
				color: var(--iata-blue-primary);
				font-weight: 500;
				white-space: nowrap;
			}
		}

		.download {
			cursor: pointer;
			color: var(--iata-blue-primary);
			text-decoration: underline;
		}
	}
}
