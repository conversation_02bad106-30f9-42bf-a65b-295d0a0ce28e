import { Component, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { Router } from '@angular/router';
import {
	ARRIVAL_LOCATION,
	BookingOptionDetail,
	BookingOptionRequestDetailObj,
	DEPARTURE_LOCATION,
	OptionRequestDetail,
	PRICE_LIST,
	TRANS_LIST,
} from '../../models/booking.model';
import { buildOptionRequestDetail } from '../../util/booking.util';
import { BookingOptionRequestDetailComponent } from '../booking-option-request-detail/booking-option-request-detail.component';
import { FormArray, FormControl, FormGroup, FormsModule, Validators, ReactiveFormsModule } from '@angular/forms';
import { CodeName } from '@shared/models/code-name.model';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { MatInput, MatInputModule } from '@angular/material/input';
import { DateTimePickerComponent } from '@shared/components/date-time-picker/date-time-picker.component';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { CommonService } from '@shared/services/common.service';
import { ErrorStateMatcher } from '@angular/material/core';
import { REGX_NUMBER_2_DECIMAL } from '@shared/models/constant';
import { formatDateTime } from '@shared/utils/common.utils';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { CodeType } from '@shared/models/search-type.model';

export class DelayedErrorStateMatcher implements ErrorStateMatcher {
	isErrorState(control: FormControl | null): boolean {
		return !!(control && control.invalid && (control.dirty || control.touched));
	}
}

@Component({
	selector: 'orll-booking-option-create',
	imports: [
		BookingOptionRequestDetailComponent,
		FormsModule,
		MatFormFieldModule,
		TranslateModule,
		MatInput,
		DateTimePickerComponent,
		ReactiveFormsModule,
		MatInputModule,
		MatSelectModule,
		MatIconModule,
		MatButtonModule,
		MatDividerModule,
		SpinnerComponent,
	],
	providers: [{ provide: ErrorStateMatcher, useClass: DelayedErrorStateMatcher }],
	templateUrl: './booking-option-create.component.html',
	styleUrl: './booking-option-create.component.scss',
})
export default class BookingOptionCreateComponent extends RolesAwareComponent implements OnInit {
	optionForm = new FormGroup({
		options: new FormArray([]),
	});

	locationList: CodeName[] = [];
	chargePaymentTypes: CodeName[] = [];
	rateClassCodes: CodeName[] = [];
	chargeTypes: CodeName[] = [];
	entitlements: CodeName[] = [];
	detailInfo: OptionRequestDetail | null = null;

	id = '';
	dataLoading = false;

	validators = Validators;

	constructor(
		private readonly bookingService: BookingOptionRequestService,
		private readonly router: Router,
		private readonly commonService: CommonService
	) {
		super();
		const navigation = this.router.getCurrentNavigation();
		if (navigation?.extras.state) {
			this.id = navigation.extras.state['id'];
		}
	}

	ngOnInit(): void {
		if (this.id) {
			this.dataLoading = true;
			this.bookingService.getCodeByType(CodeType.COMMODITY_CODE).subscribe((commodityCodes) => {
				this.bookingService.getBookingOptionDetail(this.id).subscribe({
					next: (res) => {
						this.detailInfo = buildOptionRequestDetail(res, commodityCodes);
						this.dataLoading = false;
						this.dispatchFormValue(res);
					},
					error: () => {
						this.dataLoading = false;
					},
				});
			});
		}
		this.bookingService
			.getAirports()
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((res) => {
				this.locationList = res;
			});
		for (const item of [CodeType.CHARGE_TYPE, CodeType.ENTITILEMENT, CodeType.PAYMENT_TYPE, CodeType.RATE_CLASS_CODE]) {
			this.bookingService.getCodeByType(item).subscribe((res) => {
				switch (item) {
					case CodeType.CHARGE_TYPE:
						this.chargeTypes = res;
						break;

					case CodeType.RATE_CLASS_CODE:
						this.rateClassCodes = res;
						break;
					case CodeType.PAYMENT_TYPE:
						this.chargePaymentTypes = res;
						break;
					case CodeType.ENTITILEMENT:
						this.entitlements = res;
						break;
					default:
					//ignore
				}
			});
		}
	}

	private dispatchFormValue(res: BookingOptionRequestDetailObj) {
		if (res.bookingOptionList && res.bookingOptionList.length > 0) {
			for (const item of res.bookingOptionList) {
				const optionGroup = this.createOptionGroup(true);
				(optionGroup.get(PRICE_LIST) as FormArray).clear();
				(optionGroup.get(TRANS_LIST) as FormArray).clear();
				(this.optionForm.get('options') as FormArray).push(optionGroup);
				for (const price of item.priceList) {
					const priceGroup = this.createPieceGroup(true);
					priceGroup.patchValue(price);
					(optionGroup.get(PRICE_LIST) as FormArray).push(priceGroup);
				}
				for (const trans of item.transportLegsList) {
					const transGroup = this.createTransGroup(true);
					transGroup.patchValue(trans);
					(optionGroup.get(TRANS_LIST) as FormArray).push(transGroup);
				}

				optionGroup.patchValue({
					disableOption: true,
					productDescription: item.productDescription,
					offerValidFrom: item.offerValidFrom,
					offerValidTo: item.offerValidTo,
					grandTotal: item.grandTotal,
				});
			}
		} else {
			this.addOptionGroup(false);
		}
	}

	createOptionGroup(disable: boolean): FormGroup {
		return new FormGroup({
			priceList: new FormArray([this.createPieceGroup(disable)]),
			transportLegsList: new FormArray([this.createTransGroup(disable)]),
			productDescription: new FormControl<string>({ value: '', disabled: disable }),
			offerValidFrom: new FormControl<string>({ value: '', disabled: disable }),
			offerValidTo: new FormControl<string>({ value: '', disabled: disable }),
			grandTotal: new FormControl<number>(0),
			disableOption: new FormControl<boolean>(false),
			showOption: new FormControl<boolean>(true),
		});
	}

	createPieceGroup(disable: boolean) {
		return new FormGroup({
			chargeType: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			rateClassCode: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			subTotal: new FormControl<number | null>({ value: null, disabled: disable }, [
				Validators.required,
				Validators.pattern(REGX_NUMBER_2_DECIMAL),
			]),
			chargePaymentType: new FormControl<string>({ value: 'PREPAID', disabled: disable }, [Validators.required]),
			entitlement: new FormControl<string>({ value: 'A', disabled: disable }),
		});
	}

	createTransGroup(disable: boolean) {
		return new FormGroup({
			departureLocation: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			arrivalLocation: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			airlineCode: new FormControl<string>({ value: '', disabled: disable }),
			transportIdentifier: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			departureDate: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			arrivalDate: new FormControl<string>({ value: '', disabled: disable }),
		});
	}

	addOptionGroup(disable?: boolean) {
		const newOption = this.createOptionGroup(disable ?? false);
		(this.optionForm.get('options') as FormArray).push(newOption);
	}

	private markFormGroupAsUntouched(formGroup: FormGroup | FormArray): void {
		for (const key of Object.keys(formGroup.controls)) {
			const control = formGroup.get(key);

			if (control instanceof FormGroup || control instanceof FormArray) {
				this.markFormGroupAsUntouched(control);
			} else {
				control?.markAsUntouched();
				control?.markAsPristine();
			}
		}
	}

	addPrice(optionGroup: FormGroup, disable?: boolean): void {
		const newPieceGroup = this.createPieceGroup(disable ?? false);
		(optionGroup.get(PRICE_LIST) as FormArray).push(newPieceGroup, { emitEvent: false });
		this.markFormGroupAsUntouched(this.optionForm);
	}

	delPrice(optionGroup: FormGroup, pieceIdx: number) {
		(optionGroup.get(PRICE_LIST) as FormArray).removeAt(pieceIdx);
	}

	addTrans(optionGroup: FormGroup, disable?: boolean) {
		const transGroup = this.createTransGroup(disable ?? false);
		const transList = optionGroup.get(TRANS_LIST) as FormArray;
		if (transList.length > 0) {
			const preGroup = transList.at(-1);
			transGroup.patchValue({ departureLocation: preGroup.get(ARRIVAL_LOCATION)?.value || '' });
		}
		(optionGroup.get(TRANS_LIST) as FormArray).push(transGroup);
	}

	delTrans(optionGroup: FormGroup) {
		const trans = optionGroup.get(TRANS_LIST) as FormArray;
		trans.controls.splice(trans.length - 1, 1);
	}

	removeItemFromList(itemList: FormArray, index: number): void {
		itemList.removeAt(index);
	}

	getPriceList(optionIndex: number): FormArray {
		return (this.optionForm.controls.options.at(optionIndex) as FormGroup).get(PRICE_LIST) as FormArray;
	}

	getTransportLegsList(optionIndex: number): FormArray {
		return (this.optionForm.controls.options.at(optionIndex) as FormGroup).get(TRANS_LIST) as FormArray;
	}

	updateGrandTotal(bookingOption: FormGroup) {
		const chargeableWeight = Number(this.detailInfo?.chargeableWeight ?? 0);
		const grandTotal = (bookingOption.get(PRICE_LIST) as FormArray).controls
			.reduce((sum, item) => sum + Number.parseFloat(item.get('subTotal')?.value ?? 0) * chargeableWeight, 0)
			.toFixed(2);
		bookingOption.patchValue({ grandTotal: Number(grandTotal) });
	}

	validateForm(): boolean {
		let departureCheck = true;
		for (const element of this.optionForm.controls.options.controls as FormGroup[]) {
			const transLines = (element.get(TRANS_LIST) as FormArray).controls;
			if (transLines.length === 1 || !departureCheck) {
				break;
			}
			for (let idx = 1; idx < transLines.length; idx++) {
				const preArrival = transLines[idx - 1].get(ARRIVAL_LOCATION)?.value;
				const nextDeparture = transLines[idx].get(DEPARTURE_LOCATION)?.value;
				if (nextDeparture && preArrival && nextDeparture !== preArrival) {
					departureCheck = false;
					break;
				}
			}
		}

		this.optionForm.markAllAsTouched();

		if (this.optionForm.invalid) {
			this.commonService.showFormInvalid();
			return false;
		}
		if (!departureCheck) {
			this.commonService.showWarning('booking.option.departure.check.warning');
			return false;
		}
		return true;
	}

	sendOpitonRequestToForwarder() {
		if (!this.validateForm()) {
			return;
		}

		this.dataLoading = true;

		let param: BookingOptionDetail[] = [...this.optionForm.controls.options.value];

		param = param.filter((item) => item.priceList && item.transportLegsList);
		for (const item of param) {
			item.bookingOptionRequestId = this.id;
			item.offerValidFrom = item.offerValidFrom ? formatDateTime(item.offerValidFrom.toString()) : '';
			item.offerValidTo = item.offerValidTo ? formatDateTime(item.offerValidTo.toString()) : '';
			for (const trans of item.transportLegsList) {
				trans.departureDate = trans.departureDate ? formatDateTime(trans.departureDate.toString()) : '';
				trans.arrivalDate = trans.arrivalDate ? formatDateTime(trans.arrivalDate.toString()) : '';
			}
		}
		this.bookingService.sendBookingOptonsToForwarder(param).subscribe({
			next: () => {
				this.dataLoading = false;
				this.router.navigate(['quote']);
			},
			error: () => {
				this.dataLoading = false;
			},
		});
	}
}
