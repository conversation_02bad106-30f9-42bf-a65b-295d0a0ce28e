import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DgPieceAddComponent } from './dg-piece-add.component';
import { HTTP_INTERCEPTORS, HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { SimpleChanges } from '@angular/core';
import { Piece } from '../../models/piece/piece.model';

describe('DgPieceAddComponent', () => {
	let component: DgPieceAddComponent;
	let fixture: ComponentFixture<DgPieceAddComponent>;
	let httpClient: HttpClient;
	let httpTestingController: HttpTestingController;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [DgPieceAddComponent],
			providers: [
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
			],
		}).compileComponents();

		httpClient = TestBed.inject(HttpClient);
		httpTestingController = TestBed.inject(HttpTestingController);

		fixture = TestBed.createComponent(DgPieceAddComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(httpClient).toBeInstanceOf(HttpClient);

		const req = httpTestingController.expectOne((req) => req.url.includes('/sys-management/enums/packageType'));
		req.flush({
			code: 200,
			data: [
				{
					code: '1',
					name: 'Package Type 1',
				},
			],
		});

		expect(component).toBeTruthy();

		expect(component.packagingTypes).toEqual([
			{
				code: '1',
				name: 'Package Type 1',
			},
		]);
	});

	it('should initialize form with required controls', () => {
		const form = component.sliDgPieceForm;
		expect(form.get('productDescription')).toBeDefined();
		expect(form.get('typeOfPackage')).toBeDefined();
		expect(form.get('dimensions')).toBeDefined();
		expect(form.get('qValueNumeric')).toBeDefined();
	});

	it('should require qValueNumeric when allPackedInOne is true', () => {
		const qValueCtrl = component.sliDgPieceForm.get('qValueNumeric');

		// Set condition to true
		component.sliDgPieceForm.get('allPackedInOne')?.setValue(true);

		qValueCtrl?.setValue(null);
		expect(qValueCtrl?.invalid).toBeTrue();

		qValueCtrl?.setValue(10);
		expect(qValueCtrl?.valid).toBeTrue();
	});

	it('should be valid when mandatory fields are filled', () => {
		// Set required fields
		component.sliDgPieceForm.patchValue({
			productDescription: 'Test Product',
			dimensions: {
				length: '10',
				width: '20',
				height: '30',
			},
		});

		// Handle conditional requirement
		expect(component.sliDgPieceForm.get('dimensions')?.value).toEqual({
			length: '10',
			width: '20',
			height: '30',
		});
	});

	describe('displayPackagingTypeName', () => {
		beforeEach(() => {
			component.packagingTypes = [
				{ code: 'BOX', name: 'Box Package' },
				{ code: 'DRUM', name: 'Drum Package' },
				{ code: 'BAG', name: 'Bag Package' },
			];
		});

		it('should return correct name for valid code', () => {
			const result = component.displayPackagingTypeName('BOX');
			expect(result).toBe('Box Package');
		});

		it('should return empty string for invalid code', () => {
			const result = component.displayPackagingTypeName('INVALID');
			expect(result).toBe('');
		});

		it('should return empty string for null/undefined code', () => {
			const result1 = component.displayPackagingTypeName(null as any);
			const result2 = component.displayPackagingTypeName(undefined as any);
			expect(result1).toBe('');
			expect(result2).toBe('');
		});
	});

	describe('onPackagingTypeSelected', () => {
		beforeEach(() => {
			component.packagingTypes = [
				{ code: 'BOX', name: 'Box Package' },
				{ code: 'DRUM', name: 'Drum Package' },
			];
		});

		it('should update form with selected packaging type', () => {
			const mockEvent = {
				option: { value: 'BOX' },
			} as MatAutocompleteSelectedEvent;

			component.onPackagingTypeSelected(mockEvent);

			const typeOfPackageGroup = component.sliDgPieceForm.get('typeOfPackage');
			expect(typeOfPackageGroup?.get('typeCode')?.value).toBe('BOX');
			expect(typeOfPackageGroup?.get('description')?.value).toBe('Box Package');
		});

		it('should handle selection of non-existent packaging type', () => {
			const mockEvent = {
				option: { value: 'INVALID' },
			} as MatAutocompleteSelectedEvent;

			component.onPackagingTypeSelected(mockEvent);

			const typeOfPackageGroup = component.sliDgPieceForm.get('typeOfPackage');
			expect(typeOfPackageGroup?.get('typeCode')?.value).toBe('INVALID');
			expect(typeOfPackageGroup?.get('description')?.value).toBe('');
		});
	});

	describe('hasValidTouchedFormData', () => {
		it('should return true when form is touched and valid', () => {
			// Mock the form to be touched and valid
			spyOnProperty(component.sliDgPieceForm, 'touched', 'get').and.returnValue(true);
			spyOnProperty(component.sliDgPieceForm, 'valid', 'get').and.returnValue(true);

			const result = component.hasValidTouchedFormData();
			expect(result).toBe(true);
		});

		it('should return false when form is not touched', () => {
			// Mock the form to be valid but not touched
			spyOnProperty(component.sliDgPieceForm, 'touched', 'get').and.returnValue(false);
			spyOnProperty(component.sliDgPieceForm, 'valid', 'get').and.returnValue(true);

			const result = component.hasValidTouchedFormData();
			expect(result).toBe(false);
		});

		it('should return false when form is touched but invalid', () => {
			// Mock the form to be touched but invalid
			spyOnProperty(component.sliDgPieceForm, 'touched', 'get').and.returnValue(true);
			spyOnProperty(component.sliDgPieceForm, 'valid', 'get').and.returnValue(false);

			const result = component.hasValidTouchedFormData();
			expect(result).toBe(false);
		});
	});

	describe('ngOnChanges', () => {
		it('should patch form when piece input changes to valid piece', () => {
			const mockPiece = {
				id: 'test-id',
				product: {
					description: 'Test Product Description',
					specialProvisionId: 'SP001',
					explosiveCompatibilityGroupCode: 'ECC001',
					packagingDangerLevelCode: 'PDL001',
					technicalName: 'Technical Name',
					unNumber: 'UN001',
					hsCommodityDescription: 'HS Description',
					properShippingName: 'Proper Shipping Name',
					hazardClassificationId: 'HC001',
					additionalHazardClassificationId: 'AHC001',
					packingInstructionNumber: 'PIN001',
					authorizationInformation: 'Auth Info',
				},
				packagingType: {
					typeCode: 'BOX',
					description: 'Box Package',
				},
				packagedIdentifier: 'PI001',
				nvdForCustoms: true,
				nvdForCarriage: false,
				dimensions: {
					length: 15,
					width: 25,
					height: 35,
				},
				containedItems: [
					{
						product: { description: 'Test Item 1' },
						itemQuantity: 1,
						weight: 5.0,
					},
					{
						product: { description: 'Test Item 2' },
						itemQuantity: 2,
						weight: 7.5,
					},
				],
				grossWeight: 10.5,
				pieceQuantity: 1,
				allPackedInOneIndicator: true,
				qvalueNumeric: 100,
				upid: 'UPID001',
				shippingMarks: 'Shipping Marks',
				textualHandlingInstructions: 'Handle with care',
				dgDeclaration: {
					shipperDeclarationText: 'Shipper Declaration',
					handlingInformation: 'Handling Info',
					complianceDeclarationText: 'Compliance Declaration',
					exclusiveUseIndicator: 'Yes',
					aircraftLimitationInformation: 'Aircraft Limitation',
				},
			} as Piece;

			const changes: SimpleChanges = {
				piece: {
					currentValue: mockPiece,
					previousValue: null,
					firstChange: true,
					isFirstChange: () => true,
				},
			};

			// Set the piece property directly to simulate @Input change
			component.piece = mockPiece;

			spyOn<any>(component, 'patchDgPieceForm').and.callThrough();

			component.ngOnChanges(changes);

			expect(component['patchDgPieceForm']).toHaveBeenCalledWith(mockPiece);
			expect(component.sliDgPieceForm.get('productDescription')?.value).toBe('Test Product Description');
			expect(component.sliDgPieceForm.get(['typeOfPackage', 'typeCode'])?.value).toBe('BOX');
			expect(component.sliDgPieceForm.get(['typeOfPackage', 'description'])?.value).toBe('Box Package');
			expect(component.sliDgPieceForm.get(['dimensions', 'length'])?.value).toBe(15 as any);
			expect(component.dgPieceItems).toEqual(mockPiece.containedItems);
		});

		it('should reset form when piece input changes to null', () => {
			// First set some values
			component.sliDgPieceForm.patchValue({
				productDescription: 'Test Product',
			});

			const changes: SimpleChanges = {
				piece: {
					currentValue: null,
					previousValue: {} as Piece,
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			spyOn(component.sliDgPieceForm, 'reset');
			component.ngOnChanges(changes);

			expect(component.sliDgPieceForm.reset).toHaveBeenCalled();
		});

		it('should not process changes for other inputs', () => {
			const changes: SimpleChanges = {
				otherProperty: {
					currentValue: 'new value',
					previousValue: 'old value',
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			spyOn(component.sliDgPieceForm, 'reset');
			component.ngOnChanges(changes);

			expect(component.sliDgPieceForm.reset).not.toHaveBeenCalled();
		});
	});
});
