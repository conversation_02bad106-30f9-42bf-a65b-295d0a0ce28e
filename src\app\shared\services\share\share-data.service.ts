import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ShareType } from '@shared/models/share-type.model';

@Injectable({
	providedIn: 'root',
})
export class ShareDataService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	shareData(shareType: ShareType, id: string, orgIds: string[]): Observable<string> {
		switch (shareType) {
			case ShareType.SLI:
				return super.postData<string>(`${shareType}/share`, { sliId: id, forwarderOrgIds: orgIds });
			case ShareType.HAWB:
				return super.postData<string>(`${shareType}/share`, { hawbId: id, orgIdList: orgIds });
			case ShareType.MAWB:
				return super.postData<string>(`${shareType}/share`, { mawbId: id, orgIdList: orgIds });
			case ShareType.SUBSCRIPTION_CONFIG:
				return super.postData<string>(`${shareType}/share`, { subscriptionId: id, publisherIds: orgIds });
			default:
				//shoud not be here
				throw new Error(' share type error');
		}
	}
}
