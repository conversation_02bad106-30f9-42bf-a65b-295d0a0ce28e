<div class="ecud-list">
	@if (
		(loType === moduleName.SLI && hasSomeRole(shipper) | async) ||
		(loType === moduleName.HAWB && hasSomeRole(forwarder) | async) ||
		loType === moduleName.MAWB
	) {
		<div class="ecud-list__btn-panel">
			<button mat-flat-button color="primary" (click)="createOrEditEcsd()" fxLayout="row" fxLayoutAlign="center center">
				<mat-icon>add</mat-icon>
				{{ 'ecsd.create.btn' | translate }}
			</button>
		</div>
	}

	<div class="ecud-list__table">
		@if (loId) {
			<orll-table [columns]="columns" [param]="escdParam" [service]="escdService"></orll-table>
		}
	</div>
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
