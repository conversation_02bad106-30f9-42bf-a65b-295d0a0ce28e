import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DelegationRequestService } from '@shared/services/delegation/delegation-request.service';
import { DelegationRequest } from '@shared/models/delegation-request';
import { DelegationRequestTableComponent } from '../delegation-request-table/delegation-request-table.component';

@Component({
	selector: 'orll-delegation-request-list',
	templateUrl: './delegation-request-list.component.html',
	styleUrl: './delegation-request-list.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [DelegationRequestTableComponent, SpinnerComponent],
})
export class DelegationRequestListComponent extends DestroyRefComponent implements OnInit {
	@Input() loId = '';
	@Input() isCentralMenu = false;

	delegationList: DelegationRequest[] = [];
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};
	totalRecords = 0;
	dataLoading = false;
	hasMoreData = true;

	constructor(
		private readonly delegationRequestService: DelegationRequestService,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnInit(): void {
		this.refreshData();
	}

	refreshData() {
		if (this.loId || this.isCentralMenu) {
			this.getDelegationListPage(this.pageParams);
		}
	}

	onSortChange(sort: Sort): void {
		if (sort.direction === '') {
			this.pageParams.orderByColumn = '';
			this.pageParams.isAsc = '';
			return;
		}
		this.pageParams.pageNum = 1;
		this.pageParams.pageSize = 10;
		this.pageParams.orderByColumn = sort.active;
		this.pageParams.isAsc = sort.direction;
	}

	onPageChange(event: PageEvent & { sortField?: string; sortDirection?: string }): void {
		this.pageParams.pageNum = event.pageIndex + 1;
		this.pageParams.pageSize = event.pageSize;
		if (event.sortDirection) {
			this.pageParams.orderByColumn = event.sortField;
			this.pageParams.isAsc = event.sortDirection;
		}

		this.getDelegationListPage(this.pageParams);
	}

	loadMoreData(): void {
		if (!this.hasMoreData || this.dataLoading) {
			return;
		}

		this.pageParams.pageNum += 1;
		this.getDelegationListPage(this.pageParams, true);
	}

	private getDelegationListPage(pageParams: PaginationRequest, appendData = false): void {
		this.dataLoading = true;

		this.delegationRequestService
			.getDelegationList(pageParams, this.loId)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					if (!res) {
						this.dataLoading = false;
						return;
					}

					if (appendData) {
						this.delegationList = [...this.delegationList, ...res.rows];
						this.hasMoreData = this.delegationList.length < res.total;
					} else {
						this.delegationList = res.rows;
					}
					this.totalRecords = res.total;
					this.dataLoading = false;

					this.cdr.markForCheck();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}
}
