import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LiveAnimalPieceAddComponent } from './live-animal-piece-add.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { By } from '@angular/platform-browser';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { SimpleChanges } from '@angular/core';
import { Piece } from '../../models/piece/piece.model';

describe('LiveAnimalPieceAddComponent', () => {
	let component: LiveAnimalPieceAddComponent;
	let fixture: ComponentFixture<LiveAnimalPieceAddComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [LiveAnimalPieceAddComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(LiveAnimalPieceAddComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should not update typeOfPackage form group when onTypeOfPackageChange is called with invalid code', () => {
		// Set initial form values
		component.sliLiveAnimalPieceForm.get('typeOfPackage')?.patchValue({
			typeCode: 'INITIAL',
			description: 'Initial Value',
		});

		// Call method with invalid code
		component.onTypeOfPackageChange('INVALID_CODE');

		// Values should remain unchanged
		const typeOfPackageGroup = component.sliLiveAnimalPieceForm.get('typeOfPackage');
		expect(typeOfPackageGroup?.get('typeCode')?.value).toBe('INITIAL');
		expect(typeOfPackageGroup?.get('description')?.value).toBe('Initial Value');
	});

	it('should not require optional fields', async () => {
		const optionalFields = ['packagedIdentifier', 'shippingMarks', 'upid', 'textualHandlingInstructions'];

		for (const field of optionalFields) {
			const control = component.sliLiveAnimalPieceForm.get(field);
			control?.setValue('');
			control?.markAsTouched();
			fixture.detectChanges();

			const errorElement = fixture.debugElement.query(By.css(`[formControlName="${field}"] + mat-error`));
			expect(errorElement).toBeNull();
		}
	});

	it('should initialize form with required controls', () => {
		const form = component.sliLiveAnimalPieceForm;
		expect(form.get('productDescription')).toBeDefined();
		expect(form.get('typeOfPackage')).toBeDefined();
		expect(form.get('animalQuantity')).toBeDefined();
		expect(form.get('dimensions')).toBeDefined();
	});

	it('should require animalQuantity field', () => {
		const animalQuantityCtrl = component.sliLiveAnimalPieceForm.get('animalQuantity');

		animalQuantityCtrl?.setValue(null);
		expect(animalQuantityCtrl?.invalid).toBeTrue();

		animalQuantityCtrl?.setValue(5 as any);
		expect(animalQuantityCtrl?.valid).toBeTrue();
	});

	it('should validate animalQuantity pattern (positive integers only)', () => {
		const animalQuantityCtrl = component.sliLiveAnimalPieceForm.get('animalQuantity');

		// Invalid values
		animalQuantityCtrl?.setValue(0 as any);
		expect(animalQuantityCtrl?.invalid).toBeTrue();

		animalQuantityCtrl?.setValue(-1 as any);
		expect(animalQuantityCtrl?.invalid).toBeTrue();

		animalQuantityCtrl?.setValue(1.5 as any);
		expect(animalQuantityCtrl?.invalid).toBeTrue();

		// Valid values
		animalQuantityCtrl?.setValue(1 as any);
		expect(animalQuantityCtrl?.valid).toBeTrue();

		animalQuantityCtrl?.setValue(10 as any);
		expect(animalQuantityCtrl?.valid).toBeTrue();
	});

	describe('displayPackagingTypeName', () => {
		beforeEach(() => {
			component.packagingTypes = [
				{ code: 'CAGE', name: 'Animal Cage' },
				{ code: 'CRATE', name: 'Animal Crate' },
				{ code: 'CONTAINER', name: 'Animal Container' },
			];
		});

		it('should return correct name for valid code', () => {
			const result = component.displayPackagingTypeName('CAGE');
			expect(result).toBe('Animal Cage');
		});

		it('should return empty string for invalid code', () => {
			const result = component.displayPackagingTypeName('INVALID');
			expect(result).toBe('');
		});

		it('should return empty string for null/undefined code', () => {
			const result1 = component.displayPackagingTypeName(null as any);
			const result2 = component.displayPackagingTypeName(undefined as any);
			expect(result1).toBe('');
			expect(result2).toBe('');
		});
	});

	describe('onPackagingTypeSelected', () => {
		beforeEach(() => {
			component.packagingTypes = [
				{ code: 'CAGE', name: 'Animal Cage' },
				{ code: 'CRATE', name: 'Animal Crate' },
			];
		});

		it('should update form with selected packaging type', () => {
			const mockEvent = {
				option: { value: 'CAGE' },
			} as MatAutocompleteSelectedEvent;

			component.onPackagingTypeSelected(mockEvent);

			const typeOfPackageGroup = component.sliLiveAnimalPieceForm.get('typeOfPackage');
			expect(typeOfPackageGroup?.get('typeCode')?.value).toBe('CAGE');
			expect(typeOfPackageGroup?.get('description')?.value).toBe('Animal Cage');
		});

		it('should handle selection of non-existent packaging type', () => {
			const mockEvent = {
				option: { value: 'INVALID' },
			} as MatAutocompleteSelectedEvent;

			component.onPackagingTypeSelected(mockEvent);

			const typeOfPackageGroup = component.sliLiveAnimalPieceForm.get('typeOfPackage');
			expect(typeOfPackageGroup?.get('typeCode')?.value).toBe('INVALID');
			expect(typeOfPackageGroup?.get('description')?.value).toBe('');
		});
	});

	describe('ngOnChanges', () => {
		it('should patch form when piece input changes to valid piece', () => {
			const mockPiece = {
				id: 'test-id',
				product: {
					description: 'Live Animal Product',
				},
				packagingType: {
					typeCode: 'CAGE',
					description: 'Animal Cage',
				},
				packagedIdentifier: 'LA-001',
				speciesCommonName: 'Dog',
				speciesScientificName: 'Canis lupus',
				specimenDescription: 'Golden Retriever',
				quantityAnimals: 2,
				shippingMarks: 'Handle with care',
				upid: 'UPID-123',
				grossWeight: 25.5,
				dimensions: {
					length: 100,
					width: 60,
					height: 80,
				},
				textualHandlingInstructions: 'Keep in cool, dry place',
				pieceQuantity: 1,
				containedItems: [],
			} as Piece;

			const changes: SimpleChanges = {
				piece: {
					currentValue: mockPiece,
					previousValue: null,
					firstChange: true,
					isFirstChange: () => true,
				},
			};

			// Set the piece property directly to simulate @Input change
			component.piece = mockPiece;

			spyOn<any>(component, 'patchLiveAnimalPieceForm').and.callThrough();

			component.ngOnChanges(changes);

			expect(component['patchLiveAnimalPieceForm']).toHaveBeenCalledWith(mockPiece);
			expect(component.sliLiveAnimalPieceForm.get('productDescription')?.value).toBe('Live Animal Product');
			expect(component.sliLiveAnimalPieceForm.get(['typeOfPackage', 'typeCode'])?.value).toBe('CAGE');
			expect(component.sliLiveAnimalPieceForm.get(['typeOfPackage', 'description'])?.value).toBe('Animal Cage');
			expect(component.sliLiveAnimalPieceForm.get('packagedIdentifier')?.value).toBe('LA-001');
			expect(component.sliLiveAnimalPieceForm.get('speciesCommonName')?.value).toBe('Dog');
			expect(component.sliLiveAnimalPieceForm.get('speciesScientificName')?.value).toBe('Canis lupus');
			expect(component.sliLiveAnimalPieceForm.get('specimenDescription')?.value).toBe('Golden Retriever');
			expect(component.sliLiveAnimalPieceForm.get('animalQuantity')?.value).toBe(2 as any);
			expect(component.sliLiveAnimalPieceForm.get('shippingMarks')?.value).toBe('Handle with care');
			expect(component.sliLiveAnimalPieceForm.get('upid')?.value).toBe('UPID-123');
			expect(component.sliLiveAnimalPieceForm.get('grossWeight')?.value).toBe(25.5 as any);
			expect(component.sliLiveAnimalPieceForm.get(['dimensions', 'length'])?.value).toBe(100 as any);
			expect(component.sliLiveAnimalPieceForm.get(['dimensions', 'width'])?.value).toBe(60 as any);
			expect(component.sliLiveAnimalPieceForm.get(['dimensions', 'height'])?.value).toBe(80 as any);
			expect(component.sliLiveAnimalPieceForm.get('textualHandlingInstructions')?.value).toBe('Keep in cool, dry place');
		});

		it('should reset form when piece input changes to null', () => {
			// First set some values
			component.sliLiveAnimalPieceForm.patchValue({
				productDescription: 'Test Product',
			});

			const changes: SimpleChanges = {
				piece: {
					currentValue: null,
					previousValue: {} as Piece,
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			spyOn(component.sliLiveAnimalPieceForm, 'reset');
			component.ngOnChanges(changes);

			expect(component.sliLiveAnimalPieceForm.reset).toHaveBeenCalled();
		});

		it('should not process changes for other inputs', () => {
			const changes: SimpleChanges = {
				otherProperty: {
					currentValue: 'new value',
					previousValue: 'old value',
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			spyOn(component.sliLiveAnimalPieceForm, 'reset');
			component.ngOnChanges(changes);

			expect(component.sliLiveAnimalPieceForm.reset).not.toHaveBeenCalled();
		});
	});
});
