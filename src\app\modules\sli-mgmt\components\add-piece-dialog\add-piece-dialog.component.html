<div class="orll-add-piece-dialog">
	<h2 mat-dialog-title class="orll-add-piece-dialog__title">{{ 'sli.piece.addDialog.title' | translate }}</h2>
	<mat-dialog-content>
		<form [formGroup]="pieceTypeForm">
			<div class="orll-add-piece-dialog__content">
				<mat-label>{{ 'sli.piece.addDialog.subtitle' | translate }}</mat-label>
				<mat-radio-group
					formControlName="pieceType"
					class="orll-add-piece-dialog__vertical-radio-group"
					aria-label="{{ 'sli.piece.addDialog.subtitle' | translate }}"
					required>
					<mat-radio-button value="general">{{ 'sli.piece.addDialog.general' | translate }}</mat-radio-button>
					<mat-radio-button value="dg">{{ 'sli.piece.addDialog.dg' | translate }}</mat-radio-button>
					<mat-radio-button value="la">{{ 'sli.piece.addDialog.la' | translate }}</mat-radio-button>
				</mat-radio-group>
				@if (submitted && pieceTypeForm.get('pieceType')?.hasError('required')) {
					<mat-error class="orll-add-piece-dialog__required-error">
						{{ 'sli.piece.addDialog.pieceType.required' | translate }}
					</mat-error>
				}
			</div>
		</form>
	</mat-dialog-content>
	<mat-dialog-actions align="end">
		<button mat-stroked-button (click)="onCancel()" class="orll-add-piece-dialog__cancel-button">
			{{ 'common.dialog.later' | translate }}
		</button>
		<button mat-flat-button color="primary" class="orll-add-piece-dialog__next-button" (click)="onNext()">
			<mat-icon>arrow_forward</mat-icon>
			{{ 'common.dialog.next' | translate }}
		</button>
	</mat-dialog-actions>
</div>
