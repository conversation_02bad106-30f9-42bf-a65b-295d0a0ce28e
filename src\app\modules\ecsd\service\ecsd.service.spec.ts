import { TestBed } from '@angular/core/testing';
import { EcsdService } from './ecsd.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { environment } from '@environments/environment';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { EcsdObj, SubObj, SubObjSearchReq } from '../models/ecsd.model';

describe('EcsdService', () => {
	let service: EcsdService;
	let httpMock: HttpTestingController;
	const baseUrl = environment.baseApi;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [EcsdService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(EcsdService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	describe('getDataPerPage', () => {
		it('should make POST request to pageList with param', () => {
			const mockParam: PaginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<EcsdObj> = {
				rows: [
					{
						securityStatus: '',
						receivedFrom: '',
						whetherExemptedForScreening: '',
						screeningMethod: '',
						groundsForExemption: '',
						issuedBy: '',
						employeeId: '',
						issuedOn: '',
						additionalSecurityInfo: '',
						loId: '',
						loType: '',
						regulatedEntityCategory1: '',
						regulatedEntityIdentifier1: '',
						regulatedEntityCategory: '',
						regulatedEntityIdentifier: '',
					},
				],
				total: 0,
			};

			service.getDataPerPage(mockParam).subscribe((res) => {
				expect(res).toEqual(mockResponse);
			});

			const req = httpMock.expectOne(`${baseUrl}/ecsd/pageList`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toEqual(mockParam);
			req.flush(mockResponse);
		});
	});

	describe('loadAllData', () => {
		it('should make POST request to pageList', () => {
			const mockParam = { all: true };
			const mockResponse: EcsdObj[] = [
				{
					securityStatus: '',
					receivedFrom: '',
					whetherExemptedForScreening: '',
					screeningMethod: '',
					groundsForExemption: '',
					issuedBy: '',
					employeeId: '',
					issuedOn: '',
					additionalSecurityInfo: '',
					loId: '',
					loType: '',
					regulatedEntityCategory1: '',
					regulatedEntityIdentifier1: '',
					regulatedEntityCategory: '',
					regulatedEntityIdentifier: '',
				},
			];

			service.loadAllData(mockParam).subscribe((res) => {
				expect(res).toEqual(mockResponse);
			});

			const req = httpMock.expectOne(`${baseUrl}/ecsd/pageList`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toEqual(mockParam);
			req.flush(mockResponse);
		});
	});

	describe('saveEcsdObj', () => {
		it('should call updateData (PUT) when id exists', () => {
			const mockObj: EcsdObj = {
				id: '122',
				securityStatus: '',
				receivedFrom: '',
				whetherExemptedForScreening: '',
				screeningMethod: '',
				groundsForExemption: '',
				issuedBy: '',
				employeeId: '',
				issuedOn: '',
				additionalSecurityInfo: '',
				loId: '',
				loType: '',
				regulatedEntityCategory1: '',
				regulatedEntityIdentifier1: '',
				regulatedEntityCategory: '',
				regulatedEntityIdentifier: '',
			};

			service.saveEcsd(mockObj).subscribe((res) => {
				expect(res).toBeTrue();
			});

			const req = httpMock.expectOne(`${baseUrl}/ecsd`);
			expect(req.request.method).toBe('PUT');
			expect(req.request.body).toEqual(mockObj);
			req.flush(true);
		});

		it('should call postData (POST) when no id', () => {
			const mockObj: EcsdObj = {
				securityStatus: '',
				receivedFrom: '',
				whetherExemptedForScreening: '',
				screeningMethod: '',
				groundsForExemption: '',
				issuedBy: '',
				employeeId: '',
				issuedOn: '',
				additionalSecurityInfo: '',
				loId: '',
				loType: '',
				regulatedEntityCategory1: '',
				regulatedEntityIdentifier1: '',
				regulatedEntityCategory: '',
				regulatedEntityIdentifier: '',
			};

			service.saveEcsd(mockObj).subscribe((res) => {
				expect(res).toBeTrue();
			});

			const req = httpMock.expectOne(`${baseUrl}/ecsd`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toEqual(mockObj);
			req.flush(true);
		});
	});

	describe('getDetail', () => {
		it('should POST to detail with id', () => {
			const mockObj: EcsdObj = {
				id: '123',
				securityStatus: '',
				receivedFrom: '',
				whetherExemptedForScreening: '',
				screeningMethod: '',
				groundsForExemption: '',
				issuedBy: '',
				employeeId: '',
				issuedOn: '',
				additionalSecurityInfo: '',
				loId: '',
				loType: '',
				regulatedEntityCategory1: '',
				regulatedEntityIdentifier1: '',
				regulatedEntityCategory: '',
				regulatedEntityIdentifier: '',
			};
			const mockResponse: EcsdObj = { ...mockObj };

			service.getEcsd(mockObj).subscribe((res) => {
				expect(res).toEqual(mockResponse);
			});

			const req = httpMock.expectOne(`${baseUrl}/ecsd/detail`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toEqual({ id: '123' });
			req.flush(mockResponse);
		});
	});

	describe('getSub', () => {
		it('should POST to pieceOrHawbList with param', () => {
			const mockParam: SubObjSearchReq = {
				loId: '1',
				loType: '1',
			};
			const mockResponse: SubObj = {
				pieceList: [],
				hawbList: [],
			};

			service.getPiecesAndHawb(mockParam).subscribe((res) => {
				expect(res).toEqual(mockResponse);
			});

			const req = httpMock.expectOne(`${baseUrl}/ecsd/pieceOrHawbList`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toEqual(mockParam);
			req.flush(mockResponse);
		});
	});

	describe('delete', () => {
		it('should POST to /delete with id', () => {
			const id = '123';
			service.deleteEcsd(id).subscribe((res) => {
				expect(res).toBeTrue();
			});

			const req = httpMock.expectOne(`${baseUrl}/ecsd`);
			expect(req.request.method).toBe('DELETE');
			expect(req.request.body).toEqual({ id });
			req.flush(true);
		});
	});
});
