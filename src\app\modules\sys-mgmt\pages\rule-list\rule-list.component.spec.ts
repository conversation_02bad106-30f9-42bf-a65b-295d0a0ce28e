import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import RuleListComponent from './rule-list.component';
import { RuleService } from '../../services/rule.service';
import { TranslateModule } from '@ngx-translate/core';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { RuleListObj } from '../../models/rule.model';
import { of } from 'rxjs';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { Organization } from '@shared/models/organization.model';

const mockRule = {
	holder: '111',
	requestType: '222',
	requestNames: '222',
	action: '222',
	id: '',
	request: '',
	holderNames: '',
	requestTypeDescription: '',
	actionDescription: '',
};
const orgs: Organization[] = [
	{
		id: '111',
		name: '111',
		orgType: '11',
	},
	{
		id: '222',
		name: '222',
		orgType: '222',
	},
];

describe('RuleListComponent', () => {
	let component: RuleListComponent;
	let fixture: ComponentFixture<RuleListComponent>;
	let ruleService: jasmine.SpyObj<RuleService>;
	let orgService: jasmine.SpyObj<OrgMgmtRequestService>;
	let dialog: jasmine.SpyObj<MatDialog>;

	beforeEach(async () => {
		ruleService = jasmine.createSpyObj('RuleService', ['deleteRule', 'saveRule', 'getRule', 'getDataPerPage', 'loadAllData']);
		orgService = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList']);

		dialog = jasmine.createSpyObj('MatDialog', ['open']);

		const res: PaginationResponse<RuleListObj> = {
			total: 1,
			rows: [mockRule],
		};
		ruleService.getDataPerPage.and.returnValue(of(res));
		ruleService.loadAllData.and.returnValue(of([mockRule]));
		ruleService.deleteRule.and.returnValue(of(true));
		orgService.getOrgList.and.returnValue(of(orgs));

		await TestBed.configureTestingModule({
			imports: [RuleListComponent, TranslateModule.forRoot(), MatDialogModule],
			providers: [
				{ provide: RuleService, useValue: ruleService },
				{ provide: OrgMgmtRequestService, useValue: orgService },
				{ provide: MatDialog, useValue: dialog },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(RuleListComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with default values', () => {
		expect(component.dataLoading).toBe(false);
		expect(component.orgList).toEqual([]);
		expect(component.param).toEqual(jasmine.objectContaining({ dateTime: jasmine.any(Number) }));
		expect(component.columns).toBeDefined();
		expect(component.columns.length).toBe(5);
	});

	describe('createOrUpdateRule', () => {
		it('should call dialog.open method', () => {
			spyOn(component['dialog'], 'open').and.returnValue({
				afterClosed: () => of(false),
			} as any);

			component.createOrUpdateRule();

			expect(component['dialog'].open).toHaveBeenCalled();
		});

		it('should call dialog.open method with data when editing', () => {
			spyOn(component['dialog'], 'open').and.returnValue({
				afterClosed: () => of(false),
			} as any);

			component.createOrUpdateRule(mockRule);

			expect(component['dialog'].open).toHaveBeenCalled();
		});
	});

	describe('deleteRule', () => {
		it('should call dialog.open method', () => {
			spyOn(component['dialog'], 'open').and.returnValue({
				afterClosed: () => of(false),
			} as any);

			component.deleteRule(mockRule);

			expect(component['dialog'].open).toHaveBeenCalled();
		});
	});

	describe('Column Configuration', () => {
		it('should have correct column definitions', () => {
			expect(component.columns[0].key).toBe('holderNames');
			expect(component.columns[0].header).toBe('system.rule.table.holder');

			expect(component.columns[1].key).toBe('requestTypeDescription');
			expect(component.columns[1].header).toBe('system.rule.table.request.type');

			expect(component.columns[2].key).toBe('requestNames');
			expect(component.columns[2].header).toBe('system.rule.table.requester');

			expect(component.columns[3].key).toBe('actionDescription');
			expect(component.columns[3].header).toBe('system.rule.table.action');

			expect(component.columns[4].key).toBe('actions');
			expect(component.columns[4].header).toBe('common.table.action');
		});

		it('should have correct action buttons in actions column', () => {
			const actionsColumn = component.columns[4];
			expect(actionsColumn.actions).toBeDefined();
			expect(actionsColumn.actions!.length).toBe(2);

			expect(actionsColumn.actions![0].iconKey).toBe('edit');
			expect(actionsColumn.actions![1].iconKey).toBe('delete');
		});

		it('should call createOrUpdateRule when edit action is clicked', () => {
			spyOn(component, 'createOrUpdateRule');
			const actionsColumn = component.columns[4];

			if (actionsColumn.actions![0].iconClickAction) {
				actionsColumn.actions![0].iconClickAction(mockRule);
			}

			expect(component.createOrUpdateRule).toHaveBeenCalledWith(mockRule);
		});

		it('should call deleteRule when delete action is clicked', () => {
			spyOn(component, 'deleteRule');
			const actionsColumn = component.columns[4];

			if (actionsColumn.actions![1].iconClickAction) {
				actionsColumn.actions![1].iconClickAction(mockRule);
			}

			expect(component.deleteRule).toHaveBeenCalledWith(mockRule);
		});
	});
});
