import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CarrierAgentComponent } from './carrier-agent.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { SliCreateRequestService } from 'src/app/modules/sli-mgmt/services/sli-create-request.service';
import { of } from 'rxjs';
import { SimpleChanges } from '@angular/core';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { Country } from 'src/app/modules/sli-mgmt/models/country.model';
import { Province } from 'src/app/modules/sli-mgmt/models/province.model';
import { CodeName } from '@shared/models/code-name.model';
import { OrgInfo } from '@shared/models/org-info.model';
import { OrgType } from '@shared/models/org-type.model';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

describe('CarrierAgentComponent', () => {
	let component: CarrierAgentComponent;
	let fixture: ComponentFixture<CarrierAgentComponent>;
	let sliCreateRequestServiceSpy: jasmine.SpyObj<SliCreateRequestService>;

	// Test data constants
	const MOCK_COUNTRIES: Country[] = [
		{ code: 'US', name: 'United States', provinces: [] },
		{ code: 'CA', name: 'Canada', provinces: [] },
		{ code: 'UK', name: 'United Kingdom', provinces: [] },
	];

	const MOCK_PROVINCES: Province[] = [
		{ code: 'NY', name: 'New York', cities: [] },
		{ code: 'CA', name: 'California', cities: [] },
		{ code: 'TX', name: 'Texas', cities: [] },
	];

	const MOCK_CITIES: CodeName[] = [
		{ code: 'NYC', name: 'New York City' },
		{ code: 'LA', name: 'Los Angeles' },
		{ code: 'CHI', name: 'Chicago' },
	];

	const MOCK_CARRIER_INFO: OrgInfo = {
		id: 'carrier-123',
		companyName: 'Test Carrier Company',
		iataCargoAgentCode: '***********',
		countryCode: 'US',
		regionCode: 'NY',
		cityCode: 'NYC',
		textualPostCode: '10001',
		locationName: '123 Test Street',
		persons: [
			{
				contactRole: OrgType.CUSTOMER_CONTACT,
				phoneNumber: '******-0123',
				emailAddress: '<EMAIL>',
			},
		],
	} as OrgInfo;

	/**
	 * Helper function to create service spies
	 */
	function createServiceSpies(): void {
		sliCreateRequestServiceSpy = jasmine.createSpyObj('SliCreateRequestService', ['getCountries', 'getProvinces', 'getCities']);
	}

	/**
	 * Helper function to setup default spy return values
	 */
	function setupDefaultSpyReturns(): void {
		sliCreateRequestServiceSpy.getCountries.and.returnValue(of(MOCK_COUNTRIES));
		sliCreateRequestServiceSpy.getProvinces.and.returnValue(of(MOCK_PROVINCES));
		sliCreateRequestServiceSpy.getCities.and.returnValue(of(MOCK_CITIES));
	}

	beforeEach(async () => {
		createServiceSpies();
		setupDefaultSpyReturns();

		await TestBed.configureTestingModule({
			imports: [CarrierAgentComponent, BrowserAnimationsModule],
			providers: [
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(CarrierAgentComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	afterEach(() => {
		fixture?.destroy();
	});

	describe('Component Initialization', () => {
		it('should create component successfully', () => {
			expect(component).toBeTruthy();
			expect(component).toBeInstanceOf(CarrierAgentComponent);
		});

		it('should initialize form with correct controls and validators', () => {
			expect(component.carrierAgentForm).toBeDefined();

			// Check required fields
			expect(component.carrierAgentForm.get('company')?.hasError('required')).toBe(true);
			expect(component.carrierAgentForm.get('country')?.hasError('required')).toBe(true);
			expect(component.carrierAgentForm.get('province')?.hasError('required')).toBe(true);
			expect(component.carrierAgentForm.get('cityCode')?.hasError('required')).toBe(true);
			expect(component.carrierAgentForm.get('address')?.hasError('required')).toBe(true);
		});

		it('should load countries on initialization', () => {
			expect(sliCreateRequestServiceSpy.getCountries).toHaveBeenCalledTimes(1);
			expect(component.countries).toEqual(MOCK_COUNTRIES);
			expect(component.filteredCountries).toEqual(MOCK_COUNTRIES);
		});

		it('should initialize arrays for reference data', () => {
			expect(component.countries).toEqual(MOCK_COUNTRIES);
			expect(component.provinces).toEqual([]);
			expect(component.cities).toEqual([]);
			expect(component.filteredCountries).toEqual(MOCK_COUNTRIES);
			expect(component.filteredProvinces).toEqual([]);
			expect(component.filteredCities).toEqual([]);
		});
	});

	describe('Form Validation', () => {
		describe('agentIataCode validation', () => {
			let agentIataCodeControl: any;

			beforeEach(() => {
				agentIataCodeControl = component.carrierAgentForm.get('agentIataCode');
			});

			it('should accept valid 11-digit IATA code', () => {
				agentIataCodeControl.setValue('***********');
				expect(agentIataCodeControl.valid).toBe(true);
			});

			it('should reject IATA code with less than 11 digits', () => {
				agentIataCodeControl.setValue('1234567890');
				expect(agentIataCodeControl.valid).toBe(false);
				expect(agentIataCodeControl.hasError('pattern')).toBe(true);
			});

			it('should reject IATA code with more than 11 digits', () => {
				agentIataCodeControl.setValue('***********2');
				expect(agentIataCodeControl.valid).toBe(false);
				expect(agentIataCodeControl.hasError('pattern')).toBe(true);
			});

			it('should reject IATA code with non-numeric characters', () => {
				agentIataCodeControl.setValue('1234567890a');
				expect(agentIataCodeControl.valid).toBe(false);
				expect(agentIataCodeControl.hasError('pattern')).toBe(true);
			});

			it('should accept empty IATA code (optional field)', () => {
				agentIataCodeControl.setValue('');
				expect(agentIataCodeControl.valid).toBe(true);
			});
		});

		describe('email validation', () => {
			let emailControl: any;

			beforeEach(() => {
				emailControl = component.carrierAgentForm.get('email');
			});

			it('should accept valid email addresses', () => {
				const validEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

				validEmails.forEach((email) => {
					emailControl.setValue(email);
					expect(emailControl.valid).toBe(true);
				});
			});

			it('should reject invalid email addresses', () => {
				const invalidEmails = ['invalid-email', '@domain.com', 'user@', '<EMAIL>'];

				invalidEmails.forEach((email) => {
					emailControl.setValue(email);
					expect(emailControl.valid).toBe(false);
					expect(emailControl.hasError('email')).toBe(true);
				});
			});

			it('should accept empty email (optional field)', () => {
				emailControl.setValue('');
				expect(emailControl.valid).toBe(true);
			});
		});
	});

	describe('Input Handling', () => {
		it('should fill form when carrierInfo input changes', () => {
			component.carrierInfo = MOCK_CARRIER_INFO;

			const changes: SimpleChanges = {
				carrierInfo: {
					currentValue: MOCK_CARRIER_INFO,
					previousValue: null,
					firstChange: true,
					isFirstChange: () => true,
				},
			};

			component.ngOnChanges(changes);

			expect(component.carrierAgentForm.get('company')?.value).toBe(MOCK_CARRIER_INFO.companyName);
			expect(component.carrierAgentForm.get('agentIataCode')?.value).toBe(MOCK_CARRIER_INFO.iataCargoAgentCode);
			expect(component.carrierAgentForm.get('country')?.value).toBe(MOCK_CARRIER_INFO.countryCode);
			expect(component.carrierAgentForm.get('province')?.value).toBe(MOCK_CARRIER_INFO.regionCode);
			expect(component.carrierAgentForm.get('cityCode')?.value).toBe(MOCK_CARRIER_INFO.cityCode);
			expect(component.carrierAgentForm.get('textualPostCode')?.value).toBe(MOCK_CARRIER_INFO.textualPostCode);
			expect(component.carrierAgentForm.get('address')?.value).toBe(MOCK_CARRIER_INFO.locationName);
			expect(component.carrierAgentForm.get('phoneNumber')?.value).toBe('******-0123');
			expect(component.carrierAgentForm.get('email')?.value).toBe('<EMAIL>');
		});

		it('should not fill form when carrierInfo is null', () => {
			const originalFormValue = component.carrierAgentForm.value;
			component.carrierInfo = null;

			const changes: SimpleChanges = {
				carrierInfo: {
					currentValue: null,
					previousValue: MOCK_CARRIER_INFO,
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			component.ngOnChanges(changes);

			expect(component.carrierAgentForm.value).toEqual(originalFormValue);
		});

		it('should handle carrierInfo without person contact', () => {
			const carrierInfoWithoutPerson = { ...MOCK_CARRIER_INFO, persons: [] };
			component.carrierInfo = carrierInfoWithoutPerson;

			const changes: SimpleChanges = {
				carrierInfo: {
					currentValue: carrierInfoWithoutPerson,
					previousValue: null,
					firstChange: true,
					isFirstChange: () => true,
				},
			};

			component.ngOnChanges(changes);

			expect(component.carrierAgentForm.get('phoneNumber')?.value).toBe('');
			expect(component.carrierAgentForm.get('email')?.value).toBe('');
		});
	});

	describe('Autocomplete Functionality', () => {
		it('should filter countries based on search input', () => {
			const countryControl = component.carrierAgentForm.get('country');

			// Test filtering
			countryControl?.setValue('unit');
			fixture.detectChanges();

			// Should filter to show only "United States" and "United Kingdom"
			expect(component.filteredCountries.length).toBe(2);
			expect(component.filteredCountries.some((c) => c.name.includes('United'))).toBe(true);
		});

		it('should display correct country name', () => {
			const countryName = component.displayCountryName('US');
			expect(countryName).toBe('United States');
		});

		it('should return empty string for unknown country code', () => {
			const countryName = component.displayCountryName('XX');
			expect(countryName).toBe('');
		});

		it('should display correct province name', () => {
			component.provinces = MOCK_PROVINCES;
			const provinceName = component.displayProvinceName('NY');
			expect(provinceName).toBe('New York');
		});

		it('should display correct city name', () => {
			component.cities = MOCK_CITIES;
			const cityName = component.displayCityName('NYC');
			expect(cityName).toBe('New York City');
		});
	});

	describe('Cascading Selection', () => {
		it('should load provinces when country is selected', () => {
			const mockEvent = {
				option: { value: 'US' },
			} as MatAutocompleteSelectedEvent;

			component.countryValueChange(mockEvent);

			expect(sliCreateRequestServiceSpy.getProvinces).toHaveBeenCalledWith(MOCK_COUNTRIES[0]);
			expect(component.carrierAgentForm.get('province')?.value).toBe('');
			expect(component.carrierAgentForm.get('cityCode')?.value).toBe('');
		});

		it('should load cities when province is selected', () => {
			component.provinces = MOCK_PROVINCES;
			const mockEvent = {
				option: { value: 'NY' },
			} as MatAutocompleteSelectedEvent;

			component.regionValueChange(mockEvent);

			expect(sliCreateRequestServiceSpy.getCities).toHaveBeenCalledWith(MOCK_PROVINCES[0]);
		});

		it('should handle country change without event', () => {
			component.countryValueChange();

			expect(sliCreateRequestServiceSpy.getProvinces).toHaveBeenCalled();
			expect(component.carrierAgentForm.get('province')?.value).toBe('');
			expect(component.carrierAgentForm.get('cityCode')?.value).toBe('');
		});

		it('should handle region change without event', () => {
			component.provinces = MOCK_PROVINCES;
			component.regionValueChange();

			expect(sliCreateRequestServiceSpy.getCities).toHaveBeenCalled();
		});
	});

	describe('getFormData Method', () => {
		beforeEach(() => {
			// Setup valid form data
			component.carrierAgentForm.patchValue({
				company: 'Test Carrier Company',
				agentIataCode: '***********',
				accountingNoteText: 'Test note',
				country: 'US',
				province: 'NY',
				cityCode: 'NYC',
				textualPostCode: '10001',
				address: '123 Test Street',
				phoneNumber: '******-0123',
				email: '<EMAIL>',
			});
		});

		it('should return form data when form is valid', () => {
			const formData = component.getFormData();

			expect(formData).toEqual({
				id: null,
				companyName: 'Test Carrier Company',
				contactName: '',
				countryCode: 'US',
				regionCode: 'NY',
				cityCode: 'NYC',
				textualPostCode: '10001',
				locationName: '123 Test Street',
				phoneNumber: '******-0123',
				emailAddress: '<EMAIL>',
				iataCargoAgentCode: '***********',
				companyType: OrgType.FORWARDER,
			});
		});

		it('should return null when form is invalid and ignore is false', () => {
			component.carrierAgentForm.patchValue({
				company: '', // Required field
			});

			const formData = component.getFormData(false);

			expect(formData).toBeNull();
		});

		it('should return form data when form is invalid but ignore is true', () => {
			component.carrierAgentForm.patchValue({
				company: '', // Required field
			});

			const formData = component.getFormData(true);

			expect(formData).not.toBeNull();
			expect(formData?.companyName).toBe('');
		});

		it('should handle empty optional fields', () => {
			component.carrierAgentForm.patchValue({
				agentIataCode: '',
				textualPostCode: '',
				phoneNumber: '',
				email: '',
			});

			const formData = component.getFormData();

			expect(formData?.iataCargoAgentCode).toBe('');
			expect(formData?.textualPostCode).toBe('');
			expect(formData?.phoneNumber).toBe('');
			expect(formData?.emailAddress).toBe('');
		});

		it('should set companyType to FORWARDER', () => {
			const formData = component.getFormData();

			expect(formData?.companyType).toBe(OrgType.FORWARDER);
		});

		it('should set contactName to empty string', () => {
			const formData = component.getFormData();

			expect(formData?.contactName).toBe('');
		});

		it('should include carrierInfo id when available', () => {
			component.carrierInfo = { ...MOCK_CARRIER_INFO, id: 'test-id-123' };

			const formData = component.getFormData();

			expect(formData?.id).toBe('test-id-123');
		});

		it('should return null id when carrierInfo is not available', () => {
			component.carrierInfo = null;

			const formData = component.getFormData();

			expect(formData?.id).toBeNull();
		});

		it('should handle undefined ignore parameter (defaults to false)', () => {
			component.carrierAgentForm.patchValue({
				company: '', // Required field
			});

			const formData = component.getFormData();

			expect(formData).toBeNull();
		});
	});

	describe('Private Helper Methods', () => {
		describe('isOrgInfo method', () => {
			it('should return true for OrgInfo objects', () => {
				const orgInfo: OrgInfo = {
					...MOCK_CARRIER_INFO,
					persons: [
						{
							contactRole: OrgType.CUSTOMER_CONTACT,
							contactName: 'Test Contact',
							phoneNumber: '123',
							emailAddress: '<EMAIL>',
							jobTitle: 'Manager',
							employeeId: 'EMP001',
						},
					],
				};

				const result = (component as any).isOrgInfo(orgInfo);

				expect(result).toBe(true);
			});

			it('should return false for ShipmentParty objects', () => {
				const shipmentParty = {
					companyName: 'Test Company',
					contactName: 'John Doe',
					countryCode: 'US',
					regionCode: 'NY',
					cityCode: 'NYC',
					textualPostCode: '10001',
					locationName: '123 Test St',
					phoneNumber: '555-0123',
					emailAddress: '<EMAIL>',
					companyType: OrgType.FORWARDER,
				};

				const result = (component as any).isOrgInfo(shipmentParty);

				expect(result).toBe(false);
			});
		});

		describe('getShipmentInfo method', () => {
			it('should convert OrgInfo to ShipmentParty format', () => {
				const orgInfo: OrgInfo = {
					...MOCK_CARRIER_INFO,
					persons: [
						{
							contactRole: OrgType.CUSTOMER_CONTACT,
							contactName: 'John Doe',
							phoneNumber: '555-0123',
							emailAddress: '<EMAIL>',
							jobTitle: 'Contact Manager',
							employeeId: 'EMP002',
						},
					],
					partyRole: OrgType.FORWARDER,
				};

				const result = (component as any).getShipmentInfo(orgInfo);

				expect(result).toEqual({
					companyName: orgInfo.companyName,
					contactName: 'John Doe',
					countryCode: orgInfo.countryCode,
					regionCode: orgInfo.regionCode,
					cityCode: orgInfo.cityCode,
					textualPostCode: orgInfo.textualPostCode,
					locationName: orgInfo.locationName,
					phoneNumber: '555-0123',
					emailAddress: '<EMAIL>',
					companyType: OrgType.FORWARDER,
				});
			});

			it('should handle OrgInfo without customer contact', () => {
				const orgInfo: OrgInfo = {
					...MOCK_CARRIER_INFO,
					persons: [],
					partyRole: OrgType.FORWARDER,
				};

				const result = (component as any).getShipmentInfo(orgInfo);

				expect(result.contactName).toBe('');
				expect(result.phoneNumber).toBe('');
				expect(result.emailAddress).toBe('');
			});

			it('should return ShipmentParty as-is when not OrgInfo', () => {
				const shipmentParty = {
					companyName: 'Test Company',
					contactName: 'Jane Doe',
					countryCode: 'CA',
					regionCode: 'ON',
					cityCode: 'TOR',
					textualPostCode: 'M5V 3A8',
					locationName: '456 Test Ave',
					phoneNumber: '************',
					emailAddress: '<EMAIL>',
					companyType: OrgType.SHIPPER,
				};

				const result = (component as any).getShipmentInfo(shipmentParty);

				expect(result).toEqual(shipmentParty);
			});
		});

		describe('setupCountryValueChange method', () => {
			it('should load provinces for selected country', () => {
				(component as any).setupCountryValueChange('US');

				expect(sliCreateRequestServiceSpy.getProvinces).toHaveBeenCalledWith(MOCK_COUNTRIES[0]);
				expect(component.provinces).toEqual(MOCK_PROVINCES);
				expect(component.filteredProvinces).toEqual(MOCK_PROVINCES);
			});

			it('should setup province autocomplete filtering', () => {
				(component as any).setupCountryValueChange('US');

				const provinceControl = component.carrierAgentForm.get('province');
				provinceControl?.setValue('new');
				fixture.detectChanges();

				expect(component.filteredProvinces.length).toBe(1);
				expect(component.filteredProvinces[0].name).toBe('New York');
			});

			it('should handle empty country value', () => {
				(component as any).setupCountryValueChange('');

				expect(sliCreateRequestServiceSpy.getProvinces).toHaveBeenCalled();
			});
		});

		describe('setupRegionValueChange method', () => {
			beforeEach(() => {
				component.provinces = MOCK_PROVINCES;
			});

			it('should load cities for selected province', () => {
				(component as any).setupRegionValueChange('NY');

				expect(sliCreateRequestServiceSpy.getCities).toHaveBeenCalledWith(MOCK_PROVINCES[0]);
				expect(component.cities).toEqual(MOCK_CITIES);
				expect(component.filteredCities).toEqual(MOCK_CITIES);
			});

			it('should setup city autocomplete filtering', () => {
				(component as any).setupRegionValueChange('NY');

				const cityControl = component.carrierAgentForm.get('cityCode');
				cityControl?.setValue('new');
				fixture.detectChanges();

				expect(component.filteredCities.length).toBe(1);
				expect(component.filteredCities[0].name).toBe('New York City');
			});

			it('should handle empty province value', () => {
				(component as any).setupRegionValueChange('');

				expect(sliCreateRequestServiceSpy.getCities).toHaveBeenCalled();
			});
		});
	});

	describe('fillCarrierAgentInfo Method', () => {
		it('should call setupCountryValueChange and setupRegionValueChange', () => {
			spyOn(component as any, 'setupCountryValueChange');
			spyOn(component as any, 'setupRegionValueChange');
			component.carrierInfo = MOCK_CARRIER_INFO;

			component.fillCarrierAgentInfo();

			expect((component as any).setupCountryValueChange).toHaveBeenCalledWith(MOCK_CARRIER_INFO.countryCode);
			expect((component as any).setupRegionValueChange).toHaveBeenCalledWith(MOCK_CARRIER_INFO.regionCode);
		});

		it('should patch form with converted shipment info', () => {
			spyOn(component as any, 'getShipmentInfo').and.returnValue({
				companyName: 'Converted Company',
				contactName: 'Converted Contact',
				countryCode: 'CA',
				regionCode: 'ON',
				cityCode: 'TOR',
				textualPostCode: 'M5V 3A8',
				locationName: 'Converted Address',
				phoneNumber: '************',
				emailAddress: '<EMAIL>',
				companyType: OrgType.FORWARDER,
			});
			component.carrierInfo = MOCK_CARRIER_INFO;

			component.fillCarrierAgentInfo();

			expect(component.carrierAgentForm.get('company')?.value).toBe('Converted Company');
			expect(component.carrierAgentForm.get('country')?.value).toBe('CA');
			expect(component.carrierAgentForm.get('province')?.value).toBe('ON');
			expect(component.carrierAgentForm.get('cityCode')?.value).toBe('TOR');
			expect(component.carrierAgentForm.get('textualPostCode')?.value).toBe('M5V 3A8');
			expect(component.carrierAgentForm.get('address')?.value).toBe('Converted Address');
			expect(component.carrierAgentForm.get('phoneNumber')?.value).toBe('************');
			expect(component.carrierAgentForm.get('email')?.value).toBe('<EMAIL>');
		});

		it('should patch agentIataCode from carrierInfo directly', () => {
			component.carrierInfo = { ...MOCK_CARRIER_INFO, iataCargoAgentCode: '98765432109' };

			component.fillCarrierAgentInfo();

			expect(component.carrierAgentForm.get('agentIataCode')?.value).toBe('98765432109');
		});

		it('should return early when carrierInfo is null', () => {
			spyOn(component as any, 'setupCountryValueChange');
			spyOn(component as any, 'setupRegionValueChange');
			component.carrierInfo = null;

			component.fillCarrierAgentInfo();

			expect((component as any).setupCountryValueChange).not.toHaveBeenCalled();
			expect((component as any).setupRegionValueChange).not.toHaveBeenCalled();
		});
	});

	describe('Autocomplete Filtering Edge Cases', () => {
		it('should handle null search values in country filtering', () => {
			const countryControl = component.carrierAgentForm.get('country');

			countryControl?.setValue(null);
			fixture.detectChanges();

			expect(component.filteredCountries).toEqual(MOCK_COUNTRIES);
		});

		it('should handle empty string search in country filtering', () => {
			const countryControl = component.carrierAgentForm.get('country');

			countryControl?.setValue('');
			fixture.detectChanges();

			expect(component.filteredCountries).toEqual(MOCK_COUNTRIES);
		});

		it('should handle case-insensitive filtering', () => {
			const countryControl = component.carrierAgentForm.get('country');

			countryControl?.setValue('UNITED');
			fixture.detectChanges();

			expect(component.filteredCountries.length).toBe(2);
			expect(component.filteredCountries.every((c) => c.name.toLowerCase().includes('united'))).toBe(true);
		});

		it('should handle whitespace in search terms', () => {
			const countryControl = component.carrierAgentForm.get('country');

			countryControl?.setValue('  united  ');
			fixture.detectChanges();

			expect(component.filteredCountries.length).toBe(2);
		});

		it('should return empty array when no countries match', () => {
			const countryControl = component.carrierAgentForm.get('country');

			countryControl?.setValue('nonexistent');
			fixture.detectChanges();

			expect(component.filteredCountries.length).toBe(0);
		});
	});

	describe('Component Input Properties', () => {
		it('should have default values for input properties', () => {
			expect(component.carrierInfo).toBeNull();
			expect(component.haveAccountNo).toBe(false);
		});

		it('should accept haveAccountNo input', () => {
			component.haveAccountNo = true;

			expect(component.haveAccountNo).toBe(true);
		});
	});

	describe('Form Control Access', () => {
		it('should provide access to all form controls', () => {
			expect(component.carrierAgentForm.get('id')).toBeDefined();
			expect(component.carrierAgentForm.get('company')).toBeDefined();
			expect(component.carrierAgentForm.get('agentIataCode')).toBeDefined();
			expect(component.carrierAgentForm.get('accountingNoteText')).toBeDefined();
			expect(component.carrierAgentForm.get('country')).toBeDefined();
			expect(component.carrierAgentForm.get('province')).toBeDefined();
			expect(component.carrierAgentForm.get('cityCode')).toBeDefined();
			expect(component.carrierAgentForm.get('textualPostCode')).toBeDefined();
			expect(component.carrierAgentForm.get('address')).toBeDefined();
			expect(component.carrierAgentForm.get('phoneNumber')).toBeDefined();
			expect(component.carrierAgentForm.get('email')).toBeDefined();
		});

		it('should have correct initial form values', () => {
			expect(component.carrierAgentForm.get('id')?.value).toBeNull();
			expect(component.carrierAgentForm.get('company')?.value).toBe('');
			expect(component.carrierAgentForm.get('agentIataCode')?.value).toBe('');
			expect(component.carrierAgentForm.get('accountingNoteText')?.value).toBe('');
			expect(component.carrierAgentForm.get('country')?.value).toBe('');
			expect(component.carrierAgentForm.get('province')?.value).toBe('');
			expect(component.carrierAgentForm.get('cityCode')?.value).toBe('');
			expect(component.carrierAgentForm.get('textualPostCode')?.value).toBe('');
			expect(component.carrierAgentForm.get('address')?.value).toBe('');
			expect(component.carrierAgentForm.get('phoneNumber')?.value).toBe('');
			expect(component.carrierAgentForm.get('email')?.value).toBe('');
		});
	});

	describe('Error Handling', () => {
		it('should handle service errors gracefully when loading countries', () => {
			sliCreateRequestServiceSpy.getCountries.and.returnValue(of([]));

			component.ngOnInit();

			expect(component.countries).toEqual([]);
			expect(component.filteredCountries).toEqual([]);
		});

		it('should handle service errors when loading provinces', () => {
			sliCreateRequestServiceSpy.getProvinces.and.returnValue(of([]));

			component.countryValueChange({ option: { value: 'US' } } as MatAutocompleteSelectedEvent);

			expect(component.provinces).toEqual([]);
			expect(component.filteredProvinces).toEqual([]);
		});

		it('should handle service errors when loading cities', () => {
			component.provinces = MOCK_PROVINCES;
			sliCreateRequestServiceSpy.getCities.and.returnValue(of([]));

			component.regionValueChange({ option: { value: 'NY' } } as MatAutocompleteSelectedEvent);

			expect(component.cities).toEqual([]);
			expect(component.filteredCities).toEqual([]);
		});
	});
});
