import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import PartnerAccessComponent from './partner-access.component';
import { PartnerAccessRequestService } from '../../services/partner-access-request.service';
import { TranslateModule } from '@ngx-translate/core';
import { of, throwError, Subject } from 'rxjs';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatSelectChange } from '@angular/material/select';
import { ChangeDetectorRef } from '@angular/core';
import { PartnerListObject } from '../../models/partner-access.model';

describe('PartnerAccessComponent', () => {
	let component: PartnerAccessComponent;
	let fixture: ComponentFixture<PartnerAccessComponent>;
	const mockPartnerService: Partial<PartnerAccessRequestService> = {
		getPartnerList: jasmine.createSpy('getPartnerList').and.returnValue(
			of({
				rows: [
					{
						id: '111',
						businessData: 'sli',
						orgId: '1',
						orgName: 'Org 1',
						getLogisticsObject: '1',
						patchLogisticsObject: '1',
						postLogisticsEvent: '1',
						getLogisticsEvent: '1',
						createTime: '2023-01-01',
					},
					{
						id: '222',
						businessData: 'hawb',
						orgId: '2',
						orgName: 'Org 2',
						getLogisticsObject: '1',
						patchLogisticsObject: '1',
						postLogisticsEvent: '0',
						getLogisticsEvent: '1',
						createTime: '2023-01-02',
					},
				],
				total: 2,
			})
		),

		getOrgList: jasmine.createSpy('getOrgList').and.returnValue(
			of([
				{ id: '1', name: 'Org 1', orgType: 'TYPE_A' },
				{ id: '2', name: 'Org 2', orgType: 'TYPE_B' },
				{ id: '3', name: 'Org 3', orgType: 'TYPE_C' },
			])
		),

		getSystemList: jasmine.createSpy('getSystemList').and.returnValue(
			of([
				{ code: 'sli', name: 'SLI' },
				{ code: 'hawb', name: 'HAWB' },
				{ code: 'mawb', name: 'MAWB' },
			])
		),

		addPartner: jasmine.createSpy('addPartner').and.returnValue(of('partner-123')),
		updatePartners: jasmine.createSpy('updatePartners').and.returnValue(of('Success')),
		deletePartners: jasmine.createSpy('deletePartners').and.returnValue(of('Success')),
	};

	const mockChangeDetectorRef = {
		detectChanges: jasmine.createSpy('detectChanges'),
		markForCheck: jasmine.createSpy('markForCheck'),
	};

	beforeEach(async () => {
		// Reset spies before each test
		Object.values(mockPartnerService).forEach((spy: any) => {
			if (spy && spy.calls) {
				spy.calls.reset();
			}
		});
		mockChangeDetectorRef.detectChanges.calls.reset();
		mockChangeDetectorRef.markForCheck.calls.reset();

		await TestBed.configureTestingModule({
			imports: [PartnerAccessComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: PartnerAccessRequestService, useValue: mockPartnerService },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(PartnerAccessComponent);
		component = fixture.componentInstance;
		// Don't call detectChanges() here to avoid triggering ngOnInit automatically
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should initialize component data and call service methods', () => {
			// Act
			component.ngOnInit();

			// Assert
			expect(mockPartnerService.getSystemList).toHaveBeenCalledWith('businessType');
			expect(mockPartnerService.getOrgList).toHaveBeenCalledWith('', '');
			// Note: markForCheck is called by the actual ChangeDetectorRef, not our mock
		});

		it('should set bizTypeList when getSystemList succeeds', () => {
			// Arrange
			const mockBizTypes = [
				{ code: 'sli', name: 'SLI' },
				{ code: 'hawb', name: 'HAWB' },
			];
			mockPartnerService.getSystemList = jasmine.createSpy('getSystemList').and.returnValue(of(mockBizTypes));

			// Act
			component.ngOnInit();

			// Assert
			expect(component.bizTypeList).toEqual(mockBizTypes);
		});

		it('should set orgList and call getPartnerListPage when getOrgList succeeds', () => {
			// Arrange
			const mockOrgs = [
				{ id: '1', name: 'Org 1', orgType: 'TYPE_A' },
				{ id: '2', name: 'Org 2', orgType: 'TYPE_B' },
			];
			mockPartnerService.getOrgList = jasmine.createSpy('getOrgList').and.returnValue(of(mockOrgs));
			spyOn(component, 'getPartnerListPage');

			// Act
			component.ngOnInit();

			// Assert
			expect(component.orgList).toEqual(mockOrgs);
			expect(component.getPartnerListPage).toHaveBeenCalledWith(component.pageParams);
		});
	});

	describe('addOrSaveRow', () => {
		it('should create new row when newRow is null', () => {
			// Arrange
			expect(component.newRow).toBeNull();
			const initialDataLength = component.dataSource.data.length;

			// Act
			component.addOrSaveRow();

			// Assert
			expect(component.newRow).not.toBeNull();
			expect(component.newRow?.id).toBe('');
			expect(component.newRow?.businessData).toBe('');
			expect(component.newRow?.orgId).toBe('');
			expect(component.newRow?.orgName).toBe('');
			expect(component.newRow?.getLogisticsObject).toBe('1');
			expect(component.newRow?.patchLogisticsObject).toBe('1');
			expect(component.newRow?.postLogisticsEvent).toBe('1');
			expect(component.newRow?.getLogisticsEvent).toBe('1');
			expect(component.dataSource.data.length).toBe(initialDataLength + 1);
			// Note: detectChanges is called by the actual ChangeDetectorRef, not our mock
		});

		it('should save row when newRow exists', () => {
			// Arrange
			const testRow = {
				id: '',
				businessData: 'sli',
				orgId: '1',
				orgName: 'Test Org',
				getLogisticsObject: '1',
				patchLogisticsObject: '1',
				postLogisticsEvent: '1',
				getLogisticsEvent: '1',
				createTime: '',
			};
			component.newRow = testRow;
			component.dataSource.data = [testRow];

			// Use Subject to control async behavior
			const addPartnerSubject = new Subject<string>();
			mockPartnerService.addPartner = jasmine.createSpy('addPartner').and.returnValue(addPartnerSubject.asObservable());

			// Act
			component.addOrSaveRow();

			// Assert - dataLoading should be true before observable completes
			expect(component.dataLoading).toBe(true);
			expect(mockPartnerService.addPartner).toHaveBeenCalledWith(testRow);

			// Complete the observable
			addPartnerSubject.next('new-partner-id');
			addPartnerSubject.complete();
		});

		it('should handle successful partner addition', () => {
			// Arrange
			const testRow = {
				id: '',
				businessData: 'sli',
				orgId: '1',
				orgName: 'Test Org',
				getLogisticsObject: '1',
				patchLogisticsObject: '1',
				postLogisticsEvent: '1',
				getLogisticsEvent: '1',
				createTime: '',
			};
			component.newRow = testRow;
			component.dataSource.data = [testRow];
			mockPartnerService.addPartner = jasmine.createSpy('addPartner').and.returnValue(of('new-partner-id'));

			// Act
			component.addOrSaveRow();

			// Assert
			expect(component.newRow).toBeNull();
			expect(component.dataLoading).toBe(false);
			expect(component.dataSource.data[0].id).toBe('new-partner-id');
		});

		it('should handle partner addition error', () => {
			// Arrange
			const testRow = {
				id: '',
				businessData: 'sli',
				orgId: '1',
				orgName: 'Test Org',
				getLogisticsObject: '1',
				patchLogisticsObject: '1',
				postLogisticsEvent: '1',
				getLogisticsEvent: '1',
				createTime: '',
			};
			component.newRow = testRow;
			component.dataSource.data = [testRow];
			const initialDataLength = component.dataSource.data.length;
			mockPartnerService.addPartner = jasmine.createSpy('addPartner').and.returnValue(throwError(() => new Error('API Error')));

			// Act
			component.addOrSaveRow();

			// Assert
			expect(component.dataLoading).toBe(false);
			expect(component.newRow).toBeNull();
			expect(component.dataSource.data.length).toBe(initialDataLength - 1);
		});
	});

	describe('toggleEdit', () => {
		beforeEach(() => {
			component.dataSource.data = [
				{
					id: '1',
					businessData: 'sli',
					orgId: '1',
					orgName: 'Org 1',
					getLogisticsObject: '1',
					patchLogisticsObject: '1',
					postLogisticsEvent: '1',
					getLogisticsEvent: '1',
					createTime: '2023-01-01',
				},
			];
		});

		it('should toggle edit mode to true when currently false', () => {
			// Arrange
			component.isEditMode = false;

			// Act
			component.toggleEdit();

			// Assert
			expect(component.isEditMode).toBe(true);
		});

		it('should save changes and toggle edit mode to false when currently true', () => {
			// Arrange
			component.isEditMode = true;

			// Use Subject to control async behavior
			const updatePartnersSubject = new Subject<string>();
			mockPartnerService.updatePartners = jasmine.createSpy('updatePartners').and.returnValue(updatePartnersSubject.asObservable());

			// Act
			component.toggleEdit();

			// Assert - dataLoading should be true before observable completes
			expect(component.dataLoading).toBe(true);
			expect(mockPartnerService.updatePartners).toHaveBeenCalledWith(component.dataSource.data);

			// Complete the observable
			updatePartnersSubject.next('Success');
			updatePartnersSubject.complete();
		});

		it('should handle successful update', () => {
			// Arrange
			component.isEditMode = true;
			mockPartnerService.updatePartners = jasmine.createSpy('updatePartners').and.returnValue(of('Success'));

			// Act
			component.toggleEdit();

			// Assert
			expect(component.isEditMode).toBe(false);
			expect(component.dataLoading).toBe(false);
		});

		it('should handle update error', () => {
			// Arrange
			component.isEditMode = true;
			mockPartnerService.updatePartners = jasmine
				.createSpy('updatePartners')
				.and.returnValue(throwError(() => new Error('Update failed')));

			// Act
			component.toggleEdit();

			// Assert
			expect(component.dataLoading).toBe(false);
		});

		it('should delete partners when deletedIds is not empty', () => {
			// Arrange
			component.isEditMode = true;
			component.deletedIds.add('deleted-1');
			component.deletedIds.add('deleted-2');

			// Act
			component.toggleEdit();

			// Assert
			expect(mockPartnerService.deletePartners).toHaveBeenCalledWith(['deleted-1', 'deleted-2']);
		});

		it('should handle successful deletion', () => {
			// Arrange
			component.isEditMode = true;
			component.deletedIds.add('deleted-1');
			mockPartnerService.deletePartners = jasmine.createSpy('deletePartners').and.returnValue(of('Success'));

			// Act
			component.toggleEdit();

			// Assert
			expect(component.isEditMode).toBe(false);
			expect(component.deletedIds.size).toBe(0);
			expect(component.dataLoading).toBe(false);
		});

		it('should handle deletion error', () => {
			// Arrange
			component.isEditMode = true;
			component.deletedIds.add('deleted-1');
			mockPartnerService.deletePartners = jasmine
				.createSpy('deletePartners')
				.and.returnValue(throwError(() => new Error('Delete failed')));

			// Act
			component.toggleEdit();

			// Assert
			expect(component.dataLoading).toBe(false);
		});

		it('should not call deletePartners when deletedIds is empty', () => {
			// Arrange
			component.isEditMode = true;
			component.deletedIds.clear();

			// Act
			component.toggleEdit();

			// Assert
			expect(mockPartnerService.deletePartners).not.toHaveBeenCalled();
		});
	});

	describe('shouldShowEditUI', () => {
		let testRow: PartnerListObject;

		beforeEach(() => {
			testRow = {
				id: '1',
				businessData: 'sli',
				orgId: '1',
				orgName: 'Test Org',
				getLogisticsObject: '1',
				patchLogisticsObject: '1',
				postLogisticsEvent: '1',
				getLogisticsEvent: '1',
				createTime: '2023-01-01',
			};
		});

		it('should return true when isEditMode is true', () => {
			// Arrange
			component.isEditMode = true;

			// Act
			const result = component.shouldShowEditUI(testRow);

			// Assert
			expect(result).toBe(true);
		});

		it('should return true when newRow is not null and row has no id', () => {
			// Arrange
			component.isEditMode = false;
			component.newRow = { ...testRow, id: '' };
			testRow.id = '';

			// Act
			const result = component.shouldShowEditUI(testRow);

			// Assert
			expect(result).toBe(true);
		});

		it('should return true when row has no orgId', () => {
			// Arrange
			component.isEditMode = false;
			component.newRow = null;
			testRow.orgId = '';

			// Act
			const result = component.shouldShowEditUI(testRow);

			// Assert
			expect(result).toBe(true);
		});

		it('should return true when row has no businessData', () => {
			// Arrange
			component.isEditMode = false;
			component.newRow = null;
			testRow.businessData = '';

			// Act
			const result = component.shouldShowEditUI(testRow);

			// Assert
			expect(result).toBe(true);
		});

		it('should return false when all conditions are met for hiding edit UI', () => {
			// Arrange
			component.isEditMode = false;
			component.newRow = null;

			// Act
			const result = component.shouldShowEditUI(testRow);

			// Assert
			expect(result).toBe(false);
		});

		it('should delete new row when all logistics fields are 0 and newRow exists', () => {
			// Arrange
			const zeroRow = {
				...testRow,
				id: '',
				getLogisticsObject: '0',
				patchLogisticsObject: '0',
				postLogisticsEvent: '0',
				getLogisticsEvent: '0',
			};
			component.newRow = zeroRow;
			component.dataSource.data = [zeroRow, testRow];
			const initialLength = component.dataSource.data.length;

			// Act
			component.shouldShowEditUI(zeroRow);

			// Assert
			expect(component.newRow).toBeNull();
			expect(component.dataSource.data.length).toBe(initialLength - 1);
			// Note: detectChanges is called by the actual ChangeDetectorRef, not our mock
		});

		it('should delete existing row and add to deletedIds when all logistics fields are 0 and newRow is null', () => {
			// Arrange
			const zeroRow = {
				...testRow,
				id: 'zero-row-id', // Give it a unique ID
				getLogisticsObject: '0',
				patchLogisticsObject: '0',
				postLogisticsEvent: '0',
				getLogisticsEvent: '0',
			};
			component.newRow = null;
			component.dataSource.data = [zeroRow, testRow];
			const initialLength = component.dataSource.data.length;
			const initialDeletedSize = component.deletedIds.size;

			// Act
			component.shouldShowEditUI(zeroRow);

			// Assert
			expect(component.dataSource.data.length).toBe(initialLength - 1);
			expect(component.deletedIds.size).toBe(initialDeletedSize + 1);
			expect(component.deletedIds.has(zeroRow.id)).toBe(true);
			expect(component.dataSource.data.find((item) => item.id === zeroRow.id)).toBeUndefined();
		});
	});

	describe('getPartnerListPage', () => {
		beforeEach(() => {
			component.orgList = [
				{ id: '1', name: 'Org 1', orgType: 'TYPE_A' },
				{ id: '2', name: 'Org 2', orgType: 'TYPE_B' },
			];
		});

		it('should set dataLoading to true and call service', () => {
			// Arrange
			const pageParams = { pageNum: 1, pageSize: 10 };

			// Use Subject to control async behavior
			const getPartnerListSubject = new Subject<any>();
			mockPartnerService.getPartnerList = jasmine.createSpy('getPartnerList').and.returnValue(getPartnerListSubject.asObservable());

			// Act
			component.getPartnerListPage(pageParams);

			// Assert - dataLoading should be true before observable completes
			expect(component.dataLoading).toBe(true);
			expect(mockPartnerService.getPartnerList).toHaveBeenCalledWith(pageParams, '');

			// Complete the observable
			getPartnerListSubject.next({ rows: [], total: 0 });
			getPartnerListSubject.complete();
		});

		it('should set dataSource and stop loading on success', () => {
			// Arrange
			const pageParams = { pageNum: 1, pageSize: 10 };
			const mockResponse = {
				rows: [
					{
						id: '1',
						businessData: 'sli',
						orgId: '1',
						orgName: '',
						getLogisticsObject: '1',
						patchLogisticsObject: '1',
						postLogisticsEvent: '1',
						getLogisticsEvent: '1',
						createTime: '2023-01-01',
					},
				],
				total: 1,
			};
			mockPartnerService.getPartnerList = jasmine.createSpy('getPartnerList').and.returnValue(of(mockResponse));

			// Act
			component.getPartnerListPage(pageParams);

			// Assert
			expect(component.dataSource.data.length).toBe(1);
			expect(component.dataSource.data[0].orgName).toBe('Org 1'); // Should be mapped from orgList
			expect(component.dataLoading).toBe(false);
			// Note: markForCheck is called by the actual ChangeDetectorRef, not our mock
		});

		it('should handle service error', () => {
			// Arrange
			const pageParams = { pageNum: 1, pageSize: 10 };
			mockPartnerService.getPartnerList = jasmine
				.createSpy('getPartnerList')
				.and.returnValue(throwError(() => new Error('Service error')));

			// Act
			component.getPartnerListPage(pageParams);

			// Assert
			expect(component.dataLoading).toBe(false);
		});

		it('should map orgName correctly when orgId matches', () => {
			// Arrange
			const pageParams = { pageNum: 1, pageSize: 10 };
			const mockResponse = {
				rows: [
					{
						id: '1',
						businessData: 'sli',
						orgId: '2',
						orgName: '',
						getLogisticsObject: '1',
						patchLogisticsObject: '1',
						postLogisticsEvent: '1',
						getLogisticsEvent: '1',
						createTime: '2023-01-01',
					},
				],
				total: 1,
			};
			mockPartnerService.getPartnerList = jasmine.createSpy('getPartnerList').and.returnValue(of(mockResponse));

			// Act
			component.getPartnerListPage(pageParams);

			// Assert
			expect(component.dataSource.data[0].orgName).toBe('Org 2');
		});

		it('should set empty orgName when orgId does not match', () => {
			// Arrange
			const pageParams = { pageNum: 1, pageSize: 10 };
			const mockResponse = {
				rows: [
					{
						id: '1',
						businessData: 'sli',
						orgId: '999',
						orgName: '',
						getLogisticsObject: '1',
						patchLogisticsObject: '1',
						postLogisticsEvent: '1',
						getLogisticsEvent: '1',
						createTime: '2023-01-01',
					},
				],
				total: 1,
			};
			mockPartnerService.getPartnerList = jasmine.createSpy('getPartnerList').and.returnValue(of(mockResponse));

			// Act
			component.getPartnerListPage(pageParams);

			// Assert
			expect(component.dataSource.data[0].orgName).toBe('');
		});
	});

	describe('onCheckboxChange', () => {
		it('should set field to "1" when checkbox is checked', () => {
			// Arrange
			const mockEvent = { checked: true } as MatCheckboxChange;
			const testItem = { testField: '0' };
			const fieldName = 'testField';

			// Act
			component.onCheckboxChange(mockEvent, testItem, fieldName);

			// Assert
			expect(testItem.testField).toBe('1');
		});

		it('should set field to "0" when checkbox is unchecked', () => {
			// Arrange
			const mockEvent = { checked: false } as MatCheckboxChange;
			const testItem = { testField: '1' };
			const fieldName = 'testField';

			// Act
			component.onCheckboxChange(mockEvent, testItem, fieldName);

			// Assert
			expect(testItem.testField).toBe('0');
		});

		it('should work with different field names', () => {
			// Arrange
			const mockEvent = { checked: true } as MatCheckboxChange;
			const testItem = { getLogisticsObject: '0', postLogisticsEvent: '0' };

			// Act
			component.onCheckboxChange(mockEvent, testItem, 'getLogisticsObject');
			component.onCheckboxChange(mockEvent, testItem, 'postLogisticsEvent');

			// Assert
			expect(testItem.getLogisticsObject).toBe('1');
			expect(testItem.postLogisticsEvent).toBe('1');
		});
	});

	describe('setOrgInfo', () => {
		beforeEach(() => {
			component.orgList = [
				{ id: '1', name: 'Organization One', orgType: 'TYPE_A' },
				{ id: '2', name: 'Organization Two', orgType: 'TYPE_B' },
				{ id: '3', name: 'Organization Three', orgType: 'TYPE_C' },
			];
		});

		it('should set orgId and orgName when org is found', () => {
			// Arrange
			const mockEvent = { value: '2' } as MatSelectChange;
			const testRow: PartnerListObject = {
				id: '1',
				businessData: 'sli',
				orgId: '',
				orgName: '',
				getLogisticsObject: '1',
				patchLogisticsObject: '1',
				postLogisticsEvent: '1',
				getLogisticsEvent: '1',
				createTime: '2023-01-01',
			};

			// Act
			component.setOrgInfo(mockEvent, testRow);

			// Assert
			expect(testRow.orgId).toBe('2');
			expect(testRow.orgName).toBe('Organization Two');
		});

		it('should set orgId and empty orgName when org is not found', () => {
			// Arrange
			const mockEvent = { value: '999' } as MatSelectChange;
			const testRow: PartnerListObject = {
				id: '1',
				businessData: 'sli',
				orgId: '',
				orgName: '',
				getLogisticsObject: '1',
				patchLogisticsObject: '1',
				postLogisticsEvent: '1',
				getLogisticsEvent: '1',
				createTime: '2023-01-01',
			};

			// Act
			component.setOrgInfo(mockEvent, testRow);

			// Assert
			expect(testRow.orgId).toBe('999');
			expect(testRow.orgName).toBe('');
		});

		it('should update existing orgId and orgName', () => {
			// Arrange
			const mockEvent = { value: '3' } as MatSelectChange;
			const testRow: PartnerListObject = {
				id: '1',
				businessData: 'sli',
				orgId: '1',
				orgName: 'Organization One',
				getLogisticsObject: '1',
				patchLogisticsObject: '1',
				postLogisticsEvent: '1',
				getLogisticsEvent: '1',
				createTime: '2023-01-01',
			};

			// Act
			component.setOrgInfo(mockEvent, testRow);

			// Assert
			expect(testRow.orgId).toBe('3');
			expect(testRow.orgName).toBe('Organization Three');
		});
	});

	describe('Component Properties and Initialization', () => {
		it('should have correct initial values', () => {
			// Assert
			expect(component.dataLoading).toBe(false);
			expect(component.lastAddedRowIndex).toBeNull();
			expect(component.orgList).toEqual([]);
			expect(component.bizTypeList).toEqual([]);
			expect(component.deletedIds.size).toBe(0);
			expect(component.isEditMode).toBe(false);
			expect(component.newRow).toBeNull();
			expect(component.pageParams).toEqual({ pageNum: 1, pageSize: 10 });
		});

		it('should have correct displayedColumns', () => {
			// Assert
			expect(component.displayedColumns).toEqual([
				'businessData',
				'partner',
				'getLogisticsObject',
				'patchLogisticsObject',
				'postLogisticsEvent',
				'getLogisticsEvent',
			]);
		});

		it('should have correct listObjeNames', () => {
			// Assert
			expect(component.listObjeNames).toEqual([
				{ name: 'GET_LOGISTICS_OBJECT', code: 'getLogisticsObject' },
				{ name: 'PATCH_LOGISTICS_OBJECT', code: 'patchLogisticsObject' },
				{ name: 'POST_LOGISTICS_EVENT', code: 'postLogisticsEvent' },
				{ name: 'GET_LOGISTICS_EVENT', code: 'getLogisticsEvent' },
			]);
		});
	});
});
