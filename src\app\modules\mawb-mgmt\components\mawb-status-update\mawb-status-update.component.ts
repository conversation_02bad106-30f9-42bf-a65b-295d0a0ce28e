import { Component, EventEmitter, Inject, OnInit, Output } from '@angular/core';
import { MawbStatusService } from '../../services/mawb-status.service';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { EventTimeType, MAWBEventObj } from '../../models/mawb-event.model';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatNativeDateModule, provideNativeDateAdapter } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import { DatePipe } from '@angular/common';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { DelegationRequestComponent } from '@shared/components/delegation-request/delegation-request.component';

const DATE_FORMAT = 'yyyy-MM-dd HH:mm:ss';
export interface UpdateDialog {
	param: MAWBEventObj;
}

@Component({
	selector: 'orll-mawb-status-update',
	imports: [
		MatRadioModule,
		FormsModule,
		MatCheckboxModule,
		MatFormFieldModule,
		ReactiveFormsModule,
		MatDialogModule,
		TranslateModule,
		MatIconModule,
		MatButtonModule,
		MatDatepickerModule,
		MatNativeDateModule,
		MatInputModule,
		MatDividerModule,
		SpinnerComponent,
	],
	providers: [provideNativeDateAdapter(), DatePipe],
	templateUrl: './mawb-status-update.component.html',
	styleUrl: './mawb-status-update.component.scss',
})
export class MawbStatusUpdateComponent extends DelegationRequestComponent implements OnInit {
	@Output() refreshDelegationRequest = new EventEmitter<void>();

	events: string[] = [];
	maxDate: Date;

	selectedEvent = '';
	partialEventIndicator = false;
	eventTimeType = EventTimeType[0];
	milestoneTime: Date | null = null;

	dataLoading = false;
	selectedEventsList = [];
	allEvents: string[] = [];

	constructor(
		public readonly statusService: MawbStatusService,
		private readonly datePipe: DatePipe,
		public dialogRef: MatDialogRef<MawbStatusUpdateComponent>,
		@Inject(MAT_DIALOG_DATA) public data: UpdateDialog
	) {
		super();
		const today = new Date();
		this.maxDate = new Date();
		this.maxDate.setDate(today.getDate() - 1);
	}

	ngOnInit(): void {
		this.dataLoading = true;
		this.statusService.getEventList(this.data.param).subscribe((res) => {
			this.dataLoading = false;
			this.events = res;
		});
		this.statusService.getAllEvents().subscribe((res) => {
			this.allEvents = res;
		});
	}

	onEventChange(val: string) {
		this.selectedEvent = val;
	}

	onEventTypeChange(event: MatCheckboxChange) {
		this.eventTimeType = event.checked ? EventTimeType[1] : EventTimeType[0];
	}

	updateEventStatus() {
		this.dataLoading = true;
		const updateTime = this.milestoneTime || this.maxDate;
		const param = {
			...this.data.param,
			eventTimeType: this.eventTimeType,
			status: this.selectedEvent,
			updateTime: this.datePipe.transform(updateTime, DATE_FORMAT),
			partialEventIndicator: this.partialEventIndicator,
		};
		this.statusService.updateEventStatus(param).subscribe({
			next: () => {
				this.dataLoading = false;
				this.dialogRef.close();
			},
			error: (err) => {
				this.dataLoading = false;

				// trigger delegation request when 403
				if (err.status === 403) {
					this.delegationRequest(err.error, this.data.param.mawbId)
						.pipe(takeUntilDestroyed(this.destroyRef))
						.subscribe((result) => {
							if (result) {
								this.dialogRef.close(true);
							}
						});
				}
			},
		});
	}
}
