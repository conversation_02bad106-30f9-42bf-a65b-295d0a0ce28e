import { LogisticObjType } from '@shared/models/share-type.model';

export interface NotificationListObj {
	id: string;
	eventType: string;
	dataType: LogisticObjType;
	companyName: string;
	logisticsObject: string;
	hasRead: boolean;
	createTime: string;
	waybillNumber: string;
}

export enum NotificationEventType {
	LOGISTICS_OBJECT_CREATED = 'LOGISTICS_OBJECT_CREATED',
	LOGISTICS_OBJECT_UPDATED = 'LOGISTICS_OBJECT_UPDATED',
	LOGISTICS_EVENT_RECEIVED = 'LOGISTICS_EVENT_RECEIVED',
}
