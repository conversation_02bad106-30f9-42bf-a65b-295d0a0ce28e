import { TestBed } from '@angular/core/testing';
import { DelegationRequestComponent } from './delegation-request.component';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { DelegationRequestDialogComponent } from '@shared/components/delegation-request-dialog/delegation-request-dialog.component';
import { ResponseObj } from '@shared/models/response.model';
import { of } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';

// Create a concrete implementation of the abstract class for testing
class TestDelegationRequestComponent extends DelegationRequestComponent {}

describe('DelegationRequestComponent', () => {
	let component: TestDelegationRequestComponent;
	let dialogSpy: jasmine.SpyObj<MatDialog>;
	let dialogRefSpyConfirm: jasmine.SpyObj<MatDialogRef<ConfirmDialogComponent>>;
	let dialogRefSpyDelegation: jasmine.SpyObj<MatDialogRef<DelegationRequestDialogComponent>>;

	beforeEach(() => {
		dialogRefSpyConfirm = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
		dialogRefSpyDelegation = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
		dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);

		TestBed.configureTestingModule({
			imports: [MatDialogModule],
			providers: [
				TestDelegationRequestComponent,
				{ provide: MatDialog, useValue: dialogSpy },
				provideHttpClient(),
				provideHttpClientTesting(),
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		});

		component = TestBed.inject(TestDelegationRequestComponent);
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('delegationRequest', () => {
		const mockError: ResponseObj<string> = {
			data: 'LO123',
			code: 403,
			msg: 'Permission denied',
		};
		const documentId = '123';

		it('should return false when confirm dialog is dismissed', (done) => {
			dialogRefSpyConfirm.afterClosed.and.returnValue(of(false));
			dialogSpy.open.and.returnValue(dialogRefSpyConfirm);

			component.delegationRequest(mockError, documentId).subscribe((result) => {
				expect(result).toBe(false);
				expect(dialogSpy.open).toHaveBeenCalledWith(ConfirmDialogComponent, {
					width: '500px',
					data: {
						title: 'common.dialog.alert.title',
						content: mockError.msg,
						icon: 'send',
						ok: 'common.dialog.request.delegation',
					},
				});
				done();
			});
		});

		it('should open delegation request dialog when confirm dialog is confirmed', () => {
			dialogRefSpyConfirm.afterClosed.and.returnValue(of(true));
			dialogRefSpyDelegation.afterClosed.and.returnValue(of(false));

			dialogSpy.open.and.returnValues(dialogRefSpyConfirm, dialogRefSpyDelegation);

			component.delegationRequest(mockError, documentId).subscribe();

			expect(dialogSpy.open).toHaveBeenCalledWith(DelegationRequestDialogComponent, {
				width: '60vw',
				autoFocus: false,
				data: {
					icon: 'send',
					ok: 'common.dialog.delegation.request.send',
					loId: mockError.data,
					documentId,
				},
			});
		});

		it('should return true when both dialogs are confirmed', (done) => {
			dialogRefSpyConfirm.afterClosed.and.returnValue(of(true));
			dialogRefSpyDelegation.afterClosed.and.returnValue(of(true));

			dialogSpy.open.and.returnValues(dialogRefSpyConfirm, dialogRefSpyDelegation);

			component.delegationRequest(mockError, documentId).subscribe((result) => {
				expect(result).toBe(true);
				done();
			});
		});

		it('should return false when delegation dialog is dismissed', (done) => {
			dialogRefSpyConfirm.afterClosed.and.returnValue(of(true));
			dialogRefSpyDelegation.afterClosed.and.returnValue(of(false));

			dialogSpy.open.and.returnValues(dialogRefSpyConfirm, dialogRefSpyDelegation);

			component.delegationRequest(mockError, documentId).subscribe((result) => {
				expect(result).toBe(false);
				done();
			});
		});

		it('should return true when delegation dialog returns a truthy value', (done) => {
			dialogRefSpyConfirm.afterClosed.and.returnValue(of(true));
			dialogRefSpyDelegation.afterClosed.and.returnValue(of('some result'));

			dialogSpy.open.and.returnValues(dialogRefSpyConfirm, dialogRefSpyDelegation);

			component.delegationRequest(mockError, documentId).subscribe((result) => {
				expect(result).toBe(true);
				done();
			});
		});
	});
});
