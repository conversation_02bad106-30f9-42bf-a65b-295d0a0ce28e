import { TestBed } from '@angular/core/testing';
import { RuleService } from './rule.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { RuleDetailObj, RuleListObj } from '../models/rule.model';
import { environment } from '@environments/environment';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { of, throwError } from 'rxjs';

const baseUrl = environment.baseApi;

const mockRule: RuleDetailObj = {
	holder: ['111'],
	requestType: '222',
	action: '222',
	id: '111',
	request: ['222'],
};

const mockRuleListObj: RuleListObj = {
	id: '111',
	holder: '222',
	requestType: '22',
	request: '',
	action: '',
	holderNames: '',
	requestTypeDescription: '',
	requestNames: '',
	actionDescription: '',
};

describe('RuleService', () => {
	let service: RuleService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [RuleService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(RuleService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should getRulePerpage and return RuleListObj[]', () => {
		const param: PaginationRequest = { pageSize: 10, pageNum: 1 };
		const mockPagedResponse: PaginationResponse<RuleListObj> = {
			total: 15,
			rows: [
				{
					holder: '111',
					requestType: '222',
					requestNames: '222',
					action: '222',
					id: '',
					request: '',
					holderNames: '',
					requestTypeDescription: '',
					actionDescription: '',
				},
			],
		};

		service.getDataPerPage(param).subscribe((data) => {
			expect(data).toEqual(mockPagedResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/role/role/list?pageSize=10&pageNum=1`);
		expect(req.request.method).toBe('GET');

		req.flush(mockPagedResponse);
	});

	it('should loadData and return RuleListObj[]', () => {
		const mockPagedResponse: RuleListObj[] = [
			{
				holder: '111',
				requestType: '222',
				requestNames: '222',
				action: '222',
				id: '',
				request: '',
				holderNames: '',
				requestTypeDescription: '',
				actionDescription: '',
			},
		];

		service.loadAllData({}).subscribe((data) => {
			expect(data).toEqual(mockPagedResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/role/role/list`);
		expect(req.request.method).toBe('GET');

		req.flush(mockPagedResponse);
	});

	describe('deleteRule', () => {
		it('should call deleteData with correct args and return Observable<boolean>', () => {
			const expectedBody = { ids: [mockRuleListObj.id] };
			const deleteSpy = spyOn(service, 'deleteData').and.returnValue(of(true));

			service.deleteRule(mockRuleListObj).subscribe((result) => {
				expect(result).toBeTrue();
			});

			expect(deleteSpy).toHaveBeenCalledWith('role/role', expectedBody);
		});
	});

	describe('saveRule', () => {
		it('should call updateData when row has id', () => {
			const updateSpy = spyOn(service, 'updateData').and.returnValue(of(''));

			service.saveRule(mockRule).subscribe((result) => {
				expect(result).toEqual('');
			});

			expect(updateSpy).toHaveBeenCalledWith('role/role', mockRule);
		});

		it('should call postData when row does not have id', () => {
			const newRow = { ...mockRule, id: undefined };
			const postSpy = spyOn(service, 'postData').and.returnValue(of('new-456'));

			service.saveRule(newRow).subscribe((result) => {
				expect(result).toBe('new-456');
			});

			expect(postSpy).toHaveBeenCalledWith('role/role', newRow);
		});
	});

	describe('getRule', () => {
		it('should call getData with correct params and return Observable<RuleListObj>', () => {
			const expectedResult: RuleDetailObj = { ...mockRule };
			const getSpy = spyOn(service as any, 'getData').and.returnValue(of(expectedResult));

			service.getRule(mockRuleListObj).subscribe((result) => {
				expect(result).toEqual(expectedResult);
			});

			expect(getSpy).toHaveBeenCalledWith('role/role', { id: mockRule.id });
		});

		it('should handle error from getData', () => {
			const errorResponse = new Error('Network error');
			spyOn(service, 'getData').and.returnValue(throwError(() => errorResponse));

			service.getRule(mockRuleListObj).subscribe({
				next: () => fail('Expected an error, not a success'),
				error: (error) => {
					expect(error.message).toBe('Network error');
				},
			});
		});
	});
});
