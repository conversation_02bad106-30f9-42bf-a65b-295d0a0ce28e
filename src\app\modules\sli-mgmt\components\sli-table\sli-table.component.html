<div class="orll-sli-table__container">
	<div class="orll-sli-table__create">
		<button mat-stroked-button color="primary" (click)="retrieveObj()">
			<mat-icon>search</mat-icon>
			{{ 'common.enter.uri.btn' | translate }}
		</button>
		@if (hasPermission(createPermission, sliModule) | async) {
			<button mat-flat-button color="primary" (click)="createSli()">
				<mat-icon>add</mat-icon>
				{{ 'sli.mgmt.create' | translate }}
			</button>
		}
	</div>

	<table
		mat-table
		[dataSource]="dataSource"
		[trackBy]="trackBySliCode"
		matSort
		(matSortChange)="onSortChange($event)"
		aria-label="SLI table"
		class="orll-sli-table__mat">
		<ng-container matColumnDef="waybillNumber">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-number-width">
				{{ 'sli.table.column.sliCode' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">
				<a class="sli-number__link" (click)="editSli(record.sliNumber)">
					{{ record.waybillNumber }}
				</a>
			</td>
		</ng-container>

		<ng-container matColumnDef="shipper">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-shipper-width">
				{{ 'sli.table.column.shipper' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.shipper }}</td>
		</ng-container>

		<ng-container matColumnDef="consignee">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-consignee-width">
				{{ 'sli.table.column.consignee' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.consignee }}</td>
		</ng-container>

		<ng-container matColumnDef="goodsDescription">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-description-width">
				{{ 'sli.table.column.goodsDescription' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.goodsDescription }}</td>
		</ng-container>

		<ng-container matColumnDef="departureLocation">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-airport-width">
				{{ 'sli.table.column.departureLocation' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.departureLocation }}</td>
		</ng-container>

		<ng-container matColumnDef="arrivalLocation">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-airport-width">
				{{ 'sli.table.column.arrivalLocation' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.arrivalLocation }}</td>
		</ng-container>

		<ng-container matColumnDef="createDate">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-date-width">
				{{ 'sli.table.column.createDate' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.createDate | iataDateFormat }}</td>
		</ng-container>

		<ng-container matColumnDef="receivedFrom">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="sli-date-width">
				{{ 'sli.table.column.receivedFrom' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.receiveDate | iataDateFormat }}</td>
		</ng-container>

		<ng-container matColumnDef="hawbNumber">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="hawb-number-width">
				{{ 'sli.table.column.hawbNumber' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">
				@if (hasSomeRole(shipperRoles) | async) {
					{{ record.hawbNumber }}
				}
				@if (hasSomeRole(forwarderRoles) | async) {
					@if (record.hawbNumber) {
						{{ record.hawbNumber }}
					} @else {
						<a
							mat-stroked-button
							class="create-hawb-button"
							[routerLink]="['/hawb/create', record.sliNumber, 'calculate']"
							[queryParams]="{ sliNumber: record.sliNumber }">
							{{ 'sli.mgmt.createHawb' | translate }}
						</a>
					}
				}
			</td>
		</ng-container>

		<ng-container matColumnDef="share">
			<th scope="col" mat-header-cell *matHeaderCellDef class="sli-share-width">{{ 'sli.table.column.share' | translate }}</th>
			<td mat-cell *matCellDef="let record">
				<div class="d-flex">
					<button mat-icon-button aria-label="Share a SLI record" class="share-button" (click)="shareSliObject(record)">
						<mat-icon>share</mat-icon>
					</button>
					<button mat-icon-button [orllCopy]="record.sliNumber" color="primary">
						<mat-icon>content_copy</mat-icon>
					</button>
				</div>
			</td>
		</ng-container>

		<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
		<tr mat-row *matRowDef="let record; columns: displayedColumns" class="orll-sli-table__row"></tr>
	</table>
</div>

<mat-paginator [pageSizeOptions]="tablePageSizes" [length]="totalRecords" (page)="pagination.emit($event)"></mat-paginator>
