import { TestBed } from '@angular/core/testing';
import { CommonService } from './common.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { of } from 'rxjs';

describe('CommonService', () => {
	let service: CommonService;
	let dialog: jasmine.SpyObj<MatDialog>;
	let translate: jasmine.SpyObj<TranslateService>;

	const mockDialogRef = {
		afterClosed: () => of(true),
	};

	beforeEach(() => {
		const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
		const translateSpy = jasmine.createSpyObj('TranslateService', ['instant']);
		TestBed.configureTestingModule({
			imports: [TranslateModule.forRoot()],
			providers: [CommonService, { provide: MatDialog, useValue: dialogSpy }, { provide: TranslateService, useValue: translateSpy }],
		});
		service = TestBed.inject(CommonService);
		dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
		translate = TestBed.inject(TranslateService) as jasmine.SpyObj<TranslateService>;
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	describe('showWarning', () => {
		const testKey = 'ERROR.CONNECTION';
		const translatedMessage = 'network issue';

		beforeEach(() => {
			translate.instant.and.returnValue(translatedMessage);

			dialog.open.and.returnValue(mockDialogRef as any);
		});

		it('should call dialog.open with correct config', () => {
			// Act
			service.showWarning(testKey);

			// Assert
			expect(dialog.open).toHaveBeenCalledWith(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: translatedMessage,
				},
			});
		});

		it('should call translate.instant with the provided info key', () => {
			// Act
			service.showWarning(testKey);

			// Assert
			expect(translate.instant).toHaveBeenCalledWith(testKey);
		});
	});
});
