export interface PartnerListObject {
	id: string;
	businessData: string;
	orgId: string;
	orgName: string;
	getLogisticsObject: string;
	patchLogisticsObject: string;
	postLogisticsEvent: string;
	getLogisticsEvent: string;
	createTime: string;
	newItemKey?: string;
}

export interface BizData {
	name: string;
	code: string;
}

export interface PartnerBusinessDataListObject {
	id: string;
	name: string;
	orgType: string;
	prefix: string;
}
