import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EcsdListComponent } from './ecsd-list.component';
import { EcsdService } from '../../service/ecsd.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { UserProfileService } from '@shared/services/user-profile.service';
import { of, throwError } from 'rxjs';
import { EcsdObj } from '../../models/ecsd.model';
import { CommonService } from '@shared/services/common.service';
import { UserProfile } from '@shared/models/user-profile.model';
import { Modules, UserRole } from '@shared/models/user-role.model';
import { ChangeDetectorRef } from '@angular/core';

const row: EcsdObj = {
	id: '123',
	securityStatus: '',
	receivedFrom: '',
	whetherExemptedForScreening: '',
	screeningMethod: '',
	groundsForExemption: '',
	issuedBy: '',
	employeeId: '',
	issuedOn: '',
	additionalSecurityInfo: '',
	loId: '',
	loType: '',
	regulatedEntityCategory1: '',
	regulatedEntityIdentifier1: '',
	regulatedEntityCategory: '',
	regulatedEntityIdentifier: '',
};

function createMockUserProfile(overrides: Partial<UserProfile> = {}): UserProfile {
	return {
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'ORG_TYPE',
		userType: '1',
		menuList: [],
		permissionList: [],
		orgList: [],
		...overrides,
	};
}

describe('EscdListComponent', () => {
	let component: EcsdListComponent;
	let fixture: ComponentFixture<EcsdListComponent>;
	let mockDialog: jasmine.SpyObj<MatDialog>;
	let mockEcsdService: jasmine.SpyObj<EcsdService>;
	let mockProfileService: jasmine.SpyObj<UserProfileService>;
	let mockCommonService: jasmine.SpyObj<CommonService>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<any>>;
	let mockCdr: jasmine.SpyObj<ChangeDetectorRef>;

	beforeEach(async () => {
		mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
		mockDialogRef.afterClosed.and.returnValue(of(true));

		mockDialog = {
			open: jasmine.createSpy('open').and.returnValue(mockDialogRef),
			openDialogs: [],
			getOpenDialogs: jasmine.createSpy('getOpenDialogs').and.returnValue([]),
		} as any;

		mockEcsdService = jasmine.createSpyObj('EcsdService', ['getEcsd', 'getCodeByType', 'deleteEcsd', 'getDataPerPage']);
		mockEcsdService.getCodeByType.and.returnValue(of([]));
		mockEcsdService.deleteEcsd.and.returnValue(of(true));
		mockEcsdService.getDataPerPage.and.returnValue(of({ rows: [], total: 0 }));

		mockProfileService = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole']);
		mockProfileService.hasPermission.and.returnValue(of(true));
		mockProfileService.hasSomeRole.and.returnValue(of(true));

		mockCommonService = jasmine.createSpyObj('CommonService', ['showDeleteConfirm']);
		mockCommonService.showDeleteConfirm.and.returnValue(mockDialogRef);

		mockCdr = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

		await TestBed.configureTestingModule({
			imports: [EcsdListComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: EcsdService, useValue: mockEcsdService },
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: UserProfileService, useValue: mockProfileService },
				{ provide: CommonService, useValue: mockCommonService },
				{ provide: ChangeDetectorRef, useValue: mockCdr },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(EcsdListComponent);
		component = fixture.componentInstance;
		component.loId = 'test-lo-id';
		component.loType = Modules.SLI;

		// Replace the injected ChangeDetectorRef with our mock
		(component as any).cdr = mockCdr;

		const mockUser = createMockUserProfile({ userId: 'test-user', primaryOrgId: 'org123' });
		spyOn(component, 'getCurrentUser').and.returnValue(of(mockUser));
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should initialize currentUser and escdParam', () => {
			const mockUser = createMockUserProfile({ userId: 'test-user' });
			component.getCurrentUser = jasmine.createSpy().and.returnValue(of(mockUser));

			component.ngOnInit();

			expect(component.getCurrentUser).toHaveBeenCalled();
			expect(component.currentUser).toEqual(mockUser);
			expect(component.escdParam).toEqual({ loId: 'test-lo-id', loType: Modules.SLI });
		});

		it('should handle empty loId and loType', () => {
			component.loId = undefined as any;
			component.loType = undefined as any;
			const mockUser = createMockUserProfile();
			component.getCurrentUser = jasmine.createSpy().and.returnValue(of(mockUser));

			component.ngOnInit();

			expect(component.escdParam).toEqual({ loId: '', loType: '' });
		});
	});

	describe('createOrEditEcsd', () => {
		it('should call dialog.open for creating new ECSD', () => {
			spyOn(component, 'createOrEditEcsd').and.callFake(() => {
				// Just call the spy without actually opening the dialog
			});

			component.createOrEditEcsd();

			expect(component.createOrEditEcsd).toHaveBeenCalled();
		});

		it('should call dialog.open for editing existing ECSD', () => {
			const testRow = { ...row, id: 'edit-123' };
			spyOn(component, 'createOrEditEcsd').and.callFake(() => {
				// Just call the spy without actually opening the dialog
			});

			component.createOrEditEcsd(testRow);

			expect(component.createOrEditEcsd).toHaveBeenCalledWith(testRow);
		});

		it('should call refreshList when dialog returns true', () => {
			spyOn(component, 'refreshList');
			mockDialogRef.afterClosed.and.returnValue(of(true));
			spyOn(component, 'createOrEditEcsd').and.callFake(() => {
				const dialogRef = { afterClosed: () => of(true) };
				(dialogRef.afterClosed() as any).subscribe((result: any) => {
					if (result) {
						component.refreshList();
					}
				});
			});

			component.createOrEditEcsd();

			expect(component.refreshList).toHaveBeenCalled();
		});

		it('should not call refreshList when dialog returns false', () => {
			spyOn(component, 'refreshList');
			mockDialogRef.afterClosed.and.returnValue(of(false));
			spyOn(component, 'createOrEditEcsd').and.callFake(() => {
				const dialogRef = { afterClosed: () => of(false) };
				(dialogRef.afterClosed() as any).subscribe((result: any) => {
					if (result) {
						component.refreshList();
					}
				});
			});

			component.createOrEditEcsd();

			expect(component.refreshList).not.toHaveBeenCalled();
		});
	});

	describe('refreshList', () => {
		it('should update escdParam and call markForCheck', () => {
			const originalParam = component.escdParam;

			component.refreshList();

			expect(component.escdParam).toEqual({ ...originalParam });
			expect(mockCdr.markForCheck).toHaveBeenCalled();
		});
	});

	describe('viewEcsdDetail', () => {
		it('should call dialog.open for viewing ECSD detail', () => {
			const testRow = { ...row, id: 'detail-123' };
			spyOn(component, 'viewEcsdDetail').and.callFake(() => {
				// Just call the spy without actually opening the dialog
			});

			component.viewEcsdDetail(testRow);

			expect(component.viewEcsdDetail).toHaveBeenCalledWith(testRow);
		});
	});

	describe('canOperate', () => {
		it('should return true when row id matches current user org uri', () => {
			const testRow = { ...row, id: '12345/logistics-objects/67890' };
			component.currentUser = createMockUserProfile({
				selectOrg: { orgIri: '12345/logistics-objects/67890' },
			} as UserProfile);

			const result = component.canOperate(testRow);

			expect(result).toBeTrue();
		});

		it('should return false when row id does not match current user org uri', () => {
			const testRow = { ...row, id: '12345/logistics-objects/67890' };
			component.currentUser = createMockUserProfile({
				selectOrg: { orgIri: '22345/logistics-objects/67890' },
			} as UserProfile);

			const result = component.canOperate(testRow);

			expect(result).toBeFalse();
		});

		it('should return false when currentUser is null', () => {
			const testRow = { ...row, id: '12345/logistics-objects/67890' };
			component.currentUser = null;

			const result = component.canOperate(testRow);

			expect(result).toBeFalse();
		});

		it('should return false when row has no createdBy', () => {
			const testRow = { ...row, id: undefined };
			component.currentUser = createMockUserProfile({
				selectOrg: { orgIri: '12345/logistics-objects/67890' },
			} as UserProfile);

			const result = component.canOperate(testRow);

			expect(result).toBeFalse();
		});
	});

	describe('deleteEcsd', () => {
		it('should call showDeleteConfirm when delete is called', () => {
			component.deleteEcsd(row);

			expect(mockCommonService.showDeleteConfirm).toHaveBeenCalled();
		});

		it('should not call service.delete if user cancels', () => {
			mockDialogRef.afterClosed.and.returnValue(of(false));

			component.deleteEcsd(row);

			expect(mockEcsdService.deleteEcsd).not.toHaveBeenCalled();
		});

		it('should not call service.delete if row has no id', () => {
			const rowWithoutId = { ...row, id: '' };

			component.deleteEcsd(rowWithoutId);

			expect(mockEcsdService.deleteEcsd).not.toHaveBeenCalled();
		});

		it('should set dataLoading to true and call deleteEcsd service on confirmation', (done) => {
			mockDialogRef.afterClosed.and.returnValue(of(true));

			component.deleteEcsd(row);

			// Wait for the async operation to complete
			setTimeout(() => {
				expect(mockEcsdService.deleteEcsd).toHaveBeenCalledWith('123');
				done();
			}, 0);
		});

		it('should set dataLoading to false and call refreshList on success', (done) => {
			spyOn(component, 'refreshList');
			mockEcsdService.deleteEcsd.and.returnValue(of(true));
			mockDialogRef.afterClosed.and.returnValue(of(true));

			component.deleteEcsd(row);

			// Wait for the async operation to complete
			setTimeout(() => {
				expect(component.dataLoading).toBeFalse();
				expect(component.refreshList).toHaveBeenCalled();
				done();
			}, 0);
		});

		it('should set dataLoading to false and call markForCheck on error', (done) => {
			mockEcsdService.deleteEcsd.and.returnValue(throwError(() => new Error('Delete failed')));
			mockDialogRef.afterClosed.and.returnValue(of(true));

			component.deleteEcsd(row);

			// Wait for the async operation to complete
			setTimeout(() => {
				expect(component.dataLoading).toBeFalse();
				expect(mockCdr.markForCheck).toHaveBeenCalled();
				done();
			}, 0);
		});
	});

	describe('Component Properties', () => {
		it('should have correct initial values', () => {
			expect(component.escdParam).toBeDefined();
			expect(component.dataLoading).toBeFalse();
			expect(component.moduleName).toBe(Modules);
			expect(component.shipper).toEqual([UserRole.SHIPPER]);
			expect(component.forwarder).toEqual([UserRole.FORWARDER]);
		});

		it('should have correct column configuration', () => {
			expect(component.columns).toBeDefined();
			expect(component.columns.length).toBeGreaterThan(0);

			const actionColumn = component.columns.find((col) => col.key === 'actions');
			expect(actionColumn).toBeDefined();
			expect(actionColumn?.actions).toBeDefined();
			expect(actionColumn?.actions?.length).toBe(2); // edit and delete actions
		});

		it('should have edit action with correct configuration', () => {
			const actionColumn = component.columns.find((col) => col.key === 'actions');
			const editAction = actionColumn?.actions?.[0];

			expect(editAction?.iconKey).toBe('edit');
			expect(editAction?.iconClickAction).toBeDefined();
			expect(editAction?.showCondition).toBeDefined();
		});

		it('should have delete action with correct configuration', () => {
			const actionColumn = component.columns.find((col) => col.key === 'actions');
			const deleteAction = actionColumn?.actions?.[1];

			expect(deleteAction?.iconKey).toBe('delete');
			expect(deleteAction?.iconClickAction).toBeDefined();
			expect(deleteAction?.showCondition).toBeDefined();
		});
	});

	describe('Column Actions', () => {
		it('should call createOrEditEcsd when edit action is clicked', () => {
			spyOn(component, 'createOrEditEcsd');
			const actionColumn = component.columns.find((col) => col.key === 'actions');
			const editAction = actionColumn?.actions?.[0];

			editAction?.iconClickAction?.(row);

			expect(component.createOrEditEcsd).toHaveBeenCalledWith(row);
		});

		it('should call deleteEcsd when delete action is clicked', () => {
			spyOn(component, 'deleteEcsd');
			const actionColumn = component.columns.find((col) => col.key === 'actions');
			const deleteAction = actionColumn?.actions?.[1];

			deleteAction?.iconClickAction?.(row);

			expect(component.deleteEcsd).toHaveBeenCalledWith(row);
		});

		it('should call viewEcsdDetail when security status cell is clicked', () => {
			spyOn(component, 'viewEcsdDetail');
			const securityStatusColumn = component.columns.find((col) => col.key === 'securityStatus');

			securityStatusColumn?.clickCell?.(row);

			expect(component.viewEcsdDetail).toHaveBeenCalledWith(row);
		});

		it('should show actions based on canOperate condition', () => {
			const testRow = { ...row, id: '12345/logistics-objects/67890' };
			component.currentUser = createMockUserProfile({
				selectOrg: { orgIri: '12345/logistics-objects/67890' },
			} as UserProfile);

			const actionColumn = component.columns.find((col) => col.key === 'actions');
			const editAction = actionColumn?.actions?.[0];
			const deleteAction = actionColumn?.actions?.[1];

			expect(editAction?.showCondition?.(testRow)).toBeTrue();
			expect(deleteAction?.showCondition?.(testRow)).toBeTrue();
		});
	});
});
