import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
// eslint-disable-next-line @typescript-eslint/naming-convention
import PartnerAccessComponent from '../../components/partner-access/partner-access.component';
import { DelegationRequestListComponent } from '@shared/components/delegation-request-list/delegation-request-list.component';

@Component({
	selector: 'orll-partner-list',
	templateUrl: './partner-list.component.html',
	styleUrl: './partner-list.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatTabsModule, TranslateModule, PartnerAccessComponent, DelegationRequestListComponent],
})
export default class PartnerListComponent {
	selectedTabIndex = 0;
	currentTabLabel = '';

	constructor(private readonly translateService: TranslateService) {}

	@ViewChild('delegationRequestList') delegationRequestList!: DelegationRequestListComponent;

	onTabChanged($event: MatTabChangeEvent) {
		this.currentTabLabel = $event.tab.textLabel;
		this.selectedTabIndex = $event.index;
		if (this.currentTabLabel === this.translateService.instant('partner.tab.request')) {
			this.delegationRequestList.refreshData();
		}
	}
}
