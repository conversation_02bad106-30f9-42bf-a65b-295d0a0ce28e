import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogTitle, MatDialogModule, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CommonModule } from '@angular/common';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatSortModule } from '@angular/material/sort';
import { HistoryDialogData, StatusHistory } from '../../models/mawb-event.model';
import { MawbStatusService } from '../../services/mawb-status.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';

@Component({
	selector: 'orll-status-history',
	imports: [
		MatDialogTitle,
		MatDialogContent,
		MatTableModule,
		CommonModule,
		CommonModule,
		MatIcon,
		MatSortModule,
		TranslateModule,
		MatDialogModule,
		MatIconModule,
		SpinnerComponent,
		IataDateFormatPipe,
	],
	templateUrl: './status-history.component.html',
	styleUrl: './status-history.component.scss',
})
export class StatusHistoryComponent implements OnInit {
	dataLoading = false;
	readonly displayedColumns: string[] = ['event', 'updateTime', 'updateBy'];
	dataSource = new MatTableDataSource<StatusHistory>();

	constructor(
		private readonly statusService: MawbStatusService,
		private readonly translateService: TranslateService,
		private readonly dialog: MatDialog,
		private readonly dialogRef: MatDialogRef<StatusHistoryComponent>,
		@Inject(MAT_DIALOG_DATA) public data: HistoryDialogData
	) {}

	ngOnInit(): void {
		this.initData();
	}

	private initData(): void {
		this.dataLoading = true;
		this.statusService.getStatusHistoryList(this.data.loId, this.data.type).subscribe({
			next: (items: StatusHistory[]) => {
				this.dataSource.data = items;
				this.dataLoading = false;
			},
			error: (error) => {
				this.dataLoading = false;
				if (error.status === 403) {
					const dialogRef = this.dialog.open(ConfirmDialogComponent, {
						width: '300px',
						data: { content: this.translateService.instant('mawb.event.no.permission') },
					});
					dialogRef.afterClosed().subscribe(() => {
						this.dialogRef.close();
					});
				}
			},
		});
	}
}
