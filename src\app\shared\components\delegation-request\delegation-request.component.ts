import { Directive, inject } from '@angular/core';
import { ResponseObj } from '@shared/models/response.model';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { DelegationRequestDialogComponent } from '@shared/components/delegation-request-dialog/delegation-request-dialog.component';
import { map, Observable, of, switchMap, take } from 'rxjs';
import { RolesAwareComponent } from '../roles-aware/roles-aware.component';

@Directive()
export abstract class DelegationRequestComponent extends RolesAwareComponent {
	public readonly dialog = inject(MatDialog);

	delegationRequest(error: ResponseObj<string>, documentId: string): Observable<boolean> {
		return this.dialog
			.open(ConfirmDialogComponent, {
				width: '500px',
				data: {
					title: 'common.dialog.alert.title',
					content: error.msg,
					icon: 'send',
					ok: 'common.dialog.request.delegation',
				},
			})
			.afterClosed()
			.pipe(
				take(1),
				switchMap((confirmResult) => {
					if (!confirmResult) return of(false);

					return this.dialog
						.open(DelegationRequestDialogComponent, {
							width: '60vw',
							autoFocus: false,
							data: {
								icon: 'send',
								ok: 'common.dialog.delegation.request.send',
								loId: error.data,
								documentId,
							},
						})
						.afterClosed()
						.pipe(
							take(1),
							map((dialogResult) => !!dialogResult)
						);
				})
			);
	}
}
