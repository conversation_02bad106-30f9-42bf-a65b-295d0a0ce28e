import { TestBed } from '@angular/core/testing';
import { PartnerAccessRequestService } from './partner-access-request.service';
import { environment } from '@environments/environment';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { PartnerListObject } from '../models/partner-access.model';
import { TranslateModule } from '@ngx-translate/core';
import { ResponseObj } from '@shared/models/response.model';

const baseUrl = environment.baseApi;

describe('PartnerAccessRequestService', () => {
	let service: PartnerAccessRequestService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			imports: [TranslateModule.forRoot()],
			providers: [
				PartnerAccessRequestService,
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		});

		service = TestBed.inject(PartnerAccessRequestService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify(); // Verify that no outstanding requests remain
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should update  parterners', () => {
		const mockPartners: PartnerListObject[] = [
			{
				id: '111',
				businessData: 'sli',
				orgId: '1',
				orgName: 'Org1',
				getLogisticsObject: '1',
				patchLogisticsObject: '1',
				postLogisticsEvent: '1',
				getLogisticsEvent: '1',
				createTime: '',
			},
			{
				id: '222',
				businessData: 'hawb',
				orgId: '2',
				orgName: 'Org2',
				getLogisticsObject: '1',
				patchLogisticsObject: '1',
				postLogisticsEvent: '0',
				getLogisticsEvent: '1',
				createTime: '',
			},
		];

		const mockResponse: ResponseObj<string> = {
			data: 'Success',
			code: 200,
			msg: 'Success',
		};

		service.updatePartners(mockPartners).subscribe((orgs) => {
			expect(orgs).toEqual('Success');
		});

		const req = httpMock.expectOne(`${baseUrl}/partner-access/update`);
		expect(req.request.method).toBe('PUT');
		req.flush(mockResponse);
	});

	it('should add  parterners', () => {
		const mockPartner: PartnerListObject = {
			id: '111',
			businessData: 'sli',
			orgId: '1',
			orgName: 'Org1',
			getLogisticsObject: '1',
			patchLogisticsObject: '1',
			postLogisticsEvent: '1',
			getLogisticsEvent: '1',
			createTime: '',
		};

		const mockResponse: ResponseObj<string> = {
			data: 'Success',
			code: 200,
			msg: 'Success',
		};

		service.addPartner(mockPartner).subscribe((orgs) => {
			expect(orgs).toEqual('Success');
		});

		const req = httpMock.expectOne(`${baseUrl}/partner-access/add`);
		expect(req.request.method).toBe('POST');
		req.flush(mockResponse);
	});
});
