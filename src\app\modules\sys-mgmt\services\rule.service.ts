import { Injectable } from '@angular/core';
import { ApiService } from '@shared/services/api.service';
import { GenericTableService } from '@shared/services/table/orll-table.interface';
import { RuleDetailObj, RuleListObj } from '../models/rule.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

@Injectable({
	providedIn: 'root',
})
export class RuleService extends ApiService implements GenericTableService<RuleListObj> {
	constructor(http: HttpClient) {
		super(http);
	}

	getDataPerPage(param: any): Observable<PaginationResponse<RuleListObj>> {
		return super.getData('role/role/list', param);
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	loadAllData(param: any): Observable<RuleListObj[]> {
		return this.getData('role/role/list');
	}

	deleteRule(row: RuleListObj): Observable<boolean> {
		return this.deleteData('role/role', { ids: [row.id] });
	}

	saveRule(row: RuleDetailObj): Observable<string> {
		if (row.id) {
			return this.updateData('role/role', row);
		}
		return this.postData('role/role', row);
	}

	getRule(row: RuleListObj): Observable<RuleDetailObj> {
		return this.getData('role/role', { id: row.id });
	}
}
