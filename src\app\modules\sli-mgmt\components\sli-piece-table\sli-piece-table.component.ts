import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MatPaginatorModule, MatPaginatorIntl, PageEvent } from '@angular/material/paginator';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { PieceList } from '../../models/piece/piece-list.model';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSortModule, Sort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { MatDialog } from '@angular/material/dialog';
import { AddPieceDialogComponent } from '../add-piece-dialog/add-piece-dialog.component';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { PieceType } from '../../models/piece/piece-type.model';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { PieceConsolidateDialogComponent } from '../piece-consolidate-dialog/piece-consolidate-dialog.component';
import { StatusHistoryComponent } from 'src/app/modules/mawb-mgmt/components/status-history/status-history.component';
import { Modules, UserPermission } from '@shared/models/user-role.model';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { AsyncPipe } from '@angular/common';
import { OrllCopyDirective } from '@shared/directive/orll-copy.directive';

@Component({
	selector: 'orll-sli-piece-table',
	templateUrl: './sli-piece-table.component.html',
	styleUrl: './sli-piece-table.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatTableModule,
		MatSortModule,
		MatButtonModule,
		MatMenuModule,
		MatIconModule,
		MatCheckboxModule,
		MatPaginatorModule,
		TranslateModule,
		SpinnerComponent,
		AsyncPipe,
		OrllCopyDirective,
	],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class SliPieceTableComponent extends RolesAwareComponent implements OnChanges, OnInit {
	@Input() sliNumber = '';
	@Input() records: PieceList[] = [];
	@Input() totalSlac = 0;
	@Input() totalQuantity = 0;
	@Input() totalRecords = 0;
	@Input() latestStatus = '';
	@Input() pageParams!: PaginationRequest;
	@Input() hawbId = '';
	@Input() hawbNumber = '';
	@Input() enableInfiniteScroll = false;
	@Input() hasMoreData = true;
	@Input() sliTemplateNum = '';
	@Input() isForHawb = false;
	@Input() disableUpdate = false;

	@Output() saveRequest = new EventEmitter<{ pieceType: string; pieceId?: string }>();
	@Output() sortChange = new EventEmitter<Sort>();
	@Output() pagination = new EventEmitter<PageEvent & { sortField?: string; sortDirection?: string }>();
	@Output() refresh = new EventEmitter<void>();
	@Output() loadMoreData = new EventEmitter<void>();

	currentSort: Sort = { active: '', direction: '' };

	readonly sliModule = Modules.SLI;
	readonly createPermission = UserPermission.CREATE;

	constructor(
		private readonly dialog: MatDialog,
		private readonly translate: TranslateService,
		private readonly sliCreateRequestService: SliCreateRequestService
	) {
		super();
	}

	displayedColumns: string[] = [
		'select',
		'productDescription',
		'packagingType',
		'grossWeight',
		'dimensions',
		'pieceQuantity',
		'slac',
		'actions',
	];
	readonly tablePageSizes: number[] = [10, 50, 100];

	dataSource = new MatTableDataSource<PieceList>(this.records || []);
	selection = new SelectionModel<PieceList>(true, [], false, (a: PieceList, b: PieceList) => a.pieceId === b.pieceId);
	dataLoading = false;

	ngOnInit(): void {
		if (this.hawbId || this.isForHawb) {
			this.displayedColumns.shift();
			this.displayedColumns.pop();
			this.displayedColumns.push('latestStatus');
			this.displayedColumns.push('copy');
		} else {
			this.hasPermission(this.createPermission, this.sliModule)
				.pipe(takeUntilDestroyed(this.destroyRef))
				.subscribe((res) => {
					if (!res) {
						this.displayedColumns.shift();
						this.displayedColumns.pop();
						this.displayedColumns.push('copy');
					}
				});
		}
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.dataSource.data = this.records;
		}
		if (changes['totalQuantity'] || changes['totalSlac'] || changes['totalRecords']) {
			this.selection.clear();
		}
		if (changes['disableUpdate'] && this.disableUpdate && this.displayedColumns.includes('actions')) {
			this.displayedColumns.shift();
			this.displayedColumns = this.displayedColumns.filter((item) => item !== 'actions');
		}
	}

	onSortChange(sort: Sort): void {
		this.currentSort = sort;
		this.sortChange.emit(sort);
		this.emitPaginationWithSort();
	}

	private emitPaginationWithSort(event?: PageEvent) {
		const pageEvent = event || {
			pageIndex: this.pageParams.pageNum - 1,
			pageSize: this.pageParams.pageSize,
			length: this.totalRecords,
		};

		this.pagination.emit({
			...pageEvent,
			sortField: this.currentSort.active,
			sortDirection: this.currentSort.direction,
		});
	}

	isAllSelected() {
		const numSelected = this.selection.selected.length;
		const numRows = this.dataSource.data.length;
		return numRows > 0 ? numSelected === numRows : false;
	}

	toggleAllRows() {
		if (this.isAllSelected()) {
			this.selection.clear();
			return;
		}
		this.selection.select(...this.dataSource.data);
	}

	trackByPieceId(record: PieceList): string {
		return record.pieceId;
	}

	onTableScroll(event: Event): void {
		const container = event.target as HTMLDivElement;
		const scrollTop = container.scrollTop;
		const scrollHeight = container.scrollHeight;
		const clientHeight = container.clientHeight;

		const isAtBottom = scrollHeight - (scrollTop + clientHeight) === 0;

		if (this.enableInfiniteScroll && isAtBottom && !this.dataLoading && this.hasMoreData) {
			this.loadMoreData.emit();
		}
	}

	addPiece(): void {
		const dialogRef = this.dialog.open(AddPieceDialogComponent, {
			width: '400px',
			data: {},
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (result) this.saveRequest.emit({ pieceType: result });
		});
	}

	editPiece(event: Event, record: PieceList): void {
		event.stopPropagation();
		const pieceId = record.pieceId;
		let pieceType = '';
		switch (record.type) {
			case PieceType.GENERAL:
				pieceType = 'general';
				break;

			case PieceType.DANGEROUS_GOODS:
				pieceType = 'dg';
				break;

			case PieceType.LIVE_ANIMALS:
				pieceType = 'la';
				break;

			default:
				break;
		}
		this.saveRequest.emit({ pieceType, pieceId });
	}

	delPiece(event: Event, records: PieceList[]): void {
		event.stopPropagation();
		const pieceIds = records.map((record) => record.pieceId);
		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translate.instant('common.dialog.delete.content'),
			},
		});

		dialogRef.afterClosed().subscribe((confirmed) => {
			if (confirmed) {
				this.dataLoading = true;
				this.sliCreateRequestService
					.deletePiece(pieceIds, this.sliNumber)
					.pipe(takeUntilDestroyed(this.destroyRef))
					.subscribe(() => {
						this.dataLoading = false;
						this.refresh.emit();
					});
			}
		});
	}

	consolidatePiece() {
		const dialogRef = this.dialog.open(PieceConsolidateDialogComponent, {
			width: '60vw',
			autoFocus: false,
			data: {
				pieces: this.selection.selected,
				sliNumber: this.sliNumber || this.sliTemplateNum,
				piceId: '',
			},
		});
		dialogRef.afterClosed().subscribe((res: { success: boolean }) => {
			//handle backend latency
			if (res?.success) {
				this.refresh.emit();
			}
		});
	}

	toggleNode(node: PieceList) {
		node.expanded = !node.expanded;
		if (node.expanded && node.containedPieces && node.containedPieces.length > 0) {
			this.expandNode(node);
		} else {
			this.collapseNode(node);
		}
	}

	expandNode(node: PieceList) {
		if (!node.containedPieces) return;
		const index = this.dataSource.data.indexOf(node);
		node.containedPieces.forEach((item) => (item.level = 1));
		this.dataSource.data.splice(index + 1, 0, ...node.containedPieces);
		this.dataSource.data = [...this.dataSource.data];
	}

	collapseNode(node: PieceList) {
		if (!node.containedPieces) return;

		this.dataSource.data = this.dataSource.data.filter(
			(item) => !node.containedPieces?.some((subPiece) => subPiece.pieceId === item.pieceId)
		);
	}

	unConsolidatePiece(node: PieceList) {
		const param = {
			pieceId: node.pieceId,
			containedPieceIds: node.containedPieces ? node.containedPieces.map((item) => item.pieceId) : [],
			sliId: this.sliNumber || this.sliTemplateNum,
		};
		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translate.instant('common.dialog.unConsolidate.conent'),
			},
		});

		dialogRef.afterClosed().subscribe((confirmed) => {
			if (confirmed) {
				this.dataLoading = true;
				this.sliCreateRequestService
					.unConsolidatePiece(param)
					.pipe(takeUntilDestroyed(this.destroyRef))
					.subscribe(() => {
						this.dataLoading = false;
						this.refresh.emit();
					});
			}
		});
	}

	editConsolidatePiece(event: Event, node: PieceList) {
		event.preventDefault();
		const dialogRef = this.dialog.open(PieceConsolidateDialogComponent, {
			width: '60vw',
			autoFocus: false,
			data: {
				pieces: this.selection.selected,
				sliNumber: this.sliNumber,
				pieceId: node.pieceId,
			},
		});
		dialogRef.afterClosed().subscribe((res: { success: boolean }) => {
			if (res?.success) {
				this.refresh.emit();
			}
		});
	}

	disableConsolidate() {
		if (this.dataSource.data.length === 0 || !this.selection.hasValue()) {
			return true;
		}
		if (this.selection.selected.some((item) => item.containedPieces && item.containedPieces.length > 0)) {
			return true;
		}
		return false;
	}

	openHistory(loId: string, type: string) {
		this.dialog.open(StatusHistoryComponent, {
			width: '60vw',
			autoFocus: false,
			data: {
				loId,
				type,
			},
		});
	}
}
