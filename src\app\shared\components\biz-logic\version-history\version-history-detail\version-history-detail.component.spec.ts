import { ComponentFixture, TestBed } from '@angular/core/testing';
import { OperationObj, RequestStatusChangeAction, VersionDialogData } from '@shared/models/biz-logic/version-history.model';
import { VersionHistoryDetailComponent } from './version-history-detail.component';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatTableModule } from '@angular/material/table';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { VersionHistoryService } from '@shared/services/biz-logic/verion-history/version-history.service';
import { of } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';

// Mock Service
class MockService {
	getVersionHistoryDetail = jasmine.createSpy('getVersionHistoryDetail').and.returnValue(
		of({
			id: 1,
			hasOperation: [{ type: 'name', property: 'title', oldValue: 'old', newValue: 'new' }],
		})
	);

	updateRequestStatus = jasmine.createSpy('updateRequestStatus').and.returnValue(of(true));
}

// Mock MatDialogRef
const mockDialogRef = {
	close: jasmine.createSpy('close'),
};

const mockDialogData: VersionDialogData = {
	title: 'Change Request',
	actionRequestUri: 'https://api.example.com/objects/123/history/456',
};

describe('VersionHistoryDetailComponent', () => {
	let component: VersionHistoryDetailComponent;
	let fixture: ComponentFixture<VersionHistoryDetailComponent>;
	let service: MockService;
	let dialogRef: jasmine.SpyObj<MatDialogRef<VersionHistoryDetailComponent>>;

	beforeEach(async () => {
		TestBed.configureTestingModule({
			imports: [VersionHistoryDetailComponent, MatDialogModule, MatTableModule, NoopAnimationsModule, TranslateModule.forRoot()],

			providers: [
				{ provide: VersionHistoryService, useClass: MockService },
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: mockDialogData },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(VersionHistoryDetailComponent);
		component = fixture.componentInstance;
		service = TestBed.inject(VersionHistoryService) as unknown as MockService;
		dialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<VersionHistoryDetailComponent>>;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should load detail data and set dataSource', () => {
			spyOn(component, 'ngOnInit');
			fixture.detectChanges();

			expect(service.getVersionHistoryDetail).toHaveBeenCalledWith(mockDialogData.actionRequestUri);
			expect(component.detail).toEqual(
				jasmine.objectContaining({
					hasOperation: jasmine.any(Array),
				})
			);
			expect(component.dataSource.data).toEqual(component.detail!.hasOperation);
		});

		it('should handle empty data gracefully', () => {
			service.getVersionHistoryDetail.and.returnValue(of({ hasOperation: [] }));
			fixture.detectChanges();

			expect(component.dataSource.data).toEqual([]);
		});
	});

	describe('onApprove', () => {
		it('should call approveRequest and close dialog', () => {
			component.data = mockDialogData;
			component.updateRequestStatus(RequestStatusChangeAction.REQUEST_ACCEPTED);

			expect(service.updateRequestStatus).toHaveBeenCalledWith(
				mockDialogData.actionRequestUri,
				RequestStatusChangeAction.REQUEST_ACCEPTED
			);

			const callArgs = service.updateRequestStatus.calls.mostRecent().args;
			expect(callArgs[0]).toBe(mockDialogData.actionRequestUri);

			expect(component.dataLoading).toBeFalse();
			expect(dialogRef.close).toHaveBeenCalled();
		});

		it('should not do anything if data is missing', () => {
			component.data = null!;
			component.updateRequestStatus(RequestStatusChangeAction.REQUEST_ACCEPTED);
			expect(service.updateRequestStatus).not.toHaveBeenCalled();
		});
	});

	describe('onRevoke', () => {
		it('should call revokeRequest and close dialog', () => {
			component.data = mockDialogData;
			component.updateRequestStatus(RequestStatusChangeAction.REQUEST_REVOKED);

			expect(service.updateRequestStatus).toHaveBeenCalledWith(
				mockDialogData.actionRequestUri,
				RequestStatusChangeAction.REQUEST_REVOKED
			);
			expect(component.dataLoading).toBeFalse();
			expect(dialogRef.close).toHaveBeenCalled();
		});
	});

	describe('onReject', () => {
		it('should call rejectRequest and close dialog', () => {
			component.data = mockDialogData;
			component.updateRequestStatus(RequestStatusChangeAction.REQUEST_REJECTED);

			expect(service.updateRequestStatus).toHaveBeenCalledWith(
				mockDialogData.actionRequestUri,
				RequestStatusChangeAction.REQUEST_REJECTED
			);
			expect(component.dataLoading).toBeFalse();
			expect(dialogRef.close).toHaveBeenCalled();
		});
	});

	describe('trackById', () => {
		it('should return row id for trackBy', () => {
			const row: OperationObj = { id: '111', loType: 'piece', oldValue: '111', newValue: '222', property: 'aaa' };
			expect(component.trackById(0, row)).toBe('111');
		});
	});
});
