import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { Router } from '@angular/router';
import { ARRIVAL_LOCATION, BookingInfo, BookingOptionDetail, DEPARTURE_LOCATION, PRICE_LIST, TRANS_LIST } from '../../models/booking.model';
import { FormArray, FormControl, FormGroup, FormsModule, Validators, ReactiveFormsModule } from '@angular/forms';
import { CodeName } from '@shared/models/code-name.model';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { MatInput, MatInputModule } from '@angular/material/input';
import { DateTimePickerComponent } from '@shared/components/date-time-picker/date-time-picker.component';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { CommonService } from '@shared/services/common.service';
import { REGX_NUMBER_2_DECIMAL } from '@shared/models/constant';
import { formatDateTime } from '@shared/utils/common.utils';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { CodeType } from '@shared/models/search-type.model';
import { BookingOptionRequestFormComponent } from '../booking-option-request-form/booking-option-request-form.component';

@Component({
	selector: 'orll-booking-create',
	imports: [
		FormsModule,
		MatFormFieldModule,
		TranslateModule,
		MatInput,
		DateTimePickerComponent,
		ReactiveFormsModule,
		MatInputModule,
		MatSelectModule,
		MatIconModule,
		MatButtonModule,
		MatDividerModule,
		SpinnerComponent,
		BookingOptionRequestFormComponent,
	],
	templateUrl: './booking-create.component.html',
	styleUrl: './booking-create.component.scss',
})
export default class BookingCreateComponent extends RolesAwareComponent implements OnInit, AfterViewInit {
	optionForm = new FormGroup({
		options: new FormArray([]),
	});

	locationList: CodeName[] = [];
	chargePaymentTypes: CodeName[] = [];
	rateClassCodes: CodeName[] = [];
	chargeTypes: CodeName[] = [];
	entitlements: CodeName[] = [];
	bookingInfo: any;

	dataLoading = false;

	validators = Validators;

	@ViewChild(BookingOptionRequestFormComponent)
	bookingOptionRequestFormComponent!: BookingOptionRequestFormComponent;

	constructor(
		private readonly bookingService: BookingOptionRequestService,
		private readonly router: Router,
		private readonly commonService: CommonService
	) {
		super();

		// get navigation state
		const navigation = this.router.getCurrentNavigation();
		if (navigation?.extras.state) {
			const state = navigation.extras.state;
			this.bookingInfo = state?.['bookingInfo'] ?? null;
		}
	}

	ngAfterViewInit(): void {
		if (this.bookingInfo) {
			this.patchValueFromMawb();
		}
	}

	ngOnInit(): void {
		this.addOptionGroup(false);
		this.bookingService
			.getAirports()
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((res) => {
				this.locationList = res;
			});
		for (const item of [CodeType.CHARGE_TYPE, CodeType.ENTITILEMENT, CodeType.PAYMENT_TYPE, CodeType.RATE_CLASS_CODE]) {
			this.bookingService.getCodeByType(item).subscribe((res) => {
				switch (item) {
					case CodeType.CHARGE_TYPE:
						this.chargeTypes = res;
						break;

					case CodeType.RATE_CLASS_CODE:
						this.rateClassCodes = res;
						break;
					case CodeType.PAYMENT_TYPE:
						this.chargePaymentTypes = res;
						break;
					case CodeType.ENTITILEMENT:
						this.entitlements = res;
						break;
					default:
				}
			});
		}
	}

	private patchValueFromMawb() {
		this.bookingOptionRequestFormComponent.bookingOptionReqForm.patchValue(this.bookingInfo);
		const priceListForms = this.getPriceList(0);
		const transListForms = this.getTransportLegsList(0);
		priceListForms.controls[0].patchValue({
			rateClassCode: this.bookingInfo.rateClassCode,
			subTotal: this.bookingInfo.rateCharge,
			chargePaymentType: this.bookingInfo.weightValuationIndicator.toUpperCase(),
		});
		transListForms.controls[0].patchValue({
			departureLocation: this.bookingInfo.departureLocation,
			arrivalLocation: this.bookingInfo.arrivalLocation,
			departureDate: this.bookingInfo.requestedDate,
			transportIdentifier: this.bookingInfo.requestedFlight,
			airlineCode: this.bookingInfo.airlineCode,
		});
	}

	createOptionGroup(disable: boolean): FormGroup {
		return new FormGroup({
			priceList: new FormArray([this.createPieceGroup(disable)]),
			transportLegsList: new FormArray([this.createTransGroup(disable)]),
			grandTotal: new FormControl<number>(0),
			disableOption: new FormControl<boolean>(false),
			showOption: new FormControl<boolean>(true),
		});
	}

	createPieceGroup(disable: boolean) {
		return new FormGroup({
			chargeType: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			rateClassCode: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			subTotal: new FormControl<number | null>({ value: null, disabled: disable }, [
				Validators.required,
				Validators.pattern(REGX_NUMBER_2_DECIMAL),
			]),
			chargePaymentType: new FormControl<string>({ value: 'PREPAID', disabled: disable }, [Validators.required]),
			entitlement: new FormControl<string>({ value: 'A', disabled: disable }),
		});
	}

	createTransGroup(disable: boolean) {
		return new FormGroup({
			departureLocation: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			arrivalLocation: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			airlineCode: new FormControl<string>({ value: '', disabled: disable }),
			transportIdentifier: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			departureDate: new FormControl<string>({ value: '', disabled: disable }, [Validators.required]),
			arrivalDate: new FormControl<string>({ value: '', disabled: disable }),
		});
	}

	addOptionGroup(disable?: boolean) {
		const newOption = this.createOptionGroup(disable ?? false);
		(this.optionForm.get('options') as FormArray).push(newOption);
	}

	removeItemFromList(itemList: FormArray, index: number): void {
		itemList.removeAt(index);
	}

	getPriceList(optionIndex: number): FormArray {
		return (this.optionForm.controls.options.at(optionIndex) as FormGroup).get(PRICE_LIST) as FormArray;
	}

	getTransportLegsList(optionIndex: number): FormArray {
		return (this.optionForm.controls.options.at(optionIndex) as FormGroup).get(TRANS_LIST) as FormArray;
	}

	updateGrandTotal(bookingOption: FormGroup) {
		const chargeableWeight = Number(this.bookingOptionRequestFormComponent.bookingOptionReqForm.get('chargeableWeight')?.value ?? 0);
		const grandTotal = (bookingOption.get(PRICE_LIST) as FormArray).controls
			.reduce((sum, item) => sum + Number.parseFloat(item.get('subTotal')?.value ?? 0) * chargeableWeight, 0)
			.toFixed(2);
		bookingOption.patchValue({ grandTotal: Number(grandTotal) });
	}

	saveBooking() {
		this.optionForm.markAllAsTouched();
		this.bookingOptionRequestFormComponent.bookingOptionReqForm.markAllAsTouched();
		this.bookingOptionRequestFormComponent.bookingOptionReqForm.get(DEPARTURE_LOCATION)?.clearValidators();
		this.bookingOptionRequestFormComponent.bookingOptionReqForm.get(DEPARTURE_LOCATION)?.updateValueAndValidity();
		this.bookingOptionRequestFormComponent.bookingOptionReqForm.get(ARRIVAL_LOCATION)?.clearValidators();
		this.bookingOptionRequestFormComponent.bookingOptionReqForm.get(ARRIVAL_LOCATION)?.updateValueAndValidity();

		if (this.optionForm.invalid || this.bookingOptionRequestFormComponent.bookingOptionReqForm.invalid) {
			this.commonService.showFormInvalid();
			return;
		}

		this.dataLoading = true;

		const bookingOptionReq = this.bookingOptionRequestFormComponent.getFormData() as BookingInfo;
		const optionFormvalue: BookingOptionDetail[] = [...this.optionForm.controls.options.value];

		const param: BookingInfo[] = optionFormvalue.map((item) => {
			const departureDate = item.transportLegsList[0].departureDate;
			const arrivalDate = item.transportLegsList[0].arrivalDate;
			item.transportLegsList[0].departureDate = departureDate ? formatDateTime(departureDate.toString()) : '';
			item.transportLegsList[0].arrivalDate = arrivalDate ? formatDateTime(arrivalDate.toString()) : '';
			return {
				...bookingOptionReq,
				priceSaveDto: item.priceList[0] ?? null,
				transportLegsSaveDto: item.transportLegsList[0] ?? null,
			};
		});

		this.bookingService.createBookingRequest(param[0]).subscribe({
			next: () => {
				this.dataLoading = false;
				this.router.navigate(['booking']);
			},
			error: () => {
				this.dataLoading = false;
			},
		});
	}

	onCancel() {
		this.commonService.showCancelConfirm('booking');
	}
}
