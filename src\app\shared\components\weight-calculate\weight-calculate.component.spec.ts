import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import WeightCalculateComponent from './weight-calculate.component';
import { WeightCalculateRequestService } from '@shared/services/biz-logic/weight-calculate/weight-calculate-request.service';
import { WeightInfoListObj } from '@shared/models/weight-info';
import { of, throwError } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { ChangeDetectorRef } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Location } from '@angular/common';
import { PaginationRequest } from '@shared/models/pagination-request.model';

const mockResponse: WeightInfoListObj = {
	totalSlac: 0,
	totalGrossWeight: 250,
	noPieceRcp: 0,
	chargableWeight: 150,
	pieceGrossWeight: 0,
	volume: 75,
	pieces: [
		{
			pieceId: '',
			productDescription: '',
			packagingType: '',
			grossWeight: 0,
			dimensions: {
				id: '111',
				length: 0,
				width: 0,
				height: 0,
				volume: 0,
			},
			pieceQuantity: 0,
			slac: 0,
			type: '',
			latestStatus: '',
			textualHandlingInstructions: '',
		},
	],
};

const activatedRoute = {
	snapshot: {
		paramMap: {
			get: jasmine.createSpy('get').and.callFake((key: string) => {
				if (key === 'fromSli') return '123';
				return null;
			}),
		},
	},
	paramMap: of({
		get: (key: string) => {
			if (key === 'fromSli') return '123';
			return null;
		},
	}),
};

describe('WeightCalculateComponent', () => {
	let component: WeightCalculateComponent;
	let fixture: ComponentFixture<WeightCalculateComponent>;
	let mockService: jasmine.SpyObj<WeightCalculateRequestService>;
	let cdrSpy: jasmine.SpyObj<ChangeDetectorRef>;
	let routerSpy: jasmine.SpyObj<Router>;
	let locationSpy: jasmine.SpyObj<Location>;

	beforeEach(async () => {
		mockService = jasmine.createSpyObj('WeightCalculateRequestService', ['getSliData', 'getHawbData']);
		mockService.getSliData.and.returnValue(of(mockResponse));
		mockService.getHawbData.and.returnValue(of(mockResponse));
		cdrSpy = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);
		routerSpy = jasmine.createSpyObj('Router', ['navigate', 'getCurrentNavigation']);
		locationSpy = jasmine.createSpyObj('Location', ['back']);
		await TestBed.configureTestingModule({
			imports: [WeightCalculateComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: WeightCalculateRequestService, useValue: mockService },
				{
					provide: ActivatedRoute,
					useValue: activatedRoute,
				},
				{ provide: ChangeDetectorRef, useValue: cdrSpy },
				{ provide: Router, useValue: routerSpy },
				{ provide: Location, useValue: locationSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(WeightCalculateComponent);
		component = fixture.componentInstance;

		component.weightCalculateForm = new FormGroup({
			totalGrossWeight: new FormControl<string>(''),
			volume: new FormControl<string>(''),
			chargeableWeight: new FormControl<string>(''),
		});
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('onCancel()', () => {
		it('location.back()', () => {
			component.onCancel();

			expect(locationSpy.back).toHaveBeenCalled();
		});
	});

	describe('onSave()', () => {
		it('sliNumber is right,navigate to the right url', () => {
			component.sliNumber = 'test123';

			component.weightCalculateForm.patchValue({
				totalGrossWeight: 100,
				chargeableWeight: 80,
				volume: 50,
			});

			component.onSave();

			expect(routerSpy.navigate).toHaveBeenCalledWith(['/hawb/create', 'test123', 'detail'], {
				state: {
					grossWeight: 100,
					chargeableWeight: 80,
					volume: 50,
				},
			});
		});

		it('sliNumber is empty', () => {
			component.sliNumber = undefined;
			component.state = { selectedHawbs: ['111'], bookingId: '123' };

			component.weightCalculateForm.patchValue({
				totalGrossWeight: 100,
				chargeableWeight: 80,
				volume: 50,
			});

			component.onSave();

			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb/create/detail'], {
				state: {
					selectedHawbs: ['111'],
					bookingId: '123',
					grossWeight: 100,
					chargeableWeight: 80,
					volume: 50,
				},
			});
		});

		it('slinumber and state is empty', () => {
			component.sliNumber = undefined;
			component.state = undefined;

			component.onSave();

			expect(routerSpy.navigate).not.toHaveBeenCalled();
		});
	});

	describe('getState()', () => {
		it('return right form value', () => {
			const testValues = {
				totalGrossWeight: 100,
				chargeableWeight: 80,
				volume: 50,
			};

			component.weightCalculateForm.patchValue(testValues);

			const result = component['getState']();

			expect(result).toEqual({
				grossWeight: 100,
				chargeableWeight: 80,
				volume: 50,
			});
		});

		it('if form is empty', () => {
			component.weightCalculateForm.patchValue({
				totalGrossWeight: null,
				chargeableWeight: undefined,
				volume: '',
			});

			const result = component['getState']();

			expect(result).toEqual({
				grossWeight: null,
				chargeableWeight: undefined,
				volume: '',
			});
		});
	});

	it('get form state', () => {
		component.sliNumber = 'test123';

		component.weightCalculateForm.patchValue({
			totalGrossWeight: 200,
			chargeableWeight: 150,
			volume: 75,
		});

		component.onSave();

		expect(routerSpy.navigate.calls.mostRecent().args[1]?.state).toEqual({
			grossWeight: 200,
			chargeableWeight: 150,
			volume: 75,
		});
	});

	describe('loadData()', () => {
		it('sliNumber is not empty, call getSliData', fakeAsync(() => {
			component.sliNumber = 'test123';
			component.pageIndex = 0;
			component.pageSize = 10;
			component.dataLoading = false;

			(component as any).loadData();

			tick();

			const expectedRequest: PaginationRequest = {
				pageNum: 1, // pageIndex + 1
				pageSize: 10,
			};
			expect(mockService.getSliData).toHaveBeenCalledWith(expectedRequest, 'test123');

			expect(component.data).toEqual(mockResponse);
			expect(component.dataSource.data).toEqual(mockResponse.pieces);
			expect(component.weightCalculateForm.get('chargeableWeight')?.value).toBe(150);
			expect(component.weightCalculateForm.get('totalGrossWeight')?.value).toBe(250);
			expect(component.weightCalculateForm.get('volume')?.value).toBe(75);
			expect(component.dataLoading).toBeFalse();
		}));

		it('sate is not empty, call getHawbData', fakeAsync(() => {
			component.sliNumber = undefined;
			component.state = {
				selectedHawbs: [
					{ hawbId: 'hawb1', name: 'HAWB 1' },
					{ hawbId: 'hawb2', name: 'HAWB 2' },
				],
			};
			component.pageIndex = 1;
			component.pageSize = 20;
			component.dataLoading = false;

			(component as any).loadData();

			tick();

			const expectedRequest: PaginationRequest = {
				pageNum: 2, // pageIndex + 1
				pageSize: 20,
			};
			const expectedHawbIds = ['hawb1', 'hawb2'];
			expect(mockService.getHawbData).toHaveBeenCalledWith(expectedRequest, expectedHawbIds);

			expect(component.data).toEqual(mockResponse);
			expect(component.dataSource.data).toEqual(mockResponse.pieces);
			expect(component.weightCalculateForm.get('chargeableWeight')?.value).toBe(150);
			expect(component.weightCalculateForm.get('totalGrossWeight')?.value).toBe(250);
			expect(component.weightCalculateForm.get('volume')?.value).toBe(75);
			expect(component.dataLoading).toBeFalse();
		}));

		it('sliNumber exists, getSliData error', fakeAsync(() => {
			component.sliNumber = 'test123';
			component.dataLoading = false;

			const consoleErrorSpy = spyOn(console, 'error');
			const errorResponse = new Error('API Error');

			mockService.getSliData.and.returnValue(throwError(() => errorResponse));

			(component as any).loadData();

			tick();

			expect(consoleErrorSpy).toHaveBeenCalledWith(errorResponse);
			expect(component.dataLoading).toBeFalse();
		}));

		it('state is not empty, get hawbData error', fakeAsync(() => {
			component.sliNumber = undefined;
			component.state = {
				selectedHawbs: [{ hawbId: 'hawb1' }],
			};
			component.dataLoading = false;

			const consoleErrorSpy = spyOn(console, 'error');
			const errorResponse = new Error('API Error');

			mockService.getHawbData.and.returnValue(throwError(() => errorResponse));

			(component as any).loadData();

			tick();

			expect(consoleErrorSpy).toHaveBeenCalledWith(errorResponse);
			expect(component.dataLoading).toBeFalse();
		}));

		it('handle hawb list', fakeAsync(() => {
			component.sliNumber = undefined;
			component.state = {
				selectedHawbs: [],
			};
			const mockResponse: WeightInfoListObj = {
				totalSlac: 0,
				totalGrossWeight: 0,
				noPieceRcp: 0,
				chargableWeight: 0,
				pieceGrossWeight: 0,
				volume: 0,
				pieces: [],
			};
			mockService.getHawbData.and.returnValue(of(mockResponse));

			(component as any).loadData();
			tick();

			expect(mockService.getHawbData).toHaveBeenCalledWith(jasmine.any(Object), []);
		}));
	});
});
