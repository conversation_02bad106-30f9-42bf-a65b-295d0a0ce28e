import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BookingRequestDetailsComponent } from './booking-request-details.component';
import { of, throwError } from 'rxjs';
import { BookingRequestService } from '../../services/booking-request.service';
import { ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';

describe('BookingRequestDetailsComponent', () => {
	let component: BookingRequestDetailsComponent;
	let fixture: ComponentFixture<BookingRequestDetailsComponent>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<BookingRequestDetailsComponent>>;
	let bookingRequestServiceSpy: jasmine.SpyObj<BookingRequestService>;
	let routerSpy: jasmine.SpyObj<Router>;
	let cdrSpy: jasmine.SpyObj<ChangeDetectorRef>;

	beforeEach(async () => {
		mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
		bookingRequestServiceSpy = jasmine.createSpyObj('BookingRequestService', ['getBookingRequestDetail', 'confirmBookingRequest']);
		routerSpy = jasmine.createSpyObj('Router', ['navigate']);
		cdrSpy = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

		await TestBed.configureTestingModule({
			imports: [BookingRequestDetailsComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: {} },
				{ provide: BookingRequestService, useValue: bookingRequestServiceSpy },
				{ provide: Router, useValue: routerSpy },
				{ provide: ChangeDetectorRef, useValue: cdrSpy },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(BookingRequestDetailsComponent);
		component = fixture.componentInstance;
		// provide default spy returns so initial ngOnInit (triggered by detectChanges) doesn't call undefined
		bookingRequestServiceSpy.getBookingRequestDetail.and.returnValue(of({} as any));
		bookingRequestServiceSpy.confirmBookingRequest.and.returnValue(of({} as any));
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('ngOnInit should set properties and handle successful data load', () => {
		const mockData = { bookingId: 'bk-1', some: 'value' } as any;
		component.data = { title: 'Title', buttonName: 'Done', bookingRequestId: 'req-1', canConfirm: true, confirmedStatus: false };
		bookingRequestServiceSpy.getBookingRequestDetail.and.returnValue(of(mockData));

		component.ngOnInit();

		expect(component.title).toBe('Title');
		expect(component.buttonName).toBe('Done');
		expect(component.bookingRequestId).toBe('req-1');
		expect(component.canConfirm).toBeTrue();
		// after subscription resolves
		expect(component.bookingRequestDetails).toEqual(mockData);
		expect(component.dataLoading).toBeFalse();
	});

	it('ngOnInit should handle error from service gracefully', () => {
		component.data = { title: 'Err', buttonName: 'Done', bookingRequestId: 'req-err', canConfirm: false, confirmedStatus: false };
		bookingRequestServiceSpy.getBookingRequestDetail.and.returnValue(throwError(() => new Error('fail')));

		component.ngOnInit();
		expect(component.dataLoading).toBeFalse();
	});

	it('onDone should confirm when allowed and close dialog with true on success', () => {
		component.canConfirm = true;
		component.confirmedStatus = false;
		component.bookingRequestId = 'req-confirm';
		bookingRequestServiceSpy.confirmBookingRequest.and.returnValue(of({} as any));

		component.onDone();

		expect(component.dataLoading).toBeFalse();
		expect(mockDialogRef.close).toHaveBeenCalledWith(true);
	});

	it('onDone should close dialog with false on confirm error', () => {
		component.canConfirm = true;
		component.confirmedStatus = false;
		component.bookingRequestId = 'req-confirm';
		bookingRequestServiceSpy.confirmBookingRequest.and.returnValue(throwError(() => new Error('err')));

		component.onDone();

		expect(component.dataLoading).toBeFalse();
		expect(mockDialogRef.close).toHaveBeenCalledWith(false);
	});

	it('onDone should navigate to create MAWB when cannot confirm and close dialog false', () => {
		component.canConfirm = false;
		component.confirmedStatus = false;
		component.bookingRequestDetails = { bookingId: 'bk-redirect' } as any;

		component.onDone();

		expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb/create'], {
			state: { bookingId: 'bk-redirect' },
		});
		expect(mockDialogRef.close).toHaveBeenCalledWith(false);
	});

	it('onUpdate should navigate to mawb and close dialog false', () => {
		component.bookingRequestDetails = { bookingId: 'bk-update' } as any;
		component.onUpdate();

		expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb'], { state: { bookingId: 'bk-update' } });
		expect(mockDialogRef.close).toHaveBeenCalledWith(false);
	});

	it('onCancel should close dialog with false', () => {
		component.onCancel();
		expect(mockDialogRef.close).toHaveBeenCalledWith(false);
	});
});
