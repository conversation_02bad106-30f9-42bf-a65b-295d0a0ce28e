export enum SearchType {
	SHIPPER = 'shipper',
	CONSIGNEE = 'consignee',
	ORIGIN = 'origin',
	DESTINATION = 'destination',
	HAWB = 'hawbNumber',
	MAWB = 'mawbNumber',
	AIRLINE_CODE = 'airlineCode',
	LATEST_STATUS = 'latestStatus',
}

export enum CodeType {
	CHARGE_TYPE = 'chargeType',
	RATE_CLASS_CODE = 'rateClassCode',
	PAYMENT_TYPE = 'prepaidCollectIndicator',
	ENTITILEMENT = 'entitlementCode',
	COMMODITY_CODE = 'commodityCode',
	HANDLING_CODE = 'specialHandlingCode',
	SCREEN_METHOD = 'screeningMethod',
	SECURITY_STATUS = 'securityStatus',
	SCREEN_EXEMPTION = 'screeningExemption',
	ENTITY_CATEGORY = 'regulatedEntityCategory',
	PACKAGE_TYPE = 'packageType',
}
