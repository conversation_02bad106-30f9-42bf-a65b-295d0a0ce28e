import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ChangeDetectorRef, ElementRef } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { of } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
// eslint-disable-next-line @typescript-eslint/naming-convention
import HomeDashboardComponent from './home-dashboard.component';
import { HomeDashboardService } from '../../services/home-dashboard.service';
import { ActivityStream } from '../../models/home-dashboard.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { UserProfileService } from '@shared/services/user-profile.service';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';

describe('HomeDashboardComponent', () => {
	let component: HomeDashboardComponent;
	let fixture: ComponentFixture<HomeDashboardComponent>;
	let mockHomeDashboardService: jasmine.SpyObj<HomeDashboardService>;
	let mockUserProfileService: jasmine.SpyObj<UserProfileService>;
	let mockTranslateService: jasmine.SpyObj<TranslateService>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;
	let mockDialog: jasmine.SpyObj<MatDialog>;

	const mockActivityStreamData: ActivityStream[] = [
		{
			id: '1',
			date: '2025-01-01T12:00:00.000Z',
			url: 'test-file.pdf',
			fileName: 'test-file.pdf',
			message: 'Test activity message 1',
		},
		{
			id: '2',
			date: '2025-01-02T14:30:00.000Z',
			url: 'test-file2.pdf',
			fileName: 'test-file2.pdf',
			message: 'Test activity message 2',
		},
	];

	const mockPaginationResponse: PaginationResponse<ActivityStream> = {
		total: 2,
		pageNum: 1,
		rows: mockActivityStreamData,
	};

	beforeEach(async () => {
		mockHomeDashboardService = jasmine.createSpyObj('HomeDashboardService', [
			'getActivityStream',
			'downloadFile',
			'cleanSystemData',
			'cleanActivityStream',
		]);
		mockUserProfileService = jasmine.createSpyObj('UserProfileService', ['getProfile', 'isSuperUser']);
		mockTranslateService = jasmine.createSpyObj('TranslateService', ['instant', 'get']);
		mockTranslateService.get.and.returnValue(of('Translated Text'));
		mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);
		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);

		// Set up default return values
		mockHomeDashboardService.getActivityStream.and.returnValue(of(mockPaginationResponse));
		mockHomeDashboardService.downloadFile.and.returnValue(of(new Blob(['test'], { type: 'application/pdf' })));
		mockHomeDashboardService.cleanSystemData.and.returnValue(of(true));
		mockHomeDashboardService.cleanActivityStream.and.returnValue(of(true));
		mockUserProfileService.getProfile.and.returnValue(of({} as any));
		mockUserProfileService.isSuperUser.and.returnValue(false);
		mockTranslateService.instant.and.returnValue('Translated Text');

		await TestBed.configureTestingModule({
			imports: [HomeDashboardComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			providers: [
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				{ provide: HomeDashboardService, useValue: mockHomeDashboardService },
				{ provide: UserProfileService, useValue: mockUserProfileService },
				{ provide: TranslateService, useValue: mockTranslateService },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
				{ provide: MatDialog, useValue: mockDialog },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(HomeDashboardComponent);
		component = fixture.componentInstance;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('Component Initialization', () => {
		it('should initialize chart data on ngOnInit', () => {
			spyOn(component, 'initChart' as any);

			component.ngOnInit();

			expect(component['initChart']).toHaveBeenCalled();
		});

		it('should set up weekly data correctly in initChart', () => {
			component['initChart']();

			expect(component.weeklyTotalData).toEqual([120, 132, 101, 134, 90, 230, 210, 120, 132, 101, 134, 90]);
			expect(component.weeklyExternalData).toEqual([120, 132, 101, 134, 90, 230, 210, 120, 132, 101, 134, 90]);
			expect(component.weeklyActiveData).toEqual([120, 132, 101, 134, 90, 230, 210, 120, 132, 101, 134, 90]);
		});

		it('should calculate total users correctly', () => {
			component['initChart']();

			const expectedTotal = [120, 132, 101, 134, 90, 230, 210, 120, 132, 101, 134, 90].reduce((a, b) => a + b, 0);
			expect(component.totalUsers).toBe(expectedTotal);
			expect(component.totalExternalUsers).toBe(expectedTotal);
			expect(component.totalActiveUsers).toBe(expectedTotal);
		});

		it('should have correct weeks labels', () => {
			const expectedWeeks = [
				'week 1',
				'week 2',
				'week 3',
				'week 4',
				'week 5',
				'week 6',
				'week 7',
				'week 8',
				'week 9',
				'week 10',
				'week 11',
				'week 12',
			];
			expect(component['weeks']).toEqual(expectedWeeks);
		});
	});

	describe('Chart Configuration', () => {
		beforeEach(() => {
			component['initChart']();
		});

		it('should generate line chart data with correct structure', () => {
			const chartData = component.getLineChartData([100, 200, 300], undefined, '#ff0000');

			expect(chartData.labels).toEqual(component['weeks']);
			expect(chartData.datasets).toHaveSize(1);
			expect(chartData.datasets[0].data).toEqual([100, 200, 300]);
		});

		it('should generate line chart options with correct configuration', () => {
			const titleText = 'test.title';
			mockTranslateService.instant.and.returnValue('Test Title');

			const options = component.getLineChartOptions(titleText);

			expect(mockTranslateService.instant).toHaveBeenCalledWith(titleText);
			expect(options.responsive).toBe(true);
			expect(options.maintainAspectRatio).toBe(false);
			expect(options.plugins?.title?.display).toBe(true);
			expect(options.plugins?.title?.text).toBe('Test Title');
			expect(options.plugins?.legend?.display).toBe(false);
			expect((options.scales as any)?.x?.display).toBe(false);
			expect((options.scales as any)?.y?.display).toBe(false);
		});
	});

	describe('Activity Stream Management', () => {
		it('should get activity stream data successfully', () => {
			component.getActivityStream();

			expect(mockHomeDashboardService.getActivityStream).toHaveBeenCalled();
			expect(component.activityStreamData).toEqual(mockActivityStreamData);
		});

		it('should call service method for activity stream', () => {
			component.getActivityStream();

			expect(mockHomeDashboardService.getActivityStream).toHaveBeenCalled();
		});

		it('should download activity file successfully', () => {
			const mockBlob = new Blob(['test content'], { type: 'application/pdf' });
			mockHomeDashboardService.downloadFile.and.returnValue(of(mockBlob));

			const activity = mockActivityStreamData[0];
			component.downloadActivityFile(activity);

			expect(mockHomeDashboardService.downloadFile).toHaveBeenCalledWith(activity.url);
		});

		it('should call service method for download', () => {
			const activity = mockActivityStreamData[0];
			component.downloadActivityFile(activity);

			expect(mockHomeDashboardService.downloadFile).toHaveBeenCalledWith(activity.url);
		});
	});

	describe('Data Cleaning Operations', () => {
		beforeEach(() => {
			spyOn(component, 'getActivityStream');
		});

		it('should clean system data successfully', () => {
			component.cleanSystemData();

			expect(mockHomeDashboardService.cleanSystemData).toHaveBeenCalled();
			expect(component.getActivityStream).toHaveBeenCalled();
		});

		it('should not refresh activity stream when system data cleaning fails', () => {
			mockHomeDashboardService.cleanSystemData.and.returnValue(of(false));

			component.cleanSystemData();

			expect(mockHomeDashboardService.cleanSystemData).toHaveBeenCalled();
			expect(component.getActivityStream).not.toHaveBeenCalled();
		});

		it('should clean activity stream successfully', () => {
			component.cleanActivityStream();

			expect(mockHomeDashboardService.cleanActivityStream).toHaveBeenCalled();
			expect(component.getActivityStream).toHaveBeenCalled();
		});

		it('should not refresh activity stream when activity stream cleaning fails', () => {
			mockHomeDashboardService.cleanActivityStream.and.returnValue(of(false));

			component.cleanActivityStream();

			expect(mockHomeDashboardService.cleanActivityStream).toHaveBeenCalled();
			expect(component.getActivityStream).not.toHaveBeenCalled();
		});

		it('should call service methods for cleaning operations', () => {
			component.cleanSystemData();
			component.cleanActivityStream();

			expect(mockHomeDashboardService.cleanSystemData).toHaveBeenCalled();
			expect(mockHomeDashboardService.cleanActivityStream).toHaveBeenCalled();
		});
	});

	describe('Confirm Dialog Operations', () => {
		it('should open confirm dialog for system data cleaning', () => {
			const mockDialogRef = {
				afterClosed: () => of(true),
			};
			mockDialog.open.and.returnValue(mockDialogRef as any);
			spyOn(component, 'cleanSystemData');

			component.openConfirmDialog('system.title', 'system.content');

			expect(mockDialog.open).toHaveBeenCalledWith(ConfirmDialogComponent, {
				width: '400px',
				data: {
					icon: 'cleaning_services',
					ok: 'Translated Text',
					title: 'Translated Text',
					content: 'Translated Text',
				},
			});
			expect(component.cleanSystemData).toHaveBeenCalled();
		});

		it('should open confirm dialog for activity stream cleaning', () => {
			const mockDialogRef = {
				afterClosed: () => of(true),
			};
			mockDialog.open.and.returnValue(mockDialogRef as any);
			spyOn(component, 'cleanActivityStream');

			component.openConfirmDialog('activity.title', 'activity.content');

			expect(mockDialog.open).toHaveBeenCalledWith(ConfirmDialogComponent, {
				width: '400px',
				data: {
					icon: 'cleaning_services',
					ok: 'Translated Text',
					title: 'Translated Text',
					content: 'Translated Text',
				},
			});
			expect(component.cleanActivityStream).toHaveBeenCalled();
		});

		it('should not perform action when dialog is cancelled', () => {
			const mockDialogRef = {
				afterClosed: () => of(false),
			};
			mockDialog.open.and.returnValue(mockDialogRef as any);
			spyOn(component, 'cleanSystemData');
			spyOn(component, 'cleanActivityStream');

			component.openConfirmDialog('system.title', 'system.content');

			expect(component.cleanSystemData).not.toHaveBeenCalled();
			expect(component.cleanActivityStream).not.toHaveBeenCalled();
		});
	});

	describe('Lifecycle Hooks', () => {
		it('should start auto scroll after view init', fakeAsync(() => {
			spyOn(component, 'startAutoScroll' as any);

			component.ngAfterViewInit();
			tick(1000);

			expect(component['startAutoScroll']).toHaveBeenCalled();
		}));

		it('should stop auto scroll on destroy', () => {
			spyOn(component, 'stopAutoScroll' as any);

			component.ngOnDestroy();

			expect(component['stopAutoScroll']).toHaveBeenCalled();
		});
	});

	describe('Auto Scroll Functionality', () => {
		let mockScrollContainer: jasmine.SpyObj<HTMLDivElement>;
		let mockScrollContent: jasmine.SpyObj<HTMLDivElement>;

		beforeEach(() => {
			mockScrollContainer = {
				clientHeight: 300,
				scrollTop: 0,
			} as any;
			mockScrollContent = {
				scrollHeight: 600,
			} as any;

			component.scrollContainer = {
				nativeElement: mockScrollContainer,
			} as ElementRef<HTMLDivElement>;

			component.scrollContent = {
				nativeElement: mockScrollContent,
			} as ElementRef<HTMLDivElement>;
		});

		it('should start auto scroll when containers are available', () => {
			spyOn(window, 'setInterval').and.returnValue(123 as any);

			component['startAutoScroll']();

			expect(window.setInterval).toHaveBeenCalledWith(jasmine.any(Function), 50);
			expect(component['scrollInterval']).toBe(123);
		});

		it('should not start auto scroll when containers are not available', () => {
			component.scrollContainer = undefined as any;
			spyOn(window, 'setInterval');

			component['startAutoScroll']();

			expect(window.setInterval).not.toHaveBeenCalled();
		});

		it('should stop auto scroll and clear interval', () => {
			component['scrollInterval'] = 123;
			spyOn(window, 'clearInterval');

			component['stopAutoScroll']();

			expect(window.clearInterval).toHaveBeenCalledWith(123);
			expect(component['scrollInterval']).toBeNull();
		});

		it('should perform scroll down when at top', () => {
			mockScrollContainer.scrollTop = 0;
			component['scrollDirection'] = 1;

			component['performScroll']();

			expect(mockScrollContainer.scrollTop).toBe(1); // scrollSpeed = 1
		});

		it('should change direction when reaching bottom', () => {
			mockScrollContainer.scrollTop = 299; // near bottom
			component['scrollDirection'] = 1;

			component['performScroll']();

			expect(component['scrollDirection']).toBe(-1);
			expect(mockScrollContainer.scrollTop).toBe(300); // contentHeight - containerHeight
		});

		it('should change direction when reaching top', () => {
			mockScrollContainer.scrollTop = 1;
			component['scrollDirection'] = -1;

			component['performScroll']();

			expect(component['scrollDirection']).toBe(1);
			expect(mockScrollContainer.scrollTop).toBe(0);
		});

		it('should not perform scroll when containers are not available', () => {
			component.scrollContainer = undefined as any;

			expect(() => component['performScroll']()).not.toThrow();
		});
	});

	describe('Component Properties', () => {
		it('should have correct initial values', () => {
			expect(component.lineChartType).toBe('line');
			expect(component.totalUsers).toBe(0);
			expect(component.totalExternalUsers).toBe(0);
			expect(component.totalActiveUsers).toBe(0);
			expect(component.activityStreamData).toEqual([]);
		});

		it('should have correct scroll configuration', () => {
			expect(component['scrollSpeed']).toBe(1);
			expect(component['scrollDirection']).toBe(1);
		});
	});

	describe('Component Integration', () => {
		it('should have correct component type', () => {
			expect(component.lineChartType).toBe('line');
		});

		it('should extend RolesAwareComponent', () => {
			expect(component).toBeInstanceOf(HomeDashboardComponent);
		});

		it('should have all required methods', () => {
			expect(typeof component.ngOnInit).toBe('function');
			expect(typeof component.ngAfterViewInit).toBe('function');
			expect(typeof component.ngOnDestroy).toBe('function');
			expect(typeof component.getLineChartData).toBe('function');
			expect(typeof component.getLineChartOptions).toBe('function');
			expect(typeof component.getActivityStream).toBe('function');
			expect(typeof component.downloadActivityFile).toBe('function');
			expect(typeof component.cleanSystemData).toBe('function');
			expect(typeof component.cleanActivityStream).toBe('function');
			expect(typeof component.openConfirmDialog).toBe('function');
		});
	});
});
