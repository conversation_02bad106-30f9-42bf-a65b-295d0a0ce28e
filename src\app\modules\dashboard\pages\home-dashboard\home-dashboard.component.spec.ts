import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ChangeDetectorRef, ElementRef } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { of } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
// eslint-disable-next-line @typescript-eslint/naming-convention
import HomeDashboardComponent from './home-dashboard.component';
import { HomeDashboardService } from '../../services/home-dashboard.service';
import { ActivityStream, UserStatisticsChart } from '../../models/home-dashboard.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { UserProfileService } from '@shared/services/user-profile.service';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { DashboardChartType } from '../../models/chart-type.model';

describe('HomeDashboardComponent', () => {
	let component: HomeDashboardComponent;
	let fixture: ComponentFixture<HomeDashboardComponent>;
	let mockHomeDashboardService: jasmine.SpyObj<HomeDashboardService>;
	let mockUserProfileService: jasmine.SpyObj<UserProfileService>;
	let mockTranslateService: jasmine.SpyObj<TranslateService>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;
	let mockDialog: jasmine.SpyObj<MatDialog>;

	const mockActivityStreamData: ActivityStream[] = [
		{
			id: '1',
			date: '2025-01-01T12:00:00.000Z',
			url: 'test-file.pdf',
			fileName: 'test-file.pdf',
			message: 'Test activity message 1',
		},
		{
			id: '2',
			date: '2025-01-02T14:30:00.000Z',
			url: 'test-file2.pdf',
			fileName: 'test-file2.pdf',
			message: 'Test activity message 2',
		},
	];

	const mockPaginationResponse: PaginationResponse<ActivityStream> = {
		total: 2,
		pageNum: 1,
		rows: mockActivityStreamData,
	};

	const mockUserStatisticsData: UserStatisticsChart[] = [
		{
			id: '1',
			type: DashboardChartType.WEEKLY_TOTAL_NEW_USER,
			userNumber: '1500',
			growingNumber: '100',
			growthRate: '7.1%',
			trendData: {
				weeks: ['week 1', 'week 2', 'week 3', 'week 4'],
				data: [120, 132, 101, 134],
			},
		},
		{
			id: '2',
			type: DashboardChartType.WEEKLY_EXTERNAL_NEW_USER,
			userNumber: '800',
			growingNumber: '50',
			growthRate: '6.7%',
			trendData: {
				weeks: ['week 1', 'week 2', 'week 3', 'week 4'],
				data: [90, 95, 85, 100],
			},
		},
		{
			id: '3',
			type: DashboardChartType.WEEKLY_ACTIVE_USER,
			userNumber: '1200',
			growingNumber: '75',
			growthRate: '6.7%',
			trendData: {
				weeks: ['week 1', 'week 2', 'week 3', 'week 4'],
				data: [200, 210, 195, 220],
			},
		},
	];

	beforeEach(async () => {
		mockHomeDashboardService = jasmine.createSpyObj('HomeDashboardService', [
			'getActivityStream',
			'downloadFile',
			'cleanSystemData',
			'cleanActivityStream',
			'getUserStatistics',
		]);
		mockUserProfileService = jasmine.createSpyObj('UserProfileService', ['getProfile', 'isSuperUser']);
		mockTranslateService = jasmine.createSpyObj('TranslateService', ['instant', 'get']);
		mockTranslateService.get.and.returnValue(of('Translated Text'));
		mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);
		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);

		// Set up default return values
		mockHomeDashboardService.getActivityStream.and.returnValue(of(mockPaginationResponse));
		mockHomeDashboardService.downloadFile.and.returnValue(of(new Blob(['test'], { type: 'application/pdf' })));
		mockHomeDashboardService.cleanSystemData.and.returnValue(of(true));
		mockHomeDashboardService.cleanActivityStream.and.returnValue(of(true));
		mockHomeDashboardService.getUserStatistics.and.returnValue(of(mockUserStatisticsData));
		mockUserProfileService.getProfile.and.returnValue(of({} as any));
		mockUserProfileService.isSuperUser.and.returnValue(false);
		mockTranslateService.instant.and.returnValue('Translated Text');

		await TestBed.configureTestingModule({
			imports: [HomeDashboardComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			providers: [
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				{ provide: HomeDashboardService, useValue: mockHomeDashboardService },
				{ provide: UserProfileService, useValue: mockUserProfileService },
				{ provide: TranslateService, useValue: mockTranslateService },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
				{ provide: MatDialog, useValue: mockDialog },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(HomeDashboardComponent);
		component = fixture.componentInstance;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('Component Initialization', () => {
		it('should initialize chart data on ngOnInit', () => {
			spyOn(component, 'initChart' as any);

			component.ngOnInit();

			expect(component['initChart']).toHaveBeenCalled();
		});

		it('should call getUserStatistics and getActivityStream in initChart', () => {
			spyOn(component, 'getUserStatistics');
			spyOn(component, 'getActivityStream');

			component['initChart']();

			expect(component.getUserStatistics).toHaveBeenCalled();
			expect(component.getActivityStream).toHaveBeenCalled();
		});

		it('should create canvas gradients in initChart', () => {
			component['initChart']();

			expect(component.fillGradientTotal).toBeDefined();
			expect(component.fillGradientExternal).toBeDefined();
			expect(component.fillGradientActive).toBeDefined();
		});
	});

	describe('Chart Configuration', () => {
		beforeEach(() => {
			component['initChart']();
		});

		it('should generate line chart data with correct structure', () => {
			const chartData = component.getLineChartData([100, 200, 300], undefined, '#ff0000');

			expect(chartData.labels).toEqual(component['weeks']);
			expect(chartData.datasets).toHaveSize(1);
			expect(chartData.datasets[0].data).toEqual([100, 200, 300]);
		});

		it('should generate line chart data with gradient background', () => {
			const mockGradient = {} as CanvasGradient;
			const chartData = component.getLineChartData([100, 200, 300], mockGradient, '#ff0000');

			expect(chartData.datasets[0].backgroundColor).toBe(mockGradient);
			expect(chartData.datasets[0].borderColor).toBe('#ff0000');
			expect(chartData.datasets[0].pointBackgroundColor).toBe('#ff0000');
		});

		it('should generate line chart data with correct styling properties', () => {
			const chartData = component.getLineChartData([100, 200, 300], undefined, '#ff0000');
			const dataset = chartData.datasets[0];

			expect(dataset.fill).toBe(true);
			expect(dataset.borderWidth).toBe(4);
			expect(dataset.pointBorderColor).toBe('#fff');
			expect(dataset.pointBorderWidth).toBe(3);
			expect(dataset.pointRadius).toBe(7);
			expect(dataset.pointHoverRadius).toBe(10);
			expect(dataset.pointHoverBorderColor).toBe('#fff');
			expect(dataset.pointHoverBorderWidth).toBe(4);
			expect(dataset.tension).toBe(0.4);
		});

		it('should generate line chart data with empty data array', () => {
			const chartData = component.getLineChartData([], undefined, '#ff0000');

			expect(chartData.datasets[0].data).toEqual([]);
			expect(chartData.labels).toEqual(component['weeks']);
		});

		it('should generate line chart options with correct configuration', () => {
			const titleText = 'test.title';
			mockTranslateService.instant.and.returnValue('Test Title');

			const options = component.getLineChartOptions(titleText);

			expect(mockTranslateService.instant).toHaveBeenCalledWith(titleText);
			expect(options.responsive).toBe(true);
			expect(options.maintainAspectRatio).toBe(false);
			expect(options.plugins?.title?.display).toBe(true);
			expect(options.plugins?.title?.text).toBe('Test Title');
			expect(options.plugins?.legend?.display).toBe(false);
			expect((options.scales as any)?.x?.display).toBe(false);
			expect((options.scales as any)?.y?.display).toBe(false);
		});

		it('should generate line chart options with correct title styling', () => {
			const options = component.getLineChartOptions('test.title');
			const titleConfig = options.plugins?.title;

			expect(titleConfig?.color).toBe('#333333');
			expect((titleConfig?.font as any)?.size).toBe(16);
			expect((titleConfig?.font as any)?.weight).toBe('bold');
		});

		it('should generate line chart options with correct element configuration', () => {
			const options = component.getLineChartOptions('test.title');

			expect(options.elements?.line?.tension).toBe(0.4);
		});
	});

	describe('User Statistics Management', () => {
		it('should get user statistics data successfully', () => {
			component.getUserStatistics();

			expect(mockHomeDashboardService.getUserStatistics).toHaveBeenCalled();
			expect(component.weeks).toEqual(['week 1', 'week 2', 'week 3', 'week 4']);
			expect(component.weeklyTotalData).toEqual([120, 132, 101, 134]);
			expect(component.weeklyExternalData).toEqual([90, 95, 85, 100]);
			expect(component.weeklyActiveData).toEqual([200, 210, 195, 220]);
		});

		it('should calculate total users correctly from statistics data', () => {
			component.getUserStatistics();

			expect(component.totalUsers).toBe(487); // 120 + 132 + 101 + 134
			expect(component.totalExternalUsers).toBe(370); // 90 + 95 + 85 + 100
			expect(component.totalActiveUsers).toBe(825); // 200 + 210 + 195 + 220
		});

		it('should set growth data correctly from statistics', () => {
			component.getUserStatistics();

			expect(component.totalGrowthNumber).toBe('100');
			expect(component.totalGrowthRate).toBe('7.1%');
			expect(component.externalGrowthNumber).toBe('50');
			expect(component.externalGrowthRate).toBe('6.7%');
			expect(component.activeGrowthNumber).toBe('75');
			expect(component.activeGrowthRate).toBe('6.7%');
		});

		it('should handle empty user statistics response', () => {
			mockHomeDashboardService.getUserStatistics.and.returnValue(of([]));

			component.getUserStatistics();

			expect(component.weeks).toEqual([]);
			expect(component.weeklyTotalData).toEqual([]);
			expect(component.weeklyExternalData).toEqual([]);
			expect(component.weeklyActiveData).toEqual([]);
			expect(component.totalUsers).toBe(0);
			expect(component.totalExternalUsers).toBe(0);
			expect(component.totalActiveUsers).toBe(0);
		});

		it('should handle missing chart types in statistics response', () => {
			const incompleteData: UserStatisticsChart[] = [
				{
					id: '1',
					type: DashboardChartType.WEEKLY_TOTAL_NEW_USER,
					userNumber: '500',
					growingNumber: '25',
					growthRate: '5.3%',
					trendData: {
						weeks: ['week 1', 'week 2'],
						data: [250, 250],
					},
				},
			];
			mockHomeDashboardService.getUserStatistics.and.returnValue(of(incompleteData));

			component.getUserStatistics();

			expect(component.weeks).toEqual(['week 1', 'week 2']);
			expect(component.weeklyTotalData).toEqual([250, 250]);
			expect(component.weeklyExternalData).toEqual([]);
			expect(component.weeklyActiveData).toEqual([]);
			expect(component.totalUsers).toBe(500);
			expect(component.totalExternalUsers).toBe(0);
			expect(component.totalActiveUsers).toBe(0);
		});

		it('should handle null trend data in statistics response', () => {
			const dataWithNullTrend: UserStatisticsChart[] = [
				{
					id: '1',
					type: DashboardChartType.WEEKLY_TOTAL_NEW_USER,
					userNumber: '500',
					growingNumber: '25',
					growthRate: '5.3%',
					trendData: null as any,
				},
			];
			mockHomeDashboardService.getUserStatistics.and.returnValue(of(dataWithNullTrend));

			component.getUserStatistics();

			expect(component.weeks).toEqual([]);
			expect(component.weeklyTotalData).toEqual([]);
			expect(component.totalUsers).toBe(0);
		});

		it('should process user statistics data correctly', () => {
			component.getUserStatistics();

			// Verify the service was called and data was processed
			expect(mockHomeDashboardService.getUserStatistics).toHaveBeenCalled();
			expect(component.weeks).toEqual(['week 1', 'week 2', 'week 3', 'week 4']);
			expect(component.weeklyTotalData).toEqual([120, 132, 101, 134]);
			expect(component.totalUsers).toBe(487);
			expect(component.totalGrowthNumber).toBe('100');
		});
	});

	describe('Error Handling', () => {
		it('should handle getUserStatistics service call', () => {
			// Test that the service method is called
			component.getUserStatistics();
			expect(mockHomeDashboardService.getUserStatistics).toHaveBeenCalled();
		});

		it('should handle downloadActivityFile service call', () => {
			const activity = mockActivityStreamData[0];
			component.downloadActivityFile(activity);
			expect(mockHomeDashboardService.downloadFile).toHaveBeenCalledWith(activity.url);
		});

		it('should handle getActivityStream service call', () => {
			component.getActivityStream();
			expect(mockHomeDashboardService.getActivityStream).toHaveBeenCalled();
		});
	});

	describe('Activity Stream Management', () => {
		it('should get activity stream data successfully', () => {
			component.getActivityStream();

			expect(mockHomeDashboardService.getActivityStream).toHaveBeenCalled();
			expect(component.activityStreamData).toEqual(mockActivityStreamData);
		});

		it('should call service method for activity stream', () => {
			component.getActivityStream();

			expect(mockHomeDashboardService.getActivityStream).toHaveBeenCalled();
		});

		it('should download activity file successfully', () => {
			const mockBlob = new Blob(['test content'], { type: 'application/pdf' });
			mockHomeDashboardService.downloadFile.and.returnValue(of(mockBlob));

			const activity = mockActivityStreamData[0];
			component.downloadActivityFile(activity);

			expect(mockHomeDashboardService.downloadFile).toHaveBeenCalledWith(activity.url);
		});

		it('should call service method for download', () => {
			const activity = mockActivityStreamData[0];
			component.downloadActivityFile(activity);

			expect(mockHomeDashboardService.downloadFile).toHaveBeenCalledWith(activity.url);
		});

		it('should handle different file types for download', () => {
			const mockBlob = new Blob(['test content'], { type: 'application/json' });
			mockHomeDashboardService.downloadFile.and.returnValue(of(mockBlob));

			const activity: ActivityStream = {
				id: '3',
				date: '2025-01-03T10:00:00.000Z',
				url: 'test-data.json',
				fileName: 'test-data.json',
				message: 'JSON file download test',
			};

			component.downloadActivityFile(activity);

			expect(mockHomeDashboardService.downloadFile).toHaveBeenCalledWith('test-data.json');
		});

		it('should handle empty blob response', () => {
			const emptyBlob = new Blob([], { type: 'application/pdf' });
			mockHomeDashboardService.downloadFile.and.returnValue(of(emptyBlob));

			const activity = mockActivityStreamData[0];

			expect(() => component.downloadActivityFile(activity)).not.toThrow();
			expect(mockHomeDashboardService.downloadFile).toHaveBeenCalledWith(activity.url);
		});
	});

	describe('Data Cleaning Operations', () => {
		beforeEach(() => {
			spyOn(component, 'getActivityStream');
		});

		it('should clean system data successfully', () => {
			component.cleanSystemData();

			expect(mockHomeDashboardService.cleanSystemData).toHaveBeenCalled();
			expect(component.getActivityStream).toHaveBeenCalled();
		});

		it('should not refresh activity stream when system data cleaning fails', () => {
			mockHomeDashboardService.cleanSystemData.and.returnValue(of(false));

			component.cleanSystemData();

			expect(mockHomeDashboardService.cleanSystemData).toHaveBeenCalled();
			expect(component.getActivityStream).not.toHaveBeenCalled();
		});

		it('should clean activity stream successfully', () => {
			component.cleanActivityStream();

			expect(mockHomeDashboardService.cleanActivityStream).toHaveBeenCalled();
			expect(component.getActivityStream).toHaveBeenCalled();
		});

		it('should not refresh activity stream when activity stream cleaning fails', () => {
			mockHomeDashboardService.cleanActivityStream.and.returnValue(of(false));

			component.cleanActivityStream();

			expect(mockHomeDashboardService.cleanActivityStream).toHaveBeenCalled();
			expect(component.getActivityStream).not.toHaveBeenCalled();
		});

		it('should call service methods for cleaning operations', () => {
			component.cleanSystemData();
			component.cleanActivityStream();

			expect(mockHomeDashboardService.cleanSystemData).toHaveBeenCalled();
			expect(mockHomeDashboardService.cleanActivityStream).toHaveBeenCalled();
		});
	});

	describe('Confirm Dialog Operations', () => {
		it('should open confirm dialog for system data cleaning', () => {
			const mockDialogRef = {
				afterClosed: () => of(true),
			};
			mockDialog.open.and.returnValue(mockDialogRef as any);
			spyOn(component, 'cleanSystemData');

			component.openConfirmDialog('system.title', 'system.content');

			expect(mockDialog.open).toHaveBeenCalledWith(ConfirmDialogComponent, {
				width: '400px',
				data: {
					icon: 'cleaning_services',
					ok: 'Translated Text',
					title: 'Translated Text',
					content: 'Translated Text',
				},
			});
			expect(component.cleanSystemData).toHaveBeenCalled();
		});

		it('should open confirm dialog for activity stream cleaning', () => {
			const mockDialogRef = {
				afterClosed: () => of(true),
			};
			mockDialog.open.and.returnValue(mockDialogRef as any);
			spyOn(component, 'cleanActivityStream');

			component.openConfirmDialog('activity.title', 'activity.content');

			expect(mockDialog.open).toHaveBeenCalledWith(ConfirmDialogComponent, {
				width: '400px',
				data: {
					icon: 'cleaning_services',
					ok: 'Translated Text',
					title: 'Translated Text',
					content: 'Translated Text',
				},
			});
			expect(component.cleanActivityStream).toHaveBeenCalled();
		});

		it('should not perform action when dialog is cancelled', () => {
			const mockDialogRef = {
				afterClosed: () => of(false),
			};
			mockDialog.open.and.returnValue(mockDialogRef as any);
			spyOn(component, 'cleanSystemData');
			spyOn(component, 'cleanActivityStream');

			component.openConfirmDialog('system.title', 'system.content');

			expect(component.cleanSystemData).not.toHaveBeenCalled();
			expect(component.cleanActivityStream).not.toHaveBeenCalled();
		});

		it('should handle undefined dialog result', () => {
			const mockDialogRef = {
				afterClosed: () => of(undefined),
			};
			mockDialog.open.and.returnValue(mockDialogRef as any);
			spyOn(component, 'cleanSystemData');
			spyOn(component, 'cleanActivityStream');

			component.openConfirmDialog('system.title', 'system.content');

			expect(component.cleanSystemData).not.toHaveBeenCalled();
			expect(component.cleanActivityStream).not.toHaveBeenCalled();
		});

		it('should call correct translation keys', () => {
			const mockDialogRef = {
				afterClosed: () => of(true),
			};
			mockDialog.open.and.returnValue(mockDialogRef as any);
			spyOn(component, 'cleanSystemData');

			component.openConfirmDialog('system.title', 'system.content');

			expect(mockTranslateService.instant).toHaveBeenCalledWith('home.dashboard.activity.clean');
			expect(mockTranslateService.instant).toHaveBeenCalledWith('system.title');
			expect(mockTranslateService.instant).toHaveBeenCalledWith('system.content');
		});

		it('should determine action based on title content', () => {
			const mockDialogRef = {
				afterClosed: () => of(true),
			};
			mockDialog.open.and.returnValue(mockDialogRef as any);
			spyOn(component, 'cleanSystemData');
			spyOn(component, 'cleanActivityStream');

			// Test system cleaning
			component.openConfirmDialog('system.housekeeping.title', 'system.content');
			expect(component.cleanSystemData).toHaveBeenCalled();
			expect(component.cleanActivityStream).not.toHaveBeenCalled();

			// Reset spies
			(component.cleanSystemData as jasmine.Spy).calls.reset();
			(component.cleanActivityStream as jasmine.Spy).calls.reset();

			// Test activity cleaning
			component.openConfirmDialog('activity.housekeeping.title', 'activity.content');
			expect(component.cleanActivityStream).toHaveBeenCalled();
			expect(component.cleanSystemData).not.toHaveBeenCalled();
		});
	});

	describe('Lifecycle Hooks', () => {
		it('should start auto scroll after view init', fakeAsync(() => {
			spyOn(component, 'startAutoScroll' as any);

			component.ngAfterViewInit();
			tick(1000);

			expect(component['startAutoScroll']).toHaveBeenCalled();
		}));

		it('should stop auto scroll on destroy', () => {
			spyOn(component, 'stopAutoScroll' as any);

			component.ngOnDestroy();

			expect(component['stopAutoScroll']).toHaveBeenCalled();
		});
	});

	describe('Auto Scroll Functionality', () => {
		let mockScrollContainer: jasmine.SpyObj<HTMLDivElement>;
		let mockScrollContent: jasmine.SpyObj<HTMLDivElement>;

		beforeEach(() => {
			mockScrollContainer = {
				clientHeight: 300,
				scrollTop: 0,
			} as any;
			mockScrollContent = {
				scrollHeight: 600,
			} as any;

			component.scrollContainer = {
				nativeElement: mockScrollContainer,
			} as ElementRef<HTMLDivElement>;

			component.scrollContent = {
				nativeElement: mockScrollContent,
			} as ElementRef<HTMLDivElement>;
		});

		it('should start auto scroll when containers are available', () => {
			spyOn(window, 'setInterval').and.returnValue(123 as any);

			component['startAutoScroll']();

			expect(window.setInterval).toHaveBeenCalledWith(jasmine.any(Function), 50);
			expect(component['scrollInterval']).toBe(123);
		});

		it('should not start auto scroll when containers are not available', () => {
			component.scrollContainer = undefined as any;
			spyOn(window, 'setInterval');

			component['startAutoScroll']();

			expect(window.setInterval).not.toHaveBeenCalled();
		});

		it('should stop auto scroll and clear interval', () => {
			component['scrollInterval'] = 123;
			spyOn(window, 'clearInterval');

			component['stopAutoScroll']();

			expect(window.clearInterval).toHaveBeenCalledWith(123);
			expect(component['scrollInterval']).toBeNull();
		});

		it('should perform scroll down when at top', () => {
			mockScrollContainer.scrollTop = 0;
			component['scrollDirection'] = 1;

			component['performScroll']();

			expect(mockScrollContainer.scrollTop).toBe(1); // scrollSpeed = 1
		});

		it('should change direction when reaching bottom', () => {
			mockScrollContainer.scrollTop = 299; // near bottom
			component['scrollDirection'] = 1;

			component['performScroll']();

			expect(component['scrollDirection']).toBe(-1);
			expect(mockScrollContainer.scrollTop).toBe(300); // contentHeight - containerHeight
		});

		it('should change direction when reaching top', () => {
			mockScrollContainer.scrollTop = 1;
			component['scrollDirection'] = -1;

			component['performScroll']();

			expect(component['scrollDirection']).toBe(1);
			expect(mockScrollContainer.scrollTop).toBe(0);
		});

		it('should not perform scroll when containers are not available', () => {
			component.scrollContainer = undefined as any;

			expect(() => component['performScroll']()).not.toThrow();
		});
	});

	describe('Component Properties', () => {
		it('should have correct initial values', () => {
			expect(component.lineChartType).toBe('line');
			expect(component.totalUsers).toBe(0);
			expect(component.totalExternalUsers).toBe(0);
			expect(component.totalActiveUsers).toBe(0);
			expect(component.activityStreamData).toEqual([]);
		});

		it('should have correct initial growth data values', () => {
			expect(component.totalGrowthNumber).toBe('');
			expect(component.totalGrowthRate).toBe('');
			expect(component.externalGrowthNumber).toBe('');
			expect(component.externalGrowthRate).toBe('');
			expect(component.activeGrowthNumber).toBe('');
			expect(component.activeGrowthRate).toBe('');
		});

		it('should have correct initial chart data arrays', () => {
			expect(component.weeks).toEqual([]);
			expect(component.weeklyTotalData).toEqual([]);
			expect(component.weeklyExternalData).toEqual([]);
			expect(component.weeklyActiveData).toEqual([]);
		});

		it('should have correct initial gradient values', () => {
			expect(component.fillGradientTotal).toBeUndefined();
			expect(component.fillGradientExternal).toBeUndefined();
			expect(component.fillGradientActive).toBeUndefined();
		});

		it('should have correct scroll configuration', () => {
			expect(component['scrollSpeed']).toBe(1);
			expect(component['scrollDirection']).toBe(1);
			expect(component['scrollInterval']).toBeUndefined();
		});
	});

	describe('Component Integration', () => {
		it('should have correct component type', () => {
			expect(component.lineChartType).toBe('line');
		});

		it('should extend RolesAwareComponent', () => {
			expect(component).toBeInstanceOf(HomeDashboardComponent);
		});

		it('should have all required methods', () => {
			expect(typeof component.ngOnInit).toBe('function');
			expect(typeof component.ngAfterViewInit).toBe('function');
			expect(typeof component.ngOnDestroy).toBe('function');
			expect(typeof component.getLineChartData).toBe('function');
			expect(typeof component.getLineChartOptions).toBe('function');
			expect(typeof component.getActivityStream).toBe('function');
			expect(typeof component.downloadActivityFile).toBe('function');
			expect(typeof component.cleanSystemData).toBe('function');
			expect(typeof component.cleanActivityStream).toBe('function');
			expect(typeof component.openConfirmDialog).toBe('function');
			expect(typeof component.getUserStatistics).toBe('function');
		});

		it('should have all required private methods', () => {
			expect(typeof component['initChart']).toBe('function');
			expect(typeof component['startAutoScroll']).toBe('function');
			expect(typeof component['stopAutoScroll']).toBe('function');
			expect(typeof component['performScroll']).toBe('function');
		});

		it('should properly initialize with all dependencies', () => {
			expect(component['translateService']).toBeDefined();
			expect(component['homeDashboardService']).toBeDefined();
			expect(component['cdr']).toBeDefined();
			expect(component['dialog']).toBeDefined();
		});

		it('should handle complete component lifecycle', () => {
			spyOn(component, 'getUserStatistics');
			spyOn(component, 'getActivityStream');
			spyOn(component, 'startAutoScroll' as any);
			spyOn(component, 'stopAutoScroll' as any);

			// Initialize component
			component.ngOnInit();
			expect(component.getUserStatistics).toHaveBeenCalled();
			expect(component.getActivityStream).toHaveBeenCalled();

			// After view init
			component.ngAfterViewInit();
			// startAutoScroll is called after timeout, so we can't test it directly here

			// Destroy component
			component.ngOnDestroy();
			expect(component['stopAutoScroll']).toHaveBeenCalled();
		});

		it('should handle data flow from service to component', () => {
			// Trigger data loading
			component.getUserStatistics();
			component.getActivityStream();

			// Verify data is set correctly
			expect(component.weeks).toEqual(['week 1', 'week 2', 'week 3', 'week 4']);
			expect(component.weeklyTotalData).toEqual([120, 132, 101, 134]);
			expect(component.activityStreamData).toEqual(mockActivityStreamData);

			// Verify services were called
			expect(mockHomeDashboardService.getUserStatistics).toHaveBeenCalled();
			expect(mockHomeDashboardService.getActivityStream).toHaveBeenCalled();
		});
	});
});
