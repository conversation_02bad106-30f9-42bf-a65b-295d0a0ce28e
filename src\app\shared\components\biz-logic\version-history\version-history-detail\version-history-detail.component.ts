import { Component, Inject, Input, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogContent, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import {
	OperationObj,
	RequestStatusChangeAction,
	VersionDialogData,
	VersionHistoryDetailObj,
} from '@shared/models/biz-logic/version-history.model';
import { ShareType } from '@shared/models/share-type.model';
import { VersionHistoryService } from '@shared/services/biz-logic/verion-history/version-history.service';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';

@Component({
	selector: 'orll-version-history-detail',
	imports: [
		MatDialogContent,
		MatDialogModule,
		TranslateModule,
		MatIconModule,
		MatButtonModule,
		MatTableModule,
		MatDialogModule,
		IataDateFormatPipe,
	],
	templateUrl: './version-history-detail.component.html',
	styleUrl: './version-history-detail.component.scss',
})
export class VersionHistoryDetailComponent implements OnInit {
	@Input() type!: ShareType;

	dataLoading = false;

	dataSource = new MatTableDataSource<OperationObj>([]);
	detail: VersionHistoryDetailObj | null = null;
	displayedColumns = ['loType', 'property', 'oldValue', 'newValue'];

	statusChangeAction = RequestStatusChangeAction;

	constructor(
		private readonly dialogRef: MatDialogRef<VersionHistoryDetailComponent>,
		private readonly service: VersionHistoryService,
		private readonly dialog: MatDialog,
		private readonly translateService: TranslateService,
		@Inject(MAT_DIALOG_DATA) public data: VersionDialogData
	) {}

	ngOnInit(): void {
		this.service.getVersionHistoryDetail(this.data.actionRequestUri).subscribe({
			next: (res) => {
				this.detail = res;
				this.dataSource.data = res.hasOperation;
			},
			error: (error) => {
				if (error.status === 403) {
					const dialogRef = this.dialog.open(ConfirmDialogComponent, {
						width: '300px',
						data: { content: this.translateService.instant('common.change.request.no.permission') },
					});
					dialogRef.afterClosed().subscribe(() => {
						this.dialogRef.close();
					});
				}
			},
		});
	}

	updateRequestStatus(action: RequestStatusChangeAction) {
		if (this.data) {
			this.dataLoading = true;
			this.service.updateRequestStatus(this.data.actionRequestUri, action).subscribe(() => {
				this.dataLoading = false;
				this.dialogRef.close(true);
			});
		}
	}

	// eslint-disable-next-line @typescript-eslint/naming-convention
	trackById(_index: number, row: OperationObj) {
		return row.id;
	}
}
