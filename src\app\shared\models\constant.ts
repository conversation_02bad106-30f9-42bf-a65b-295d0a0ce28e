export const REGX_NUMBER_1_DECIMAL = /^([1-9]\d*(\.\d)?|0\.[1-9])$/;
export const REGX_NUMBER_2_DECIMAL = /^([1-9]\d*(\.\d{1,2})?|0\.(?!0+$)\d{1,2})$/;
export const SLI_TAB_INDEX = 0;
export const DATE_TIME_FORMAT = 'yyyy-MM-dd HH:mm:ss';
export const REGX_POSITIVE_NUMBER = '^[1-9]\\d*$';
export const TIME_FORMAT = 'HH:mm';
export const INPUT_DATE_TIME_FORMAT = 'MM/dd/yyyy HH:mm';
export const DEFAULT_WEIGHT_UNIT = 'KG';
export const DATE_TIME_MINS_FORMAT = 'yyyy-MM-dd HH:mm';
