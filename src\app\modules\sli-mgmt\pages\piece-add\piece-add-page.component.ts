import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { SliPieceFormComponent } from '../../components/sli-piece-form/sli-piece-form.component';
import { SliPieceItemComponent } from '../../components/sli-piece-item/sli-piece-item.component';
import { DgPieceAddComponent } from '../dg-piece-add/dg-piece-add.component';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { LiveAnimalPieceAddComponent } from '../live-animal-piece-add/live-animal-piece-add.component';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { Piece } from '../../models/piece/piece.model';
import { map, Observable } from 'rxjs';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { NotificationService } from '@shared/services/notification.service';
import { PieceType } from '../../models/piece/piece-type.model';
import { isBlank } from '@shared/utils/type.utils';
import { EventService } from '@shared/services/event/event-service.service';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';

const REGX_POSITIVE_NUMBER = '^[1-9]\\d*$';
//piece tab
const TAB_IDX = 1;

@Component({
	selector: 'orll-piece-add-page',
	templateUrl: './piece-add-page.component.html',
	styleUrl: './piece-add-page.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatInputModule,
		MatButtonModule,
		MatIconModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		SliPieceFormComponent,
		SliPieceItemComponent,
		DgPieceAddComponent,
		LiveAnimalPieceAddComponent,
		SpinnerComponent,
	],
})
export default class PieceAddPageComponent extends DestroyRefComponent implements OnInit {
	@Input({ required: true })
	pieceType!: string;
	@Input() pieceId = '';
	@Input() sliNumber = '';

	@ViewChild(SliPieceFormComponent) gelPiece!: SliPieceFormComponent;
	@ViewChild(SliPieceItemComponent) pieceItemWrapper!: SliPieceItemComponent;

	@ViewChild(DgPieceAddComponent, { static: false })
	dgPieceComponent: DgPieceAddComponent | null = null;

	@ViewChild(LiveAnimalPieceAddComponent, { static: false })
	liveAnimalPieceComponent: LiveAnimalPieceAddComponent | null = null;

	pieceQuantity = new FormControl<number>(1, [Validators.required, Validators.pattern(REGX_POSITIVE_NUMBER)]);
	dataLoading = false;
	isSaved = false;
	isConfirmed = false;
	piece: Piece | null = null;

	constructor(
		private readonly router: Router,
		private readonly dialog: MatDialog,
		private readonly cdr: ChangeDetectorRef,
		private readonly translate: TranslateService,
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly notificationService: NotificationService,
		private readonly eventService: EventService
	) {
		super();
	}

	ngOnInit(): void {
		if (this.pieceId) this.getPieceDetail(this.pieceId);
	}

	getPieceDetail(pieceId: string): void {
		this.sliCreateRequestService
			.getPieceDetail(pieceId)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					if (res) {
						this.piece = res;
						this.pieceQuantity.patchValue(res?.pieceQuantity ?? 0);
						this.cdr.markForCheck();
					}
				},
			});
	}

	canDeactivate(): boolean | Observable<boolean> {
		if (this.isSaved) return true;

		if (!this.isConfirmed) {
			const dialogRef = this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.cancel.content'),
				},
			});

			return dialogRef.afterClosed().pipe(
				map((confirmed) => {
					if (confirmed) {
						this.isConfirmed = true;
						this.gotoPieceTab();
						return true;
					}
					return false;
				})
			);
		} else {
			return true;
		}
	}

	onCancel(): void {
		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translate.instant('common.dialog.cancel.content'),
			},
		});

		dialogRef.afterClosed().subscribe((confirmed) => {
			if (confirmed) {
				this.isConfirmed = true;
				this.gotoPieceTab();
			}
		});
	}

	private pieceDetailRequest(piecePayload: Piece): Observable<string> {
		if (!this.pieceId) {
			return this.sliCreateRequestService.createPiece(piecePayload);
		} else {
			return this.sliCreateRequestService.updatePiece(piecePayload, this.pieceId);
		}
	}

	private pieceDetailDgRequest(piecePayload: Piece): Observable<string> {
		if (!this.pieceId) {
			return this.sliCreateRequestService.createDgPiece(piecePayload);
		} else {
			return this.sliCreateRequestService.updateDgPiece(piecePayload, this.pieceId);
		}
	}

	private pieceDetailLaRequest(piecePayload: Piece): Observable<string> {
		if (!this.pieceId) {
			return this.sliCreateRequestService.createLaPiece(piecePayload);
		} else {
			return this.sliCreateRequestService.updateLaPiece(piecePayload, this.pieceId);
		}
	}

	onDone(): void {
		if (this.pieceType === 'general') {
			this.addOrUpdatePiece();
		} else if (this.pieceType === 'dg') {
			this.addOrUpdateDgPiece();
		} else if (this.pieceType === 'la') {
			this.addOrUpdateLiveAnimalPiece();
		}
	}

	private addOrUpdatePiece() {
		this.gelPiece.sliPieceForm?.markAllAsTouched();
		this.pieceQuantity?.markAsTouched();
		const pieceFormData: Record<string, any> | null = this.gelPiece.getFormData();
		const containedItems = this.pieceItemWrapper.getPieceItemList();
		const containedPieces = this.pieceItemWrapper.getPieceInList();
		if (!pieceFormData || this.pieceQuantity.invalid) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.form.validate'),
				},
			});

			return;
		}

		const piecePayload = {
			...pieceFormData,
			slac: containedPieces.length,
			pieceQuantity: this.pieceQuantity.value,
			containedItems,
			containedPieces,
		} as Piece;

		if (this.sliNumber) {
			piecePayload.sliNumber = this.sliNumber;
		}

		this.dataLoading = true;
		this.pieceDetailRequest(piecePayload)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: () => {
					this.dataLoading = false;
					this.isSaved = true;
					this.cdr.markForCheck();
					//publish instruction to sli component
					this.publishHandlingMsg(pieceFormData['textualHandlingInstructions']);
					this.gotoPieceTab();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}

	private publishHandlingMsg(msg: string | undefined) {
		if (msg) {
			this.eventService.publicPicesInstructionChange(msg);
		}
	}

	private addOrUpdateDgPiece() {
		if (!this.dgPieceComponent) {
			this.notificationService.showError('DG piece component not found');
			return;
		}

		this.dgPieceComponent.sliDgPieceForm.markAllAsTouched();

		if (this.dgPieceComponent.sliDgPieceForm.invalid) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.form.validate'),
				},
			});
			return;
		}

		const {
			productDescription,
			typeOfPackage,
			packagedIdentifier,
			whetherHaveDeclaredValueForCustoms,
			whetherHaveDeclaredValueForCarriage,
			specialProvisionId,
			explosiveCompatibilityGroupCode,
			packagingDangerLevelCode,
			technicalName,
			unNumber,
			shippersDeclaration,
			handlingInformation,
			allPackedInOne,
			qValueNumeric,
			upid,
			shippingMarks,
			grossWeight,
			dimensions,
			hsCommodityDescription,
			properShippingName,
			textualHandlingInstructions,
			hazardClassificationId,
			additionalHazardClassificationId,
			packingInstructionNumber,
			complianceDeclaration,
			exclusiveUseIndicator,
			authorizationInformation,
			aircraftLimitationInformation,
		} = this.dgPieceComponent.sliDgPieceForm.value;

		const dgPieceItems = this.dgPieceComponent.dgPieceItems;
		const payload: any = {
			type: PieceType.DANGEROUS_GOODS,
			packagingType: {
				typeCode: typeOfPackage?.typeCode,
				description: typeOfPackage?.description,
			},
			nvdForCustoms: whetherHaveDeclaredValueForCustoms,
			nvdForCarriage: whetherHaveDeclaredValueForCarriage,
			grossWeight: !isBlank(grossWeight) ? Number(grossWeight) : null,
			upid: upid,
			dimensions: {
				length: !isBlank(dimensions?.length) ? Number(dimensions?.length) : null,
				width: !isBlank(dimensions?.width) ? Number(dimensions?.width) : null,
				height: !isBlank(dimensions?.height) ? Number(dimensions?.height) : null,
			},
			packagedIdentifier,
			textualHandlingInstructions,
			shippingMarks,
			pieceQuantity: this.pieceQuantity.value,
			sliNumber: this.sliNumber,
			slac: 0,
			product: {
				specialProvisionId,
				explosiveCompatibilityGroupCode,
				packagingDangerLevelCode,
				technicalName,
				unNumber,
				description: productDescription,
				hsCommodityDescription,
				properShippingName,
				hazardClassificationId,
				additionalHazardClassificationId,
				packingInstructionNumber,
				authorizationInformation,
			},
			allPackedInOneIndicator: allPackedInOne,
			dgDeclaration: {
				shipperDeclarationText: shippersDeclaration,
				handlingInformation,
				complianceDeclarationText: complianceDeclaration,
				exclusiveUseIndicator,
				aircraftLimitationInformation,
			},
			containedItems: dgPieceItems,
			qvalueNumeric: +(qValueNumeric ?? 0),
		};
		if (this.pieceId && this.pieceId !== this.sliNumber) {
			payload.id = this.pieceId;
		}
		this.dataLoading = true;
		this.pieceDetailDgRequest(payload)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: () => {
					this.dataLoading = false;
					this.isSaved = true;
					this.cdr.markForCheck();
					this.publishHandlingMsg(textualHandlingInstructions);
					this.gotoPieceTab();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}

	private gotoPieceTab() {
		if (this.sliNumber) {
			this.router.navigate(['sli/edit', this.sliNumber, TAB_IDX]);
		} else {
			this.router.navigate(['sli/create', TAB_IDX]);
		}
	}

	private addOrUpdateLiveAnimalPiece() {
		if (!this.liveAnimalPieceComponent) {
			this.notificationService.showError('live animal piece component not found');
			return;
		}

		this.liveAnimalPieceComponent.sliLiveAnimalPieceForm.markAllAsTouched();

		if (this.liveAnimalPieceComponent.sliLiveAnimalPieceForm.invalid) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.form.validate'),
				},
			});
			return;
		}

		const {
			productDescription,
			typeOfPackage,
			packagedIdentifier,
			speciesCommonName,
			speciesScientificName,
			specimenDescription,
			animalQuantity,
			shippingMarks,
			upid,
			grossWeight,
			dimensions,
			whetherHaveDeclaredValueForCustoms,
			whetherHaveDeclaredValueForCarriage,
			textualHandlingInstructions,
		} = this.liveAnimalPieceComponent.sliLiveAnimalPieceForm.value;

		const payload: any = {
			type: PieceType.LIVE_ANIMALS,
			packagingType: {
				typeCode: typeOfPackage?.typeCode,
				description: typeOfPackage?.description,
			},
			nvdForCustoms: whetherHaveDeclaredValueForCustoms,
			nvdForCarriage: whetherHaveDeclaredValueForCarriage,
			grossWeight: +(grossWeight ?? 0),
			upid: upid,
			dimensions: {
				length: !isBlank(dimensions?.length) ? Number(dimensions?.length) : null,
				width: !isBlank(dimensions?.width) ? Number(dimensions?.width) : null,
				height: !isBlank(dimensions?.height) ? Number(dimensions?.height) : null,
			},
			packagedIdentifier,
			textualHandlingInstructions,
			shippingMarks: shippingMarks,
			pieceQuantity: +(this.pieceQuantity.value ?? 0),
			sliNumber: this.sliNumber,
			slac: 0,
			product: {
				description: productDescription,
				hsCommodityDescription: '',
			},
			quantityAnimals: animalQuantity,
			speciesCommonName,
			speciesScientificName,
			specimenDescription,
		};
		if (this.pieceId && this.pieceId !== this.sliNumber) {
			payload.id = this.pieceId;
		}
		this.dataLoading = true;
		this.pieceDetailLaRequest(payload)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: () => {
					this.dataLoading = false;
					this.isSaved = true;
					this.cdr.markForCheck();
					this.publishHandlingMsg(textualHandlingInstructions);
					this.gotoPieceTab();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}
}
