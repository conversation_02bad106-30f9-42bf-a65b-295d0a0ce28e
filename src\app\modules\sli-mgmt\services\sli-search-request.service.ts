import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { AIRPORTS } from '../ref-data/airports.data';
import { Airport } from '../models/airport.model';
import { SliListObject } from '../models/sli-list-object.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { AbstractAutocompleteService } from '@shared/models/autocomplete.model';
import { CodeName } from '@shared/models/code-name.model';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { Organization } from '../models/organization.model';
import { SliSearchPayload } from '../models/sli-search-payload.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';

@Injectable({ providedIn: 'root' })
export class SliSearchRequestService extends ApiService implements AbstractAutocompleteService<CodeName> {
	constructor(http: HttpClient) {
		super(http);
	}

	getSliList(pageParams: PaginationRequest, sliSearchPayload: SliSearchPayload): Observable<PaginationResponse<SliListObject>> {
		return super.getData<PaginationResponse<SliListObject>>('sli', {
			...pageParams,
			...sliSearchPayload,
		});
	}

	getSharedSliList(pageParams: PaginationRequest, sliSearchPayload: SliSearchPayload): Observable<PaginationResponse<SliListObject>> {
		return super.getData<PaginationResponse<SliListObject>>('sli/share/list', {
			...pageParams,
			...sliSearchPayload,
		});
	}

	getOptions(keyword: string, id?: string): Observable<CodeName[]> {
		if (id === 'shipper') {
			return super.getData<Organization[]>('sli/listSliOrgName', { orgType: 'SHP' }).pipe(
				map((res) => {
					if (res && Array.isArray(res)) {
						return res
							.filter((shipper: Organization) => shipper.name.toLowerCase().includes(keyword.toLowerCase().trim()))
							.map((shipper: Organization) => {
								return { code: shipper.name, name: shipper.name };
							});
					} else {
						return [];
					}
				})
			);
		} else if (id === 'consignee') {
			return super.getData<Organization[]>('sli/listSliOrgName', { orgType: 'CNE' }).pipe(
				map((res) => {
					if (res && Array.isArray(res)) {
						return res
							.filter((consignee: Organization) => consignee.name.toLowerCase().includes(keyword.toLowerCase().trim()))
							.map((consignee: Organization) => {
								return { code: consignee.name, name: consignee.name };
							});
					} else {
						return [];
					}
				})
			);
		} else if (id === 'orglist') {
			return super.getData<Organization[]>('org/org/list', { orgType: '' }).pipe(
				map((res) => {
					if (res && Array.isArray(res)) {
						return res
							.filter((org: Organization) => org.name.toLowerCase().includes(keyword.toLowerCase().trim()))
							.map((org: Organization) => {
								return { code: org.id, name: org.name };
							});
					} else {
						return [];
					}
				})
			);
		} else if (id === 'airport') {
			return of(AIRPORTS).pipe(
				map((airports: Airport[]) => {
					if (airports && Array.isArray(airports)) {
						return airports
							.filter((airport: Airport) => airport.code.toLowerCase().includes(keyword.toLowerCase().trim()))
							.map((airport: Airport) => {
								return { code: airport.code, name: airport.name };
							});
					} else {
						return [];
					}
				})
			);
		} else if (id === 'sliCode') {
			return super.getData<{ sliCodeList: string[] }>('sli/listQueryParam').pipe(
				map((res) => {
					if (res.sliCodeList && Array.isArray(res.sliCodeList)) {
						return res.sliCodeList
							.filter((sliCode: string) => sliCode.toLowerCase().includes(keyword.toLowerCase().trim()))
							.map((sliCode: string) => {
								return { code: sliCode, name: sliCode };
							});
					} else {
						return [];
					}
				})
			);
		} else if (id === 'hawbNumber') {
			return super.getData<{ hawbNumber: string[] }>('sli/listQueryParam').pipe(
				map((res) => {
					if (res.hawbNumber && Array.isArray(res.hawbNumber)) {
						return res.hawbNumber
							.filter((hawbNumber: string) => hawbNumber.toLowerCase().includes(keyword.toLowerCase().trim()))
							.map((hawbNumber: string) => {
								return { code: hawbNumber, name: hawbNumber };
							});
					} else {
						return [];
					}
				})
			);
		} else {
			return of([]);
		}
	}
}
