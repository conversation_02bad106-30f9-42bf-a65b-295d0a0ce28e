$first-item-fixed-width: 435px;

$second-item-fixed-width: 400px;
$third-item-fixed-width: 400px;
$fourth-item-fixed-width: 400px;


.col-lastest-status {
	width: $second-item-fixed-width;
}

.col-update-date {
	width: $third-item-fixed-width;
}

.col-update-by {
	width: $fourth-item-fixed-width;
}


.mawb-row {
	display: flex;

	.col-mawb-fixed {
		width: $first-item-fixed-width;
	}

}

.hawb-row {
	display: flex;

	.col-hawb-fixed {
		width: $first-item-fixed-width - 20px;
	}
}

.piece-level1-wrapper {
	background: rgb(237, 238, 242);
	margin-left: 50px;
	margin-right: 50px;
	border-radius: 6px;
}

.piece-level1-row {
	display: flex;
	border-bottom: 1px solid var(--iata-grey-200);

	.col-piece-l1-fixed {
		width: $first-item-fixed-width - 72px;
	}
}

.piece-level2-row {
	background: rgb(220, 221, 224);
	padding-left: 25px;
	display: flex;

	.col-piece-l2-fixed {
		width: $first-item-fixed-width - 95px;
	}
}


.item {
	padding: 8px 0;
}

.h-full {
	height: 100%;
}

.col-mawb-fixed {
	width: 423px;
}

.col-hawb-fixed {
	width: 420px;
	padding-left: 15px;
	padding-right: 15px;
}

.mawb-header-item > :first-child {
	margin-left: 8px;
}

.bordered {
	border: 1px solid var(--iata-grey-200);
}

.rounded {
	border-radius: 4px;
}

.mt-8 {
	margin-top: 1.5rem;
}

.ml-6 {
	margin-left: 1rem;
}

.mb-40 {
	margin-bottom: 40px;
}

.relative {
	position: relative;
}

.absolute {
	position: absolute;
}

.icon-panel-toggle {
	color: var(--iata-blue-primary);
	right: 34px;
	top: 16px;
}

.ml-2 {
	margin-left: 8px;
}

