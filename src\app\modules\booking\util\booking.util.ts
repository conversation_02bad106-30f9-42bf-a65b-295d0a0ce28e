import { CodeName } from '@shared/models/code-name.model';
import { BookingOptionRequestDetailObj, OptionRequestDetail } from '../models/booking.model';

export function buildOptionRequestDetail(res: BookingOptionRequestDetailObj, commodityCodeList: CodeName[]) {
	const result: OptionRequestDetail = {
		totalGrossWeight: res?.bookingShipmentDetails?.totalGrossWeight?.numericalValue?.toString() ?? '',
		chargeableWeight: res?.bookingShipmentDetails?.chargeableWeight?.numericalValue?.toString() ?? '',
		dimLength: res?.bookingShipmentDetails?.dimensions?.length?.toString() ?? '',
		dimWidth: res?.bookingShipmentDetails?.dimensions?.width?.toString() ?? '',
		dimHeight: res?.bookingShipmentDetails?.dimensions?.height?.toString() ?? '',
		pieceGroupCount: res?.bookingShipmentDetails?.pieceGroups?.pieceGroupCount?.toString() ?? '',
		expectedCommodity: '',
		specialHandlingCodes: '',
		textualHandlingInstructions: res?.bookingShipmentDetails?.textualHandlingInstructions ?? '',
		departureLocation: '',
		arrivalLocation: '',
		maxSegments: res?.bookingPreference?.length > 0 ? res?.bookingPreference[0]?.maxSegments?.toString() : '',
		preferredTransportId: res?.bookingPreference?.length > 0 ? res?.bookingPreference[0]?.preferredTransportId : '',
		earliestAcceptanceTime: transToIsoDate(res?.timePreferences?.earliestAcceptanceTime ?? ''),
		latestAcceptanceTime: transToIsoDate(res?.timePreferences?.latestAcceptanceTime ?? ''),
		latestArrivalTime: transToIsoDate(res?.timePreferences?.latestArrivalTime ?? ''),
		timeOfAvailability: transToIsoDate(res?.timePreferences?.timeOfAvailability ?? ''),
		currency: res?.unitsPreference.currency?.currencyUnit ?? '',
		iataCargoAgentCode: '',
	};
	if (res?.transportLegs?.length > 0) {
		result.departureLocation = res.transportLegs[0].departureLocation?.locationName ?? '';
		result.arrivalLocation = res.transportLegs[0].arrivalLocation?.locationName ?? '';
	}
	if (res?.bookingShipmentDetails?.expectedCommodity) {
		result.expectedCommodity =
			commodityCodeList.find((item) => item.code === res?.bookingShipmentDetails?.expectedCommodity)?.name ?? '';
	}
	if (res?.bookingShipmentDetails?.specialHandlingCodes) {
		result.specialHandlingCodes = res?.bookingShipmentDetails?.specialHandlingCodes.filter((d) => d !== '').join(';');
	}
	if (res.involvedParties?.length > 0) {
		result.iataCargoAgentCode = res.involvedParties[0]?.iataCargoAgentCode ?? '';
		result.companyName = res.involvedParties[0]?.companyName ?? '';
	}
	return result;
}

function transToIsoDate(timeStr: string) {
	if (timeStr) {
		return new Date(timeStr).toISOString();
	}
	return '';
}
