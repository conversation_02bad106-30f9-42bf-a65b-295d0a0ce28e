import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { OptionRequestDetail } from '../../models/booking.model';
import { TranslateModule } from '@ngx-translate/core';
import { MatDividerModule } from '@angular/material/divider';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { UserRole } from '@shared/models/user-role.model';
import { AsyncPipe } from '@angular/common';

@Component({
	selector: 'orll-booking-option-request-detail',
	templateUrl: './booking-option-request-detail.component.html',
	styleUrl: './booking-option-request-detail.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [TranslateModule, MatDividerModule, IataDateFormatPipe, AsyncPipe],
})
export class BookingOptionRequestDetailComponent extends RolesAwareComponent implements OnInit {
	@Input() quote = false;
	@Input() detailInfo!: OptionRequestDetail;

	forwarder = [UserRole.FORWARDER];
	carrier = [UserRole.CARRIER];
	currentOrgName = '';

	ngOnInit(): void {
		this.getCurrentUser().subscribe((res) => {
			this.currentOrgName = res?.primaryOrgName ?? '';
		});
	}
}
