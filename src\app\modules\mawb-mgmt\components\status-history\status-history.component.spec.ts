import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, throwError, Subject } from 'rxjs';
import { StatusHistoryComponent } from './status-history.component';
import { MawbStatusService } from '../../services/mawb-status.service';
import { HistoryDialogData, StatusHistory } from '../../models/mawb-event.model';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';

describe('StatusHistoryComponent', () => {
	let component: StatusHistoryComponent;
	let fixture: ComponentFixture<StatusHistoryComponent>;
	let statusService: jasmine.SpyObj<MawbStatusService>;
	const mockDialogRef = jasmine.createSpyObj(['close']);

	const mockDialogData: HistoryDialogData = {
		loId: '123',
		type: 'shipment',
	};

	beforeEach(() => {
		const statusServiceSpy = jasmine.createSpyObj('MawbStatusService', ['getStatusHistoryList']);

		TestBed.configureTestingModule({
			imports: [StatusHistoryComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MawbStatusService, useValue: statusServiceSpy },
				{ provide: 'MAT_DIALOG_DATA', useValue: mockDialogData },
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: mockDialogData },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		});

		fixture = TestBed.createComponent(StatusHistoryComponent);
		component = fixture.componentInstance;
		statusService = TestBed.inject(MawbStatusService) as jasmine.SpyObj<MawbStatusService>;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should call initData on ngOnInit', () => {
		const mockData: StatusHistory[] = [
			{
				latestStatus: 'Created',
				eventDate: '2025-01-01',
				userName: 'Admin',
				eventTimeType: 'System',
				partialEventIndicator: false,
				orgName: '',
				eventLoId: '',
			},
		];

		statusService.getStatusHistoryList.and.returnValue(of(mockData));

		fixture.detectChanges();

		expect(statusService.getStatusHistoryList).toHaveBeenCalledWith(mockDialogData.loId, mockDialogData.type);
		expect(component.dataSource.data).toEqual(mockData);
		expect(component.dataLoading).toBeFalse();
	});

	it('should call getHistory with correct params and update dataSource', () => {
		const mockData: StatusHistory[] = [
			{
				latestStatus: 'Created',
				eventDate: '2025-01-01',
				userName: 'Admin',
				eventTimeType: 'System',
				partialEventIndicator: true,
				orgName: '',
				eventLoId: '',
			},
		];

		statusService.getStatusHistoryList.and.returnValue(of(mockData));

		component['initData']();

		expect(statusService.getStatusHistoryList).toHaveBeenCalledWith(mockDialogData.loId, mockDialogData.type);
		expect(component.dataSource.data).toEqual(mockData);
		expect(component.dataLoading).toBeFalse();
	});

	it('should handle error and set dataLoading to false', () => {
		statusService.getStatusHistoryList.and.returnValue(throwError(() => new Error('API Error')));

		component['initData']();

		expect(statusService.getStatusHistoryList).toHaveBeenCalled();
		expect(component.dataLoading).toBeFalse();
	});

	it('should have correct displayedColumns', () => {
		const expectedColumns = ['event', 'updateTime', 'updateBy'];
		expect(component.displayedColumns).toEqual(expectedColumns);
	});

	describe('Component Initialization', () => {
		it('should initialize with correct dialog data', () => {
			expect(component.data).toEqual(mockDialogData);
			expect(component.data.loId).toBe('123');
			expect(component.data.type).toBe('shipment');
		});

		it('should initialize dataSource as empty', () => {
			expect(component.dataSource.data).toEqual([]);
		});

		it('should set dataLoading to false initially', () => {
			expect(component.dataLoading).toBeFalse();
		});
	});

	describe('Data Loading States', () => {
		it('should set dataLoading to true when initData starts', () => {
			const subject = new Subject<StatusHistory[]>();
			statusService.getStatusHistoryList.and.returnValue(subject.asObservable());

			component['initData']();

			expect(component.dataLoading).toBeTruthy();

			// Complete the observable to clean up
			subject.next([]);
			subject.complete();
		});

		it('should handle empty response correctly', () => {
			statusService.getStatusHistoryList.and.returnValue(of([]));

			component['initData']();

			expect(component.dataSource.data).toEqual([]);
			expect(component.dataLoading).toBeFalse();
		});

		it('should handle multiple status history items', () => {
			const mockData: StatusHistory[] = [
				{
					latestStatus: 'Created',
					eventDate: '2025-01-01',
					userName: 'Admin',
					eventTimeType: 'System',
					partialEventIndicator: false,
					orgName: 'Org1',
					eventLoId: 'event1',
				},
				{
					latestStatus: 'Updated',
					eventDate: '2025-01-02',
					userName: 'User',
					eventTimeType: 'Manual',
					partialEventIndicator: true,
					orgName: 'Org2',
					eventLoId: 'event2',
				},
			];

			statusService.getStatusHistoryList.and.returnValue(of(mockData));

			component['initData']();

			expect(component.dataSource.data).toEqual(mockData);
			expect(component.dataSource.data.length).toBe(2);
			expect(component.dataLoading).toBeFalse();
		});
	});

	describe('Error Handling', () => {
		it('should handle network errors gracefully', () => {
			const networkError = new Error('Network Error');
			statusService.getStatusHistoryList.and.returnValue(throwError(() => networkError));

			component['initData']();

			expect(component.dataLoading).toBeFalse();
			expect(component.dataSource.data).toEqual([]);
		});

		it('should handle HTTP 404 errors', () => {
			const httpError = { status: 404, message: 'Not Found' };
			statusService.getStatusHistoryList.and.returnValue(throwError(() => httpError));

			component['initData']();

			expect(component.dataLoading).toBeFalse();
		});

		it('should handle HTTP 500 errors', () => {
			const serverError = { status: 500, message: 'Internal Server Error' };
			statusService.getStatusHistoryList.and.returnValue(throwError(() => serverError));

			component['initData']();

			expect(component.dataLoading).toBeFalse();
		});
	});

	describe('Service Integration', () => {
		it('should call service with correct parameters for different types', () => {
			const testCases = [
				{ loId: 'mawb-123', type: 'mawb' },
				{ loId: 'hawb-456', type: 'hawb' },
				{ loId: 'piece-789', type: 'piece' },
			];

			testCases.forEach((testCase) => {
				// Reset the component with new data
				component.data = testCase;
				statusService.getStatusHistoryList.and.returnValue(of([]));

				component['initData']();

				expect(statusService.getStatusHistoryList).toHaveBeenCalledWith(testCase.loId, testCase.type);
			});
		});

		it('should handle service response with all possible field values', () => {
			const mockData: StatusHistory[] = [
				{
					latestStatus: 'Status with special chars: @#$%',
					eventDate: '2025-12-31T23:59:59Z',
					userName: 'User with spaces',
					eventTimeType: 'Estimated',
					partialEventIndicator: true,
					orgName: 'Organization Name',
					eventLoId: 'complex-event-id-123',
				},
			];

			statusService.getStatusHistoryList.and.returnValue(of(mockData));

			component['initData']();

			expect(component.dataSource.data).toEqual(mockData);
			expect(component.dataSource.data[0].latestStatus).toContain('special chars');
			expect(component.dataSource.data[0].partialEventIndicator).toBeTruthy();
		});
	});

	describe('Component Lifecycle', () => {
		it('should call initData only once during ngOnInit', () => {
			spyOn(component as any, 'initData');

			component.ngOnInit();

			expect((component as any).initData).toHaveBeenCalledTimes(1);
		});

		it('should not call initData multiple times if ngOnInit is called multiple times', () => {
			spyOn(component as any, 'initData');

			component.ngOnInit();
			component.ngOnInit();
			component.ngOnInit();

			expect((component as any).initData).toHaveBeenCalledTimes(3);
		});
	});
});
