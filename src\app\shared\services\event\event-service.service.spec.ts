import { TestBed } from '@angular/core/testing';
import { EventService } from './event-service.service';
import { Observable } from 'rxjs';

describe('EventService', () => {
	let service: EventService;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [EventService],
		});
		service = TestBed.inject(EventService);
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	describe('Service Initialization', () => {
		it('should initialize with empty string as default value', () => {
			service.pieceInstructionChange$.subscribe((value) => {
				expect(value).toBe('');
			});
		});

		it('should have pieceInstructionChange$ observable defined', () => {
			expect(service.pieceInstructionChange$).toBeDefined();
			expect(service.pieceInstructionChange$).toBeInstanceOf(Observable);
		});

		it('should be provided in root', () => {
			// Test that the service is a singleton by creating another instance
			const anotherService = TestBed.inject(EventService);
			expect(service).toBe(anotherService);
		});
	});

	describe('publicPicesInstructionChange', () => {
		it('should update the observable with new message', () => {
			const testMessage = 'Test instruction message';
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange(testMessage);

			expect(receivedMessage!).toBe(testMessage);
		});

		it('should handle empty string message', () => {
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange('');

			expect(receivedMessage!).toBe('');
		});

		it('should handle null message', () => {
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange(null as any);

			expect(receivedMessage!).toBeNull();
		});

		it('should handle undefined message', () => {
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange(undefined as any);

			expect(receivedMessage!).toBeUndefined();
		});

		it('should handle special characters in message', () => {
			const specialMessage = 'Test with special chars: !@#$%^&*()_+-=[]{}|;:,.<>?';
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange(specialMessage);

			expect(receivedMessage!).toBe(specialMessage);
		});

		it('should handle long message strings', () => {
			const longMessage = 'A'.repeat(1000);
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange(longMessage);

			expect(receivedMessage!).toBe(longMessage);
			expect(receivedMessage!.length).toBe(1000);
		});

		it('should handle unicode characters', () => {
			const unicodeMessage = '测试消息 🚀 émojis and ñoñó';
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange(unicodeMessage);

			expect(receivedMessage!).toBe(unicodeMessage);
		});
	});

	describe('Observable Behavior', () => {
		it('should emit latest value to new subscribers', () => {
			const firstMessage = 'First message';
			const secondMessage = 'Second message';

			// Send first message
			service.publicPicesInstructionChange(firstMessage);

			// New subscriber should receive the latest value (firstMessage)
			let receivedMessage1: string;
			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage1 = message;
			});
			expect(receivedMessage1!).toBe(firstMessage);

			// Send second message
			service.publicPicesInstructionChange(secondMessage);

			// Another new subscriber should receive the latest value (secondMessage)
			let receivedMessage2: string;
			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage2 = message;
			});
			expect(receivedMessage2!).toBe(secondMessage);
		});

		it('should notify all subscribers when message changes', () => {
			const testMessage = 'Broadcast message';
			const subscribers: string[] = [];

			// Create multiple subscribers
			service.pieceInstructionChange$.subscribe((message) => {
				subscribers.push(`subscriber1: ${message}`);
			});

			service.pieceInstructionChange$.subscribe((message) => {
				subscribers.push(`subscriber2: ${message}`);
			});

			service.pieceInstructionChange$.subscribe((message) => {
				subscribers.push(`subscriber3: ${message}`);
			});

			// Clear initial empty string notifications
			subscribers.length = 0;

			// Send message
			service.publicPicesInstructionChange(testMessage);

			// All subscribers should receive the message
			expect(subscribers).toContain(`subscriber1: ${testMessage}`);
			expect(subscribers).toContain(`subscriber2: ${testMessage}`);
			expect(subscribers).toContain(`subscriber3: ${testMessage}`);
			expect(subscribers.length).toBe(3);
		});

		it('should maintain message history for BehaviorSubject', () => {
			const messages = ['Message 1', 'Message 2', 'Message 3'];
			const receivedMessages: string[] = [];

			// Subscribe to capture all messages
			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessages.push(message);
			});

			// Send multiple messages
			messages.forEach((message) => {
				service.publicPicesInstructionChange(message);
			});

			// Should have received initial empty string + all sent messages
			expect(receivedMessages.length).toBe(messages.length + 1);
			expect(receivedMessages[0]).toBe(''); // Initial value
			expect(receivedMessages.slice(1)).toEqual(messages);
		});
	});

	describe('Integration and Performance Tests', () => {
		it('should handle rapid successive message changes', () => {
			// eslint-disable-next-line @typescript-eslint/naming-convention
			const messages = Array.from({ length: 100 }, (_, i) => `Message ${i}`);
			let lastReceivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				lastReceivedMessage = message;
			});

			// Send messages rapidly
			messages.forEach((message) => {
				service.publicPicesInstructionChange(message);
			});

			// Should have the last message
			expect(lastReceivedMessage!).toBe('Message 99');
		});

		it('should handle subscription and unsubscription correctly', () => {
			let receivedMessage: string;
			const subscription = service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			// Send message while subscribed
			service.publicPicesInstructionChange('Subscribed message');
			expect(receivedMessage!).toBe('Subscribed message');

			// Unsubscribe
			subscription.unsubscribe();

			// Send message after unsubscription
			service.publicPicesInstructionChange('Unsubscribed message');

			// Should still have the old message
			expect(receivedMessage!).toBe('Subscribed message');
		});

		it('should work correctly with multiple service instances in different contexts', () => {
			// Since it's provided in root, all instances should be the same
			const service1 = TestBed.inject(EventService);
			const service2 = TestBed.inject(EventService);

			expect(service1).toBe(service2);
			expect(service1).toBe(service);

			let message1: string;
			let message2: string;

			service1.pieceInstructionChange$.subscribe((msg) => (message1 = msg));
			service2.pieceInstructionChange$.subscribe((msg) => (message2 = msg));

			service1.publicPicesInstructionChange('Test singleton');

			expect(message1!).toBe('Test singleton');
			expect(message2!).toBe('Test singleton');
		});

		it('should handle concurrent subscriptions and message publishing', () => {
			const results: Record<string, string[]> = {
				subscriber1: [],
				subscriber2: [],
				subscriber3: [],
			};

			// Create concurrent subscriptions
			service.pieceInstructionChange$.subscribe((message) => {
				results['subscriber1'].push(message);
			});

			service.pieceInstructionChange$.subscribe((message) => {
				results['subscriber2'].push(message);
			});

			service.pieceInstructionChange$.subscribe((message) => {
				results['subscriber3'].push(message);
			});

			// Send multiple messages
			const messages = ['Msg1', 'Msg2', 'Msg3'];
			messages.forEach((msg) => {
				service.publicPicesInstructionChange(msg);
			});

			// All subscribers should have received all messages (plus initial empty string)
			Object.values(results).forEach((subscriberMessages) => {
				expect(subscriberMessages.length).toBe(4); // Initial '' + 3 messages
				expect(subscriberMessages[0]).toBe(''); // Initial value
				expect(subscriberMessages.slice(1)).toEqual(messages);
			});
		});
	});

	describe('Edge Cases and Error Handling', () => {
		it('should handle JSON string messages', () => {
			const jsonMessage = JSON.stringify({ type: 'instruction', data: 'test data' });
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange(jsonMessage);

			expect(receivedMessage!).toBe(jsonMessage);
			expect(() => JSON.parse(receivedMessage!)).not.toThrow();
		});

		it('should handle HTML string messages', () => {
			const htmlMessage = '<div>Test <strong>HTML</strong> content</div>';
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange(htmlMessage);

			expect(receivedMessage!).toBe(htmlMessage);
		});

		it('should handle whitespace-only messages', () => {
			const whitespaceMessage = '   \t\n\r   ';
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange(whitespaceMessage);

			expect(receivedMessage!).toBe(whitespaceMessage);
		});

		it('should handle numeric string messages', () => {
			const numericMessage = '12345.67';
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange(numericMessage);

			expect(receivedMessage!).toBe(numericMessage);
			expect(typeof receivedMessage!).toBe('string');
		});

		it('should handle boolean string messages', () => {
			const booleanMessage = 'true';
			let receivedMessage: string;

			service.pieceInstructionChange$.subscribe((message) => {
				receivedMessage = message;
			});

			service.publicPicesInstructionChange(booleanMessage);

			expect(receivedMessage!).toBe(booleanMessage);
			expect(typeof receivedMessage!).toBe('string');
		});
	});

	describe('Memory Management', () => {
		it('should not leak memory with multiple subscriptions and unsubscriptions', () => {
			const subscriptions = [];

			// Create multiple subscriptions
			for (let i = 0; i < 10; i++) {
				const subscription = service.pieceInstructionChange$.subscribe(() => {
					// Do nothing, just subscribe
				});
				subscriptions.push(subscription);
			}

			// Send some messages
			for (let i = 0; i < 5; i++) {
				service.publicPicesInstructionChange(`Message ${i}`);
			}

			// Unsubscribe all
			subscriptions.forEach((sub) => sub.unsubscribe());

			// Service should still work after all unsubscriptions
			let finalMessage: string;
			service.pieceInstructionChange$.subscribe((message) => {
				finalMessage = message;
			});

			service.publicPicesInstructionChange('Final test message');
			expect(finalMessage!).toBe('Final test message');
		});
	});
});
