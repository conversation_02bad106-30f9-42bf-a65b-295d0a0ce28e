import { ShipmentParty } from '../../sli-mgmt/models/shipment-party.model';

export interface BookingOptionRequestListObj {
	id: string;
	bookingOptionRequestId: string;
	expectedCommodity: string;
	departureLocation: string;
	arrivalLocation: string;
	latestStatus: string;
}

export interface BookingOptionRequestDetailObj {
	id?: string;
	bookingOptionRequestId?: string;
	mawbId?: string;
	bookingShipmentDetails: BookingShipmentObj;
	transportLegs: TransportObj[];
	bookingPreference: PreferenceObj[];
	timePreferences: TimePreferenceObj;
	unitsPreference: UnitPreferenceObj;
	involvedParties: AirlineObj[];
	bookingOptionList?: BookingOptionInfo[];
	shareList?: SharedInfo[];
}

export interface SharedInfo {
	orgId: string;
	orgName: string;
	airlineCode: string;
	requestTime: string;
}

export interface BookingOptionInfo {
	id: string;
	bookingOptionRequestId: string;
	carrier: ShipmentParty;
	grandTotal: number;
	priceList: BookingOptionPieceObj[];
	transportLegsList: BookingOptionTransportObj[];
	productDescription: string;
	offerValidFrom: string;
	offerValidTo: string;
	isChoose: boolean;
}

export interface BookingInfo {
	id?: string;
	waybillPrefix: string;
	waybillNumber: string;
	priceSaveDto: BookingOptionPieceObj | null;
	transportLegsSaveDto: BookingOptionTransportObj | null;
	bookingShipment: BookingShipmentObj;
	unitsPreference: UnitPreferenceObj;
}

export interface BookingShipmentObj {
	id: string;
	pieceGroups: PieceGroupObj;
	totalGrossWeight: WeightObj;
	chargeableWeight: WeightObj;
	dimensions: DimensionObj;
	expectedCommodity: string;
	specialHandlingCodes: string[];
	textualHandlingInstructions: string;
}

export interface WeightObj {
	currencyUnit: string;
	numericalValue: number;
}

export interface AirlineObj {
	airlineCode: string;
	iataCargoAgentCode: string;
	companyName: string;
}

export interface PieceGroupObj {
	id: string;
	pieceGroupCount: number;
}

export interface DimensionObj {
	length: number;
	width: number;
	height: number;
}

export interface TransportObj {
	departureLocation: LocationInfo;
	arrivalLocation: LocationInfo;
}

export interface LocationInfo {
	locationCodes: {
		code: string;
	}[];
	locationName: string;
}

export interface PreferenceObj {
	maxSegments: number;
	preferredTransportId: string;
}

export interface TimePreferenceObj {
	earliestAcceptanceTime: string;
	latestAcceptanceTime: string;
	latestArrivalTime: string;
	timeOfAvailability: string;
}

export interface UnitPreferenceObj {
	currency: { currencyUnit: string };
}

export interface OptionRequestDetail {
	totalGrossWeight: string;
	chargeableWeight: string;
	dimLength: string;
	dimWidth: string;
	dimHeight: string;
	pieceGroupCount: string;
	expectedCommodity: string;
	specialHandlingCodes: string;
	textualHandlingInstructions: string;
	departureLocation: string;
	arrivalLocation: string;
	requestedFlight?: string;
	requestedDate?: string;
	airlineCode?: string;
	rateClassCode?: string;
	rateCharge?: number;
	weightValuationIndicator?: string;
	maxSegments: string;
	preferredTransportId: string;
	earliestAcceptanceTime: string;
	latestAcceptanceTime: string;
	latestArrivalTime: string;
	timeOfAvailability: string;
	currency: string;
	iataCargoAgentCode: string;
	companyName?: string;
	mawbPrefix?: string;
	mawbNumber?: string;
	bookingOptionRequestId?: string;
}

export interface BookingRequestSearchParam {
	requestedProduct?: string;
	requestedStatus?: string;
	departureLocation?: string;
	arrivalLocation?: string;
}

export interface BookingRequestListObj {
	id: string;
	copyId?: string;
	bookingId?: string;
	bookingRequestId: string;
	requestedBy: string;
	requestedProduct: string;
	requestedFlight: string;
	requestedStatus: string;
	departureLocation: string;
	arrivalLocation: string;
	mawbId?: string;
	mawbNumber?: string;
	flightDate: string;
	canConfirm: boolean;
	canShare: boolean;
}

export interface BookingRequestObj {
	waybillPrefix?: string;
	waybillNumber?: string;
	bookingOptionId: string;
	bookingShipmentId: string;
	airlineName: string;
	requestedProduct: string;
	requestedFlight: string;
	departureLocation: string;
	arrivalLocation: string;
	flightDate: string;
}

export interface BookingOptionDetail {
	bookingOptionRequestId: string;
	priceList: BookingOptionPieceObj[];
	transportLegsList: BookingOptionTransportObj[];
	productDescription: string;
	offerValidFrom: string;
	offerValidTo: string;
}

export interface BookingOptionPieceObj {
	chargeType: string;
	rateClassCode: string;
	subTotal: number;
	chargePaymentType: string;
	entitlement: string;
}

export interface BookingOptionTransportObj {
	departureLocation: string;
	arrivalLocation: string;
	airlineCode: string;
	transportIdentifier: string;
	legNumber: number;
	departureDate: string;
	arrivalDate: string;
}

export const ARRIVAL_LOCATION = 'arrivalLocation';
export const DEPARTURE_LOCATION = 'departureLocation';
export const TRANS_LIST = 'transportLegsList';
export const PRICE_LIST = 'priceList';
export const BOOKING_FINAL_STATUS = ['Booking Requested', 'Booking Confirmed'];
export const QUOTE_PROVIDED_STATUS = 'Quote Provided';
