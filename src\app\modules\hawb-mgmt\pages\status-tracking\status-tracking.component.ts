import { Component } from '@angular/core';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { NgClass } from '@angular/common';

@Component({
	selector: 'orll-status-tracking',
	imports: [MatCheckbox, MatIconModule, NgClass],
	templateUrl: './status-tracking.component.html',
	styleUrl: './status-tracking.component.scss',
})
export class StatusTrackingComponent {
	hawbList = [
		{
			id: '1',
			hawbNumber: 'HAWB 1',
			expanded: true,
			children: [
				{
					id: '1.1',
					pieceNumber: 'PIECE 1',
					expanded: true,
					children: [
						{
							id: '1.1.1',
							pieceeNumber: 'PIECE 2',
						},
						{
							id: '1.1.1',
							pieceeNumber: 'PIECE 2',
						},
						{
							id: '1.1.1',
							pieceeNumber: 'PIECE 2',
						},
					],
				},
				{
					id: '1.2',
				},
				{
					id: '1.3',
				},
			],
		},

		{
			id: '2',
			expanded: false,
		},

		{
			id: '3',
			expanded: false,
		},
	];

	toggleExpanded(item: any) {
		item.expanded = !item.expanded;
	}
}
