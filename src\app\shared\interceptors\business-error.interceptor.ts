import { Injectable } from '@angular/core';
import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { NotificationService } from '@shared/services/notification.service';
import { catchError, filter, map } from 'rxjs/operators';

@Injectable()
export class BusinessErrorInterceptor implements HttpInterceptor {
	constructor(private readonly notificationService: NotificationService) {}

	intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
		if (!/(^https?:\/\/[^/]+)?\/api/g.exec(request.url)) {
			return next.handle(request);
		} else {
			return next.handle(request).pipe(
				filter((res) => res instanceof HttpResponse),
				map((res: HttpResponse<any>) => {
					if (res.body.code && res.body.code !== 200) {
						throw res;
					} else if (res.body.code === 200 && 'data' in res.body) {
						const msg = res.body.msg ?? res.body.showMessage;
						if (msg && request.method !== 'GET') {
							this.notificationService.showSuccess(msg);
						}

						return res.clone({
							body: res.body.data,
						});
					}
					return res;
				}),
				catchError((err) => {
					if (err instanceof HttpResponse) {
						if (err.body.code === 100401) {
							this.notificationService.showError(err.body.msg);
						}
					} else if (err instanceof HttpErrorResponse) {
						if (err.error?.msg) {
							this.notificationService.showError(err.error.msg);
						} else {
							this.notificationService.showError(err.message);
						}
					}

					throw err;
				})
			);
		}
	}
}
