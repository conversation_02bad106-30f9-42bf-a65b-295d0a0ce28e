import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';
// eslint-disable-next-line @typescript-eslint/naming-convention
import BookingRequestListComponent from './booking-request-list.component';
import { BookingRequestService } from '../../services/booking-request.service';
import { SliSearchRequestService } from 'src/app/modules/sli-mgmt/services/sli-search-request.service';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';
import { BookingRequestListObj } from '../../models/booking.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { CodeName } from '@shared/models/code-name.model';
import { UserProfileService } from '@shared/services/user-profile.service';
import { UserProfile } from '@shared/models/user-profile.model';
import { Router } from '@angular/router';
import { BookingOptionAirlineComponent } from '../../components/booking-option-airline/booking-option-airline.component';

describe('BookingRequestListComponent', () => {
	let component: BookingRequestListComponent;
	let fixture: ComponentFixture<BookingRequestListComponent>;
	let bookingRequestServiceSpy: jasmine.SpyObj<BookingRequestService>;
	let routerSpy: jasmine.SpyObj<Router>;

	const mockBookingRequestData: BookingRequestListObj[] = [
		{
			id: '1',
			bookingRequestId: '123',
			canConfirm: true,
			canShare: true,
			requestedBy: 'John Doe',
			requestedProduct: 'SLI',
			requestedFlight: 'AA123',
			requestedStatus: 'Booking Requested',
			departureLocation: 'LAX',
			arrivalLocation: 'JFK',
			mawbNumber: '123456789',
			flightDate: '2023-12-01T10:00:00Z',
		},
		{
			id: '2',
			bookingRequestId: '456',
			canConfirm: false,
			canShare: false,
			requestedBy: 'Jane Smith',
			requestedProduct: 'MAWB',
			requestedFlight: 'BA456',
			requestedStatus: 'Booking Confirmed',
			departureLocation: 'LHR',
			arrivalLocation: 'CDG',
			mawbNumber: '987654321',
			flightDate: '2023-12-02T14:30:00Z',
		},
	];

	const mockPaginationResponse: PaginationResponse<BookingRequestListObj> = {
		total: 2,
		rows: mockBookingRequestData,
	};

	beforeEach(async () => {
		const bookingRequestServiceSpyObj = jasmine.createSpyObj('BookingRequestService', ['getDataPerPage', 'loadAllData']);
		const sliSearchRequestServiceSpyObj = jasmine.createSpyObj('SliSearchRequestService', ['getAirportList', 'getOptions']);
		sliSearchRequestServiceSpyObj.getOptions.and.returnValue(of([]));
		const iataDateFormatPipeSpyObj = jasmine.createSpyObj('IataDateFormatPipe', ['transform']);
		const routerSpyObj = jasmine.createSpyObj('Router', ['navigate']);
		const userProfileServiceSpyObj = jasmine.createSpyObj('UserProfileService', ['hasSomeRole', 'hasPermission', 'isSuperUser'], {
			currentUser$: of({
				userId: 'test-user',
				email: '<EMAIL>',
				firstName: 'Test',
				lastName: 'User',
				primaryOrgId: 'org-1',
				primaryOrgName: 'Test Org',
				orgId: 'org-1',
				orgName: 'Test Org',
				orgType: 'SHP',
				userType: '1',
				menuList: [],
				permissionList: [],
				orgList: [],
			} as UserProfile),
		});

		// Set default return values for UserProfileService methods
		userProfileServiceSpyObj.hasSomeRole.and.returnValue(of(false));
		userProfileServiceSpyObj.hasPermission.and.returnValue(of(false));
		userProfileServiceSpyObj.isSuperUser.and.returnValue(of(false));

		await TestBed.configureTestingModule({
			imports: [BookingRequestListComponent, ReactiveFormsModule, BrowserAnimationsModule, TranslateModule.forRoot()],
			providers: [
				{ provide: BookingRequestService, useValue: bookingRequestServiceSpyObj },
				{ provide: SliSearchRequestService, useValue: sliSearchRequestServiceSpyObj },
				{ provide: IataDateFormatPipe, useValue: iataDateFormatPipeSpyObj },
				{ provide: Router, useValue: routerSpyObj },
				{ provide: UserProfileService, useValue: userProfileServiceSpyObj },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(BookingRequestListComponent);
		component = fixture.componentInstance;
		bookingRequestServiceSpy = TestBed.inject(BookingRequestService) as jasmine.SpyObj<BookingRequestService>;
		routerSpy = TestBed.inject(Router) as jasmine.SpyObj<Router>;

		// Setup default spy returns
		bookingRequestServiceSpy.getDataPerPage.and.returnValue(of(mockPaginationResponse));
		bookingRequestServiceSpy.loadAllData.and.returnValue(of(mockBookingRequestData));

		fixture.detectChanges();
	});

	describe('Dialog and Reset behaviour', () => {
		it('should open request detail dialog with correct payload and trigger onSearch when closed with result', () => {
			const row = mockBookingRequestData[0];
			const dialogRef = { afterClosed: () => of(true) } as any;
			spyOn((component as any).dialog, 'open').and.returnValue(dialogRef);
			spyOn(component, 'onSearch');

			component.openRequestDetailDialog(row);

			expect((component as any).dialog.open).toHaveBeenCalled();
			const args = ((component as any).dialog.open as jasmine.Spy).calls.mostRecent().args[1];
			expect(args.data.bookingRequestId).toBe(row.bookingRequestId);
			expect(args.data.canConfirm).toBe(row.canConfirm);
			expect(args.data.buttonName).toBe('booking.dialog.button.confirm');
			expect(component.onSearch).toHaveBeenCalled();
		});

		it('should call dialog.open when requestedStatus column clickCell is invoked', () => {
			const row = mockBookingRequestData[1];
			spyOn((component as any).dialog, 'open').and.returnValue({ afterClosed: () => of(null) } as any);

			const statusColumn = component.columns.find((c) => c.key === 'requestedStatus');
			expect(typeof statusColumn?.clickCell).toBe('function');
			(statusColumn?.clickCell as any)(row);

			expect((component as any).dialog.open).toHaveBeenCalled();
		});

		it('should reset form, clear selected locations and call eraseValue on autocompletes when onReset is invoked', () => {
			const mockEvent = { preventDefault: jasmine.createSpy(), stopPropagation: jasmine.createSpy() } as any;
			// populate form and selected locations
			component.bookingRequestSearchForm.patchValue({ requestedProduct: 'SLI', requestedStatus: 'Booking Requested' });
			component.selectedDepartureLocations = [{ code: 'LAX', name: 'Los Angeles' } as any];
			component.selectedArrivalLocations = [{ code: 'JFK', name: 'New York' } as any];

			const mockAutocomplete = { eraseValue: jasmine.createSpy('eraseValue') } as any;
			// Mock autocompleteList as an iterable array
			component.autocompleteList = [mockAutocomplete] as any;

			component.onReset(mockEvent);

			expect(component.bookingRequestSearchForm.get('requestedProduct')?.value).toBe('');
			expect(component.bookingRequestSearchForm.get('requestedStatus')?.value).toBe('');
			expect(component.selectedDepartureLocations).toEqual([]);
			expect(component.selectedArrivalLocations).toEqual([]);
			expect(mockAutocomplete.eraseValue).toHaveBeenCalledWith(mockEvent);
		});
	});

	describe('Component Initialization', () => {
		it('should create', () => {
			expect(component).toBeTruthy();
		});

		it('should extend RolesAwareComponent', () => {
			expect(component).toBeInstanceOf(BookingRequestListComponent);
		});

		it('should initialize form with correct controls', () => {
			expect(component.bookingRequestSearchForm).toBeDefined();
			expect(component.bookingRequestSearchForm.get('requestedProduct')).toBeTruthy();
			expect(component.bookingRequestSearchForm.get('requestedStatus')).toBeTruthy();
		});

		it('should initialize form controls with empty values', () => {
			expect(component.bookingRequestSearchForm.get('requestedProduct')?.value).toBe('');
			expect(component.bookingRequestSearchForm.get('requestedStatus')?.value).toBe('');
		});

		it('should initialize booking request status array', () => {
			expect(component.bookingRequestStatus).toEqual([DropDownType.REQUESTED_STATUS, DropDownType.CONFIRMED_STATUS]);
		});

		it('should initialize selected locations arrays as empty', () => {
			expect(component.selectedDepartureLocations).toEqual([]);
			expect(component.selectedArrivalLocations).toEqual([]);
		});

		it('should initialize search parameters as empty object', () => {
			expect(component.bookingRequestSearchParam).toEqual({});
		});

		it('should initialize dataLoading as false', () => {
			expect(component.dataLoading).toBe(false);
		});
	});

	describe('Form Handling', () => {
		it('should update search parameters when form values change', () => {
			component.bookingRequestSearchForm.patchValue({
				requestedProduct: 'SLI',
				requestedStatus: 'Booking Requested',
			});

			component.onSearch();

			expect(component.bookingRequestSearchParam.requestedProduct).toBe('SLI');
			expect(component.bookingRequestSearchParam.requestedStatus).toBe('Booking Requested');
		});

		it('should handle empty form values', () => {
			component.bookingRequestSearchForm.patchValue({
				requestedProduct: '',
				requestedStatus: '',
			});

			component.onSearch();

			expect(component.bookingRequestSearchParam.requestedProduct).toBe('');
			expect(component.bookingRequestSearchParam.requestedStatus).toBe('');
		});

		it('should allow form values to be reset manually', () => {
			component.bookingRequestSearchForm.patchValue({
				requestedProduct: 'SLI',
				requestedStatus: 'Booking Requested',
			});

			component.bookingRequestSearchForm.reset();

			expect(component.bookingRequestSearchForm.get('requestedProduct')?.value).toBeNull();
			expect(component.bookingRequestSearchForm.get('requestedStatus')?.value).toBeNull();
		});

		it('should allow selected locations to be cleared manually', () => {
			component.selectedDepartureLocations = [
				{ code: 'LAX', name: 'Los Angeles' },
				{ code: 'JFK', name: 'New York' },
			];
			component.selectedArrivalLocations = [
				{ code: 'CDG', name: 'Paris' },
				{ code: 'LHR', name: 'London' },
			];

			component.selectedDepartureLocations = [];
			component.selectedArrivalLocations = [];

			expect(component.selectedDepartureLocations).toEqual([]);
			expect(component.selectedArrivalLocations).toEqual([]);
		});

		it('should allow search parameters to be cleared manually', () => {
			component.bookingRequestSearchParam = {
				requestedProduct: 'SLI',
				requestedStatus: 'Booking Requested',
			};

			component.bookingRequestSearchParam = {};

			expect(component.bookingRequestSearchParam).toEqual({});
		});
	});

	describe('Data Loading', () => {
		it('should update search parameters on search', () => {
			component.bookingRequestSearchForm.patchValue({
				requestedProduct: 'SLI',
			});

			component.onSearch();

			expect(component.bookingRequestSearchParam.requestedProduct).toBe('SLI');
		});

		it('should update search parameters with form values and location arrays', () => {
			component.bookingRequestSearchForm.patchValue({
				requestedProduct: 'SLI',
				requestedStatus: 'Booking Requested',
			});
			component.selectedDepartureLocations = [{ code: 'LAX', name: 'Los Angeles' }];
			component.selectedArrivalLocations = [{ code: 'JFK', name: 'New York' }];

			component.onSearch();

			expect(component.bookingRequestSearchParam).toEqual(
				jasmine.objectContaining({
					requestedProduct: 'SLI',
					requestedStatus: 'Booking Requested',
					departureLocationList: ['LAX'],
					arrivalLocationList: ['JFK'],
				})
			);
		});

		it('should handle empty location arrays', () => {
			component.selectedDepartureLocations = [];
			component.selectedArrivalLocations = [];

			component.onSearch();

			expect(component.bookingRequestSearchParam).toEqual(
				jasmine.objectContaining({
					departureLocationList: [],
					arrivalLocationList: [],
				})
			);
		});

		it('should merge form values with existing search parameters', () => {
			component.bookingRequestSearchParam = { requestedProduct: 'HAWB' } as any;
			component.bookingRequestSearchForm.patchValue({
				requestedStatus: 'Booking Confirmed',
			});

			component.onSearch();

			// Form values override existing parameters, and empty form values become empty strings
			expect(component.bookingRequestSearchParam).toEqual(
				jasmine.objectContaining({
					requestedProduct: '', // Form value overrides existing value
					requestedStatus: 'Booking Confirmed',
					departureLocationList: [],
					arrivalLocationList: [],
				})
			);
		});

		it('should not throw errors during search', () => {
			expect(() => component.onSearch()).not.toThrow();
		});
	});

	describe('Location Selection', () => {
		it('should allow departure locations to be set', () => {
			const mockLocations: CodeName[] = [
				{ code: 'LAX', name: 'Los Angeles' },
				{ code: 'JFK', name: 'New York' },
			];

			component.selectedDepartureLocations = mockLocations;

			expect(component.selectedDepartureLocations).toEqual(mockLocations);
			expect(component.selectedDepartureLocations.length).toBe(2);
		});

		it('should allow arrival locations to be set', () => {
			const mockLocations: CodeName[] = [
				{ code: 'CDG', name: 'Paris' },
				{ code: 'LHR', name: 'London' },
			];

			component.selectedArrivalLocations = mockLocations;

			expect(component.selectedArrivalLocations).toEqual(mockLocations);
			expect(component.selectedArrivalLocations.length).toBe(2);
		});

		it('should handle empty location arrays', () => {
			component.selectedDepartureLocations = [];
			component.selectedArrivalLocations = [];

			expect(component.selectedDepartureLocations).toEqual([]);
			expect(component.selectedArrivalLocations).toEqual([]);
		});

		it('should map location codes correctly in onSearch', () => {
			component.selectedDepartureLocations = [{ code: 'LAX', name: 'Los Angeles' }];
			component.selectedArrivalLocations = [{ code: 'JFK', name: 'New York' }];

			component.onSearch();

			expect(component.bookingRequestSearchParam).toEqual(
				jasmine.objectContaining({
					departureLocationList: ['LAX'],
					arrivalLocationList: ['JFK'],
				})
			);
		});
	});

	describe('Table Configuration', () => {
		it('should configure table columns correctly', () => {
			expect(component.columns).toBeDefined();
			expect(component.columns.length).toBeGreaterThanOrEqual(8);
		});

		it('should have correct column properties', () => {
			const requestedByColumn = component.columns.find((col) => col.key === 'requestedBy');
			expect(requestedByColumn).toBeDefined();
			expect(requestedByColumn?.header).toBe('booking.mgmt.requestedBy');

			const statusColumn = component.columns.find((col) => col.key === 'requestedStatus');
			expect(statusColumn).toBeDefined();
			expect(statusColumn?.header).toBe('booking.mgmt.requestedStatus');
		});

		it('should have flight date column with transform function', () => {
			const flightDateColumn = component.columns.find((col) => col.key === 'flightDate');
			expect(flightDateColumn).toBeDefined();
			expect(flightDateColumn?.transform).toBeDefined();
			expect(typeof flightDateColumn?.transform).toBe('function');
		});

		it('should have all required columns', () => {
			const expectedColumns = [
				'requestedBy',
				'requestedProduct',
				'requestedFlight',
				'flightDate',
				'departureLocation',
				'arrivalLocation',
				'requestedStatus',
			];

			expectedColumns.forEach((columnKey) => {
				const column = component.columns.find((col) => col.key === columnKey);
				expect(column).toBeDefined();
			});
		});
	});

	describe('Component Properties', () => {
		it('should have correct booking request status options', () => {
			expect(component.bookingRequestStatus).toEqual([DropDownType.REQUESTED_STATUS, DropDownType.CONFIRMED_STATUS]);
		});

		it('should have dataLoading property', () => {
			expect(component.dataLoading).toBeDefined();
			expect(typeof component.dataLoading).toBe('boolean');
		});

		it('should have bookingRequestService injected', () => {
			expect(component.bookingRequestService).toBeDefined();
		});

		it('should have sliSearchRequestService injected', () => {
			expect(component.sliSearchRequestService).toBeDefined();
		});
	});

	describe('Navigation Methods', () => {
		describe('onCreate', () => {
			it('should navigate to booking create page', () => {
				component.onCreate();

				expect(routerSpy.navigate).toHaveBeenCalledWith(['booking/create']);
			});
		});

		describe('linkMawb', () => {
			it('should navigate to mawb edit page with correct mawbId', () => {
				const mawbId = 'test-mawb-123';

				component.linkMawb(mawbId);

				expect(routerSpy.navigate).toHaveBeenCalledWith(['mawb/edit', mawbId]);
			});

			it('should not navigate when mawbId is empty', () => {
				component.linkMawb('');

				expect(routerSpy.navigate).not.toHaveBeenCalled();
			});

			it('should not navigate when mawbId is null', () => {
				component.linkMawb(null as any);

				expect(routerSpy.navigate).not.toHaveBeenCalled();
			});

			it('should not navigate when mawbId is undefined', () => {
				component.linkMawb(undefined as any);

				expect(routerSpy.navigate).not.toHaveBeenCalled();
			});
		});
	});

	describe('Dialog Methods', () => {
		describe('shareBookingRequest', () => {
			it('should open BookingOptionAirlineComponent dialog with correct data', () => {
				const row = mockBookingRequestData[0];
				const dialogRef = { afterClosed: () => of(true) } as any;
				spyOn((component as any).dialog, 'open').and.returnValue(dialogRef);
				spyOn(component, 'onSearch');

				component.shareBookingRequest(row);

				expect((component as any).dialog.open).toHaveBeenCalledWith(BookingOptionAirlineComponent, {
					width: '30vw',
					autoFocus: false,
					data: { id: row.bookingRequestId, fromBooking: true },
				});
				expect(component.onSearch).toHaveBeenCalled();
			});

			it('should call onSearch when dialog closes with success result', () => {
				const row = mockBookingRequestData[0];
				const dialogRef = { afterClosed: () => of(true) } as any;
				spyOn((component as any).dialog, 'open').and.returnValue(dialogRef);
				spyOn(component, 'onSearch');

				component.shareBookingRequest(row);

				expect(component.onSearch).toHaveBeenCalled();
			});

			it('should not call onSearch when dialog closes without success result', () => {
				const row = mockBookingRequestData[0];
				const dialogRef = { afterClosed: () => of(false) } as any;
				spyOn((component as any).dialog, 'open').and.returnValue(dialogRef);
				spyOn(component, 'onSearch');

				component.shareBookingRequest(row);

				expect(component.onSearch).not.toHaveBeenCalled();
			});

			it('should not call onSearch when dialog closes with null result', () => {
				const row = mockBookingRequestData[0];
				const dialogRef = { afterClosed: () => of(null) } as any;
				spyOn((component as any).dialog, 'open').and.returnValue(dialogRef);
				spyOn(component, 'onSearch');

				component.shareBookingRequest(row);

				expect(component.onSearch).not.toHaveBeenCalled();
			});
		});
	});

	describe('ngOnInit Role-based Actions', () => {
		it('should add actions column when user has forwarder role', () => {
			// Mock hasSomeRole to return true for forwarder role
			const userProfileService = TestBed.inject(UserProfileService) as jasmine.SpyObj<UserProfileService>;
			userProfileService.hasSomeRole.and.returnValue(of(true));

			// Reset component to test ngOnInit
			component.ngOnInit();

			// Check that actions column was added
			const actionsColumn = component.columns.find((col) => col.key === 'actions');
			expect(actionsColumn).toBeDefined();
			expect(actionsColumn?.header).toBe('common.table.action');
			expect(actionsColumn?.actions).toBeDefined();
			expect(actionsColumn?.actions?.length).toBe(2);
		});

		it('should configure share action correctly', () => {
			const userProfileService = TestBed.inject(UserProfileService) as jasmine.SpyObj<UserProfileService>;
			userProfileService.hasSomeRole.and.returnValue(of(true));

			// Create a fresh component instance to test role-based initialization
			const fixture2 = TestBed.createComponent(BookingRequestListComponent);
			const component2 = fixture2.componentInstance;
			fixture2.detectChanges();

			const actionsColumn = component2.columns.find((col) => col.key === 'actions');
			const shareAction = actionsColumn?.actions?.[0];

			expect(shareAction?.iconKey).toBe('share');
			expect(shareAction?.iconClickAction).toBeDefined();
			expect(shareAction?.showCondition).toBeDefined();

			// Test showCondition
			const rowWithShare = { ...mockBookingRequestData[0], canShare: true };
			const rowWithoutShare = { ...mockBookingRequestData[0], canShare: false };

			expect(shareAction?.showCondition?.(rowWithShare)).toBeTrue();
			expect(shareAction?.showCondition?.(rowWithoutShare)).toBeFalse();
		});

		it('should configure copy action correctly', () => {
			const userProfileService = TestBed.inject(UserProfileService) as jasmine.SpyObj<UserProfileService>;
			userProfileService.hasSomeRole.and.returnValue(of(true));

			component.ngOnInit();

			const actionsColumn = component.columns.find((col) => col.key === 'actions');
			const copyAction = actionsColumn?.actions?.[1];

			expect(copyAction?.iconKey).toBe('content_copy');
			expect(copyAction?.showCopy).toBeDefined();

			// Test showCopy
			const confirmedRow = { ...mockBookingRequestData[0], requestedStatus: DropDownType.CONFIRMED_STATUS, bookingId: 'booking-123' };
			const requestedRow = { ...mockBookingRequestData[0], requestedStatus: DropDownType.REQUESTED_STATUS, bookingId: 'booking-456' };

			expect(copyAction?.showCopy?.(confirmedRow)).toBeTrue();
			expect(confirmedRow.copyId).toBe('booking-123');

			expect(copyAction?.showCopy?.(requestedRow)).toBeFalse();
		});

		it('should not show share action when user does not have forwarder role', () => {
			const userProfileService = TestBed.inject(UserProfileService) as jasmine.SpyObj<UserProfileService>;
			userProfileService.hasSomeRole.and.returnValue(of(false));

			component.ngOnInit();

			const actionsColumn = component.columns.find((col) => col.key === 'actions');
			const shareAction = actionsColumn?.actions?.[0];

			const rowWithShare = { ...mockBookingRequestData[0], canShare: true };
			expect(shareAction?.showCondition?.(rowWithShare)).toBeFalse();
		});
	});

	describe('Column Click Actions', () => {
		it('should call linkMawb when mawbNumber column is clicked', () => {
			spyOn(component, 'linkMawb');
			const row = { ...mockBookingRequestData[0], mawbId: 'test-mawb-id' };

			const mawbColumn = component.columns.find((col) => col.key === 'mawbNumber');
			expect(mawbColumn?.clickCell).toBeDefined();

			mawbColumn?.clickCell?.(row);

			expect(component.linkMawb).toHaveBeenCalledWith('test-mawb-id');
		});

		it('should show link for mawbNumber when mawbId exists', () => {
			const rowWithMawbId = { ...mockBookingRequestData[0], mawbId: 'test-mawb-id' };
			const rowWithoutMawbId = { ...mockBookingRequestData[0], mawbId: undefined };

			const mawbColumn = component.columns.find((col) => col.key === 'mawbNumber');

			expect(mawbColumn?.noshowLink?.(rowWithMawbId)).toBeFalse();
			expect(mawbColumn?.noshowLink?.(rowWithoutMawbId)).toBeTrue();
		});
	});
});
